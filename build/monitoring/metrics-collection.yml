# Metrics Collection Configuration for CCL Platform CI/CD Pipeline
# This file defines custom metrics to be collected during CI/CD operations

# Custom metrics definitions
custom_metrics:
  # Deployment metrics
  - name: "ccl/deployments"
    type: "COUNTER"
    description: "Number of deployments initiated"
    labels:
      - service_name
      - environment
      - deployment_strategy
      - triggered_by
    unit: "1"
    
  - name: "ccl/deployment_status"
    type: "COUNTER"
    description: "Deployment status outcomes"
    labels:
      - service_name
      - environment
      - status  # SUCCESS, FAILED, ROLLBACK
      - failure_reason
    unit: "1"
    
  - name: "ccl/deployment_duration"
    type: "HISTOGRAM"
    description: "Time taken for deployments to complete"
    labels:
      - service_name
      - environment
      - deployment_strategy
    unit: "s"
    buckets: [30, 60, 120, 300, 600, 1200, 1800, 3600]
    
  # Build metrics
  - name: "ccl/build_duration"
    type: "HISTOGRAM"
    description: "Time taken for builds to complete"
    labels:
      - service_name
      - language
      - build_type  # debug, release
    unit: "s"
    buckets: [30, 60, 120, 300, 600, 900, 1200, 1800]
    
  - name: "ccl/build_success_rate"
    type: "GAUGE"
    description: "Build success rate over time"
    labels:
      - service_name
      - branch
    unit: "1"
    
  - name: "ccl/build_queue_time"
    type: "HISTOGRAM"
    description: "Time builds spend waiting in queue"
    labels:
      - runner_type
      - service_name
    unit: "s"
    buckets: [5, 10, 30, 60, 120, 300, 600]
    
  # Test metrics
  - name: "ccl/test_duration"
    type: "HISTOGRAM"
    description: "Time taken for test suites to complete"
    labels:
      - service_name
      - test_type  # unit, integration, e2e
      - language
    unit: "s"
    buckets: [10, 30, 60, 120, 300, 600, 900, 1800]
    
  - name: "ccl/test_coverage"
    type: "GAUGE"
    description: "Test coverage percentage"
    labels:
      - service_name
      - coverage_type  # line, branch, function
    unit: "%"
    
  - name: "ccl/test_results"
    type: "COUNTER"
    description: "Test execution results"
    labels:
      - service_name
      - test_type
      - result  # passed, failed, skipped
    unit: "1"
    
  # Security metrics
  - name: "ccl/security_issues"
    type: "COUNTER"
    description: "Security issues found during scans"
    labels:
      - service_name
      - severity  # CRITICAL, HIGH, MEDIUM, LOW
      - issue_type  # vulnerability, secret, license
      - scanner  # trivy, bandit, gosec, etc.
    unit: "1"
    
  - name: "ccl/security_scan_duration"
    type: "HISTOGRAM"
    description: "Time taken for security scans"
    labels:
      - service_name
      - scanner
    unit: "s"
    buckets: [30, 60, 120, 300, 600, 900]
    
  # Quality metrics
  - name: "ccl/code_quality_score"
    type: "GAUGE"
    description: "Code quality score from static analysis"
    labels:
      - service_name
      - analyzer  # sonarqube, clippy, eslint
    unit: "1"
    
  - name: "ccl/code_smells"
    type: "GAUGE"
    description: "Number of code smells detected"
    labels:
      - service_name
      - severity
    unit: "1"
    
  # Performance metrics
  - name: "ccl/pipeline_duration"
    type: "HISTOGRAM"
    description: "Total pipeline execution time"
    labels:
      - service_name
      - pipeline_stage  # ci, cd, full
      - trigger_type  # push, pr, manual
    unit: "s"
    buckets: [300, 600, 900, 1200, 1800, 2400, 3600]
    
  - name: "ccl/lead_time"
    type: "HISTOGRAM"
    description: "Lead time from commit to production"
    labels:
      - service_name
      - change_type  # feature, bugfix, hotfix
    unit: "s"
    buckets: [1800, 3600, 7200, 14400, 28800, 43200, 86400]
    
  - name: "ccl/mttr"
    type: "HISTOGRAM"
    description: "Mean time to recovery from incidents"
    labels:
      - service_name
      - incident_type
    unit: "s"
    buckets: [300, 600, 1800, 3600, 7200, 14400, 28800]
    
  # Resource utilization metrics
  - name: "ccl/runner_utilization"
    type: "GAUGE"
    description: "CI/CD runner utilization percentage"
    labels:
      - runner_type  # github-hosted, self-hosted
      - runner_os
    unit: "%"
    
  - name: "ccl/artifact_size"
    type: "HISTOGRAM"
    description: "Size of build artifacts"
    labels:
      - service_name
      - artifact_type  # binary, container, package
    unit: "By"
    buckets: [1048576, 10485760, 52428800, 104857600, 524288000, **********]  # 1MB to 1GB
    
  # Rollback metrics
  - name: "ccl/rollback_events"
    type: "COUNTER"
    description: "Number of rollback events"
    labels:
      - service_name
      - environment
      - rollback_reason  # health_check_failed, high_error_rate, manual
    unit: "1"
    
  - name: "ccl/rollback_duration"
    type: "HISTOGRAM"
    description: "Time taken to complete rollbacks"
    labels:
      - service_name
      - environment
    unit: "s"
    buckets: [30, 60, 120, 300, 600]

# Metric collection configuration
collection_config:
  # Collection intervals
  intervals:
    real_time: "10s"    # For critical metrics
    standard: "60s"     # For most metrics
    batch: "300s"       # For heavy computations
    
  # Retention policies
  retention:
    short_term: "7d"    # High-resolution data
    medium_term: "30d"  # Aggregated data
    long_term: "365d"   # Summary data
    
  # Export configuration
  export:
    # Export to Cloud Monitoring
    cloud_monitoring:
      enabled: true
      project_id: "${PROJECT_ID}"
      
    # Export to BigQuery for analysis
    bigquery:
      enabled: true
      dataset: "ccl_cicd_metrics"
      table: "pipeline_metrics"
      
    # Export to Prometheus (if using)
    prometheus:
      enabled: false
      endpoint: "http://prometheus:9090"

# Metric aggregation rules
aggregation_rules:
  # Service-level aggregations
  - name: "service_deployment_rate"
    source_metric: "ccl/deployments"
    aggregation: "rate"
    grouping: ["service_name", "environment"]
    window: "1h"
    
  - name: "service_success_rate"
    source_metric: "ccl/deployment_status"
    aggregation: "ratio"
    numerator_filter: 'status="SUCCESS"'
    denominator_filter: '*'
    grouping: ["service_name", "environment"]
    window: "24h"
    
  # Platform-level aggregations
  - name: "platform_lead_time_p95"
    source_metric: "ccl/lead_time"
    aggregation: "percentile"
    percentile: 95
    grouping: ["environment"]
    window: "7d"
    
  - name: "platform_mttr_mean"
    source_metric: "ccl/mttr"
    aggregation: "mean"
    grouping: ["environment"]
    window: "30d"

# Alerting thresholds
alerting_thresholds:
  # Deployment thresholds
  deployment_failure_rate:
    warning: 0.05   # 5%
    critical: 0.10  # 10%
    
  deployment_duration:
    warning: 1200   # 20 minutes
    critical: 1800  # 30 minutes
    
  # Build thresholds
  build_failure_rate:
    warning: 0.10   # 10%
    critical: 0.20  # 20%
    
  build_duration:
    warning: 900    # 15 minutes
    critical: 1200  # 20 minutes
    
  # Security thresholds
  critical_security_issues:
    warning: 1
    critical: 5
    
  # Quality thresholds
  test_coverage:
    warning: 85     # 85%
    critical: 80    # 80%
    
  code_quality_score:
    warning: 7.0
    critical: 6.0
    
  # Performance thresholds
  lead_time:
    warning: 7200   # 2 hours
    critical: 14400 # 4 hours
    
  mttr:
    warning: 3600   # 1 hour
    critical: 7200  # 2 hours

# Dashboard configuration
dashboards:
  - name: "CCL CI/CD Overview"
    metrics:
      - "ccl/deployments"
      - "ccl/build_success_rate"
      - "ccl/lead_time"
      - "ccl/mttr"
    refresh_interval: "30s"
    
  - name: "CCL Security Dashboard"
    metrics:
      - "ccl/security_issues"
      - "ccl/security_scan_duration"
    refresh_interval: "60s"
    
  - name: "CCL Quality Dashboard"
    metrics:
      - "ccl/test_coverage"
      - "ccl/code_quality_score"
      - "ccl/code_smells"
    refresh_interval: "300s"

# Integration with external systems
integrations:
  # GitHub integration
  github:
    webhook_events:
      - "push"
      - "pull_request"
      - "deployment"
      - "deployment_status"
    metrics_mapping:
      - github_event: "deployment"
        metric: "ccl/deployments"
      - github_event: "deployment_status"
        metric: "ccl/deployment_status"
        
  # Slack integration
  slack:
    channels:
      - name: "#ccl-metrics"
        metrics:
          - "ccl/deployment_status"
          - "ccl/security_issues"
        threshold_alerts: true
        
  # PagerDuty integration
  pagerduty:
    services:
      - name: "CCL Critical"
        metrics:
          - "ccl/deployment_status"
          - "ccl/rollback_events"
        severity_mapping:
          critical: "critical"
          warning: "warning"
