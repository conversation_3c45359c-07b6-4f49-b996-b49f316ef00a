# Alerting Policies for CCL Platform CI/CD Pipeline
# This file defines alerting policies for monitoring deployment health

alerting_policies:
  # High-priority alerts for production issues
  - name: "CCL Deployment Failure"
    display_name: "CCL Deployment Failure"
    documentation:
      content: |
        This alert fires when a deployment fails in any environment.
        
        ## Immediate Actions:
        1. Check the deployment logs in GitHub Actions
        2. Verify the service health endpoints
        3. Consider rolling back if in production
        
        ## Escalation:
        - If production: Page on-call engineer immediately
        - If staging: Notify development team
        - If development: Create ticket for investigation
      mime_type: "text/markdown"
    conditions:
      - display_name: "Deployment failure detected"
        condition_threshold:
          filter: 'metric.type="custom.googleapis.com/ccl/deployment_status" AND metric.label.status="FAILED"'
          comparison: COMPARISON_GT
          threshold_value: 0
          duration: "60s"
          aggregations:
            - alignment_period: "60s"
              per_series_aligner: ALIGN_SUM
              cross_series_reducer: REDUCE_SUM
              group_by_fields:
                - "resource.label.service_name"
                - "resource.label.environment"
    alert_strategy:
      auto_close: "86400s"  # 24 hours
    notification_channels:
      - "projects/ccl-platform/notificationChannels/slack-critical"
      - "projects/ccl-platform/notificationChannels/pagerduty-critical"
      - "projects/ccl-platform/notificationChannels/email-oncall"
    severity: CRITICAL

  - name: "CCL High Build Failure Rate"
    display_name: "CCL High Build Failure Rate"
    documentation:
      content: |
        This alert fires when the build failure rate exceeds 20% over a 1-hour period.
        
        ## Possible Causes:
        - Infrastructure issues
        - Dependency problems
        - Test environment instability
        - Code quality issues
        
        ## Actions:
        1. Check recent commits for breaking changes
        2. Verify CI/CD infrastructure health
        3. Review test failure patterns
      mime_type: "text/markdown"
    conditions:
      - display_name: "Build failure rate > 20%"
        condition_threshold:
          filter: 'metric.type="custom.googleapis.com/ccl/build_success_rate"'
          comparison: COMPARISON_LT
          threshold_value: 0.8
          duration: "300s"
          aggregations:
            - alignment_period: "3600s"
              per_series_aligner: ALIGN_MEAN
              cross_series_reducer: REDUCE_MEAN
    notification_channels:
      - "projects/ccl-platform/notificationChannels/slack-builds"
      - "projects/ccl-platform/notificationChannels/email-devteam"
    severity: WARNING

  - name: "CCL Long Lead Time"
    display_name: "CCL Long Lead Time for Changes"
    documentation:
      content: |
        This alert fires when the lead time for changes exceeds 2 hours.
        
        ## Impact:
        - Slower feature delivery
        - Reduced developer productivity
        - Potential customer impact
        
        ## Investigation Steps:
        1. Check pipeline bottlenecks
        2. Review test execution times
        3. Analyze deployment queue
      mime_type: "text/markdown"
    conditions:
      - display_name: "Lead time > 2 hours"
        condition_threshold:
          filter: 'metric.type="custom.googleapis.com/ccl/lead_time"'
          comparison: COMPARISON_GT
          threshold_value: 7200  # 2 hours in seconds
          duration: "600s"
          aggregations:
            - alignment_period: "3600s"
              per_series_aligner: ALIGN_MEAN
              cross_series_reducer: REDUCE_MEAN
    notification_channels:
      - "projects/ccl-platform/notificationChannels/slack-performance"
      - "projects/ccl-platform/notificationChannels/email-devteam"
    severity: WARNING

  - name: "CCL Security Scan Critical Issues"
    display_name: "CCL Security Scan Critical Issues"
    documentation:
      content: |
        This alert fires when critical security issues are detected in the codebase.
        
        ## Immediate Actions:
        1. Block deployment if not already blocked
        2. Review security scan results
        3. Prioritize fixing critical vulnerabilities
        
        ## Escalation:
        - Notify security team immediately
        - Create high-priority security tickets
      mime_type: "text/markdown"
    conditions:
      - display_name: "Critical security issues detected"
        condition_threshold:
          filter: 'metric.type="custom.googleapis.com/ccl/security_issues" AND metric.label.severity="CRITICAL"'
          comparison: COMPARISON_GT
          threshold_value: 0
          duration: "60s"
          aggregations:
            - alignment_period: "300s"
              per_series_aligner: ALIGN_SUM
              cross_series_reducer: REDUCE_SUM
    notification_channels:
      - "projects/ccl-platform/notificationChannels/slack-security"
      - "projects/ccl-platform/notificationChannels/email-security"
      - "projects/ccl-platform/notificationChannels/pagerduty-security"
    severity: CRITICAL

  - name: "CCL Test Coverage Drop"
    display_name: "CCL Test Coverage Drop"
    documentation:
      content: |
        This alert fires when test coverage drops below 85% for any service.
        
        ## Actions:
        1. Identify which service has low coverage
        2. Review recent changes that may have reduced coverage
        3. Add missing tests before next deployment
      mime_type: "text/markdown"
    conditions:
      - display_name: "Test coverage < 85%"
        condition_threshold:
          filter: 'metric.type="custom.googleapis.com/ccl/test_coverage"'
          comparison: COMPARISON_LT
          threshold_value: 85
          duration: "300s"
          aggregations:
            - alignment_period: "3600s"
              per_series_aligner: ALIGN_MEAN
              cross_series_reducer: REDUCE_MEAN
              group_by_fields:
                - "resource.label.service_name"
    notification_channels:
      - "projects/ccl-platform/notificationChannels/slack-quality"
      - "projects/ccl-platform/notificationChannels/email-devteam"
    severity: WARNING

  - name: "CCL Pipeline Performance Degradation"
    display_name: "CCL Pipeline Performance Degradation"
    documentation:
      content: |
        This alert fires when pipeline execution time increases significantly.
        
        ## Investigation:
        1. Check for infrastructure issues
        2. Review test execution times
        3. Analyze build performance
        4. Check for resource constraints
      mime_type: "text/markdown"
    conditions:
      - display_name: "Pipeline duration > 20 minutes"
        condition_threshold:
          filter: 'metric.type="custom.googleapis.com/ccl/pipeline_duration"'
          comparison: COMPARISON_GT
          threshold_value: 1200  # 20 minutes
          duration: "300s"
          aggregations:
            - alignment_period: "3600s"
              per_series_aligner: ALIGN_PERCENTILE_95
              cross_series_reducer: REDUCE_PERCENTILE_95
    notification_channels:
      - "projects/ccl-platform/notificationChannels/slack-performance"
      - "projects/ccl-platform/notificationChannels/email-devops"
    severity: WARNING

  - name: "CCL Rollback Executed"
    display_name: "CCL Rollback Executed"
    documentation:
      content: |
        This alert fires when an automatic rollback is executed.
        
        ## Immediate Actions:
        1. Verify rollback was successful
        2. Investigate root cause of the issue
        3. Plan fix for the rolled-back deployment
        
        ## Follow-up:
        - Conduct post-incident review
        - Update monitoring if needed
        - Improve deployment validation
      mime_type: "text/markdown"
    conditions:
      - display_name: "Rollback event detected"
        condition_threshold:
          filter: 'metric.type="custom.googleapis.com/ccl/rollback_events"'
          comparison: COMPARISON_GT
          threshold_value: 0
          duration: "60s"
          aggregations:
            - alignment_period: "60s"
              per_series_aligner: ALIGN_SUM
              cross_series_reducer: REDUCE_SUM
    notification_channels:
      - "projects/ccl-platform/notificationChannels/slack-critical"
      - "projects/ccl-platform/notificationChannels/email-oncall"
      - "projects/ccl-platform/notificationChannels/pagerduty-incidents"
    severity: CRITICAL

# Notification channels configuration
notification_channels:
  - name: "slack-critical"
    type: "slack"
    display_name: "Slack Critical Alerts"
    description: "Critical alerts sent to #ccl-critical-alerts"
    labels:
      channel_name: "#ccl-critical-alerts"
      webhook_url: "${SLACK_WEBHOOK_CRITICAL}"

  - name: "slack-builds"
    type: "slack"
    display_name: "Slack Build Alerts"
    description: "Build-related alerts sent to #ccl-builds"
    labels:
      channel_name: "#ccl-builds"
      webhook_url: "${SLACK_WEBHOOK_BUILDS}"

  - name: "slack-security"
    type: "slack"
    display_name: "Slack Security Alerts"
    description: "Security alerts sent to #ccl-security"
    labels:
      channel_name: "#ccl-security"
      webhook_url: "${SLACK_WEBHOOK_SECURITY}"

  - name: "slack-performance"
    type: "slack"
    display_name: "Slack Performance Alerts"
    description: "Performance alerts sent to #ccl-performance"
    labels:
      channel_name: "#ccl-performance"
      webhook_url: "${SLACK_WEBHOOK_PERFORMANCE}"

  - name: "slack-quality"
    type: "slack"
    display_name: "Slack Quality Alerts"
    description: "Quality alerts sent to #ccl-quality"
    labels:
      channel_name: "#ccl-quality"
      webhook_url: "${SLACK_WEBHOOK_QUALITY}"

  - name: "email-oncall"
    type: "email"
    display_name: "On-call Email"
    description: "Critical alerts sent to on-call engineer"
    labels:
      email_address: "<EMAIL>"

  - name: "email-devteam"
    type: "email"
    display_name: "Development Team Email"
    description: "Development team notifications"
    labels:
      email_address: "<EMAIL>"

  - name: "email-security"
    type: "email"
    display_name: "Security Team Email"
    description: "Security team notifications"
    labels:
      email_address: "<EMAIL>"

  - name: "email-devops"
    type: "email"
    display_name: "DevOps Team Email"
    description: "DevOps team notifications"
    labels:
      email_address: "<EMAIL>"

  - name: "pagerduty-critical"
    type: "pagerduty"
    display_name: "PagerDuty Critical"
    description: "Critical incidents via PagerDuty"
    labels:
      service_key: "${PAGERDUTY_SERVICE_KEY_CRITICAL}"

  - name: "pagerduty-security"
    type: "pagerduty"
    display_name: "PagerDuty Security"
    description: "Security incidents via PagerDuty"
    labels:
      service_key: "${PAGERDUTY_SERVICE_KEY_SECURITY}"

  - name: "pagerduty-incidents"
    type: "pagerduty"
    display_name: "PagerDuty Incidents"
    description: "General incidents via PagerDuty"
    labels:
      service_key: "${PAGERDUTY_SERVICE_KEY_INCIDENTS}"

# Alert routing rules
alert_routing:
  # Production alerts get highest priority
  production:
    severity_mapping:
      CRITICAL: ["pagerduty-critical", "slack-critical", "email-oncall"]
      WARNING: ["slack-critical", "email-devteam"]
      INFO: ["slack-builds"]

  # Staging alerts for validation
  staging:
    severity_mapping:
      CRITICAL: ["slack-critical", "email-devteam"]
      WARNING: ["slack-builds", "email-devteam"]
      INFO: ["slack-builds"]

  # Development alerts for early detection
  development:
    severity_mapping:
      CRITICAL: ["slack-builds", "email-devteam"]
      WARNING: ["slack-builds"]
      INFO: ["slack-builds"]

# SLO-based alerting
slo_alerts:
  - name: "CCL Deployment SLO"
    display_name: "CCL Deployment Success Rate SLO"
    slo_target: 0.99  # 99% success rate
    lookback_period: "30d"
    burn_rate_thresholds:
      - burn_rate: 14.4  # 1% error budget in 2 hours
        duration: "3600s"
        severity: CRITICAL
      - burn_rate: 6     # 1% error budget in 6 hours  
        duration: "3600s"
        severity: WARNING
