{"displayName": "CCL Deployment Dashboard", "mosaicLayout": {"tiles": [{"width": 6, "height": 4, "widget": {"title": "Deployment Frequency", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/ccl/deployments\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["resource.label.service_name"]}}}, "plotType": "LINE", "targetAxis": "Y1"}], "timeshiftDuration": "0s", "yAxis": {"label": "Deployments per hour", "scale": "LINEAR"}}}}, {"width": 6, "height": 4, "xPos": 6, "widget": {"title": "Build Success Rate", "scorecard": {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/ccl/build_success_rate\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}}}, "sparkChartView": {"sparkChartType": "SPARK_LINE"}, "gaugeView": {"lowerBound": 0.0, "upperBound": 1.0}}}}, {"width": 12, "height": 4, "yPos": 4, "widget": {"title": "Lead Time for Changes", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/ccl/lead_time\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["resource.label.service_name"]}}}, "plotType": "STACKED_BAR", "targetAxis": "Y1"}], "yAxis": {"label": "Minutes", "scale": "LINEAR"}}}}, {"width": 6, "height": 4, "yPos": 8, "widget": {"title": "Deployment Status by Service", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/ccl/deployment_status\" AND metric.label.status=\"SUCCESS\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_SUM", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["resource.label.service_name"]}}}, "plotType": "STACKED_AREA", "targetAxis": "Y1"}, {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/ccl/deployment_status\" AND metric.label.status=\"FAILED\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_SUM", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["resource.label.service_name"]}}}, "plotType": "STACKED_AREA", "targetAxis": "Y1"}], "yAxis": {"label": "Count", "scale": "LINEAR"}}}}, {"width": 6, "height": 4, "xPos": 6, "yPos": 8, "widget": {"title": "Mean Time to Recovery", "scorecard": {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/ccl/mttr\"", "aggregation": {"alignmentPeriod": "86400s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}}}, "sparkChartView": {"sparkChartType": "SPARK_LINE"}}}}, {"width": 12, "height": 4, "yPos": 12, "widget": {"title": "Pipeline Execution Time", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/ccl/pipeline_duration\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_PERCENTILE_95", "crossSeriesReducer": "REDUCE_PERCENTILE_95", "groupByFields": ["metric.label.pipeline_stage"]}}}, "plotType": "STACKED_BAR", "targetAxis": "Y1"}], "yAxis": {"label": "Seconds", "scale": "LINEAR"}}}}, {"width": 6, "height": 4, "yPos": 16, "widget": {"title": "Test Coverage Trend", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/ccl/test_coverage\"", "aggregation": {"alignmentPeriod": "86400s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["resource.label.service_name"]}}}, "plotType": "LINE", "targetAxis": "Y1"}], "yAxis": {"label": "Coverage %", "scale": "LINEAR"}}}}, {"width": 6, "height": 4, "xPos": 6, "yPos": 16, "widget": {"title": "Security Scan Results", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/ccl/security_issues\"", "aggregation": {"alignmentPeriod": "86400s", "perSeriesAligner": "ALIGN_SUM", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.severity"]}}}, "plotType": "STACKED_BAR", "targetAxis": "Y1"}], "yAxis": {"label": "Issues Count", "scale": "LINEAR"}}}}, {"width": 12, "height": 4, "yPos": 20, "widget": {"title": "Recent Deployments", "logsPanel": {"filter": "resource.type=\"cloud_run_service\" AND jsonPayload.event_type=\"deployment\"", "resourceNames": ["projects/ccl-platform-dev", "projects/ccl-platform-staging", "projects/ccl-platform-prod"]}}}]}, "dashboardFilters": [{"filterType": "RESOURCE_LABEL", "labelKey": "service_name", "stringValue": ""}, {"filterType": "RESOURCE_LABEL", "labelKey": "environment", "stringValue": ""}]}