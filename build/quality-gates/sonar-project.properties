# SonarQube configuration for CCL Platform
# This file contains the base configuration for SonarQube analysis
# Service-specific configurations should override these settings

# Project identification
sonar.organization=ccl-platform
sonar.projectKey=ccl-${SERVICE_NAME}
sonar.projectName=CCL ${SERVICE_NAME}
sonar.projectVersion=${VERSION}

# Source code configuration
sonar.sources=.
sonar.sourceEncoding=UTF-8

# Exclusions - files and directories to exclude from analysis
sonar.exclusions=\
  **/*_test.go,\
  **/*.test.ts,\
  **/*.test.js,\
  **/test_*.py,\
  **/tests.rs,\
  **/target/**,\
  **/node_modules/**,\
  **/dist/**,\
  **/build/**,\
  **/.venv/**,\
  **/venv/**,\
  **/__pycache__/**,\
  **/coverage/**,\
  **/*.min.js,\
  **/*.min.css,\
  **/vendor/**,\
  **/third_party/**

# Test files configuration
sonar.test.inclusions=\
  **/*_test.go,\
  **/*.test.ts,\
  **/*.test.js,\
  **/test_*.py,\
  **/tests.rs,\
  **/tests/**,\
  **/test/**

# Coverage configuration
sonar.coverage.exclusions=\
  **/cmd/**,\
  **/examples/**,\
  **/benchmarks/**,\
  **/migrations/**,\
  **/scripts/**,\
  **/*.pb.go,\
  **/*.generated.*

# Language-specific configurations
# Go
sonar.go.coverage.reportPaths=coverage.out

# Python
sonar.python.coverage.reportPaths=coverage.xml
sonar.python.xunit.reportPath=test-results.xml

# JavaScript/TypeScript
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.typescript.lcov.reportPaths=coverage/lcov.info

# Rust (using generic coverage format)
sonar.coverageReportPaths=coverage.xml

# Quality gate configuration
sonar.qualitygate.wait=true

# Duplication configuration
sonar.cpd.exclusions=\
  **/*.pb.go,\
  **/*.generated.*,\
  **/migrations/**

# Analysis parameters
sonar.analysis.mode=publish
sonar.buildString=${BUILD_NUMBER}
sonar.scm.provider=git

# Security configuration
sonar.security.hotspots.inheritFromParent=true

# New code definition
sonar.newCode.referenceBranch=main

# Branch analysis
sonar.branch.name=${BRANCH_NAME}
sonar.branch.target=main

# Pull request analysis
sonar.pullrequest.key=${PR_NUMBER}
sonar.pullrequest.branch=${PR_BRANCH}
sonar.pullrequest.base=${PR_BASE}
