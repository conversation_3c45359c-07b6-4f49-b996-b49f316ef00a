# Security Configuration for CCL Platform CI/CD Pipeline
# This file defines security scanning tools and configurations

# Global security settings
security:
  # Fail build on security issues
  fail_on_critical: true
  fail_on_high: true
  fail_on_medium: false
  
  # Security scanning tools
  tools:
    dependency_check: true
    container_scan: true
    sast_scan: true
    secret_scan: true
    license_check: true
    
  # Reporting
  generate_sarif: true
  upload_to_github_security: true

# Dependency vulnerability scanning
dependency_scanning:
  # Tools per language
  rust:
    tool: cargo-audit
    config:
      ignore_yanked: false
      ignore_unmaintained: false
      
  python:
    tools:
      - safety
      - pip-audit
    config:
      ignore_ids: []
      
  go:
    tool: govulncheck
    config:
      mode: source
      
  typescript:
    tools:
      - npm-audit
      - retire
      - snyk
    config:
      audit_level: moderate
      
  # Common configuration
  cvss_threshold: 7.0
  max_age_days: 30
  
# Container security scanning
container_scanning:
  # Primary scanner
  primary_tool: trivy
  
  # Trivy configuration
  trivy:
    severity: HIGH,CRITICAL
    vuln_type: os,library
    ignore_unfixed: false
    format: sarif
    
  # Additional scanners
  additional_tools:
    - grype
    - snyk-container
    
  # Image signing
  cosign:
    enabled: true
    key_ref: gcpkms://projects/ccl-platform/locations/us-central1/keyRings/cosign/cryptoKeys/cosign
    
# Static Application Security Testing (SAST)
sast_scanning:
  # Primary SAST tool
  primary_tool: sonarqube
  
  # Language-specific SAST tools
  rust:
    tools:
      - clippy
      - cargo-geiger
    clippy_config:
      deny: warnings
      
  python:
    tools:
      - bandit
      - semgrep
    bandit_config:
      confidence_level: medium
      severity_level: medium
      
  go:
    tools:
      - gosec
      - staticcheck
    gosec_config:
      severity: medium
      confidence: medium
      
  typescript:
    tools:
      - eslint-security
      - tslint-security
      - semgrep
      
  # CodeQL configuration
  codeql:
    enabled: true
    languages:
      - go
      - python
      - typescript
    queries: security-and-quality

# Secret scanning
secret_scanning:
  # Tools
  tools:
    - gitleaks
    - truffleHog
    - detect-secrets
    
  # Gitleaks configuration
  gitleaks:
    config_path: .gitleaks.toml
    fail_on_detection: true
    
  # Patterns to detect
  patterns:
    - api_keys
    - passwords
    - tokens
    - certificates
    - database_urls
    - cloud_credentials
    
  # Allowlist
  allowlist:
    paths:
      - "**/*test*"
      - "**/*example*"
      - "**/*mock*"
    commits:
      - "initial commit"

# License compliance scanning
license_scanning:
  # Tools
  tools:
    - fossa
    - licensee
    - license-checker
    
  # Allowed licenses
  allowed_licenses:
    - MIT
    - Apache-2.0
    - BSD-2-Clause
    - BSD-3-Clause
    - ISC
    - Unlicense
    
  # Forbidden licenses
  forbidden_licenses:
    - GPL-2.0
    - GPL-3.0
    - AGPL-1.0
    - AGPL-3.0
    - LGPL-2.1
    - LGPL-3.0
    - CDDL-1.0
    - EPL-1.0
    - EPL-2.0
    
  # License policy
  policy:
    fail_on_forbidden: true
    require_license_file: true
    require_attribution: true

# Infrastructure security
infrastructure_security:
  # Terraform scanning
  terraform:
    tools:
      - tfsec
      - checkov
      - terrascan
    config:
      minimum_severity: medium
      
  # Kubernetes security
  kubernetes:
    tools:
      - kube-score
      - polaris
      - falco
      
  # Cloud security
  cloud:
    gcp:
      tools:
        - forseti
        - cloud-security-scanner
      config:
        scan_compute: true
        scan_storage: true
        scan_iam: true

# Security testing
security_testing:
  # Dynamic Application Security Testing (DAST)
  dast:
    enabled: true
    tool: zap
    config:
      target_url: https://staging.ccl-platform.com
      scan_type: baseline
      
  # Interactive Application Security Testing (IAST)
  iast:
    enabled: false  # Enable when ready
    
  # Penetration testing
  pentest:
    frequency: quarterly
    scope: full_application
    
# Compliance frameworks
compliance:
  # SOC2 Type II
  soc2:
    controls:
      - access_control
      - change_management
      - data_protection
      - monitoring
      - incident_response
      
  # ISO 27001
  iso27001:
    controls:
      - information_security_policies
      - risk_management
      - asset_management
      - access_control
      - cryptography
      
  # NIST Cybersecurity Framework
  nist:
    functions:
      - identify
      - protect
      - detect
      - respond
      - recover

# Incident response
incident_response:
  # Security incident classification
  classification:
    critical:
      - data_breach
      - system_compromise
      - privilege_escalation
    high:
      - vulnerability_exploitation
      - unauthorized_access
      - service_disruption
    medium:
      - policy_violation
      - suspicious_activity
      - configuration_drift
      
  # Response procedures
  procedures:
    detection_time_minutes: 15
    response_time_minutes: 60
    containment_time_hours: 4
    recovery_time_hours: 24
    
  # Notification requirements
  notifications:
    internal:
      - security_team
      - incident_commander
      - legal_team
    external:
      - customers
      - regulators
      - law_enforcement

# Security metrics and KPIs
metrics:
  # Vulnerability metrics
  vulnerabilities:
    - total_count
    - critical_count
    - high_count
    - mean_time_to_fix
    - vulnerability_density
    
  # Security testing metrics
  testing:
    - scan_coverage
    - false_positive_rate
    - security_test_execution_rate
    
  # Compliance metrics
  compliance:
    - control_effectiveness
    - audit_findings
    - remediation_rate
    
  # Incident metrics
  incidents:
    - incident_count
    - mean_time_to_detect
    - mean_time_to_respond
    - mean_time_to_recover
