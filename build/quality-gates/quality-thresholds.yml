# Quality Gates Configuration for CCL Platform
# This file defines the quality thresholds that must be met for all services

# Global quality gates
quality_gates:
  # Code coverage requirements
  coverage:
    minimum: 90
    fail_build: true
    new_code_minimum: 95
    
  # Code smells and maintainability
  code_smells:
    maximum: 5
    severity: major
    fail_build: true
    
  # Security requirements
  security_hotspots:
    maximum: 0
    fail_build: true
    
  vulnerabilities:
    maximum: 0
    severity: high
    fail_build: true
    
  # Code duplication
  duplicated_lines:
    maximum_percent: 3
    fail_build: true
    
  # Complexity requirements
  cyclomatic_complexity:
    maximum: 10
    fail_build: false  # Warning only
    
  # Reliability requirements
  bugs:
    maximum: 0
    severity: major
    fail_build: true
    
  # Technical debt
  technical_debt_ratio:
    maximum_percent: 5
    fail_build: false  # Warning only
    
  # Performance requirements
  performance:
    build_time_seconds: 300
    test_time_seconds: 600
    fail_build: true

# Service-specific overrides
service_overrides:
  analysis-engine:
    # Rust service - stricter requirements
    coverage:
      minimum: 95
    cyclomatic_complexity:
      maximum: 8
    performance:
      build_time_seconds: 180  # Rust builds can be slower
      
  query-intelligence:
    # Python AI service - ML code can be more complex
    cyclomatic_complexity:
      maximum: 15
    technical_debt_ratio:
      maximum_percent: 8
    coverage:
      minimum: 85  # ML code harder to test
      
  pattern-mining:
    # Python ML service - similar to query-intelligence
    cyclomatic_complexity:
      maximum: 15
    technical_debt_ratio:
      maximum_percent: 8
    coverage:
      minimum: 85
      
  marketplace:
    # Go service - business logic focused
    coverage:
      minimum: 95
    bugs:
      maximum: 0  # Zero tolerance for commerce bugs
      
  web:
    # TypeScript frontend - different requirements
    coverage:
      minimum: 80  # Frontend testing is different
    duplicated_lines:
      maximum_percent: 5  # Some duplication acceptable in UI
      
  collaboration:
    # TypeScript real-time service
    coverage:
      minimum: 85
    performance:
      test_time_seconds: 300  # Real-time tests are faster

# Language-specific configurations
language_configs:
  rust:
    tools:
      - clippy
      - rustfmt
      - cargo-audit
      - cargo-deny
    clippy_config:
      deny_warnings: true
      
  python:
    tools:
      - ruff
      - black
      - mypy
      - bandit
      - safety
    ruff_config:
      line_length: 88
      target_version: py311
      
  go:
    tools:
      - golangci-lint
      - gosec
      - govulncheck
    golangci_config:
      timeout: 5m
      
  typescript:
    tools:
      - eslint
      - prettier
      - tsc
      - retire
    eslint_config:
      extends: "@typescript-eslint/recommended"

# Security scanning configuration
security_scanning:
  # Dependency scanning
  dependency_check:
    enabled: true
    fail_on_cvss: 7.0
    
  # Container scanning
  container_scan:
    enabled: true
    fail_on_critical: true
    fail_on_high: false
    
  # SAST scanning
  static_analysis:
    enabled: true
    tools:
      - sonarqube
      - semgrep
      - codeql
      
  # Secret scanning
  secret_scan:
    enabled: true
    fail_on_detection: true
    
  # License compliance
  license_check:
    enabled: true
    allowed_licenses:
      - MIT
      - Apache-2.0
      - BSD-3-Clause
      - ISC
    forbidden_licenses:
      - GPL-3.0
      - AGPL-3.0

# Performance benchmarks
performance_benchmarks:
  analysis_engine:
    parse_1k_files: 5s
    parse_10k_files: 30s
    memory_usage_mb: 512
    
  query_intelligence:
    query_response_ms: 100
    batch_processing_10_queries: 500ms
    memory_usage_mb: 1024
    
  pattern_mining:
    detect_patterns_1mb: 30s
    detect_patterns_10mb: 300s
    memory_usage_mb: 2048
    
  marketplace:
    api_response_ms: 50
    concurrent_users: 1000
    memory_usage_mb: 256
    
  web:
    first_contentful_paint_ms: 1500
    largest_contentful_paint_ms: 2500
    cumulative_layout_shift: 0.1
    
  collaboration:
    websocket_latency_ms: 50
    concurrent_connections: 10000
    memory_usage_mb: 512

# Monitoring and alerting
monitoring:
  # Quality metrics to track
  metrics:
    - coverage_percentage
    - code_smells_count
    - security_hotspots_count
    - build_success_rate
    - test_execution_time
    - deployment_frequency
    
  # Alert thresholds
  alerts:
    coverage_drop_threshold: 5  # Alert if coverage drops by 5%
    build_failure_threshold: 3  # Alert after 3 consecutive failures
    security_issue_threshold: 1  # Alert on any security issue
    
  # Reporting
  reports:
    frequency: daily
    recipients:
      - <EMAIL>
      - <EMAIL>
    format: html

# Compliance requirements
compliance:
  # SOC2 requirements
  soc2:
    code_review_required: true
    security_scan_required: true
    access_logging: true
    
  # GDPR requirements
  gdpr:
    data_classification: required
    privacy_impact_assessment: required
    
  # Industry standards
  standards:
    - OWASP_Top_10
    - CWE_Top_25
    - NIST_Cybersecurity_Framework
