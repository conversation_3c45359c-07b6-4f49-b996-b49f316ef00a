# Rust-specific Makefile for CCL Services

# Rust configuration
RUST_VERSION ?= 1.75.0
CARGO_TARGET_DIR ?= target
RUST_BACKTRACE ?= 1

# Rust build flags
CARGO_BUILD_FLAGS := --release
CARGO_TEST_FLAGS := --all-features
CARGO_CLIPPY_FLAGS := -- -D warnings

# Override language-specific targets
deps-rust: ## Install Rust dependencies
	@echo "$(GREEN)Installing Rust dependencies...$(NC)"
	@rustup update $(RUST_VERSION)
	@rustup default $(RUST_VERSION)
	@rustup component add rustfmt clippy
	@cargo install cargo-audit cargo-tarpaulin cargo-deny --locked
	@cargo fetch

clean-rust: ## Clean Rust build artifacts
	@echo "$(GREEN)Cleaning Rust artifacts...$(NC)"
	@cargo clean
	@rm -rf $(COVERAGE_DIR)

lint-rust: ## Run Rust linters
	@echo "$(GREEN)Running Rust linters...$(NC)"
	@cargo fmt --check
	@cargo clippy --all-targets --all-features $(CARGO_CLIPPY_FLAGS)
	@cargo deny check

fmt-rust: ## Format Rust code
	@echo "$(GREEN)Formatting Rust code...$(NC)"
	@cargo fmt

security-scan-rust: ## Run Rust security scans
	@echo "$(GREEN)Running Rust security scans...$(NC)"
	@cargo audit
	@cargo deny check advisories

test-rust: ## Run Rust tests
	@echo "$(GREEN)Running Rust tests...$(NC)"
	@RUST_BACKTRACE=$(RUST_BACKTRACE) cargo test $(CARGO_TEST_FLAGS)

test-ci-rust: ## Run Rust tests in CI mode
	@echo "$(GREEN)Running Rust CI tests...$(NC)"
	@RUST_BACKTRACE=$(RUST_BACKTRACE) cargo test $(CARGO_TEST_FLAGS) --no-fail-fast

coverage-rust: ## Generate Rust coverage report
	@echo "$(GREEN)Generating Rust coverage report...$(NC)"
	@mkdir -p $(COVERAGE_DIR)
	@cargo tarpaulin --out xml --output-dir $(COVERAGE_DIR)
	@cargo tarpaulin --out html --output-dir $(COVERAGE_DIR)

build-rust: ## Build Rust service
	@echo "$(GREEN)Building Rust service...$(NC)"
	@cargo build $(CARGO_BUILD_FLAGS)
	@mkdir -p $(DIST_DIR)
	@cp $(CARGO_TARGET_DIR)/release/$(SERVICE_NAME) $(DIST_DIR)/

smoke-test-rust: ## Run Rust smoke tests
	@echo "$(GREEN)Running Rust smoke tests...$(NC)"
	@cargo test --test smoke_tests --release

dev-setup-rust: ## Set up Rust development environment
	@echo "$(GREEN)Setting up Rust development environment...$(NC)"
	@rustup component add rust-analyzer
	@cargo install cargo-watch --locked

dev-run-rust: ## Run Rust service in development mode
	@echo "$(GREEN)Running Rust service in development mode...$(NC)"
	@cargo run

dev-test-rust: ## Run Rust tests in watch mode
	@echo "$(GREEN)Running Rust tests in watch mode...$(NC)"
	@cargo watch -x test

benchmark-rust: ## Run Rust benchmarks
	@echo "$(GREEN)Running Rust benchmarks...$(NC)"
	@cargo bench

# Rust-specific targets
check-rust: ## Run cargo check
	@echo "$(GREEN)Running cargo check...$(NC)"
	@cargo check --all-targets --all-features

doc-rust: ## Generate Rust documentation
	@echo "$(GREEN)Generating Rust documentation...$(NC)"
	@cargo doc --no-deps --open

update-rust: ## Update Rust dependencies
	@echo "$(GREEN)Updating Rust dependencies...$(NC)"
	@cargo update

tree-rust: ## Show dependency tree
	@echo "$(GREEN)Showing Rust dependency tree...$(NC)"
	@cargo tree

# Analysis Engine specific targets (if SERVICE_NAME is analysis-engine)
ifeq ($(SERVICE_NAME),analysis-engine)
validate-parsers: ## Validate language parsers
	@echo "$(GREEN)Validating language parsers...$(NC)"
	@cargo test --test parser_validation --release

benchmark-parsing: ## Benchmark parsing performance
	@echo "$(GREEN)Benchmarking parsing performance...$(NC)"
	@cargo bench --bench parsing_benchmarks

test-languages: ## Test all supported languages
	@echo "$(GREEN)Testing all supported languages...$(NC)"
	@cargo test --test language_support --release
endif
