# Python-specific Makefile for CCL Services

# Python configuration
PYTHON_VERSION ?= 3.11
VENV_DIR ?= .venv
PYTHON := $(VENV_DIR)/bin/python
PIP := $(VENV_DIR)/bin/pip
POETRY := poetry

# Python build flags
PYTEST_FLAGS := -v --tb=short
PYTEST_COV_FLAGS := --cov=. --cov-report=xml --cov-report=html --cov-report=term

# Override language-specific targets
deps-python: ## Install Python dependencies
	@echo "$(GREEN)Installing Python dependencies...$(NC)"
	@if command -v poetry >/dev/null 2>&1; then \
		poetry install --with dev; \
	else \
		python -m venv $(VENV_DIR); \
		$(PIP) install --upgrade pip; \
		$(PIP) install -r requirements.txt; \
		if [ -f requirements-dev.txt ]; then $(PIP) install -r requirements-dev.txt; fi; \
	fi

clean-python: ## Clean Python build artifacts
	@echo "$(GREEN)Cleaning Python artifacts...$(NC)"
	@rm -rf build/ dist/ *.egg-info/
	@rm -rf .pytest_cache/ .coverage htmlcov/
	@rm -rf $(COVERAGE_DIR)
	@find . -type d -name __pycache__ -exec rm -rf {} +
	@find . -type f -name "*.pyc" -delete

lint-python: ## Run Python linters
	@echo "$(GREEN)Running Python linters...$(NC)"
	@if command -v poetry >/dev/null 2>&1; then \
		poetry run ruff check .; \
		poetry run black --check .; \
		poetry run mypy .; \
	else \
		$(PYTHON) -m ruff check .; \
		$(PYTHON) -m black --check .; \
		$(PYTHON) -m mypy .; \
	fi

fmt-python: ## Format Python code
	@echo "$(GREEN)Formatting Python code...$(NC)"
	@if command -v poetry >/dev/null 2>&1; then \
		poetry run ruff check --fix .; \
		poetry run black .; \
	else \
		$(PYTHON) -m ruff check --fix .; \
		$(PYTHON) -m black .; \
	fi

security-scan-python: ## Run Python security scans
	@echo "$(GREEN)Running Python security scans...$(NC)"
	@if command -v poetry >/dev/null 2>&1; then \
		poetry run bandit -r . -f json -o bandit-report.json; \
		poetry run safety check --json --output safety-report.json; \
	else \
		$(PYTHON) -m bandit -r . -f json -o bandit-report.json; \
		$(PYTHON) -m safety check --json --output safety-report.json; \
	fi

test-python: ## Run Python tests
	@echo "$(GREEN)Running Python tests...$(NC)"
	@if command -v poetry >/dev/null 2>&1; then \
		poetry run pytest $(PYTEST_FLAGS); \
	else \
		$(PYTHON) -m pytest $(PYTEST_FLAGS); \
	fi

test-ci-python: ## Run Python tests in CI mode
	@echo "$(GREEN)Running Python CI tests...$(NC)"
	@if command -v poetry >/dev/null 2>&1; then \
		poetry run pytest $(PYTEST_FLAGS) --maxfail=1; \
	else \
		$(PYTHON) -m pytest $(PYTEST_FLAGS) --maxfail=1; \
	fi

coverage-python: ## Generate Python coverage report
	@echo "$(GREEN)Generating Python coverage report...$(NC)"
	@mkdir -p $(COVERAGE_DIR)
	@if command -v poetry >/dev/null 2>&1; then \
		poetry run pytest $(PYTEST_COV_FLAGS); \
	else \
		$(PYTHON) -m pytest $(PYTEST_COV_FLAGS); \
	fi
	@mv htmlcov $(COVERAGE_DIR)/ 2>/dev/null || true
	@mv coverage.xml $(COVERAGE_DIR)/ 2>/dev/null || true

build-python: ## Build Python service
	@echo "$(GREEN)Building Python service...$(NC)"
	@mkdir -p $(DIST_DIR)
	@if command -v poetry >/dev/null 2>&1; then \
		poetry build; \
		cp dist/* $(DIST_DIR)/; \
	else \
		$(PYTHON) setup.py sdist bdist_wheel; \
		cp dist/* $(DIST_DIR)/; \
	fi

smoke-test-python: ## Run Python smoke tests
	@echo "$(GREEN)Running Python smoke tests...$(NC)"
	@if command -v poetry >/dev/null 2>&1; then \
		poetry run pytest tests/smoke/ -v; \
	else \
		$(PYTHON) -m pytest tests/smoke/ -v; \
	fi

dev-setup-python: ## Set up Python development environment
	@echo "$(GREEN)Setting up Python development environment...$(NC)"
	@if command -v poetry >/dev/null 2>&1; then \
		poetry install --with dev; \
		poetry run pre-commit install; \
	else \
		$(PIP) install -e .; \
		$(PIP) install pre-commit; \
		pre-commit install; \
	fi

dev-run-python: ## Run Python service in development mode
	@echo "$(GREEN)Running Python service in development mode...$(NC)"
	@if command -v poetry >/dev/null 2>&1; then \
		poetry run python -m $(SERVICE_NAME); \
	else \
		$(PYTHON) -m $(SERVICE_NAME); \
	fi

dev-test-python: ## Run Python tests in watch mode
	@echo "$(GREEN)Running Python tests in watch mode...$(NC)"
	@if command -v poetry >/dev/null 2>&1; then \
		poetry run ptw -- $(PYTEST_FLAGS); \
	else \
		$(PYTHON) -m ptw -- $(PYTEST_FLAGS); \
	fi

benchmark-python: ## Run Python benchmarks
	@echo "$(GREEN)Running Python benchmarks...$(NC)"
	@if command -v poetry >/dev/null 2>&1; then \
		poetry run pytest benchmarks/ --benchmark-only; \
	else \
		$(PYTHON) -m pytest benchmarks/ --benchmark-only; \
	fi

# Python-specific targets
install-python: ## Install package in development mode
	@echo "$(GREEN)Installing Python package in development mode...$(NC)"
	@if command -v poetry >/dev/null 2>&1; then \
		poetry install; \
	else \
		$(PIP) install -e .; \
	fi

update-python: ## Update Python dependencies
	@echo "$(GREEN)Updating Python dependencies...$(NC)"
	@if command -v poetry >/dev/null 2>&1; then \
		poetry update; \
	else \
		$(PIP) install --upgrade -r requirements.txt; \
	fi

freeze-python: ## Freeze Python dependencies
	@echo "$(GREEN)Freezing Python dependencies...$(NC)"
	@if command -v poetry >/dev/null 2>&1; then \
		poetry export -f requirements.txt --output requirements.txt; \
	else \
		$(PIP) freeze > requirements.txt; \
	fi

# Service-specific targets
ifeq ($(SERVICE_NAME),query-intelligence)
validate-models: ## Validate AI models
	@echo "$(GREEN)Validating AI models...$(NC)"
	@poetry run python -m query_intelligence.validation.model_validator

benchmark-inference: ## Benchmark inference performance
	@echo "$(GREEN)Benchmarking inference performance...$(NC)"
	@poetry run python -m query_intelligence.benchmarks.inference_benchmark

test-vertex-ai: ## Test Vertex AI integration
	@echo "$(GREEN)Testing Vertex AI integration...$(NC)"
	@poetry run pytest tests/integration/test_vertex_ai.py -v
endif

ifeq ($(SERVICE_NAME),pattern-mining)
train-models: ## Train ML models
	@echo "$(GREEN)Training ML models...$(NC)"
	@poetry run python -m pattern_mining.training.train_models

validate-patterns: ## Validate pattern detection
	@echo "$(GREEN)Validating pattern detection...$(NC)"
	@poetry run python -m pattern_mining.validation.pattern_validator

benchmark-detection: ## Benchmark pattern detection
	@echo "$(GREEN)Benchmarking pattern detection...$(NC)"
	@poetry run python -m pattern_mining.benchmarks.detection_benchmark
endif
