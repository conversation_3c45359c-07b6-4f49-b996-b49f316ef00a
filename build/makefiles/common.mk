# Common Makefile for CCL Services
# This file contains shared targets and variables for all CCL services

SHELL := /bin/bash
.DEFAULT_GOAL := help

# Project configuration
PROJECT_ID ?= ccl-platform
REGION ?= us-central1
VERSION ?= $(shell git describe --tags --always --dirty)
ENV ?= development
SERVICE_NAME ?= $(notdir $(CURDIR))
LANGUAGE ?= 

# Docker configuration
DOCKER_REGISTRY := us-central1-docker.pkg.dev
DOCKER_PROJECT := $(PROJECT_ID)
DOCKER_REPO := services
DOCKER_IMAGE := $(DOCKER_REGISTRY)/$(DOCKER_PROJECT)/$(DOCKER_REPO)/$(SERVICE_NAME)

# Colors for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
NC := \033[0m # No Color

# Common directories
BUILD_DIR := build
DIST_DIR := dist
COVERAGE_DIR := coverage

.PHONY: help deps clean lint test build docker-build docker-push deploy health-check

help: ## Show this help message
	@echo "CCL $(SERVICE_NAME) Service - Available targets:"
	@echo ""
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(BLUE)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "Environment variables:"
	@echo "  $(YELLOW)SERVICE_NAME$(NC)  = $(SERVICE_NAME)"
	@echo "  $(YELLOW)LANGUAGE$(NC)      = $(LANGUAGE)"
	@echo "  $(YELLOW)VERSION$(NC)       = $(VERSION)"
	@echo "  $(YELLOW)ENV$(NC)           = $(ENV)"
	@echo "  $(YELLOW)PROJECT_ID$(NC)    = $(PROJECT_ID)"

# Include language-specific makefiles
ifneq ($(LANGUAGE),)
-include build/makefiles/$(LANGUAGE).mk
endif

# Common targets
deps: ## Install dependencies
	@echo "$(GREEN)Installing dependencies for $(SERVICE_NAME)...$(NC)"
	@$(MAKE) deps-$(LANGUAGE)

clean: ## Clean build artifacts
	@echo "$(GREEN)Cleaning build artifacts...$(NC)"
	@rm -rf $(BUILD_DIR) $(DIST_DIR) $(COVERAGE_DIR)
	@$(MAKE) clean-$(LANGUAGE)

lint: ## Run linters
	@echo "$(GREEN)Running linters for $(SERVICE_NAME)...$(NC)"
	@$(MAKE) lint-$(LANGUAGE)

fmt: ## Format code
	@echo "$(GREEN)Formatting code for $(SERVICE_NAME)...$(NC)"
	@$(MAKE) fmt-$(LANGUAGE)

security-scan: ## Run security scans
	@echo "$(GREEN)Running security scans for $(SERVICE_NAME)...$(NC)"
	@$(MAKE) security-scan-$(LANGUAGE)

test: ## Run tests
	@echo "$(GREEN)Running tests for $(SERVICE_NAME)...$(NC)"
	@$(MAKE) test-$(LANGUAGE)

test-ci: ## Run tests in CI mode
	@echo "$(GREEN)Running CI tests for $(SERVICE_NAME)...$(NC)"
	@$(MAKE) test-ci-$(LANGUAGE)

coverage: ## Generate coverage report
	@echo "$(GREEN)Generating coverage report for $(SERVICE_NAME)...$(NC)"
	@mkdir -p $(COVERAGE_DIR)
	@$(MAKE) coverage-$(LANGUAGE)

build: ## Build the service
	@echo "$(GREEN)Building $(SERVICE_NAME)...$(NC)"
	@mkdir -p $(DIST_DIR)
	@$(MAKE) build-$(LANGUAGE)

docker-build: ## Build Docker image
	@echo "$(GREEN)Building Docker image for $(SERVICE_NAME)...$(NC)"
	@docker build -t $(DOCKER_IMAGE):$(VERSION) .
	@docker build -t $(DOCKER_IMAGE):latest .

docker-push: docker-build ## Push Docker image to registry
	@echo "$(GREEN)Pushing Docker image for $(SERVICE_NAME)...$(NC)"
	@gcloud auth configure-docker $(DOCKER_REGISTRY)
	@docker push $(DOCKER_IMAGE):$(VERSION)
	@docker push $(DOCKER_IMAGE):latest

deploy: ## Deploy service to Cloud Run
	@echo "$(GREEN)Deploying $(SERVICE_NAME) to $(ENV)...$(NC)"
	@gcloud run deploy $(SERVICE_NAME) \
		--image=$(DOCKER_IMAGE):$(VERSION) \
		--platform=managed \
		--region=$(REGION) \
		--memory=2Gi \
		--cpu=2 \
		--max-instances=100 \
		--min-instances=1 \
		--set-env-vars="VERSION=$(VERSION),ENVIRONMENT=$(ENV)" \
		--project=$(PROJECT_ID)

health-check: ## Check service health
	@echo "$(GREEN)Checking health of $(SERVICE_NAME) in $(ENV)...$(NC)"
	@SERVICE_URL=$$(gcloud run services describe $(SERVICE_NAME) \
		--region=$(REGION) \
		--project=$(PROJECT_ID) \
		--format="value(status.url)"); \
	curl -f "$$SERVICE_URL/health" || exit 1

smoke-test: ## Run smoke tests
	@echo "$(GREEN)Running smoke tests for $(SERVICE_NAME)...$(NC)"
	@$(MAKE) smoke-test-$(LANGUAGE)

# Validation targets
validate-architecture: ## Validate service architecture compliance
	@echo "$(GREEN)Validating architecture for $(SERVICE_NAME)...$(NC)"
	@scripts/validate-architecture.sh $(SERVICE_NAME) $(LANGUAGE)

validate-security: ## Validate security compliance
	@echo "$(GREEN)Validating security for $(SERVICE_NAME)...$(NC)"
	@$(MAKE) security-scan
	@scripts/validate-security.sh $(SERVICE_NAME)

validate-performance: ## Validate performance benchmarks
	@echo "$(GREEN)Validating performance for $(SERVICE_NAME)...$(NC)"
	@$(MAKE) benchmark-$(LANGUAGE)

# Development targets
dev-setup: ## Set up development environment
	@echo "$(GREEN)Setting up development environment for $(SERVICE_NAME)...$(NC)"
	@$(MAKE) deps
	@$(MAKE) dev-setup-$(LANGUAGE)

dev-run: ## Run service in development mode
	@echo "$(GREEN)Running $(SERVICE_NAME) in development mode...$(NC)"
	@$(MAKE) dev-run-$(LANGUAGE)

dev-test: ## Run tests in watch mode
	@echo "$(GREEN)Running tests in watch mode for $(SERVICE_NAME)...$(NC)"
	@$(MAKE) dev-test-$(LANGUAGE)

# CI/CD targets
ci-validate: lint security-scan test coverage ## Run full CI validation
	@echo "$(GREEN)CI validation complete for $(SERVICE_NAME)$(NC)"

cd-validate: health-check smoke-test ## Run CD validation
	@echo "$(GREEN)CD validation complete for $(SERVICE_NAME)$(NC)"

pre-commit: lint test ## Run pre-commit checks
	@echo "$(GREEN)Pre-commit checks passed for $(SERVICE_NAME)$(NC)"

pre-push: ci-validate ## Run pre-push checks
	@echo "$(GREEN)Pre-push checks passed for $(SERVICE_NAME)$(NC)"

# Utility targets
logs: ## Show service logs
	@gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=$(SERVICE_NAME)" \
		--limit=50 \
		--format="table(timestamp,severity,textPayload)" \
		--project=$(PROJECT_ID)

shell: ## Open shell in service container
	@docker run -it --rm $(DOCKER_IMAGE):latest /bin/bash

# Default language-specific targets (will be overridden by language makefiles)
deps-%:
	@echo "$(RED)No dependency installation defined for language: $*$(NC)"

clean-%:
	@echo "Cleaning $* artifacts..."

lint-%:
	@echo "$(RED)No linting defined for language: $*$(NC)"

fmt-%:
	@echo "$(RED)No formatting defined for language: $*$(NC)"

security-scan-%:
	@echo "$(RED)No security scanning defined for language: $*$(NC)"

test-%:
	@echo "$(RED)No tests defined for language: $*$(NC)"

test-ci-%:
	@$(MAKE) test-$*

coverage-%:
	@echo "$(RED)No coverage generation defined for language: $*$(NC)"

build-%:
	@echo "$(RED)No build process defined for language: $*$(NC)"

smoke-test-%:
	@echo "$(RED)No smoke tests defined for language: $*$(NC)"

dev-setup-%:
	@echo "Development setup complete for $*"

dev-run-%:
	@echo "$(RED)No development run defined for language: $*$(NC)"

dev-test-%:
	@echo "$(RED)No development test mode defined for language: $*$(NC)"

benchmark-%:
	@echo "$(RED)No benchmarks defined for language: $*$(NC)"
