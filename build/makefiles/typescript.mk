# TypeScript-specific Makefile for CCL Services

# Node.js/TypeScript configuration
NODE_VERSION ?= 20
NPM_REGISTRY ?= https://registry.npmjs.org/
BUILD_MODE ?= production

# TypeScript build flags
TSC_FLAGS := --noEmit
ESLINT_FLAGS := --ext .ts,.tsx,.js,.jsx
JEST_FLAGS := --coverage --passWithNoTests
PLAYWRIGHT_FLAGS := --reporter=html

# Override language-specific targets
deps-typescript: ## Install TypeScript dependencies
	@echo "$(GREEN)Installing TypeScript dependencies...$(NC)"
	@npm ci --registry=$(NPM_REGISTRY)
	@if [ -f package-lock.json ]; then npm audit fix; fi

clean-typescript: ## Clean TypeScript build artifacts
	@echo "$(GREEN)Cleaning TypeScript artifacts...$(NC)"
	@rm -rf dist/ build/ .next/ out/
	@rm -rf coverage/ test-results/ playwright-report/
	@rm -rf node_modules/.cache/
	@rm -rf $(COVERAGE_DIR)

lint-typescript: ## Run TypeScript linters
	@echo "$(GREEN)Running TypeScript linters...$(NC)"
	@npm run lint || npx eslint $(ESLINT_FLAGS) .
	@npm run type-check || npx tsc $(TSC_FLAGS)

fmt-typescript: ## Format TypeScript code
	@echo "$(GREEN)Formatting TypeScript code...$(NC)"
	@npm run format || npx prettier --write .
	@npm run lint:fix || npx eslint $(ESLINT_FLAGS) . --fix

security-scan-typescript: ## Run TypeScript security scans
	@echo "$(GREEN)Running TypeScript security scans...$(NC)"
	@npm audit --audit-level=moderate
	@npx retire --path .
	@if command -v snyk >/dev/null 2>&1; then snyk test; fi

test-typescript: ## Run TypeScript tests
	@echo "$(GREEN)Running TypeScript tests...$(NC)"
	@npm test || npx jest $(JEST_FLAGS)

test-ci-typescript: ## Run TypeScript tests in CI mode
	@echo "$(GREEN)Running TypeScript CI tests...$(NC)"
	@npm run test:ci || npx jest $(JEST_FLAGS) --ci --watchAll=false

coverage-typescript: ## Generate TypeScript coverage report
	@echo "$(GREEN)Generating TypeScript coverage report...$(NC)"
	@mkdir -p $(COVERAGE_DIR)
	@npm run test:coverage || npx jest $(JEST_FLAGS) --collectCoverage
	@if [ -d coverage ]; then cp -r coverage/* $(COVERAGE_DIR)/; fi

build-typescript: ## Build TypeScript service
	@echo "$(GREEN)Building TypeScript service...$(NC)"
	@mkdir -p $(DIST_DIR)
	@npm run build || npx tsc --build
	@if [ -d dist ]; then cp -r dist/* $(DIST_DIR)/; fi
	@if [ -d build ]; then cp -r build/* $(DIST_DIR)/; fi
	@if [ -d .next ]; then cp -r .next $(DIST_DIR)/; fi

smoke-test-typescript: ## Run TypeScript smoke tests
	@echo "$(GREEN)Running TypeScript smoke tests...$(NC)"
	@npm run test:smoke || npx jest --testPathPattern=smoke

dev-setup-typescript: ## Set up TypeScript development environment
	@echo "$(GREEN)Setting up TypeScript development environment...$(NC)"
	@npm install
	@npx husky install || true
	@if [ -f .env.example ]; then cp .env.example .env.local; fi

dev-run-typescript: ## Run TypeScript service in development mode
	@echo "$(GREEN)Running TypeScript service in development mode...$(NC)"
	@npm run dev || npm start

dev-test-typescript: ## Run TypeScript tests in watch mode
	@echo "$(GREEN)Running TypeScript tests in watch mode...$(NC)"
	@npm run test:watch || npx jest --watch

benchmark-typescript: ## Run TypeScript benchmarks
	@echo "$(GREEN)Running TypeScript benchmarks...$(NC)"
	@npm run benchmark || npx jest --testPathPattern=benchmark

# TypeScript-specific targets
type-check: ## Run TypeScript type checking
	@echo "$(GREEN)Running TypeScript type checking...$(NC)"
	@npx tsc $(TSC_FLAGS)

update-typescript: ## Update TypeScript dependencies
	@echo "$(GREEN)Updating TypeScript dependencies...$(NC)"
	@npm update
	@npm audit fix

outdated-typescript: ## Check for outdated dependencies
	@echo "$(GREEN)Checking for outdated TypeScript dependencies...$(NC)"
	@npm outdated

# Web service specific targets
ifeq ($(SERVICE_NAME),web)
build-production: ## Build for production
	@echo "$(GREEN)Building web app for production...$(NC)"
	@npm run build:prod || NODE_ENV=production npm run build

start-production: ## Start production server
	@echo "$(GREEN)Starting production web server...$(NC)"
	@npm run start:prod || NODE_ENV=production npm start

e2e-test: ## Run end-to-end tests
	@echo "$(GREEN)Running end-to-end tests...$(NC)"
	@npx playwright test $(PLAYWRIGHT_FLAGS)

e2e-test-headed: ## Run end-to-end tests in headed mode
	@echo "$(GREEN)Running end-to-end tests in headed mode...$(NC)"
	@npx playwright test --headed

lighthouse: ## Run Lighthouse performance audit
	@echo "$(GREEN)Running Lighthouse performance audit...$(NC)"
	@npx lighthouse http://localhost:3000 --output=html --output-path=lighthouse-report.html

bundle-analyze: ## Analyze bundle size
	@echo "$(GREEN)Analyzing bundle size...$(NC)"
	@npm run analyze || npx webpack-bundle-analyzer dist/static/js/*.js
endif

# SDK service specific targets
ifeq ($(SERVICE_NAME),sdk)
build-lib: ## Build library for distribution
	@echo "$(GREEN)Building SDK library...$(NC)"
	@npm run build:lib || npx rollup -c

publish-npm: ## Publish to NPM
	@echo "$(GREEN)Publishing SDK to NPM...$(NC)"
	@npm publish --access public

version-bump: ## Bump version
	@echo "$(GREEN)Bumping SDK version...$(NC)"
	@npm version patch

generate-docs: ## Generate API documentation
	@echo "$(GREEN)Generating SDK documentation...$(NC)"
	@npx typedoc --out docs src/index.ts
endif

# Collaboration service specific targets
ifeq ($(SERVICE_NAME),collaboration)
test-websockets: ## Test WebSocket functionality
	@echo "$(GREEN)Testing WebSocket functionality...$(NC)"
	@npm run test:websockets || npx jest --testPathPattern=websocket

benchmark-realtime: ## Benchmark real-time performance
	@echo "$(GREEN)Benchmarking real-time performance...$(NC)"
	@npm run benchmark:realtime || node benchmarks/realtime-benchmark.js

validate-sessions: ## Validate session management
	@echo "$(GREEN)Validating session management...$(NC)"
	@npm run test:sessions || npx jest --testPathPattern=session
endif

# Common web development targets
install-playwright: ## Install Playwright browsers
	@echo "$(GREEN)Installing Playwright browsers...$(NC)"
	@npx playwright install

serve-coverage: ## Serve coverage report
	@echo "$(GREEN)Serving coverage report...$(NC)"
	@npx http-server coverage/lcov-report -p 8080

storybook: ## Start Storybook
	@echo "$(GREEN)Starting Storybook...$(NC)"
	@npm run storybook || npx storybook dev -p 6006
