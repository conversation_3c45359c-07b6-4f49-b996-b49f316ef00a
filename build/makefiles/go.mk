# Go-specific Makefile for CCL Services

# Go configuration
GO_VERSION ?= 1.21
GOPATH ?= $(shell go env GOPATH)
GOOS ?= linux
GOARCH ?= amd64
CGO_ENABLED ?= 0

# Go build flags
GO_BUILD_FLAGS := -ldflags="-w -s -X main.version=$(VERSION) -X main.buildTime=$(shell date -u +%Y-%m-%dT%H:%M:%SZ)"
GO_TEST_FLAGS := -race -coverprofile=coverage.out
GO_LINT_FLAGS := --timeout=5m

# Override language-specific targets
deps-go: ## Install Go dependencies
	@echo "$(GREEN)Installing Go dependencies...$(NC)"
	@go mod download
	@go mod verify
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
	@go install golang.org/x/vuln/cmd/govulncheck@latest
	@go install github.com/axw/gocov/gocov@latest
	@go install github.com/AlekSi/gocov-xml@latest

clean-go: ## Clean Go build artifacts
	@echo "$(GREEN)Cleaning Go artifacts...$(NC)"
	@go clean -cache -testcache -modcache
	@rm -rf $(DIST_DIR) coverage.out coverage.xml coverage.html

lint-go: ## Run Go linters
	@echo "$(GREEN)Running Go linters...$(NC)"
	@gofmt -l -s . | tee /dev/stderr | (! read)
	@go vet ./...
	@golangci-lint run $(GO_LINT_FLAGS)

fmt-go: ## Format Go code
	@echo "$(GREEN)Formatting Go code...$(NC)"
	@gofmt -w -s .
	@go mod tidy

security-scan-go: ## Run Go security scans
	@echo "$(GREEN)Running Go security scans...$(NC)"
	@gosec ./...
	@govulncheck ./...

test-go: ## Run Go tests
	@echo "$(GREEN)Running Go tests...$(NC)"
	@go test $(GO_TEST_FLAGS) ./...

test-ci-go: ## Run Go tests in CI mode
	@echo "$(GREEN)Running Go CI tests...$(NC)"
	@go test $(GO_TEST_FLAGS) -v ./...

coverage-go: ## Generate Go coverage report
	@echo "$(GREEN)Generating Go coverage report...$(NC)"
	@mkdir -p $(COVERAGE_DIR)
	@go test $(GO_TEST_FLAGS) ./...
	@go tool cover -html=coverage.out -o $(COVERAGE_DIR)/coverage.html
	@gocov convert coverage.out | gocov-xml > $(COVERAGE_DIR)/coverage.xml
	@go tool cover -func=coverage.out

build-go: ## Build Go service
	@echo "$(GREEN)Building Go service...$(NC)"
	@mkdir -p $(DIST_DIR)
	@CGO_ENABLED=$(CGO_ENABLED) GOOS=$(GOOS) GOARCH=$(GOARCH) \
		go build $(GO_BUILD_FLAGS) -o $(DIST_DIR)/$(SERVICE_NAME) ./cmd/$(SERVICE_NAME)

smoke-test-go: ## Run Go smoke tests
	@echo "$(GREEN)Running Go smoke tests...$(NC)"
	@go test -tags=smoke ./tests/smoke/...

dev-setup-go: ## Set up Go development environment
	@echo "$(GREEN)Setting up Go development environment...$(NC)"
	@go install github.com/cosmtrek/air@latest
	@go install github.com/go-delve/delve/cmd/dlv@latest

dev-run-go: ## Run Go service in development mode
	@echo "$(GREEN)Running Go service in development mode...$(NC)"
	@air -c .air.toml

dev-test-go: ## Run Go tests in watch mode
	@echo "$(GREEN)Running Go tests in watch mode...$(NC)"
	@find . -name "*.go" | entr -r go test ./...

benchmark-go: ## Run Go benchmarks
	@echo "$(GREEN)Running Go benchmarks...$(NC)"
	@go test -bench=. -benchmem ./...

# Go-specific targets
mod-tidy: ## Tidy Go modules
	@echo "$(GREEN)Tidying Go modules...$(NC)"
	@go mod tidy

mod-verify: ## Verify Go modules
	@echo "$(GREEN)Verifying Go modules...$(NC)"
	@go mod verify

mod-graph: ## Show Go module dependency graph
	@echo "$(GREEN)Showing Go module dependency graph...$(NC)"
	@go mod graph

update-go: ## Update Go dependencies
	@echo "$(GREEN)Updating Go dependencies...$(NC)"
	@go get -u ./...
	@go mod tidy

vendor: ## Vendor Go dependencies
	@echo "$(GREEN)Vendoring Go dependencies...$(NC)"
	@go mod vendor

# Build multiple architectures
build-all-go: ## Build for all architectures
	@echo "$(GREEN)Building for all architectures...$(NC)"
	@mkdir -p $(DIST_DIR)
	@for os in linux darwin windows; do \
		for arch in amd64 arm64; do \
			echo "Building for $$os/$$arch..."; \
			CGO_ENABLED=0 GOOS=$$os GOARCH=$$arch \
				go build $(GO_BUILD_FLAGS) \
				-o $(DIST_DIR)/$(SERVICE_NAME)-$$os-$$arch \
				./cmd/$(SERVICE_NAME); \
		done; \
	done

# Marketplace service specific targets
ifeq ($(SERVICE_NAME),marketplace)
validate-api: ## Validate API specifications
	@echo "$(GREEN)Validating API specifications...$(NC)"
	@go test ./internal/api/validation/... -v

benchmark-api: ## Benchmark API performance
	@echo "$(GREEN)Benchmarking API performance...$(NC)"
	@go test -bench=BenchmarkAPI ./internal/api/... -benchmem

test-payments: ## Test payment integration
	@echo "$(GREEN)Testing payment integration...$(NC)"
	@go test ./internal/payments/... -v -tags=integration

validate-commerce: ## Validate commerce logic
	@echo "$(GREEN)Validating commerce logic...$(NC)"
	@go test ./internal/commerce/... -v
endif

# Database migration targets (if applicable)
migrate-up: ## Run database migrations up
	@echo "$(GREEN)Running database migrations up...$(NC)"
	@go run ./cmd/migrate up

migrate-down: ## Run database migrations down
	@echo "$(GREEN)Running database migrations down...$(NC)"
	@go run ./cmd/migrate down

migrate-status: ## Check migration status
	@echo "$(GREEN)Checking migration status...$(NC)"
	@go run ./cmd/migrate status
