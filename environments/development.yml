# Development Environment Configuration for CCL Platform
# This configuration is used for local development and dev deployments

environment: development
region: us-central1

# GCP Project Configuration
gcp:
  project_id: vibe-match-463114
  region: us-central1
  zone: us-central1-a
  
  # Service Account
  service_account: <EMAIL>
  
  # Artifact Registry
  artifact_registry:
    repository: ccl-dev-services
    location: us-central1
    
  # Cloud Storage
  storage:
    bucket: ccl-dev-storage
    location: us-central1

# Service Configurations
services:
  # Analysis Engine (Rust)
  analysis_engine:
    cloud_run:
      cpu: 1
      memory: 1Gi
      min_instances: 0
      max_instances: 10
      timeout: 300s
      concurrency: 100
    environment_variables:
      RUST_LOG: debug
      RUST_BACKTRACE: 1
      MAX_FILE_SIZE: 10MB
      PARSER_TIMEOUT: 30s
    
  # Query Intelligence (Python)
  query_intelligence:
    cloud_run:
      cpu: 2
      memory: 4Gi
      min_instances: 1
      max_instances: 20
      timeout: 600s
      concurrency: 50
    vertex_ai:
      location: us-central1
      model: gemini-2.5-flash-exp
      max_tokens: 8192
    environment_variables:
      PYTHONPATH: /app
      LOG_LEVEL: DEBUG
      VERTEX_AI_PROJECT: ccl-platform-dev
      VERTEX_AI_LOCATION: us-central1
      
  # Pattern Mining (Python)
  pattern_mining:
    cloud_run:
      cpu: 4
      memory: 8Gi
      min_instances: 0
      max_instances: 5
      timeout: 1800s
      concurrency: 10
    vertex_ai:
      location: us-central1
      training_job_region: us-central1
    environment_variables:
      PYTHONPATH: /app
      LOG_LEVEL: DEBUG
      ML_MODEL_BUCKET: ccl-dev-ml-models
      TRAINING_DATA_BUCKET: ccl-dev-training-data
      
  # Marketplace (Go)
  marketplace:
    cloud_run:
      cpu: 1
      memory: 2Gi
      min_instances: 1
      max_instances: 15
      timeout: 300s
      concurrency: 1000
    environment_variables:
      GO_ENV: development
      LOG_LEVEL: debug
      STRIPE_WEBHOOK_SECRET: whsec_dev_test_key
      
  # Web Frontend (TypeScript)
  web:
    cloud_run:
      cpu: 1
      memory: 1Gi
      min_instances: 1
      max_instances: 10
      timeout: 300s
      concurrency: 1000
    environment_variables:
      NODE_ENV: development
      NEXT_PUBLIC_API_URL: https://api-dev.ccl-platform.com
      NEXT_PUBLIC_WS_URL: wss://ws-dev.ccl-platform.com
      
  # Collaboration (TypeScript)
  collaboration:
    cloud_run:
      cpu: 2
      memory: 2Gi
      min_instances: 1
      max_instances: 10
      timeout: 300s
      concurrency: 100
    environment_variables:
      NODE_ENV: development
      WS_PORT: 8080
      REDIS_URL: redis://redis-dev.ccl-platform.com:6379

# Database Configuration
databases:
  # Spanner (Primary OLTP)
  spanner:
    instance: ccl-dev-instance
    database: ccl-dev-db
    node_count: 1
    processing_units: 100
    
  # BigQuery (Analytics)
  bigquery:
    dataset: ccl_dev_analytics
    location: us-central1
    
  # Firestore (Real-time)
  firestore:
    database: ccl-dev-firestore
    location: us-central1
    
  # Redis (Caching)
  redis:
    instance: ccl-dev-redis
    memory_size_gb: 1
    tier: basic
    location: us-central1

# Networking
networking:
  # VPC
  vpc:
    name: ccl-dev-vpc
    subnet: ccl-dev-subnet
    
  # Load Balancer
  load_balancer:
    name: ccl-dev-lb
    ssl_certificate: ccl-dev-ssl-cert
    
  # Cloud NAT
  nat:
    name: ccl-dev-nat
    router: ccl-dev-router

# Security Configuration
security:
  # IAM
  iam:
    service_accounts:
      - name: ccl-dev-analysis-engine
        roles:
          - roles/spanner.databaseUser
          - roles/storage.objectViewer
      - name: ccl-dev-query-intelligence
        roles:
          - roles/aiplatform.user
          - roles/spanner.databaseUser
      - name: ccl-dev-pattern-mining
        roles:
          - roles/aiplatform.user
          - roles/ml.developer
      - name: ccl-dev-marketplace
        roles:
          - roles/spanner.databaseUser
          - roles/pubsub.publisher
          
  # Secrets
  secrets:
    - name: database-credentials
      version: latest
    - name: api-keys
      version: latest
    - name: stripe-keys
      version: latest
    - name: jwt-signing-key
      version: latest
      
  # VPC Service Controls
  vpc_service_controls:
    enabled: false  # Disabled for development ease

# Monitoring and Logging
monitoring:
  # Cloud Monitoring
  metrics:
    retention_days: 30
    
  # Cloud Logging
  logging:
    retention_days: 30
    log_level: DEBUG
    
  # Alerting
  alerting:
    notification_channels:
      - email: <EMAIL>
      - slack: "#ccl-dev-alerts"
    
  # Uptime Monitoring
  uptime_checks:
    - name: analysis-engine-health
      url: https://analysis-engine-dev.ccl-platform.com/health
      frequency: 60s
    - name: marketplace-health
      url: https://marketplace-dev.ccl-platform.com/health
      frequency: 60s

# CI/CD Configuration
cicd:
  # GitHub Actions
  github_actions:
    environment: development
    auto_deploy: true
    require_approval: false
    
  # Deployment Strategy
  deployment:
    strategy: rolling
    health_check_timeout: 300s
    rollback_on_failure: true
    
  # Testing
  testing:
    run_integration_tests: true
    run_e2e_tests: false  # Skip E2E in dev
    performance_testing: false

# Feature Flags
feature_flags:
  # Development features
  debug_mode: true
  verbose_logging: true
  mock_external_apis: true
  
  # Experimental features
  new_pattern_detection: true
  advanced_query_features: true
  real_time_collaboration: true

# Resource Limits
resource_limits:
  # Per-service limits
  analysis_engine:
    max_concurrent_analyses: 10
    max_file_size_mb: 50
    
  query_intelligence:
    max_concurrent_queries: 100
    query_timeout_seconds: 30
    
  pattern_mining:
    max_training_jobs: 2
    max_model_size_gb: 5
    
  marketplace:
    max_concurrent_transactions: 50
    rate_limit_per_minute: 1000

# Development Tools
development:
  # Local development
  local_services:
    - analysis-engine
    - query-intelligence
    - marketplace
    - web
    
  # Debug endpoints
  debug_endpoints:
    enabled: true
    paths:
      - /debug/health
      - /debug/metrics
      - /debug/config
      
  # Hot reload
  hot_reload:
    enabled: true
    watch_patterns:
      - "**/*.rs"
      - "**/*.py"
      - "**/*.go"
      - "**/*.ts"
      - "**/*.tsx"

# Cost Optimization
cost_optimization:
  # Auto-scaling
  auto_scaling:
    scale_to_zero: true
    scale_down_delay: 300s
    
  # Resource scheduling
  resource_scheduling:
    shutdown_unused_resources: true
    schedule: "0 22 * * 1-5"  # Shutdown at 10 PM weekdays
    
  # Budget alerts
  budget_alerts:
    monthly_budget_usd: 500
    alert_thresholds: [50, 80, 100]
