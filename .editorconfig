# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Global settings for all files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# Python files
[*.py]
indent_style = space
indent_size = 4
max_line_length = 88

# JavaScript/TypeScript files
[*.{js,ts,jsx,tsx}]
indent_style = space
indent_size = 2
quote_type = single

# Go files
[*.go]
indent_style = tab
indent_size = 4

# Rust files
[*.rs]
indent_style = space
indent_size = 4
max_line_length = 100

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# JSON files
[*.json]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = 80

# Makefile
[Makefile]
indent_style = tab

# Shell scripts
[*.sh]
indent_style = space
indent_size = 2

# Dockerfile
[Dockerfile*]
indent_style = space
indent_size = 2

# Configuration files
[*.{toml,ini,cfg}]
indent_style = space
indent_size = 2

# Protocol Buffers
[*.proto]
indent_style = space
indent_size = 2

# SQL files
[*.sql]
indent_style = space
indent_size = 2