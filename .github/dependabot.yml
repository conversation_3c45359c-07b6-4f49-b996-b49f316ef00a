version: 2
updates:
  # Rust dependencies (Analysis Engine)
  - package-ecosystem: "cargo"
    directory: "/services/analysis-engine"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "04:00"
    open-pull-requests-limit: 5
    reviewers:
      - "platform-team"
    assignees:
      - "rust-maintainers"
    commit-message:
      prefix: "deps(rust)"
      include: "scope"
    labels:
      - "dependencies"
      - "rust"
      - "analysis-engine"

  # Python dependencies (Query Intelligence)
  - package-ecosystem: "pip"
    directory: "/services/query-intelligence"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "04:00"
    open-pull-requests-limit: 5
    reviewers:
      - "platform-team"
    assignees:
      - "python-maintainers"
    commit-message:
      prefix: "deps(python)"
      include: "scope"
    labels:
      - "dependencies"
      - "python"
      - "query-intelligence"

  # Python dependencies (Pattern Mining)
  - package-ecosystem: "pip"
    directory: "/services/pattern-mining"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "04:00"
    open-pull-requests-limit: 5
    reviewers:
      - "platform-team"
    assignees:
      - "python-maintainers"
    commit-message:
      prefix: "deps(python)"
      include: "scope"
    labels:
      - "dependencies"
      - "python"
      - "pattern-mining"

  # Go dependencies (Marketplace)
  - package-ecosystem: "gomod"
    directory: "/services/marketplace"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "04:00"
    open-pull-requests-limit: 5
    reviewers:
      - "platform-team"
    assignees:
      - "go-maintainers"
    commit-message:
      prefix: "deps(go)"
      include: "scope"
    labels:
      - "dependencies"
      - "go"
      - "marketplace"

  # TypeScript dependencies (Web)
  - package-ecosystem: "npm"
    directory: "/services/web"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "04:00"
    open-pull-requests-limit: 5
    reviewers:
      - "platform-team"
    assignees:
      - "frontend-maintainers"
    commit-message:
      prefix: "deps(npm)"
      include: "scope"
    labels:
      - "dependencies"
      - "typescript"
      - "web"

  # TypeScript dependencies (Collaboration)
  - package-ecosystem: "npm"
    directory: "/services/collaboration"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "04:00"
    open-pull-requests-limit: 5
    reviewers:
      - "platform-team"
    assignees:
      - "frontend-maintainers"
    commit-message:
      prefix: "deps(npm)"
      include: "scope"
    labels:
      - "dependencies"
      - "typescript"
      - "collaboration"

  # TypeScript dependencies (SDK)
  - package-ecosystem: "npm"
    directory: "/services/sdk"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "04:00"
    open-pull-requests-limit: 5
    reviewers:
      - "platform-team"
    assignees:
      - "sdk-maintainers"
    commit-message:
      prefix: "deps(npm)"
      include: "scope"
    labels:
      - "dependencies"
      - "typescript"
      - "sdk"

  # Infrastructure dependencies
  - package-ecosystem: "docker"
    directory: "/infrastructure"
    schedule:
      interval: "weekly"
      day: "tuesday"
      time: "04:00"
    open-pull-requests-limit: 3
    reviewers:
      - "platform-team"
    assignees:
      - "devops-maintainers"
    commit-message:
      prefix: "deps(docker)"
      include: "scope"
    labels:
      - "dependencies"
      - "docker"
      - "infrastructure"

  # GitHub Actions dependencies
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "tuesday"
      time: "04:00"
    open-pull-requests-limit: 3
    reviewers:
      - "platform-team"
    assignees:
      - "devops-maintainers"
    commit-message:
      prefix: "deps(actions)"
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions"
      - "ci-cd"

  # ML Training Data dependencies
  - package-ecosystem: "pip"
    directory: "/ml/training-data"
    schedule:
      interval: "weekly"
      day: "wednesday"
      time: "04:00"
    open-pull-requests-limit: 5
    reviewers:
      - "platform-team"
    assignees:
      - "ml-maintainers"
    commit-message:
      prefix: "deps(ml)"
      include: "scope"
    labels:
      - "dependencies"
      - "python"
      - "machine-learning"

# Security updates - higher priority, checked daily
  - package-ecosystem: "cargo"
    directory: "/services/analysis-engine"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 10
    allow:
      - dependency-type: "direct"
        update-type: "security"
    labels:
      - "security"
      - "priority-high"

  - package-ecosystem: "pip"
    directory: "/services/query-intelligence"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 10
    allow:
      - dependency-type: "direct"
        update-type: "security"
    labels:
      - "security"
      - "priority-high"

  - package-ecosystem: "pip"
    directory: "/services/pattern-mining"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 10
    allow:
      - dependency-type: "direct"
        update-type: "security"
    labels:
      - "security"
      - "priority-high"

  - package-ecosystem: "gomod"
    directory: "/services/marketplace"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 10
    allow:
      - dependency-type: "direct"
        update-type: "security"
    labels:
      - "security"
      - "priority-high"

  - package-ecosystem: "npm"
    directory: "/services/web"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 10
    allow:
      - dependency-type: "direct"
        update-type: "security"
    labels:
      - "security"
      - "priority-high"

  - package-ecosystem: "npm"
    directory: "/services/collaboration"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 10
    allow:
      - dependency-type: "direct"
        update-type: "security"
    labels:
      - "security"
      - "priority-high"