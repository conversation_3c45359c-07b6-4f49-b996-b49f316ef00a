# Required GitHub Secrets Configuration

This document describes the secrets that must be configured in the GitHub repository settings for the CI/CD workflows to function properly.

## Required Secrets

### 1. GCP_SA_KEY
**Description**: Google Cloud Platform Service Account Key for deployment and artifact registry access.

**Required for**: 
- Building and pushing Docker images to Google Artifact Registry
- Deploying services to Google Cloud Run
- Accessing Google Cloud Storage for deployment artifacts

**How to obtain**:
1. Create a service account in Google Cloud Console
2. Grant the following roles:
   - `Artifact Registry Writer`
   - `Cloud Run Admin`
   - `Storage Admin`
   - `Service Account User`
3. Create and download a JSON key for the service account
4. Add the entire JSON content as the secret value

**Format**: JSON string containing the service account credentials

### 2. SONAR_TOKEN
**Description**: SonarCloud authentication token for code quality analysis.

**Required for**:
- Running SonarCloud code quality scans
- Publishing code coverage reports
- Security vulnerability analysis

**How to obtain**:
1. Go to [SonarCloud](https://sonarcloud.io/)
2. Log in with your GitHub account
3. Go to My Account > Security
4. Generate a new token
5. Copy the token value

**Format**: String token (e.g., `sqp_1234567890abcdef...`)

### 3. SLACK_WEBHOOK_URL (Optional)
**Description**: Slack webhook URL for deployment notifications.

**Required for**:
- Sending deployment status notifications to Slack

**How to obtain**:
1. Create a Slack app in your workspace
2. Enable incoming webhooks
3. Create a webhook for your desired channel
4. Copy the webhook URL

**Format**: URL string (e.g., `https://hooks.slack.com/services/...`)

## How to Configure Secrets

1. Go to your GitHub repository
2. Click on **Settings** tab
3. In the left sidebar, click **Secrets and variables** > **Actions**
4. Click **New repository secret**
5. Enter the secret name and value
6. Click **Add secret**

## Validation

The workflows include validation steps that will check if required secrets are configured:

```yaml
- name: Validate required secrets
  run: |
    if [ -z "${{ secrets.GCP_SA_KEY }}" ]; then
      echo "Error: GCP_SA_KEY secret is not configured"
      exit 1
    fi
    if [ -z "${{ secrets.SONAR_TOKEN }}" ]; then
      echo "Error: SONAR_TOKEN secret is not configured"
      exit 1
    fi
    echo "All required secrets are configured"
```

## Security Best Practices

1. **Rotate secrets regularly**: Update service account keys and tokens periodically
2. **Use least privilege**: Grant only the minimum required permissions
3. **Monitor usage**: Review secret usage in workflow logs
4. **Environment-specific secrets**: Consider using environment-specific secrets for different deployment stages

## Troubleshooting

### Common Issues

1. **"Context access might be invalid" warnings**: These are IDE warnings that appear when secrets are not yet configured. They will resolve once secrets are added to the repository.

2. **Authentication failures**: Ensure the service account has the correct permissions and the JSON key is valid.

3. **SonarCloud failures**: Verify the token is valid and the project is properly configured in SonarCloud.

### Testing Secret Configuration

You can test if secrets are properly configured by running a workflow manually or by pushing a commit that triggers the CI pipeline. The validation steps will fail early if secrets are missing or invalid.
