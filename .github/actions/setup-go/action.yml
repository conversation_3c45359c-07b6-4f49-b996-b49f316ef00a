name: Setup Go Environment
description: Set up Go environment for CCL services
inputs:
  service_name:
    description: Name of the service being built
    required: true
  go_version:
    description: Go version to install
    required: false
    default: '1.21'

runs:
  using: composite
  steps:
    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: ${{ inputs.go_version }}

    - name: Install development tools
      shell: bash
      run: |
        # Install golangci-lint for comprehensive linting
        curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.55.2
        
        # Install gosec for security scanning
        go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
        
        # Install govulncheck for vulnerability scanning
        go install golang.org/x/vuln/cmd/govulncheck@latest
        
        # Install gocov for coverage
        go install github.com/axw/gocov/gocov@latest
        go install github.com/AlekSi/gocov-xml@latest

    - name: Configure Go environment
      shell: bash
      run: |
        # Set up Go environment variables
        echo "GOPROXY=https://proxy.golang.org,direct" >> $GITHUB_ENV
        echo "GOSUMDB=sum.golang.org" >> $GITHUB_ENV
        echo "CGO_ENABLED=0" >> $GITHUB_ENV
        
        # Add Go bin to PATH
        echo "$(go env GOPATH)/bin" >> $GITHUB_PATH

    - name: Verify Go installation
      shell: bash
      run: |
        go version
        golangci-lint --version
        gosec -version
        govulncheck -version
