name: Setup Python Environment
description: Set up Python environment for CCL services
inputs:
  service_name:
    description: Name of the service being built
    required: true
  python_version:
    description: Python version to install
    required: false
    default: '3.11'

runs:
  using: composite
  steps:
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: ${{ inputs.python_version }}

    - name: Install Poetry
      shell: bash
      run: |
        curl -sSL https://install.python-poetry.org | python3 -
        echo "$HOME/.local/bin" >> $GITHUB_PATH

    - name: Configure Poetry
      shell: bash
      run: |
        poetry config virtualenvs.create true
        poetry config virtualenvs.in-project true
        poetry config cache-dir ~/.cache/pypoetry

    - name: Install development tools
      shell: bash
      run: |
        pip install --upgrade pip
        pip install ruff black mypy pytest pytest-cov pytest-asyncio
        pip install bandit safety

    - name: Install service-specific tools
      shell: bash
      run: |
        if [[ "${{ inputs.service_name }}" == "query-intelligence" || "${{ inputs.service_name }}" == "pattern-mining" ]]; then
          # Install ML/AI specific tools
          pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
          pip install transformers datasets scikit-learn
        fi

    - name: Verify Python installation
      shell: bash
      run: |
        python --version
        pip --version
        poetry --version
        ruff --version
        black --version
        mypy --version
