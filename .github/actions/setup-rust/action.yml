name: Setup Rust Environment
description: Set up Rust toolchain for CCL services
inputs:
  service_name:
    description: Name of the service being built
    required: true
  rust_version:
    description: Rust version to install
    required: false
    default: '1.75.0'

runs:
  using: composite
  steps:
    - name: Install Rust toolchain
      uses: dtolnay/rust-toolchain@stable
      with:
        toolchain: ${{ inputs.rust_version }}
        components: rustfmt, clippy

    - name: Install additional tools
      shell: bash
      run: |
        # Install cargo-audit for security scanning
        cargo install cargo-audit --locked
        
        # Install cargo-tarpaulin for coverage
        cargo install cargo-tarpaulin --locked
        
        # Install cargo-deny for dependency checking
        cargo install cargo-deny --locked

    - name: Configure Rust environment
      shell: bash
      run: |
        # Set up cargo config for faster builds
        mkdir -p ~/.cargo
        cat > ~/.cargo/config.toml << EOF
        [build]
        jobs = 4
        
        [net]
        retry = 3
        
        [registry]
        default = "crates-io"
        
        [target.x86_64-unknown-linux-gnu]
        linker = "clang"
        rustflags = ["-C", "link-arg=-fuse-ld=lld"]
        EOF

    - name: Verify Rust installation
      shell: bash
      run: |
        rustc --version
        cargo --version
        rustfmt --version
        cargo clippy --version
