name: Setup TypeScript Environment
description: Set up Node.js and TypeScript environment for CCL services
inputs:
  service_name:
    description: Name of the service being built
    required: true
  node_version:
    description: Node.js version to install
    required: false
    default: '20'

runs:
  using: composite
  steps:
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node_version }}
        cache: 'npm'

    - name: Install global tools
      shell: bash
      run: |
        npm install -g typescript@latest
        npm install -g @typescript-eslint/parser@latest
        npm install -g @typescript-eslint/eslint-plugin@latest
        npm install -g prettier@latest
        npm install -g jest@latest
        npm install -g @playwright/test@latest

    - name: Install security tools
      shell: bash
      run: |
        npm install -g npm-audit-resolver
        npm install -g retire
        npm install -g snyk

    - name: Configure npm
      shell: bash
      run: |
        # Set up npm configuration for faster installs
        npm config set registry https://registry.npmjs.org/
        npm config set cache ~/.npm
        npm config set fund false
        npm config set audit-level moderate

    - name: Verify installation
      shell: bash
      run: |
        node --version
        npm --version
        tsc --version
        eslint --version
        prettier --version
