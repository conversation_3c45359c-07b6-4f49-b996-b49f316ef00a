#!/usr/bin/env python3
"""
Validate GitHub Actions workflow files for proper structure and syntax.
"""

import yaml
import os
import sys
from pathlib import Path

def validate_workflow_file(file_path):
    """Validate a single workflow file."""
    print(f"Validating {file_path}...")
    
    try:
        with open(file_path, 'r') as f:
            workflow = yaml.safe_load(f)
        
        # Check required fields
        required_fields = ['name', 'on', 'jobs']
        for field in required_fields:
            if field not in workflow:
                print(f"  ❌ Missing required field: {field}")
                return False
        
        # Check if it's a reusable workflow
        if 'workflow_call' in workflow['on']:
            print(f"  ✅ Reusable workflow detected")
            
            # Validate workflow_call structure
            workflow_call = workflow['on']['workflow_call']
            if 'inputs' in workflow_call:
                print(f"  ✅ Has inputs defined")
            if 'secrets' in workflow_call:
                print(f"  ✅ Has secrets defined")
        
        # Check jobs structure
        if not isinstance(workflow['jobs'], dict):
            print(f"  ❌ Jobs must be a dictionary")
            return False
        
        for job_name, job_config in workflow['jobs'].items():
            if 'uses' in job_config:
                print(f"  ✅ Job '{job_name}' uses reusable workflow: {job_config['uses']}")
            elif 'runs-on' in job_config:
                print(f"  ✅ Job '{job_name}' has runs-on defined")
            else:
                print(f"  ❌ Job '{job_name}' missing runs-on or uses")
                return False
        
        print(f"  ✅ {file_path} is valid")
        return True
        
    except yaml.YAMLError as e:
        print(f"  ❌ YAML syntax error: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Error validating {file_path}: {e}")
        return False

def main():
    """Main validation function."""
    workflows_dir = Path('.github/workflows')
    
    if not workflows_dir.exists():
        print("❌ .github/workflows directory not found")
        sys.exit(1)
    
    workflow_files = list(workflows_dir.glob('*.yml')) + list(workflows_dir.glob('*.yaml'))
    
    if not workflow_files:
        print("❌ No workflow files found")
        sys.exit(1)
    
    print(f"Found {len(workflow_files)} workflow files")
    print("=" * 50)
    
    all_valid = True
    for workflow_file in workflow_files:
        if not validate_workflow_file(workflow_file):
            all_valid = False
        print()
    
    if all_valid:
        print("✅ All workflow files are valid!")
        sys.exit(0)
    else:
        print("❌ Some workflow files have issues")
        sys.exit(1)

if __name__ == '__main__':
    main()
