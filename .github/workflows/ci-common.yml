name: CI Common
"on":
  workflow_call:
    inputs:
      service_name:
        required: true
        type: string
      language:
        required: true
        type: string
      working_directory:
        required: false
        type: string
        default: "."
    secrets:
      GCP_SA_KEY:
        required: true
      SONAR_TOKEN:
        required: true

permissions:
  contents: read
  security-events: write
  actions: read
  checks: write
  pull-requests: write

jobs:
  quality-checks:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ${{ inputs.working_directory }}
    
    steps:
      - name: Validate required secrets
        run: |
          if [ -z "${{ secrets.GCP_SA_KEY }}" ]; then
            echo "Error: GCP_SA_KEY secret is not configured"
            exit 1
          fi
          if [ -z "${{ secrets.SONAR_TOKEN }}" ]; then
            echo "Error: SONAR_TOKEN secret is not configured"
            exit 1
          fi
          echo "All required secrets are configured"

      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Full history for better analysis

      - name: Set up Rust environment
        if: ${{ inputs.language == 'rust' }}
        uses: ./.github/actions/setup-rust
        with:
          service_name: ${{ inputs.service_name }}

      - name: Set up Python environment
        if: ${{ inputs.language == 'python' }}
        uses: ./.github/actions/setup-python
        with:
          service_name: ${{ inputs.service_name }}

      - name: Set up Go environment
        if: ${{ inputs.language == 'go' }}
        uses: ./.github/actions/setup-go
        with:
          service_name: ${{ inputs.service_name }}

      - name: Set up TypeScript environment
        if: ${{ inputs.language == 'typescript' }}
        uses: ./.github/actions/setup-typescript
        with:
          service_name: ${{ inputs.service_name }}

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo
            ~/.cache/pip
            ~/go/pkg/mod
            ~/.npm
            node_modules
            target
          key: ${{ runner.os }}-${{ inputs.language }}-${{ hashFiles('**/Cargo.lock', '**/requirements.txt', '**/go.mod', '**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-${{ inputs.language }}-

      - name: Install dependencies
        run: make deps
        env:
          SERVICE_NAME: ${{ inputs.service_name }}
          LANGUAGE: ${{ inputs.language }}

      - name: Lint code
        run: make lint
        env:
          SERVICE_NAME: ${{ inputs.service_name }}
          LANGUAGE: ${{ inputs.language }}

      - name: Security scan
        run: make security-scan
        env:
          SERVICE_NAME: ${{ inputs.service_name }}
          LANGUAGE: ${{ inputs.language }}

      - name: Run tests
        run: make test-ci
        env:
          CI: true
          SERVICE_NAME: ${{ inputs.service_name }}
          LANGUAGE: ${{ inputs.language }}

      - name: Generate coverage report
        run: make coverage
        env:
          SERVICE_NAME: ${{ inputs.service_name }}
          LANGUAGE: ${{ inputs.language }}

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage.xml
          flags: ${{ inputs.service_name }}
          name: ${{ inputs.service_name }}-coverage
          fail_ci_if_error: true

      - name: SonarQube analysis
        uses: sonarsource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        with:
          projectBaseDir: ${{ inputs.working_directory }}
          args: >
            -Dsonar.projectKey=ccl-${{ inputs.service_name }}
            -Dsonar.organization=ccl-platform
            -Dsonar.sources=.
            -Dsonar.exclusions=**/*_test.go,**/*.test.ts,**/test_*.py,**/tests.rs,**/target/**,**/node_modules/**

      - name: Build artifacts
        run: make build
        env:
          SERVICE_NAME: ${{ inputs.service_name }}
          LANGUAGE: ${{ inputs.language }}
          VERSION: ${{ github.sha }}

      - name: Build Docker image
        run: |
          docker build -t us-central1-docker.pkg.dev/ccl-platform/services/${{ inputs.service_name }}:${{ github.sha }} .
          docker build -t us-central1-docker.pkg.dev/ccl-platform/services/${{ inputs.service_name }}:latest .

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker for Artifact Registry
        run: gcloud auth configure-docker us-central1-docker.pkg.dev

      - name: Push Docker image
        run: |
          docker push us-central1-docker.pkg.dev/ccl-platform/services/${{ inputs.service_name }}:${{ github.sha }}
          docker push us-central1-docker.pkg.dev/ccl-platform/services/${{ inputs.service_name }}:latest

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ inputs.service_name }}-artifacts-${{ github.sha }}
          path: |
            dist/
            target/release/
            build/
          retention-days: 30

      - name: Generate SBOM
        uses: anchore/sbom-action@v0
        with:
          image: us-central1-docker.pkg.dev/ccl-platform/services/${{ inputs.service_name }}:${{ github.sha }}
          format: spdx-json
          output-file: ${{ inputs.service_name }}-sbom.spdx.json

      - name: Upload SBOM
        uses: actions/upload-artifact@v4
        with:
          name: ${{ inputs.service_name }}-sbom-${{ github.sha }}
          path: ${{ inputs.service_name }}-sbom.spdx.json

  vulnerability-scan:
    runs-on: ubuntu-latest
    needs: quality-checks
    steps:
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: us-central1-docker.pkg.dev/ccl-platform/services/${{ inputs.service_name }}:${{ github.sha }}
          format: sarif
          output: trivy-results.sarif

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: trivy-results.sarif

      - name: Fail on critical vulnerabilities
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: us-central1-docker.pkg.dev/ccl-platform/services/${{ inputs.service_name }}:${{ github.sha }}
          exit-code: 1
          severity: CRITICAL,HIGH

  performance-testing:
    runs-on: ubuntu-latest
    needs: quality-checks
    if: github.event_name == 'pull_request'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Compose for testing
        run: |
          docker-compose -f docker-compose.test.yml up -d
          sleep 30  # Wait for services to be ready

      - name: Install k6
        run: |
          sudo gpg -k
          sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6

      - name: Run performance tests
        run: |
          SERVICE_NAME="${{ inputs.service_name }}"
          
          # Run service-specific performance tests
          if [ -f "tests/performance/${SERVICE_NAME}-load-test.js" ]; then
            echo "Running load tests for $SERVICE_NAME"
            k6 run --out json=performance-results.json tests/performance/${SERVICE_NAME}-load-test.js
          else
            echo "No specific load tests found for $SERVICE_NAME, running generic API tests"
            k6 run --out json=performance-results.json tests/performance/generic-api-test.js
          fi
        env:
          BASE_URL: http://localhost:8000
          SERVICE_NAME: ${{ inputs.service_name }}

      - name: Analyze performance results
        run: |
          # Check if performance results meet baseline requirements
          python3 scripts/analyze-performance.py performance-results.json ${{ inputs.service_name }}

      - name: Comment performance results on PR
        uses: actions/github-script@v7
        if: github.event_name == 'pull_request'
        with:
          script: |
            const fs = require('fs');
            const path = require('path');
            
            let performanceReport = 'Performance test results not available';
            
            try {
              const results = JSON.parse(fs.readFileSync('performance-results.json', 'utf8'));
              const stats = results.metrics;
              
              performanceReport = `## 🚀 Performance Test Results for \`${{ inputs.service_name }}\`
              
              | Metric | Value | Status |
              |--------|-------|--------|
              | Avg Response Time | ${stats.http_req_duration?.avg || 'N/A'}ms | ${stats.http_req_duration?.avg < 200 ? '✅' : '⚠️'} |
              | 95th Percentile | ${stats.http_req_duration?.p95 || 'N/A'}ms | ${stats.http_req_duration?.p95 < 500 ? '✅' : '⚠️'} |
              | Request Rate | ${stats.http_reqs?.rate || 'N/A'} req/s | ✅ |
              | Error Rate | ${((stats.http_req_failed?.rate || 0) * 100).toFixed(2)}% | ${stats.http_req_failed?.rate < 0.01 ? '✅' : '❌'} |
              
              **VUs (Virtual Users):** ${stats.vus?.value || 'N/A'}  
              **Test Duration:** ${stats.iteration_duration?.avg || 'N/A'}ms  
              
              ${stats.http_req_failed?.rate > 0.01 ? '⚠️ **Warning:** Error rate exceeds 1%' : ''}  
              ${stats.http_req_duration?.p95 > 500 ? '⚠️ **Warning:** 95th percentile response time exceeds 500ms' : ''}  
              `;
            } catch (e) {
              console.log('Error reading performance results:', e);
            }
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: performanceReport
            });

      - name: Upload performance results
        uses: actions/upload-artifact@v4
        with:
          name: ${{ inputs.service_name }}-performance-results-${{ github.sha }}
          path: performance-results.json
          retention-days: 30

      - name: Cleanup test environment
        if: always()
        run: |
          docker-compose -f docker-compose.test.yml down -v
