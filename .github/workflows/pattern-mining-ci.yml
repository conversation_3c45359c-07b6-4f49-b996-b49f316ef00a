name: Pattern Mining CI

"on":
  push:
    branches: [main, develop]
    paths:
      - 'pattern-mining/**'
      - '.github/workflows/pattern-mining-ci.yml'
      - '.github/workflows/ci-common.yml'
      - '.github/actions/setup-python/**'
  pull_request:
    branches: [main, develop]
    paths:
      - 'pattern-mining/**'
      - '.github/workflows/pattern-mining-ci.yml'
      - '.github/workflows/ci-common.yml'
      - '.github/actions/setup-python/**'

permissions:
  contents: read
  security-events: write
  actions: read
  checks: write
  pull-requests: write
  deployments: write

jobs:
  ci:
    uses: ./.github/workflows/ci-common.yml
    with:
      service_name: pattern-mining
      language: python
      working_directory: pattern-mining
    secrets:
      GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  deploy-dev:
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    needs: ci
    uses: ./.github/workflows/cd-deploy.yml
    with:
      service_name: pattern-mining
      environment: development
      version: ${{ github.sha }}
      rollout_strategy: rolling
    secrets:
      GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}

  deploy-staging:
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: ci
    uses: ./.github/workflows/cd-deploy.yml
    with:
      service_name: pattern-mining
      environment: staging
      version: ${{ github.sha }}
      rollout_strategy: canary
      initial_traffic_percentage: 20
    secrets:
      GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
