# Phase 4: Marketplace API Foundation Implementation

## Context
You are implementing the Marketplace service, which provides the commerce platform for sharing and monetizing code patterns. This service is written in **Go** and handles pattern distribution, licensing, payments, and usage tracking.

## Pre-Implementation Checklist

### Essential Files to Read First (In Order)
1. **Architecture & Planning**
   - `PLANNING.md` - Marketplace service overview
   - `PHASED-DEVELOPMENT-APPROACH.md` - Phase 4 Marketplace sprint
   - `PRPs/architecture-patterns.md` - API and commerce patterns

2. **Service-Specific PRPs**
   - `PRPs/services/marketplace.md` - Complete service specification
   - `PRPs/api/rest-api.md` - REST API standards
   - `PRPs/database/spanner-schema.md` - Transactional data storage
   - `PRPs/implementation-guide.md` - Go coding standards

3. **Integration Contracts** (CRITICAL)
   - `contracts/README.md` - Service integration overview
   - `contracts/schemas/pattern-output-v1.json` - Input from Pattern Detection
   - `contracts/schemas/marketplace-pattern-v1.json` - Marketplace pattern format
   - `contracts/schemas/error-response-v1.json` - Error handling
   - `contracts/implementation/marketplace-guide.md` - Implementation guide

4. **Existing Infrastructure**
   - `services/marketplace/` - Current scaffolding
   - `services/marketplace/go.mod` - Dependencies
   - `services/marketplace/Dockerfile.dev` - Development container
   - `.github/workflows/marketplace-ci.yml` - CI pipeline
   - `examples/marketplace/api_handler.go` - Example code patterns

5. **Commerce & Security**
   - `PRPs/security/authentication.md` - Auth patterns
   - `PRPs/security/authorization.md` - Permission models
   - Payment integration docs (Stripe)

## Technical Requirements

### Core Features to Implement

1. **Pattern Management**
   ```go
   // Pattern lifecycle states
   type PatternStatus string
   const (
       PatternDraft      PatternStatus = "draft"
       PatternPending    PatternStatus = "pending_review"
       PatternApproved   PatternStatus = "approved"
       PatternPublished  PatternStatus = "published"
       PatternDeprecated PatternStatus = "deprecated"
   )
   
   // Pattern categories from Pattern Detection
   type PatternCategory string
   const (
       DesignPattern    PatternCategory = "design_pattern"
       SecurityPattern  PatternCategory = "security_pattern"
       PerformanceOpt   PatternCategory = "performance_optimization"
       CodeQuality      PatternCategory = "code_quality"
       Architecture     PatternCategory = "architecture"
   )
   ```

2. **Commerce Features**
   - Pattern listing and search
   - Pricing tiers (free, pro, enterprise)
   - Subscription management
   - Usage-based billing
   - Revenue sharing for contributors

3. **REST API Endpoints**
   ```
   # Pattern Management
   GET    /api/v1/patterns - List patterns with filtering
   GET    /api/v1/patterns/{id} - Get pattern details
   POST   /api/v1/patterns - Create new pattern
   PUT    /api/v1/patterns/{id} - Update pattern
   DELETE /api/v1/patterns/{id} - Delete pattern
   
   # Pattern Discovery
   GET    /api/v1/search - Search patterns
   GET    /api/v1/categories - List categories
   GET    /api/v1/trending - Trending patterns
   GET    /api/v1/recommended - Personalized recommendations
   
   # Commerce
   POST   /api/v1/subscriptions - Create subscription
   GET    /api/v1/subscriptions/{id} - Get subscription
   PUT    /api/v1/subscriptions/{id} - Update subscription
   DELETE /api/v1/subscriptions/{id} - Cancel subscription
   
   # Usage & Analytics
   POST   /api/v1/usage - Track pattern usage
   GET    /api/v1/analytics/patterns/{id} - Pattern analytics
   GET    /api/v1/analytics/revenue - Revenue analytics
   
   # User Management
   GET    /api/v1/users/{id}/patterns - User's patterns
   GET    /api/v1/users/{id}/purchases - Purchase history
   POST   /api/v1/users/{id}/payouts - Request payout
   ```

4. **Data Models (Spanner)**
   ```sql
   -- Core tables
   CREATE TABLE patterns (
       id STRING(36) NOT NULL,
       name STRING(255) NOT NULL,
       description STRING(MAX),
       category STRING(50) NOT NULL,
       author_id STRING(36) NOT NULL,
       price_cents INT64,
       status STRING(20) NOT NULL,
       quality_score FLOAT64,
       created_at TIMESTAMP NOT NULL,
       updated_at TIMESTAMP NOT NULL,
       metadata JSON,
   ) PRIMARY KEY (id);
   
   CREATE TABLE subscriptions (
       id STRING(36) NOT NULL,
       user_id STRING(36) NOT NULL,
       plan_id STRING(36) NOT NULL,
       stripe_subscription_id STRING(255),
       status STRING(20) NOT NULL,
       current_period_start TIMESTAMP,
       current_period_end TIMESTAMP,
       created_at TIMESTAMP NOT NULL,
   ) PRIMARY KEY (id);
   ```

5. **Pattern Storage**
   - Pattern artifacts in Cloud Storage
   - Metadata in Spanner
   - Search index in Elasticsearch/Algolia
   - CDN distribution for artifacts

### Integration Requirements

1. **Input Processing**
   - Subscribe to Pub/Sub topic: `patterns-detected`
   - Validate and enrich patterns
   - Quality scoring algorithm
   - Auto-categorization

2. **Payment Integration (Stripe)**
   ```go
   // Stripe webhook handling
   func (h *WebhookHandler) HandleStripeWebhook(w http.ResponseWriter, r *http.Request) {
       // 1. Verify webhook signature
       // 2. Process event types:
       //    - subscription.created
       //    - subscription.updated
       //    - subscription.deleted
       //    - payment_intent.succeeded
       //    - payout.completed
   }
   ```

3. **Performance Requirements**
   - API response time <50ms (p95)
   - Support 10k concurrent users
   - 99.9% uptime SLA
   - Handle 1M API calls/day

### Phase 3 Issues to Address
From `docs/phase4-prep-issues.md`:
- Implement performance testing in CI pipeline
- Add multi-region deployment support
- Create business metrics dashboards

## Implementation Steps

### Step 1: Service Foundation
```go
// In services/marketplace/cmd/server/main.go
package main

import (
    "github.com/gin-gonic/gin"
    "github.com/ccl/marketplace/internal/api"
    "github.com/ccl/marketplace/internal/config"
    "go.opentelemetry.io/otel"
)

func main() {
    // 1. Load configuration
    cfg := config.Load()
    
    // 2. Initialize OpenTelemetry
    initTracing(cfg)
    
    // 3. Set up database connections
    db := initSpanner(cfg)
    
    // 4. Initialize services
    svc := initServices(db, cfg)
    
    // 5. Set up HTTP server
    router := gin.New()
    api.RegisterRoutes(router, svc)
    
    // 6. Start server
    router.Run(cfg.Port)
}
```

### Step 2: Pattern Service Implementation
```go
// Create services/marketplace/internal/patterns/service.go
type PatternService struct {
    db         *spanner.Client
    storage    *storage.Client
    pubsub     *pubsub.Client
    cache      *redis.Client
    validator  *PatternValidator
}

func (s *PatternService) CreatePattern(ctx context.Context, input CreatePatternInput) (*Pattern, error) {
    // 1. Validate input
    if err := s.validator.Validate(input); err != nil {
        return nil, fmt.Errorf("validation failed: %w", err)
    }
    
    // 2. Check permissions
    if !s.canCreatePattern(ctx, input.AuthorID) {
        return nil, ErrUnauthorized
    }
    
    // 3. Store artifact in GCS
    artifactURL, err := s.storeArtifact(ctx, input.Artifact)
    if err != nil {
        return nil, fmt.Errorf("failed to store artifact: %w", err)
    }
    
    // 4. Create database record
    pattern := &Pattern{
        ID:          uuid.New().String(),
        Name:        input.Name,
        Description: input.Description,
        Category:    input.Category,
        AuthorID:    input.AuthorID,
        ArtifactURL: artifactURL,
        Status:      PatternDraft,
        CreatedAt:   time.Now(),
    }
    
    // 5. Save to Spanner
    if err := s.savePattern(ctx, pattern); err != nil {
        return nil, fmt.Errorf("failed to save pattern: %w", err)
    }
    
    // 6. Invalidate cache
    s.cache.Del(ctx, "patterns:list")
    
    // 7. Publish event
    s.publishPatternCreated(ctx, pattern)
    
    return pattern, nil
}
```

### Step 3: Search Implementation
```go
// Create services/marketplace/internal/search/service.go
type SearchService struct {
    elastic    *elasticsearch.Client
    db         *spanner.Client
    cache      *redis.Client
}

func (s *SearchService) SearchPatterns(ctx context.Context, query SearchQuery) (*SearchResults, error) {
    // 1. Check cache
    cacheKey := s.buildCacheKey(query)
    if cached, err := s.getFromCache(ctx, cacheKey); err == nil {
        return cached, nil
    }
    
    // 2. Build Elasticsearch query
    esQuery := s.buildElasticQuery(query)
    
    // 3. Execute search
    res, err := s.elastic.Search(
        s.elastic.Search.WithContext(ctx),
        s.elastic.Search.WithIndex("patterns"),
        s.elastic.Search.WithBody(esQuery),
        s.elastic.Search.WithSize(query.Limit),
        s.elastic.Search.WithFrom(query.Offset),
    )
    
    // 4. Parse results
    results := s.parseResults(res)
    
    // 5. Enrich with additional data
    s.enrichResults(ctx, results)
    
    // 6. Cache results
    s.cacheResults(ctx, cacheKey, results)
    
    return results, nil
}
```

### Step 4: Commerce Integration
```go
// Create services/marketplace/internal/commerce/stripe.go
type StripeService struct {
    client     *stripe.Client
    db         *spanner.Client
    webhookSec string
}

func (s *StripeService) CreateSubscription(ctx context.Context, input CreateSubscriptionInput) (*Subscription, error) {
    // 1. Create or get Stripe customer
    customer, err := s.ensureCustomer(ctx, input.UserID)
    if err != nil {
        return nil, err
    }
    
    // 2. Create subscription in Stripe
    params := &stripe.SubscriptionParams{
        Customer: stripe.String(customer.ID),
        Items: []*stripe.SubscriptionItemsParams{
            {
                Price: stripe.String(input.PriceID),
            },
        },
        PaymentBehavior: stripe.String("default_incomplete"),
        Expand:          []*string{stripe.String("latest_invoice.payment_intent")},
    }
    
    stripeSub, err := subscription.New(params)
    if err != nil {
        return nil, fmt.Errorf("stripe error: %w", err)
    }
    
    // 3. Save to database
    sub := &Subscription{
        ID:                   uuid.New().String(),
        UserID:               input.UserID,
        PlanID:               input.PlanID,
        StripeSubscriptionID: stripeSub.ID,
        Status:               string(stripeSub.Status),
        CurrentPeriodStart:   time.Unix(stripeSub.CurrentPeriodStart, 0),
        CurrentPeriodEnd:     time.Unix(stripeSub.CurrentPeriodEnd, 0),
        CreatedAt:            time.Now(),
    }
    
    if err := s.saveSubscription(ctx, sub); err != nil {
        // Rollback Stripe subscription
        subscription.Cancel(stripeSub.ID, nil)
        return nil, err
    }
    
    return sub, nil
}
```

### Step 5: Analytics Service
```go
// Create services/marketplace/internal/analytics/service.go
type AnalyticsService struct {
    bigquery *bigquery.Client
    db       *spanner.Client
}

func (s *AnalyticsService) GetPatternAnalytics(ctx context.Context, patternID string) (*PatternAnalytics, error) {
    query := `
        SELECT 
            DATE(timestamp) as date,
            COUNT(*) as views,
            COUNT(DISTINCT user_id) as unique_users,
            SUM(CASE WHEN event_type = 'download' THEN 1 ELSE 0 END) as downloads,
            AVG(quality_rating) as avg_rating
        FROM pattern_events
        WHERE pattern_id = @pattern_id
            AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
        GROUP BY date
        ORDER BY date DESC
    `
    
    // Execute BigQuery query
    // Process results
    // Return analytics
}
```

### Step 6: API Implementation
```go
// Create services/marketplace/internal/api/patterns.go
func RegisterPatternRoutes(r *gin.RouterGroup, svc *services.Services) {
    patterns := r.Group("/patterns")
    
    // Public endpoints
    patterns.GET("", handleListPatterns(svc))
    patterns.GET("/:id", handleGetPattern(svc))
    patterns.GET("/search", handleSearchPatterns(svc))
    
    // Authenticated endpoints
    auth := patterns.Group("", requireAuth())
    auth.POST("", handleCreatePattern(svc))
    auth.PUT("/:id", handleUpdatePattern(svc))
    auth.DELETE("/:id", handleDeletePattern(svc))
    
    // Admin endpoints
    admin := patterns.Group("", requireAdmin())
    admin.POST("/:id/approve", handleApprovePattern(svc))
    admin.POST("/:id/reject", handleRejectPattern(svc))
}

func handleCreatePattern(svc *services.Services) gin.HandlerFunc {
    return func(c *gin.Context) {
        var input CreatePatternInput
        if err := c.ShouldBindJSON(&input); err != nil {
            c.JSON(400, gin.H{"error": "invalid input"})
            return
        }
        
        // Add user context
        input.AuthorID = c.GetString("user_id")
        
        pattern, err := svc.Patterns.CreatePattern(c.Request.Context(), input)
        if err != nil {
            handleError(c, err)
            return
        }
        
        c.JSON(201, pattern)
    }
}
```

## Validation & Testing

### Required Tests
1. **Unit Tests** (90% coverage)
   ```go
   // internal/patterns/service_test.go
   func TestCreatePattern(t *testing.T) {
       // Test pattern creation
       // Test validation
       // Test permissions
   }
   
   // internal/commerce/stripe_test.go
   func TestCreateSubscription(t *testing.T) {
       // Mock Stripe API
       // Test subscription creation
       // Test error handling
   }
   ```

2. **Integration Tests**
   ```go
   // tests/integration/api_test.go
   func TestPatternLifecycle(t *testing.T) {
       // 1. Create pattern
       // 2. Update pattern
       // 3. Publish pattern
       // 4. Search for pattern
       // 5. Purchase pattern
   }
   ```

3. **Load Tests**
   ```go
   // tests/load/marketplace_load_test.go
   func TestHighTraffic(t *testing.T) {
       // Simulate 10k concurrent users
       // Measure response times
       // Check error rates
   }
   ```

### Validation Commands
```bash
# Run tests
cd services/marketplace
go test ./... -v -cover

# Linting
golangci-lint run

# Security scan
gosec ./...

# Build
go build -o bin/marketplace cmd/server/main.go

# Run locally
go run cmd/server/main.go

# Test API
curl http://localhost:8080/api/v1/patterns

# Load test
go test -run=^$ -bench=. ./tests/load/
```

## Performance Optimization

### Caching Strategy
```go
// Cache layers:
// 1. CDN for pattern artifacts
// 2. Redis for API responses
// 3. In-memory cache for hot data

func (s *PatternService) GetPattern(ctx context.Context, id string) (*Pattern, error) {
    // L1: In-memory cache
    if p, ok := s.memCache.Get(id); ok {
        return p.(*Pattern), nil
    }
    
    // L2: Redis cache
    if p, err := s.getFromRedis(ctx, id); err == nil {
        s.memCache.Set(id, p, 5*time.Minute)
        return p, nil
    }
    
    // L3: Database
    p, err := s.getFromDB(ctx, id)
    if err != nil {
        return nil, err
    }
    
    // Update caches
    s.cachePattern(ctx, p)
    
    return p, nil
}
```

### Database Optimization
```sql
-- Spanner optimizations
-- 1. Use interleaved tables for related data
CREATE TABLE pattern_versions (
    pattern_id STRING(36) NOT NULL,
    version STRING(20) NOT NULL,
    artifact_url STRING(MAX),
    changelog STRING(MAX),
    created_at TIMESTAMP NOT NULL,
) PRIMARY KEY (pattern_id, version),
  INTERLEAVE IN PARENT patterns ON DELETE CASCADE;

-- 2. Create composite indexes for common queries
CREATE INDEX idx_patterns_category_status_created 
ON patterns(category, status, created_at DESC);

-- 3. Use STORING clause for covering indexes
CREATE INDEX idx_patterns_search 
ON patterns(name, category) 
STORING (description, author_id, price_cents);
```

## Development Workflow

### Local Development
```bash
# Set up Go environment
cd services/marketplace
go mod download

# Set up local dependencies
docker-compose up -d postgres redis

# Environment variables
cp .env.example .env
# Configure Stripe test keys

# Run with hot reload
air

# Or use Make
make dev-marketplace
```

### Stripe Integration Testing
```bash
# Install Stripe CLI
brew install stripe/stripe-cli/stripe

# Login to Stripe
stripe login

# Forward webhooks to local
stripe listen --forward-to localhost:8080/webhooks/stripe

# Trigger test events
stripe trigger payment_intent.succeeded
```

## Important Notes

### Service Boundaries
- Only handle commerce, not pattern detection
- Don't analyze code quality
- Rely on Pattern Detection for quality scores
- No direct ML operations

### Security Considerations
- Validate all payment data
- Implement rate limiting per user
- Audit all financial transactions
- PCI compliance for payment data
- GDPR compliance for user data

### Error Handling
```go
// Custom errors
var (
    ErrPatternNotFound = errors.New("pattern not found")
    ErrUnauthorized    = errors.New("unauthorized")
    ErrPaymentFailed   = errors.New("payment failed")
)

// Error response format
type ErrorResponse struct {
    Error     string            `json:"error"`
    Message   string            `json:"message"`
    Details   map[string]string `json:"details,omitempty"`
    RequestID string            `json:"request_id"`
}

// Middleware for error handling
func ErrorHandler() gin.HandlerFunc {
    return func(c *gin.Context) {
        c.Next()
        
        if len(c.Errors) > 0 {
            err := c.Errors[0]
            response := BuildErrorResponse(err)
            c.JSON(response.Status, response)
        }
    }
}
```

## Success Criteria
- [ ] All CRUD operations for patterns
- [ ] Stripe integration working
- [ ] Search functionality with <50ms response
- [ ] 99.9% uptime achieved
- [ ] Load testing passes (10k users)
- [ ] Revenue tracking accurate
- [ ] Multi-region deployment ready
- [ ] Performance dashboards created

## Deliverables
1. Complete Marketplace API
2. Stripe payment integration
3. Search functionality with filters
4. Admin dashboard endpoints
5. Analytics and reporting
6. Comprehensive test suite
7. API documentation (OpenAPI)
8. Performance test results
9. Multi-region deployment configs
10. Business metrics dashboards

Remember: This is a commerce platform handling real money. Prioritize reliability, security, and accurate financial tracking. Every transaction must be auditable.