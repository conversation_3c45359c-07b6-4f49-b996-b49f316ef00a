# Phase 4: Repository Analysis API Implementation

## Context
You are implementing the Repository Analysis API, the foundational service of the CCL platform. This service is written in **Rust** and provides AST parsing, code analysis, and metrics calculation for multiple programming languages.

## Pre-Implementation Checklist

### Essential Files to Read First (In Order)
1. **Architecture & Planning**
   - `PLANNING.md` - Overall architecture (Read sections on Repository Analysis)
   - `PHASED-DEVELOPMENT-APPROACH.md` - Phase 4 specifications
   - `PRPs/architecture-patterns.md` - System-wide patterns to follow

2. **Service-Specific PRPs**
   - `PRPs/services/analysis-engine.md` - Complete service specification
   - `PRPs/features/repository-analysis-api.md` - Feature-specific requirements (if exists)
   - `PRPs/implementation-guide.md` - Coding standards for Rust

3. **Integration Contracts** (CRITICAL)
   - `contracts/README.md` - Overview of service integration
   - `contracts/schemas/ast-output-v1.json` - Output format you MUST produce
   - `contracts/examples/ast-output-example.json` - Example of expected output
   - `contracts/implementation/repository-analysis-guide.md` - Implementation specifics

4. **Existing Infrastructure**
   - `services/analysis-engine/` - Current scaffolding
   - `services/analysis-engine/Cargo.toml` - Dependencies already configured
   - `services/analysis-engine/Dockerfile.dev` - Development container
   - `.github/workflows/analysis-engine-ci.yml` - CI pipeline

5. **Examples & Patterns**
   - `examples/analysis-engine/ast_parser.rs` - AST parsing example code
   - `examples/analysis-engine/metrics_calculator.rs` - Metrics calculation patterns

## Technical Requirements

### Core Features to Implement
1. **Git Repository Management**
   - Clone repositories (HTTPS and SSH)
   - Handle large repositories (>1M LOC) with streaming
   - Branch/tag/commit checkout
   - Cleanup and caching strategies

2. **Multi-Language AST Parsing**
   - Use Tree-sitter for parsing
   - Initial support for: Rust, Python, JavaScript, TypeScript, Go
   - Extensible architecture for adding languages
   - Handle parsing errors gracefully

3. **Code Analysis**
   - File-level metrics (LOC, complexity, maintainability)
   - Repository-level aggregations
   - Language detection and statistics
   - Symbol extraction (functions, classes, variables)

4. **REST API Endpoints**
   ```
   POST /api/v1/analyze - Start repository analysis
   GET  /api/v1/analyze/{id} - Get analysis status
   GET  /api/v1/analyze/{id}/results - Get analysis results
   POST /api/v1/analyze/webhook - GitHub webhook endpoint
   GET  /api/v1/languages - List supported languages
   GET  /api/v1/health - Health check
   ```

5. **Performance Requirements**
   - Analyze 1M LOC in <5 minutes
   - Support concurrent analyses (min 10)
   - Memory usage <4GB per analysis
   - Implement progress tracking

### Integration Requirements
1. **Output Format**: MUST match `contracts/schemas/ast-output-v1.json`
2. **Error Handling**: Use error schema from `contracts/schemas/error-response-v1.json`
3. **Service Communication**: Publish to Pub/Sub topic `repository-analysis-complete`
4. **Storage**: 
   - Analysis results to Cloud Storage
   - Metadata to Spanner
   - Temporary files to local disk with cleanup

### Phase 3 Issues to Address
From `docs/phase4-prep-issues.md`:
- Extend AST parsing beyond Python (implement Rust, JavaScript, TypeScript, Go)
- Add seed data script that includes sample repositories for testing

## Implementation Steps

### Step 1: Core Service Setup
```rust
// In services/analysis-engine/src/main.rs
// 1. Set up Actix-web server with OpenTelemetry
// 2. Configure structured logging with tracing
// 3. Set up health check endpoint
// 4. Configure CORS and security headers
```

### Step 2: Git Operations Module
```rust
// Create services/analysis-engine/src/git/mod.rs
// 1. Implement repository cloning with git2
// 2. Add progress callbacks for large repos
// 3. Implement cleanup on drop
// 4. Add caching layer for repeated analyses
```

### Step 3: AST Parser Module
```rust
// Create services/analysis-engine/src/parser/mod.rs
// 1. Initialize Tree-sitter with language grammars
// 2. Implement language detection
// 3. Create parser trait for extensibility
// 4. Implement parsers for each language
// 5. Generate AST output matching contract schema
```

### Step 4: Metrics Calculator
```rust
// Create services/analysis-engine/src/metrics/mod.rs
// 1. Implement cyclomatic complexity calculation
// 2. Calculate maintainability index
// 3. Count lines of code (excluding comments/blanks)
// 4. Aggregate metrics at file and repo level
```

### Step 5: API Implementation
```rust
// Create services/analysis-engine/src/api/mod.rs
// 1. Implement all REST endpoints
// 2. Add request validation
// 3. Implement async job processing
// 4. Add progress tracking via SSE or polling
```

### Step 6: Storage Integration
```rust
// Create services/analysis-engine/src/storage/mod.rs
// 1. Implement Cloud Storage client for results
// 2. Add Spanner client for metadata
// 3. Implement Pub/Sub publisher
// 4. Add retry logic and error handling
```

## Validation & Testing

### Required Tests
1. **Unit Tests** (90% coverage minimum)
   - Parser tests for each language
   - Metrics calculation tests
   - Git operation tests

2. **Integration Tests**
   - Full analysis flow
   - API endpoint tests
   - Storage integration tests

3. **Performance Tests**
   - Large repository handling
   - Concurrent analysis
   - Memory usage monitoring

### Validation Commands
```bash
# Run from project root
cd services/analysis-engine

# Run tests
cargo test

# Check formatting
cargo fmt -- --check

# Run linter
cargo clippy -- -D warnings

# Run security audit
cargo audit

# Check test coverage
cargo tarpaulin --out Html

# Run service locally
cargo run

# Test with example repository
curl -X POST http://localhost:8080/api/v1/analyze \
  -H "Content-Type: application/json" \
  -d '{"repository_url": "https://github.com/rust-lang/rust-clippy", "branch": "master"}'
```

## Development Workflow

### Local Development
```bash
# Start all dependencies
make dev-up

# Watch for changes (from services/analysis-engine)
cargo watch -x run

# Run specific test
cargo test test_parse_rust_file

# Check contract compliance
make validate-contracts SERVICE=analysis-engine
```

### Debugging Tips
1. Use `RUST_LOG=debug` for verbose logging
2. Tree-sitter parse errors often indicate grammar issues
3. Memory issues: profile with `valgrind` or `heaptrack`
4. For Git issues, check SSH keys and repository access

## Important Notes

### Service Boundaries
- This service ONLY does analysis - no pattern detection or query processing
- Output MUST be pure data (no opinions or interpretations)
- No direct database writes except Spanner metadata

### Security Considerations
- Validate repository URLs (no local file access)
- Implement rate limiting
- Scan for malicious code patterns
- Clean up cloned repositories immediately
- No execution of analyzed code

### Error Handling
- Use `anyhow` for error propagation
- Return structured errors matching contract
- Log all errors with correlation IDs
- Implement circuit breakers for external services

## Success Criteria
- [ ] All API endpoints implemented and documented
- [ ] Supports 5+ programming languages
- [ ] Analyzes 1M LOC in <5 minutes
- [ ] 90%+ test coverage
- [ ] Zero security vulnerabilities
- [ ] Fully integrated with monitoring stack
- [ ] Contract validation passes
- [ ] Performance benchmarks met

## Additional Resources
- Tree-sitter documentation: https://tree-sitter.github.io
- Rust async book: https://rust-lang.github.io/async-book
- Actix-web guide: https://actix.rs/docs
- Git2-rs examples: https://github.com/rust-lang/git2-rs

## Deliverables
1. Fully functional Repository Analysis API
2. Comprehensive test suite
3. Performance benchmark results
4. API documentation (OpenAPI spec)
5. Updated seed data script with test repositories
6. Integration test with other services

Remember: This is the foundation service - take time to get it right. Other services depend on your clean, well-structured output.