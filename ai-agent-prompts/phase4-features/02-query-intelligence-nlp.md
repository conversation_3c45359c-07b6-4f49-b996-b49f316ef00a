# Phase 4: Query Intelligence Natural Language Processing

## Context
You are implementing the Query Intelligence service, which provides natural language understanding for code queries. This service is written in **Python** and uses Gemini 2.5 for AI capabilities and RAG (Retrieval-Augmented Generation) for context-aware responses.

## Pre-Implementation Checklist

### Essential Files to Read First (In Order)
1. **Architecture & Context**
   - `PLANNING.md` - Query Intelligence architecture section
   - `PHASED-DEVELOPMENT-APPROACH.md` - Phase 4 Query Intelligence sprint
   - `PRPs/architecture-patterns.md` - Event-driven patterns

2. **Service-Specific PRPs**
   - `PRPs/services/query-intelligence.md` - Complete service specification
   - `PRPs/ai-ml/gemini-integration.md` - Gemini 2.5 integration patterns
   - `PRPs/ai-ml/embeddings.md` - Embedding strategy
   - `PRPs/implementation-guide.md` - Python coding standards

3. **Integration Contracts** (CRITICAL)
   - `contracts/README.md` - Service integration overview
   - `contracts/schemas/ast-output-v1.json` - Input from Repository Analysis
   - `contracts/schemas/query-context-v1.json` - Expected input format
   - `contracts/schemas/service-events-v1.json` - Event bus messages
   - `contracts/implementation/query-intelligence-guide.md` - Implementation guide

4. **Existing Infrastructure**
   - `services/query-intelligence/` - Current scaffolding
   - `services/query-intelligence/requirements.txt` - Dependencies
   - `services/query-intelligence/Dockerfile.dev` - Development container
   - `.github/workflows/query-intelligence-ci.yml` - CI pipeline
   - `infrastructure/monitoring/instrumentation/python-instrumentation.py` - Monitoring setup

5. **Examples & ML Resources**
   - `examples/query-intelligence/query_processor.py` - Query processing patterns
   - `examples/query-intelligence/rag_pipeline.py` - RAG implementation example
   - `ml/training-data/` - Related ML infrastructure

## Technical Requirements

### Core Features to Implement

1. **Natural Language Query Processing**
   ```python
   # Query types to support:
   - "Find all authentication implementations"
   - "Show me error handling patterns in this codebase"
   - "What design patterns are used in the payment module?"
   - "How does the caching layer work?"
   - "Find security vulnerabilities in API endpoints"
   ```

2. **RAG Pipeline**
   - Vector store using Vertex AI Vector Search
   - Embeddings with text-embedding-004 (768 dimensions)
   - Chunk code intelligently (function/class boundaries)
   - Semantic search with relevance scoring
   - Context window management (2M tokens)

3. **Gemini 2.5 Integration**
   - Use Vertex AI Python SDK
   - Implement retry logic for rate limits (Gemini 2.5 improved limits)
   - Handle streaming responses
   - Manage conversation context
   - Implement confidence scoring

4. **REST API Endpoints**
   ```
   POST /api/v1/query - Submit natural language query
   GET  /api/v1/query/{id} - Get query status/results
   POST /api/v1/feedback - Submit feedback on results
   GET  /api/v1/suggestions - Get query suggestions
   POST /api/v1/index - Trigger re-indexing
   GET  /api/v1/health - Health check
   ```

5. **Query Enhancement**
   - Intent classification
   - Query expansion with synonyms
   - Code-specific term recognition
   - Multi-language query support

### Integration Requirements

1. **Input Processing**
   - Subscribe to Pub/Sub topic: `repository-analysis-complete`
   - Process AST output from Repository Analysis
   - Transform to query context format (contracts/schemas/query-context-v1.json)

2. **Data Storage**
   - Embeddings in Vertex AI Vector Search
   - Query history in Spanner
   - Cache in Redis with TTL
   - Results in Cloud Storage

3. **Performance Requirements**
   - Query response <100ms (p95)
   - Support 1000 concurrent queries
   - Index 100k files in <30 minutes
   - Cache hit rate >80%

### Phase 3 Issues to Address
From `docs/phase4-prep-issues.md`:
- Add development-specific Grafana dashboards for query monitoring
- Implement custom business metrics (query types, satisfaction scores)
- Create cost tracking for Vertex AI usage

## Implementation Steps

### Step 1: Service Foundation (DONE)
```python
# In services/query-intelligence/src/main.py
# 1. [X] Set up FastAPI with async support
# 2. [ ] Configure OpenTelemetry instrumentation
# 3. [ ] Set up structured logging
# 4. [X] Initialize health check endpoints
# 5. [ ] Configure CORS and security
```

### Step 2: Vector Store Setup
```python
# Create services/query-intelligence/src/embeddings/vector_store.py
# 1. Initialize Vertex AI Vector Search
# 2. Create index with proper dimensions (1536)
# 3. Implement batch embedding generation
# 4. Add metadata filtering support
# 5. Configure similarity thresholds
```

### Step 3: Code Chunking Strategy
```python
# Create services/query-intelligence/src/chunking/code_chunker.py
# 1. Parse AST output from Repository Analysis
# 2. Identify logical boundaries (functions, classes)
# 3. Maintain context (imports, class membership)
# 4. Generate chunks with overlap
# 5. Add chunk metadata (language, type, complexity)
```

### Step 4: Query Processor
```python
# Create services/query-intelligence/src/query/processor.py
class QueryProcessor:
    def __init__(self):
        self.gemini_client = vertexai.generative_models.GenerativeModel('gemini-2.5-flash')
        self.vector_store = VectorStore()
        self.cache = Redis()
    
    async def process_query(self, query: str, repository_id: str) -> QueryResult:
        # 1. Check cache
        # 2. Classify intent
        # 3. Enhance query
        # 4. Retrieve relevant chunks
        # 5. Generate response with Gemini
        # 6. Calculate confidence
        # 7. Cache results
```

### Step 5: RAG Implementation
```python
# Create services/query-intelligence/src/rag/pipeline.py
# 1. Implement retrieval strategy
# 2. Rank results by relevance
# 3. Manage context window
# 4. Handle multi-turn conversations
# 5. Implement fallback strategies
```

### Step 6: API Implementation
```python
# Create services/query-intelligence/src/api/routes.py
# 1. Implement all REST endpoints
# 2. Add request validation (Pydantic)
# 3. Implement streaming responses
# 4. Add rate limiting
# 5. Handle async operations
```

### Step 7: Monitoring & Metrics
```python
# Create services/query-intelligence/src/monitoring/metrics.py
# 1. Track query latency by type
# 2. Monitor Gemini API usage/costs
# 3. Track cache hit rates
# 4. Monitor embedding generation time
# 5. Create custom Grafana dashboard
```

## Validation & Testing

### Required Tests
1. **Unit Tests** (90% coverage)
   ```python
   # tests/test_query_processor.py
   - Test intent classification
   - Test query enhancement
   - Test confidence scoring
   - Test error handling
   ```

2. **Integration Tests**
   ```python
   # tests/test_integration.py
   - Test with real AST output
   - Test Vertex AI integration
   - Test vector search accuracy
   - Test caching behavior
   ```

3. **Performance Tests**
   ```python
   # tests/test_performance.py
   - Load test with 1000 concurrent queries
   - Measure indexing speed
   - Test context window limits
   - Monitor memory usage
   ```

### Validation Commands
```bash
# Run from services/query-intelligence
python -m pytest tests/ -v --cov=src --cov-report=html

# Type checking
mypy src/

# Linting
ruff check src/
black --check src/

# Security scan
bandit -r src/

# Run locally
uvicorn src.main:app --reload --port 8001

# Test query
curl -X POST http://localhost:8001/api/v1/query \
  -H "Content-Type: application/json" \
  -d '{"query": "Find authentication patterns", "repository_id": "repo_123"}'
```

## Cost Optimization

### Vertex AI Cost Management
```python
# Implement cost tracking
class CostTracker:
    def track_gemini_usage(self, tokens_used: int, model: str):
        # Log to BigQuery for analysis
        # Alert if exceeding budget
        # Implement quota management
```

### Optimization Strategies
1. Cache frequently asked queries
2. Batch embedding generation
3. Use Gemini Flash for simple queries
4. Implement query result pagination
5. Pre-compute common patterns

## Development Workflow

### Local Development Setup
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # or `venv\Scripts\activate` on Windows

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Set up environment
cp .env.example .env
# Add your Vertex AI credentials

# Run with hot reload
make dev-query-intelligence
```

### Debugging Tips
1. Use `import pdb; pdb.set_trace()` for breakpoints
2. Enable Vertex AI debug logging
3. Monitor rate limit headers
4. Use smaller models for testing
5. Test with synthetic data first

## Important Notes

### Service Boundaries
- Only process queries - don't detect patterns
- Depend on Repository Analysis for AST data
- Don't store source code, only embeddings
- Return references, not full code blocks

### Security Considerations
- Sanitize all queries before processing
- Implement query complexity limits
- Validate repository access permissions
- No execution of retrieved code
- Audit log all queries

### Error Handling
```python
# Use custom exceptions
class QueryTooComplexError(Exception):
    pass

class InsufficientContextError(Exception):
    pass

# Always return structured errors
@app.exception_handler(QueryTooComplexError)
async def handle_complex_query(request, exc):
    return JSONResponse(
        status_code=400,
        content={
            "error": "query_too_complex",
            "message": "Query exceeds complexity limits",
            "suggestion": "Try breaking into smaller queries"
        }
    )
```

## Success Criteria
- [ ] Natural language queries return relevant results
- [ ] Response time <100ms for cached queries
- [ ] 90%+ user satisfaction score
- [ ] Handles 1000+ concurrent users
- [ ] Cost per query <$0.001
- [ ] Zero security vulnerabilities
- [ ] Full observability with custom dashboards
- [ ] Graceful degradation when Gemini unavailable

## Deliverables
1. Fully functional Query Intelligence API
2. Comprehensive test suite with >90% coverage
3. Performance benchmark report
4. Cost analysis and optimization plan
5. Custom Grafana dashboards
6. API documentation with query examples
7. Integration tests with Repository Analysis service

Remember: User experience is critical here. Focus on query understanding, response relevance, and speed. This is the primary interface users will interact with.