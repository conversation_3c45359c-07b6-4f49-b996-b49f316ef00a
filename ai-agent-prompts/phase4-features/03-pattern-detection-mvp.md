# Phase 4: Pattern Detection MVP Implementation

## Context
You are implementing the Pattern Detection service, which uses machine learning to identify code patterns, anti-patterns, and potential issues. This service is written in **Python** and leverages BigQuery ML and custom ML models for pattern recognition.

## Pre-Implementation Checklist

### Essential Files to Read First (In Order)
1. **Architecture & Planning**
   - `PLANNING.md` - Pattern Detection service overview
   - `PHASED-DEVELOPMENT-APPROACH.md` - Phase 4 Pattern Detection sprint
   - `PRPs/architecture-patterns.md` - ML pipeline patterns

2. **Service-Specific PRPs**
   - `PRPs/services/pattern-mining.md` - Complete service specification
   - `PRPs/ai-ml/pattern-recognition.md` - ML approach for patterns
   - `PRPs/database/bigquery-analytics.md` - BigQuery ML usage
   - `PRPs/implementation-guide.md` - Python coding standards

3. **Integration Contracts** (CRITICAL)
   - `contracts/README.md` - Service integration map
   - `contracts/schemas/ast-output-v1.json` - Input from Repository Analysis
   - `contracts/schemas/pattern-input-v1.json` - Expected input format
   - `contracts/schemas/pattern-output-v1.json` - Output format you MUST produce
   - `contracts/implementation/pattern-detection-guide.md` - Implementation specifics

4. **ML Infrastructure**
   - `ml/training-data/` - Training data strategy (IMPORTANT)
   - `ml/training-data/data-pipeline.py` - Data pipeline implementation
   - `ml/training-data/labeling/weak-supervision.py` - Labeling framework
   - `ml/training-data/quality/validation-pipeline.py` - Quality checks
   - `ml/training-data/storage/schema.sql` - BigQuery schema

5. **Existing Infrastructure**
   - `services/pattern-mining/` - Current scaffolding
   - `services/pattern-mining/requirements.txt` - Dependencies
   - `services/pattern-mining/Dockerfile.dev` - Development container
   - `.github/workflows/pattern-mining-ci.yml` - CI pipeline

## Technical Requirements

### Core Features to Implement

1. **Pattern Types to Detect**
   ```python
   PATTERN_CATEGORIES = {
       "design_patterns": [
           "singleton", "factory", "observer", "strategy",
           "decorator", "adapter", "facade", "proxy"
       ],
       "anti_patterns": [
           "god_class", "spaghetti_code", "copy_paste",
           "dead_code", "long_method", "large_class"
       ],
       "security_patterns": [
           "sql_injection", "xss_vulnerable", "hardcoded_secrets",
           "weak_crypto", "path_traversal", "unsafe_deserialization"
       ],
       "performance_patterns": [
           "n_plus_one", "memory_leak", "inefficient_loop",
           "blocking_io", "unnecessary_allocation"
       ],
       "code_smells": [
           "duplicate_code", "long_parameter_list", "feature_envy",
           "inappropriate_intimacy", "refused_bequest"
       ]
   }
   ```

2. **ML Pipeline Architecture**
   - Feature extraction from AST
   - Multiple detection strategies:
     - Rule-based detection (deterministic)
     - ML classification (probabilistic)
     - Clustering for unknown patterns
   - Ensemble voting for confidence

3. **BigQuery ML Integration**
   - Train models on labeled data
   - Batch prediction for efficiency
   - A/B testing different models
   - Cost optimization strategies

4. **REST API Endpoints**
   ```
   POST /api/v1/detect - Detect patterns in repository
   GET  /api/v1/detect/{id} - Get detection status
   GET  /api/v1/patterns - List detected patterns
   GET  /api/v1/patterns/{id} - Get pattern details
   POST /api/v1/patterns/feedback - User feedback
   POST /api/v1/train - Trigger model retraining
   GET  /api/v1/models - List active models
   GET  /api/v1/health - Health check
   ```

5. **Feature Engineering**
   - AST-based features (node types, depth, complexity)
   - Code metrics (LOC, cyclomatic complexity)
   - Semantic features (variable names, comments)
   - Structural features (inheritance, coupling)

### Integration Requirements

1. **Input Processing**
   - Subscribe to Pub/Sub topic: `repository-analysis-complete`
   - Process full AST output
   - Handle large repositories with streaming

2. **Output Publishing**
   - Publish to topic: `patterns-detected`
   - Store results in BigQuery
   - Cache in Redis for API queries
   - Send to Marketplace when ready

3. **Performance Requirements**
   - Process 100k LOC in <30 seconds
   - Maintain >80% precision
   - Support batch processing
   - Handle 100 concurrent analyses

### Phase 3 Issues to Address
From `docs/phase4-prep-issues.md`:
- Extend AST parsing beyond Python (coordinate with Repository Analysis team)
- Implement pattern evolution tracking system
- Add cost tracking for BigQuery ML operations

## Implementation Steps

### Step 1: Service Foundation
```python
# In services/pattern-mining/src/main.py
from fastapi import FastAPI
from opentelemetry import trace
import structlog

app = FastAPI(title="Pattern Detection Service")
logger = structlog.get_logger()

# 1. Set up FastAPI with async support
# 2. Configure OpenTelemetry
# 3. Initialize BigQuery clients
# 4. Set up Pub/Sub subscribers
# 5. Configure health checks
```

### Step 2: Feature Extraction Pipeline
```python
# Create services/pattern-mining/src/features/extractor.py
class FeatureExtractor:
    def extract_from_ast(self, ast_data: dict) -> np.ndarray:
        features = []
        # 1. AST depth and breadth
        # 2. Node type distribution
        # 3. Cyclomatic complexity
        # 4. Method/class sizes
        # 5. Naming patterns
        # 6. Comment density
        # 7. Import complexity
        return self.vectorize(features)
```

### Step 3: Pattern Detectors
```python
# Create services/pattern-mining/src/detectors/base.py
from abc import ABC, abstractmethod

class PatternDetector(ABC):
    @abstractmethod
    async def detect(self, features: np.ndarray) -> List[Pattern]:
        pass

# Create specific detectors
# services/pattern-mining/src/detectors/design_patterns.py
class DesignPatternDetector(PatternDetector):
    def __init__(self):
        self.rules = self.load_rules()
        self.ml_model = self.load_model()
    
    async def detect(self, features: np.ndarray) -> List[Pattern]:
        # 1. Apply rule-based detection
        # 2. Run ML classification
        # 3. Combine results with confidence
```

### Step 4: BigQuery ML Integration
```python
# Create services/pattern-mining/src/ml/bigquery_ml.py
class BigQueryMLPipeline:
    def __init__(self):
        self.client = bigquery.Client()
        self.dataset = "pattern_detection"
    
    async def train_model(self, pattern_type: str):
        query = f"""
        CREATE OR REPLACE MODEL `{self.dataset}.{pattern_type}_model`
        OPTIONS(
            model_type='BOOSTED_TREE_CLASSIFIER',
            input_label_cols=['is_pattern'],
            auto_class_weights=TRUE
        ) AS
        SELECT * FROM `{self.dataset}.training_data`
        WHERE pattern_type = '{pattern_type}'
        """
        # Execute and monitor training
    
    async def batch_predict(self, features_table: str):
        # Run batch prediction
        # Return results with confidence scores
```

### Step 5: Clustering for Unknown Patterns
```python
# Create services/pattern-mining/src/ml/clustering.py
from sklearn.cluster import DBSCAN
import hdbscan

class PatternClusterer:
    def __init__(self):
        self.clusterer = hdbscan.HDBSCAN(
            min_cluster_size=5,
            min_samples=3
        )
    
    async def find_new_patterns(self, features: np.ndarray):
        # 1. Run clustering
        # 2. Identify stable clusters
        # 3. Extract pattern candidates
        # 4. Generate pattern descriptions
        # 5. Queue for human review
```

### Step 6: API Implementation
```python
# Create services/pattern-mining/src/api/routes.py
@app.post("/api/v1/detect")
async def detect_patterns(request: DetectionRequest):
    # 1. Validate request
    # 2. Check cache
    # 3. Queue for processing
    # 4. Return job ID
    
    job_id = str(uuid.uuid4())
    await queue_detection_job(job_id, request)
    
    return {"job_id": job_id, "status": "processing"}

@app.get("/api/v1/patterns/{pattern_id}")
async def get_pattern(pattern_id: str):
    # 1. Fetch from cache/BigQuery
    # 2. Include confidence and evidence
    # 3. Add similar patterns
    # 4. Include remediation suggestions
```

### Step 7: Continuous Learning
```python
# Create services/pattern-mining/src/ml/feedback_loop.py
class FeedbackProcessor:
    async def process_feedback(self, pattern_id: str, feedback: Feedback):
        # 1. Validate feedback
        # 2. Update training data
        # 3. Trigger retraining if needed
        # 4. Adjust confidence thresholds
        # 5. Track model drift
```

## Validation & Testing

### Required Tests
1. **Unit Tests** (90% coverage)
   ```python
   # tests/test_feature_extraction.py
   def test_ast_feature_extraction():
       # Test with various AST structures
   
   # tests/test_pattern_detection.py
   def test_singleton_detection():
       # Test specific pattern detection
   ```

2. **Integration Tests**
   ```python
   # tests/test_integration.py
   async def test_full_pipeline():
       # 1. Mock AST input
       # 2. Run detection
       # 3. Verify output format
       # 4. Check BigQuery writes
   ```

3. **ML Model Tests**
   ```python
   # tests/test_ml_models.py
   def test_model_performance():
       # Test precision/recall
       # Test with adversarial examples
       # Verify confidence calibration
   ```

### Validation Commands
```bash
# Run tests
cd services/pattern-mining
python -m pytest tests/ -v --cov=src

# Type checking
mypy src/

# Linting
ruff check src/
black --check src/

# Run locally
python -m uvicorn src.main:app --reload --port 8002

# Test pattern detection
curl -X POST http://localhost:8002/api/v1/detect \
  -H "Content-Type: application/json" \
  -d '{"repository_id": "repo_123", "analysis_id": "analysis_456"}'
```

## BigQuery Cost Optimization

### Cost Management Strategies
```python
# 1. Use partitioned tables
CREATE TABLE pattern_results
PARTITION BY DATE(detected_at)
CLUSTER BY repository_id, pattern_type

# 2. Implement query result caching
# 3. Use BigQuery slots for predictable costs
# 4. Archive old results to Cloud Storage
# 5. Monitor query costs in real-time
```

### ML Cost Tracking
```python
class CostMonitor:
    async def track_ml_operation(self, operation: str, bytes_processed: int):
        cost = self.calculate_cost(bytes_processed)
        await self.log_to_bigquery(operation, cost)
        
        if cost > COST_ALERT_THRESHOLD:
            await self.send_alert(operation, cost)
```

## Development Workflow

### Local Development
```bash
# Set up environment
cd services/pattern-mining
python -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Configure BigQuery emulator
export BIGQUERY_EMULATOR_HOST=localhost:9050

# Run with sample data
make seed-patterns
make dev-pattern-mining
```

### Training Data Management
```bash
# Use training data pipeline
cd ml/training-data
python data-pipeline.py --source github --pattern-type design_patterns

# Validate data quality
python quality/validation-pipeline.py --dataset pattern_training_v1

# Apply weak supervision
python labeling/weak-supervision.py --unlabeled-data raw_patterns
```

## Important Notes

### Service Boundaries
- Only detect patterns, don't fix them
- Depend on Repository Analysis for AST
- Don't store source code
- Provide evidence, not opinions

### ML Best Practices
- Version all models
- Track data lineage
- Monitor for drift
- A/B test new models
- Maintain interpretability

### Error Handling
```python
class PatternDetectionError(Exception):
    pass

class ModelNotFoundError(PatternDetectionError):
    pass

class FeatureExtractionError(PatternDetectionError):
    pass

# Graceful degradation
async def detect_with_fallback(features):
    try:
        return await ml_detect(features)
    except ModelNotFoundError:
        logger.warning("ML model unavailable, using rules")
        return await rule_based_detect(features)
```

## Success Criteria
- [ ] Detects 20+ pattern types accurately
- [ ] >80% precision, >70% recall
- [ ] Processes 100k LOC in <30 seconds
- [ ] Handles new/unknown patterns
- [ ] Continuous learning implemented
- [ ] Cost <$0.10 per repository
- [ ] Full ML pipeline observability
- [ ] A/B testing framework operational

## Deliverables
1. Pattern Detection API with all endpoints
2. ML pipeline with BigQuery ML integration
3. 20+ pattern detectors implemented
4. Comprehensive test suite
5. Performance benchmarks
6. Cost analysis report
7. Pattern catalog documentation
8. Integration with Repository Analysis
9. Feedback loop implementation

Remember: This service provides intelligence about code quality. Focus on accuracy, explainability, and continuous improvement. False positives erode user trust.