# Phase 4 Feature Implementation Prompts

## Overview
This directory contains comprehensive prompts for implementing Phase 4 of the CCL platform - Core Features Implementation. Each prompt provides detailed context, requirements, and implementation guidance for AI agents or development teams.

## Prompt Structure

Each prompt follows a consistent structure:
1. **Context** - Role and service overview
2. **Pre-Implementation Checklist** - Essential files to read
3. **Technical Requirements** - What to build
4. **Integration Requirements** - How services connect
5. **Implementation Steps** - Detailed guidance
6. **Validation & Testing** - Quality assurance
7. **Success Criteria** - Definition of done

## Available Prompts

### 00-phase4-coordination-guide.md
**Purpose**: Strategic coordination guide for all Phase 4 teams  
**Audience**: Team leads, project managers, all developers  
**Key Topics**:
- Team structure and assignments
- Timeline and dependencies
- Communication protocols
- Integration testing strategy
- Risk mitigation

### 01-repository-analysis-api.md
**Service**: Repository Analysis Engine  
**Language**: Rust  
**Timeline**: Weeks 1-3  
**Key Features**:
- Git repository cloning and analysis
- Multi-language AST parsing with Tree-sitter
- Code metrics calculation
- REST API implementation
- Performance: 1M LOC in <5 minutes

**Critical Output**: Must produce AST data matching `contracts/schemas/ast-output-v1.json`

### 02-query-intelligence-nlp.md
**Service**: Query Intelligence  
**Language**: Python  
**Timeline**: Weeks 4-6  
**Key Features**:
- Natural language query processing
- Gemini 2.5 integration
- RAG pipeline with vector search
- Semantic code search
- Response time <100ms

**Dependencies**: Requires AST output from Repository Analysis

### 03-pattern-detection-mvp.md
**Service**: Pattern Mining  
**Language**: Python  
**Timeline**: Weeks 7-9  
**Key Features**:
- ML-based pattern detection
- BigQuery ML integration
- 20+ pattern types
- Continuous learning
- Processing: 100k LOC in <30 seconds

**Dependencies**: Requires AST output from Repository Analysis

### 04-marketplace-api-foundation.md
**Service**: Marketplace  
**Language**: Go  
**Timeline**: Weeks 10-12  
**Key Features**:
- Pattern marketplace APIs
- Stripe payment integration
- Search and discovery
- Subscription management
- 99.9% uptime SLA

**Dependencies**: Requires patterns from Pattern Detection

### 05-phase3-improvements.md
**Purpose**: Address remaining Phase 3 improvements  
**Timeline**: Continuous throughout Phase 4  
**Key Tasks**:
- Development scripts (seed data, reset)
- Monitoring enhancements
- CI/CD improvements
- Multi-language AST support
- Security enhancements

## Using These Prompts

### For AI Agents
1. Read the prompt completely before starting
2. Follow the pre-implementation checklist in order
3. Reference the exact files mentioned
4. Validate against contracts frequently
5. Run all validation commands

### For Human Developers
1. Use as comprehensive implementation guides
2. Check off tasks as completed
3. Coordinate with other teams on dependencies
4. Update prompts if requirements change

## Integration Points

### Critical Integration Contracts
All services must comply with schemas in `contracts/schemas/`:
- `ast-output-v1.json` - Repository Analysis output
- `query-context-v1.json` - Query Intelligence input
- `pattern-input-v1.json` - Pattern Detection input
- `pattern-output-v1.json` - Pattern Detection output
- `marketplace-pattern-v1.json` - Marketplace format
- `service-events-v1.json` - Event bus messages
- `error-response-v1.json` - Error handling

### Service Communication Flow
```
Repository Analysis → AST Output → Query Intelligence
                  ↓
            Pattern Detection → Pattern Output → Marketplace
```

## Key Commands

### Start a Service Implementation
```bash
# For AI agents
# 1. Read the appropriate prompt file
# 2. Read all referenced files in order
# 3. Begin implementation

# For developers
cd services/{service-name}
make dev-{service-name}
```

### Validate Implementation
```bash
# Run tests
make test SERVICE={service-name}

# Check contract compliance
make validate-contracts SERVICE={service-name}

# Run integration tests
make test-integration
```

## Success Metrics

### Phase 4 Completion Requires:
- ✅ All 4 core services implemented
- ✅ Integration tests passing between all services
- ✅ Performance benchmarks met
- ✅ 90% test coverage
- ✅ Security scans passing
- ✅ API documentation complete
- ✅ Deployed to staging environment

## Timeline Overview

```
Week 1-3:   Repository Analysis API (Foundation)
Week 2-6:   Query Intelligence (Depends on Analysis)
Week 4-9:   Pattern Detection (Depends on Analysis)
Week 7-12:  Marketplace (Depends on Patterns)
Continuous: Platform Improvements
```

## Support

### Getting Help
- Coordination: See `00-phase4-coordination-guide.md`
- Blockers: Post in #phase4-blockers Slack channel
- Technical questions: Reference service-specific PRPs
- Integration issues: Check contracts documentation

### Updates
These prompts may be updated based on:
- Lessons learned during implementation
- Changed requirements
- New dependencies discovered

Always check for the latest version before starting work.

---

**Last Updated**: 2025-01-07  
**Phase 4 Duration**: 12 weeks  
**Next Phase**: Phase 5 - Advanced Features & Polish