# AI Agent Prompt: Set Up CI/CD Pipeline for CCL Platform

## Task Overview
Establish a comprehensive CI/CD pipeline that enables rapid, safe deployment of all CCL services. This pipeline must support multi-language services (Rust, Python, Go, TypeScript), ensure quality gates, and enable progressive rollouts.

## Context
CCL is a microservices platform on Google Cloud Platform. The CI/CD pipeline must handle:
- 4 different programming languages
- Independent service deployments
- Automated testing and quality checks
- Progressive rollouts with feature flags
- Multi-environment deployments (dev, staging, prod)

## Files You MUST Read First

1. **Architecture Context**:
   - `PLANNING.md` - Understand service architecture
   - `PRPs/implementation-guide.md` - Development standards
   - `CLAUDE.md` - Project conventions

2. **Service Specifications**:
   - `PRPs/services/analysis-engine.md` - Rust service needs
   - `PRPs/services/query-intelligence.md` - Python service needs
   - `PRPs/services/marketplace.md` - Go service needs
   - `PRPs/services/collaboration.md` - TypeScript needs

3. **Review Findings**:
   - `PRPs/reviews/master-enhancement-plan.md` - CI/CD requirements

## Deliverables You Must Create

### 1. GitHub Actions Workflows
Create these in `.github/workflows/`:

#### A. `.github/workflows/ci-common.yml`
Shared CI workflow for all services:
```yaml
name: CI Common
on:
  workflow_call:
    inputs:
      service_name:
        required: true
        type: string
      language:
        required: true
        type: string
    secrets:
      GCP_SA_KEY:
        required: true
      SONAR_TOKEN:
        required: true

jobs:
  quality-checks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Full history for better analysis

      - name: Set up language environment
        uses: ./.github/actions/setup-${{ inputs.language }}

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo
            ~/.cache/pip
            ~/go/pkg/mod
            ~/.npm
          key: ${{ runner.os }}-${{ inputs.language }}-${{ hashFiles('**/lock-files') }}

      - name: Install dependencies
        run: |
          cd ${{ inputs.service_name }}
          make deps

      - name: Lint
        run: |
          cd ${{ inputs.service_name }}
          make lint

      - name: Security scan
        run: |
          cd ${{ inputs.service_name }}
          make security-scan

      - name: Run tests
        run: |
          cd ${{ inputs.service_name }}
          make test-ci
        env:
          CI: true

      - name: Generate coverage report
        run: |
          cd ${{ inputs.service_name }}
          make coverage

      - name: SonarQube analysis
        uses: sonarsource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        with:
          projectBaseDir: ${{ inputs.service_name }}

      - name: Build artifacts
        run: |
          cd ${{ inputs.service_name }}
          make build

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ inputs.service_name }}-artifacts
          path: ${{ inputs.service_name }}/dist/
```

#### B. `.github/workflows/cd-deploy.yml`
Deployment workflow:
```yaml
name: Deploy Service
on:
  workflow_call:
    inputs:
      service_name:
        required: true
        type: string
      environment:
        required: true
        type: string
      version:
        required: true
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    steps:
      - uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker for Artifact Registry
        run: |
          gcloud auth configure-docker us-central1-docker.pkg.dev

      - name: Deploy to Cloud Run
        run: |
          cd ${{ inputs.service_name }}
          make deploy ENV=${{ inputs.environment }} VERSION=${{ inputs.version }}

      - name: Run smoke tests
        run: |
          cd ${{ inputs.service_name }}
          make smoke-test ENV=${{ inputs.environment }}

      - name: Update deployment record
        run: |
          echo "${{ inputs.service_name }},${{ inputs.version }},${{ inputs.environment }},$(date -u +%Y-%m-%dT%H:%M:%SZ)" >> deployments.csv
          gsutil cp deployments.csv gs://ccl-deployments/history/
```

#### C. Service-Specific Workflows
Create for each service:
- `.github/workflows/analysis-engine-ci.yml`
- `.github/workflows/query-intelligence-ci.yml`
- `.github/workflows/pattern-mining-ci.yml`
- `.github/workflows/marketplace-ci.yml`

### 2. Makefile Templates
Create `build/makefiles/`:

#### A. `build/makefiles/common.mk`
```makefile
# Common targets for all services
.PHONY: help deps lint test build deploy

SHELL := /bin/bash
VERSION ?= $(shell git describe --tags --always --dirty)
ENV ?= development
PROJECT_ID := ccl-platform
REGION := us-central1

help:
	@echo "Available targets:"
	@echo "  deps       - Install dependencies"
	@echo "  lint       - Run linters"
	@echo "  test       - Run tests"
	@echo "  build      - Build artifacts"
	@echo "  deploy     - Deploy to GCP"

# Service-specific includes
-include build/makefiles/$(LANGUAGE).mk
```

#### B. Language-specific makefiles:
- `build/makefiles/rust.mk`
- `build/makefiles/python.mk`
- `build/makefiles/go.mk`
- `build/makefiles/typescript.mk`

### 3. Quality Gates Configuration
Create `build/quality-gates/`:

#### A. `build/quality-gates/sonar-project.properties`
```properties
# SonarQube configuration
sonar.organization=ccl-platform
sonar.projectKey=ccl-${SERVICE_NAME}
sonar.sources=.
sonar.exclusions=**/*_test.go,**/*.test.ts,**/test_*.py,**/tests.rs
sonar.coverage.exclusions=**/cmd/**,**/examples/**
sonar.go.coverage.reportPaths=coverage.out
sonar.python.coverage.reportPaths=coverage.xml
sonar.javascript.lcov.reportPaths=coverage/lcov.info
```

#### B. `build/quality-gates/quality-thresholds.yml`
```yaml
quality_gates:
  coverage:
    minimum: 90
    fail_build: true
  
  code_smells:
    maximum: 5
    severity: major
  
  security_hotspots:
    maximum: 0
    fail_build: true
  
  duplicated_lines:
    maximum_percent: 3
  
  cyclomatic_complexity:
    maximum: 10
    
  performance:
    build_time_seconds: 300
    test_time_seconds: 600
```

### 4. Environment Configuration
Create `environments/`:

#### A. `environments/development.yml`
```yaml
environment: development
gcp:
  project_id: ccl-platform-dev
  region: us-central1
  
services:
  analysis_engine:
    cloud_run:
      cpu: 1
      memory: 1Gi
      min_instances: 0
      max_instances: 10
      
  query_intelligence:
    cloud_run:
      cpu: 2
      memory: 4Gi
      min_instances: 1
      max_instances: 20
    vertex_ai:
      location: us-central1
      
secrets:
  - name: api-keys
    version: latest
  - name: service-accounts
    version: latest
```

#### B. Create similar for `staging.yml` and `production.yml`

### 5. Deployment Scripts
Create `scripts/deploy/`:

#### A. `scripts/deploy/rollout.sh`
```bash
#!/bin/bash
set -euo pipefail

SERVICE=$1
VERSION=$2
ENVIRONMENT=$3
PERCENTAGE=${4:-10}  # Start with 10% traffic

echo "Rolling out $SERVICE:$VERSION to $ENVIRONMENT ($PERCENTAGE% traffic)"

# Deploy new revision without traffic
gcloud run deploy $SERVICE \
  --image=us-central1-docker.pkg.dev/ccl-platform/services/$SERVICE:$VERSION \
  --no-traffic \
  --tag=$VERSION \
  --region=us-central1

# Gradually shift traffic
gcloud run services update-traffic $SERVICE \
  --to-tags=$VERSION=$PERCENTAGE \
  --region=us-central1

echo "Monitoring metrics for 5 minutes..."
sleep 300

# Check error rate
ERROR_RATE=$(gcloud monitoring read \
  --project=$PROJECT_ID \
  --filter='metric.type="run.googleapis.com/request_count" AND resource.type="cloud_run_revision" AND metric.label.response_code_class!="2xx"')

if [ "$ERROR_RATE" -gt "5" ]; then
  echo "High error rate detected, rolling back..."
  gcloud run services update-traffic $SERVICE \
    --to-revisions=LATEST=100 \
    --region=us-central1
  exit 1
fi

echo "Rollout successful"
```

### 6. Monitoring Integration
Create `build/monitoring/`:

#### A. `build/monitoring/deployment-dashboard.json`
```json
{
  "displayName": "CCL Deployment Dashboard",
  "dashboardFilters": [],
  "gridLayout": {
    "widgets": [
      {
        "title": "Deployment Frequency",
        "xyChart": {
          "dataSets": [{
            "timeSeriesQuery": {
              "timeSeriesFilter": {
                "filter": "metric.type=\"custom.googleapis.com/ccl/deployments\""
              }
            }
          }]
        }
      },
      {
        "title": "Build Success Rate",
        "scorecard": {
          "timeSeriesQuery": {
            "timeSeriesFilter": {
              "filter": "metric.type=\"custom.googleapis.com/ccl/build_success_rate\""
            }
          }
        }
      },
      {
        "title": "Lead Time for Changes",
        "xyChart": {
          "dataSets": [{
            "timeSeriesQuery": {
              "timeSeriesFilter": {
                "filter": "metric.type=\"custom.googleapis.com/ccl/lead_time\""
              }
            }
          }]
        }
      }
    ]
  }
}
```

### 7. Documentation
Create `docs/cicd/`:

#### A. `docs/cicd/README.md`
```markdown
# CCL CI/CD Pipeline

## Overview
Automated pipeline for building, testing, and deploying CCL services.

## Pipeline Stages
1. **Code Commit** → Triggers CI
2. **Quality Checks** → Lint, test, security scan
3. **Build** → Create artifacts
4. **Deploy to Dev** → Automatic
5. **Deploy to Staging** → Requires approval
6. **Deploy to Prod** → Progressive rollout

## Service-Specific Guides
- [Rust Services](./rust-pipeline.md)
- [Python Services](./python-pipeline.md)
- [Go Services](./go-pipeline.md)
- [TypeScript Services](./typescript-pipeline.md)

## Deployment Strategies
- **Blue-Green**: For critical services
- **Canary**: Default strategy (10% → 50% → 100%)
- **Rolling**: For stateless services

## Emergency Procedures
- [Rollback Process](./rollback.md)
- [Hotfix Workflow](./hotfix.md)
- [Break Glass Access](./emergency-access.md)
```

## Special Considerations

### Multi-Language Support
- Each language has specific build tools
- Standardize through Makefiles
- Use language-specific quality tools

### Security Requirements
- Never commit secrets
- Use Google Secret Manager
- Scan for vulnerabilities
- Sign container images

### Performance Optimization
- Cache dependencies aggressively
- Parallelize where possible
- Use build matrices for testing
- Optimize Docker layers

## Validation Criteria

Your CI/CD pipeline is complete when:
- [ ] All services can be built automatically
- [ ] Tests run on every commit
- [ ] Security scans are mandatory
- [ ] Deployments are progressive
- [ ] Rollbacks are automated
- [ ] Monitoring is integrated
- [ ] Documentation is comprehensive
- [ ] Emergency procedures exist

## Success Metrics
- Build time: <5 minutes
- Deploy time: <2 minutes
- Pipeline reliability: >99%
- Rollback time: <30 seconds

Remember: This pipeline is the backbone of CCL's development velocity. It must be fast, reliable, and safe.