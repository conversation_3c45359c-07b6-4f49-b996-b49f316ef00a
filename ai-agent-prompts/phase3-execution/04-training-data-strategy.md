# AI Agent Prompt: Define ML Training Data Strategy

## Task Overview
Create a comprehensive strategy for acquiring, preparing, and managing training data for CCL's machine learning components, particularly the Pattern Detection service. This is critical as the PRP review identified Pattern Detection as the weakest component (6.5/10) primarily due to lack of training data strategy.

## Context
CCL needs high-quality labeled data to train ML models that can:
- Detect design patterns in code (Singleton, Factory, Observer, etc.)
- Identify anti-patterns and code smells
- Find security vulnerabilities
- Recognize performance bottlenecks
- Discover architectural patterns

The challenge: Getting labeled code data at scale while respecting licenses and privacy.

## Files You MUST Read First

1. **Pattern Detection Requirements**:
   - `PRPs/features/pattern-detection-mvp.md` - ML requirements
   - `PRPs/reviews/pattern-detection-review.md` - Identified gaps
   - `PRPs/ai-ml/pattern-recognition.md` - ML approach

2. **Architecture Context**:
   - `PRPs/services/pattern-mining.md` - Service design
   - `PRPs/database/bigquery-analytics.md` - Data storage

3. **Legal/Privacy**:
   - `PRPs/security/compliance.md` - Data privacy requirements
   - `PLANNING.md` - Business constraints

## Deliverables You Must Create

### 1. Data Acquisition Strategy
Create `ml/training-data/`:

#### A. `ml/training-data/acquisition-strategy.md`
```markdown
# CCL Training Data Acquisition Strategy

## Overview
Multi-source approach to build a comprehensive training dataset for pattern detection.

## Data Sources

### 1. Open Source Repositories (Primary Source)
**Target Volume**: 10,000 repositories, 100M+ lines of code
**Timeline**: 4 weeks

#### Selection Criteria
- GitHub stars > 1000 (quality indicator)
- Active maintenance (commits in last 6 months)
- Diverse languages (Java, Python, Go, JavaScript, Rust)
- Explicit patterns in documentation
- Permissive licenses (MIT, Apache 2.0, BSD)

#### Collection Pipeline
```python
def collect_open_source_data():
    """
    1. Query GitHub API for repositories
    2. Filter by criteria
    3. Clone repositories
    4. Extract code + documentation
    5. Initial pattern labeling
    """
```

### 2. Synthetic Data Generation (Augmentation)
**Target Volume**: 50,000 synthetic examples
**Timeline**: 2 weeks

#### Pattern Templates
```python
# Example: Generate variations of Singleton pattern
def generate_singleton_variations():
    templates = [
        "eager_initialization",
        "lazy_initialization", 
        "thread_safe_singleton",
        "enum_singleton"
    ]
    
    for template in templates:
        for language in ["java", "python", "go", "typescript"]:
            generate_pattern(template, language)
```

### 3. Community Contributions (Ongoing)
**Target**: 1000 labeled examples/month
**Incentive**: Free CCL credits

#### Contribution Platform
- Web interface for pattern submission
- Automated validation
- Community voting on quality
- Reward system

### 4. Enterprise Partnerships (Future)
**Target**: 5 enterprise partners
**Timeline**: 6 months

#### Partnership Model
- Anonymized code analysis
- Shared pattern library
- Revenue sharing on patterns
```

#### B. `ml/training-data/data-pipeline.py`
```python
"""
Training Data Pipeline for Pattern Detection
"""
import apache_beam as beam
from apache_beam.options.pipeline_options import PipelineOptions
import tensorflow as tf
from typing import Dict, List, Tuple

class CodePatternPipeline:
    def __init__(self, project_id: str, dataset_id: str):
        self.project_id = project_id
        self.dataset_id = dataset_id
        
    def create_pipeline(self) -> beam.Pipeline:
        options = PipelineOptions([
            f'--project={self.project_id}',
            '--runner=DataflowRunner',
            '--region=us-central1',
            '--temp_location=gs://ccl-ml-temp/dataflow',
            '--max_num_workers=50'
        ])
        
        return beam.Pipeline(options=options)
    
    def run(self, source_repos: List[str]):
        with self.create_pipeline() as p:
            # Step 1: Read repository list
            repos = p | 'CreateRepos' >> beam.Create(source_repos)
            
            # Step 2: Clone and extract code
            code_files = (
                repos 
                | 'CloneRepos' >> beam.ParDo(CloneRepository())
                | 'ExtractFiles' >> beam.ParDo(ExtractCodeFiles())
            )
            
            # Step 3: Parse AST
            ast_data = (
                code_files
                | 'ParseAST' >> beam.ParDo(ParseToAST())
                | 'ValidateAST' >> beam.Filter(lambda x: x.is_valid)
            )
            
            # Step 4: Extract features
            features = (
                ast_data
                | 'ExtractFeatures' >> beam.ParDo(FeatureExtractor())
                | 'NormalizeFeatures' >> beam.ParDo(FeatureNormalizer())
            )
            
            # Step 5: Apply weak labels
            weak_labels = (
                features
                | 'ApplyHeuristics' >> beam.ParDo(HeuristicLabeler())
                | 'AggregateLabels' >> beam.CombinePerKey(LabelAggregator())
            )
            
            # Step 6: Store in BigQuery
            weak_labels | 'WriteToBQ' >> beam.io.WriteToBigQuery(
                table=f'{self.project_id}:{self.dataset_id}.training_data',
                schema=self.get_schema(),
                write_disposition=beam.io.BigQueryDisposition.WRITE_APPEND
            )

class CloneRepository(beam.DoFn):
    def process(self, repo_url: str):
        """Clone repository and yield file paths"""
        # Implementation details
        pass

class FeatureExtractor(beam.DoFn):
    def process(self, ast_data: Dict):
        """Extract ML features from AST"""
        features = {
            'structural': self.extract_structural_features(ast_data),
            'lexical': self.extract_lexical_features(ast_data),
            'semantic': self.extract_semantic_features(ast_data),
            'metrics': self.extract_code_metrics(ast_data)
        }
        yield features
```

### 2. Labeling Strategy
Create `ml/training-data/labeling/`:

#### A. `ml/training-data/labeling/labeling-framework.md`
```markdown
# Pattern Labeling Framework

## Labeling Hierarchy

### Level 1: Pattern Category
- Design Pattern
- Anti-Pattern
- Security Pattern
- Performance Pattern
- Architectural Pattern

### Level 2: Specific Pattern
Design Patterns:
- Creational: Singleton, Factory, Builder, Prototype
- Structural: Adapter, Decorator, Facade, Proxy
- Behavioral: Observer, Strategy, Template, Iterator

### Level 3: Implementation Quality
- Textbook (perfect implementation)
- Good (minor variations)
- Acceptable (works but not ideal)
- Poor (barely recognizable)

## Labeling Methods

### 1. Automated Heuristic Labeling
```python
def label_singleton_pattern(ast: ASTNode) -> Optional[Label]:
    """
    Heuristic rules for Singleton detection:
    1. Private constructor
    2. Static instance variable
    3. Public static getInstance method
    """
    confidence = 0.0
    
    if has_private_constructor(ast):
        confidence += 0.4
    
    if has_static_instance(ast):
        confidence += 0.3
        
    if has_get_instance_method(ast):
        confidence += 0.3
    
    if confidence >= 0.7:
        return Label(
            pattern="singleton",
            confidence=confidence,
            method="heuristic"
        )
```

### 2. Weak Supervision with Snorkel
```python
from snorkel.labeling import labeling_function

@labeling_function()
def lf_has_singleton_name(x):
    """Label function based on naming"""
    if "singleton" in x.class_name.lower():
        return SINGLETON
    return ABSTAIN

@labeling_function()
def lf_has_instance_field(x):
    """Label function based on structure"""
    if x.has_static_field and "instance" in x.field_names:
        return SINGLETON
    return ABSTAIN

# Combine label functions
label_model = LabelModel(cardinality=len(PATTERN_TYPES))
label_model.fit(L_train, n_epochs=500)
```

### 3. Active Learning Pipeline
```python
class ActiveLearningPipeline:
    def __init__(self, model, unlabeled_pool):
        self.model = model
        self.unlabeled_pool = unlabeled_pool
        
    def select_samples(self, n_samples: int) -> List[Sample]:
        """Select most informative samples for labeling"""
        # Uncertainty sampling
        predictions = self.model.predict_proba(self.unlabeled_pool)
        uncertainty = -np.sum(predictions * np.log(predictions), axis=1)
        
        # Select top uncertain samples
        indices = np.argsort(uncertainty)[-n_samples:]
        return [self.unlabeled_pool[i] for i in indices]
```

### 4. Expert Validation
- Senior developers review edge cases
- Pattern authors validate implementations
- Community consensus for ambiguous patterns
```

### 3. Data Quality Assurance
Create `ml/training-data/quality/`:

#### A. `ml/training-data/quality/validation-pipeline.py`
```python
"""
Data Quality Validation Pipeline
"""
from dataclasses import dataclass
from typing import List, Dict, Tuple
import pandas as pd

@dataclass
class QualityMetrics:
    completeness: float
    consistency: float
    accuracy: float
    class_balance: Dict[str, float]
    
class DataQualityValidator:
    def __init__(self, min_quality_threshold: float = 0.8):
        self.min_quality_threshold = min_quality_threshold
        
    def validate_dataset(self, df: pd.DataFrame) -> QualityReport:
        """Comprehensive data quality validation"""
        
        # Check completeness
        completeness = self.check_completeness(df)
        
        # Check label consistency
        consistency = self.check_label_consistency(df)
        
        # Check class balance
        class_balance = self.check_class_balance(df)
        
        # Validate against golden dataset
        accuracy = self.validate_against_golden(df)
        
        # Check for duplicates
        duplicates = self.check_duplicates(df)
        
        # Validate feature distributions
        feature_quality = self.validate_features(df)
        
        return QualityReport(
            completeness=completeness,
            consistency=consistency,
            accuracy=accuracy,
            class_balance=class_balance,
            duplicates=duplicates,
            feature_quality=feature_quality
        )
    
    def check_label_consistency(self, df: pd.DataFrame) -> float:
        """Check if similar code has consistent labels"""
        # Group by code similarity
        similar_groups = self.group_by_similarity(df)
        
        consistent_groups = 0
        for group in similar_groups:
            labels = group['label'].unique()
            if len(labels) == 1:
                consistent_groups += 1
                
        return consistent_groups / len(similar_groups)
    
    def check_class_balance(self, df: pd.DataFrame) -> Dict[str, float]:
        """Ensure reasonable class distribution"""
        label_counts = df['label'].value_counts()
        total = len(df)
        
        return {
            label: count / total 
            for label, count in label_counts.items()
        }
```

### 4. Privacy & Legal Compliance
Create `ml/training-data/compliance/`:

#### A. `ml/training-data/compliance/data-privacy.md`
```markdown
# Data Privacy & Compliance Framework

## Privacy Requirements

### 1. License Compliance
- Only use repositories with permissive licenses
- Maintain attribution records
- Respect license terms

### 2. PII Removal
```python
def remove_pii(code: str) -> str:
    """Remove personally identifiable information"""
    # Remove email addresses
    code = re.sub(r'[\w\.-]+@[\w\.-]+\.\w+', '<EMAIL>', code)
    
    # Remove API keys
    code = re.sub(r'api[_-]?key\s*=\s*["\'][\w\-]+["\']', 'api_key="REDACTED"', code)
    
    # Remove credentials
    code = re.sub(r'password\s*=\s*["\'][^"\']+["\']', 'password="REDACTED"', code)
    
    return code
```

### 3. Data Retention
- Training data: 2 years
- Model artifacts: Indefinite
- User submissions: 90 days (unless permitted)

### 4. Right to Deletion
- Track data provenance
- Enable selective deletion
- Update models quarterly
```

### 5. Continuous Learning Strategy
Create `ml/training-data/continuous-learning/`:

#### A. `ml/training-data/continuous-learning/feedback-loop.py`
```python
"""
Continuous Learning from Production Feedback
"""
class ContinuousLearningPipeline:
    def __init__(self, model_registry, data_store):
        self.model_registry = model_registry
        self.data_store = data_store
        
    def collect_production_feedback(self):
        """Collect user corrections and validations"""
        feedback = []
        
        # User corrections
        corrections = self.get_user_corrections()
        feedback.extend(corrections)
        
        # High-confidence predictions
        confident_predictions = self.get_confident_predictions()
        feedback.extend(confident_predictions)
        
        # Edge cases
        edge_cases = self.get_edge_cases()
        feedback.extend(edge_cases)
        
        return feedback
    
    def retrain_model(self, feedback_data):
        """Incrementally retrain with new data"""
        # Validate feedback quality
        validated_data = self.validate_feedback(feedback_data)
        
        # Merge with existing training data
        combined_data = self.merge_datasets(
            self.data_store.get_training_data(),
            validated_data
        )
        
        # Retrain model
        new_model = self.train_model(combined_data)
        
        # A/B test new model
        if self.validate_model_improvement(new_model):
            self.model_registry.register_model(new_model)
```

### 6. Data Storage Architecture
Create `ml/training-data/storage/`:

#### A. `ml/training-data/storage/schema.sql`
```sql
-- BigQuery schema for training data

CREATE OR REPLACE TABLE `ccl-ml.training_data.code_patterns` (
  -- Identifiers
  sample_id STRING NOT NULL,
  source_repo STRING,
  file_path STRING,
  
  -- Code content
  code_snippet STRING,
  ast_json JSON,
  language STRING,
  
  -- Features
  structural_features ARRAY<FLOAT64>,
  lexical_features ARRAY<FLOAT64>,
  semantic_features ARRAY<FLOAT64>,
  metric_features ARRAY<FLOAT64>,
  
  -- Labels
  pattern_category STRING,
  pattern_type STRING,
  confidence FLOAT64,
  labeling_method STRING,
  
  -- Metadata
  created_at TIMESTAMP,
  updated_at TIMESTAMP,
  version STRING,
  
  -- Quality
  quality_score FLOAT64,
  validation_status STRING
)
PARTITION BY DATE(created_at)
CLUSTER BY pattern_type, language;

-- Create views for training
CREATE OR REPLACE VIEW `ccl-ml.training_data.high_quality_patterns` AS
SELECT *
FROM `ccl-ml.training_data.code_patterns`
WHERE quality_score >= 0.8
  AND validation_status = 'validated';
```

### 7. Implementation Timeline
Create `ml/training-data/implementation-plan.md`:

```markdown
# Training Data Implementation Timeline

## Week 1-2: Foundation
- [ ] Set up GCS buckets and BigQuery datasets
- [ ] Implement GitHub crawler
- [ ] Create basic labeling functions
- [ ] Set up data pipeline infrastructure

## Week 3-4: Data Collection
- [ ] Crawl 1000 high-quality repos
- [ ] Generate 10K synthetic examples
- [ ] Apply heuristic labels
- [ ] Validate data quality

## Week 5-6: Labeling Enhancement
- [ ] Implement Snorkel labeling
- [ ] Set up active learning
- [ ] Create labeling UI
- [ ] Get expert validation (100 samples)

## Week 7-8: Production Pipeline
- [ ] Automate data collection
- [ ] Implement continuous learning
- [ ] Set up monitoring
- [ ] Documentation and training

## Success Metrics
- 100K+ labeled samples
- 80%+ label accuracy
- 20+ pattern types covered
- <1 hour to add new pattern type
```

## Validation Criteria

Your training data strategy is complete when:
- [ ] Multiple data sources identified
- [ ] Automated labeling pipeline built
- [ ] Quality validation implemented
- [ ] Privacy compliance ensured
- [ ] Storage architecture defined
- [ ] Continuous learning designed
- [ ] Implementation timeline clear
- [ ] Budget estimated (<$50K)

## Critical Success Factors
- Data quality > data quantity
- Respect licenses and privacy
- Enable continuous improvement
- Balance automation with accuracy
- Plan for pattern evolution

Remember: The quality of pattern detection directly depends on training data quality. This strategy must be robust, scalable, and legally compliant.