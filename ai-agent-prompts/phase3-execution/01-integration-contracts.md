# AI Agent Prompt: Create Integration Contracts Between CCL Services

## Task Overview
Create comprehensive data contracts that define how CCL services communicate. This is THE MOST CRITICAL task in Phase 1 as it unblocks all service development. Without these contracts, teams cannot build compatible services.

## Context
Based on the PRP review, the lack of explicit data contracts between services was identified as the #1 critical gap. You need to create JSON schemas, example payloads, and validation rules that ensure all services can communicate seamlessly.

## Background Knowledge You Need
- CCL has 4 main services that must integrate:
  - **Repository Analysis API** (Rust) → Outputs AST and metrics
  - **Query Intelligence** (Python) → Consumes AST for NL queries  
  - **Pattern Detection** (Python) → Consumes AST for ML analysis
  - **Marketplace API** (Go) → Consumes patterns for commerce

## Files You MUST Read First

1. **Review Findings**:
   - `PRPs/reviews/integration-analysis.md` - Identified integration gaps
   - `PRPs/reviews/master-enhancement-plan.md` - Contract requirements

2. **Service PRPs** (understand data needs):
   - `PRPs/features/repository-analysis-api.md`
   - `PRPs/features/natural-language-query.md`
   - `PRPs/features/pattern-detection-mvp.md`
   - `PRPs/features/marketplace-api-foundation.md`

3. **Architecture Context**:
   - `PRPs/implementation-guide.md` - Technical patterns
   - `CLAUDE.md` - Project conventions

## Deliverables You Must Create

### 1. Core Schema Definitions
Create these files in `contracts/schemas/`:

#### A. `contracts/schemas/ast-output-v1.json`
The complete AST output from Repository Analysis API:
```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "Repository Analysis AST Output",
  "version": "1.0.0",
  "type": "object",
  "required": ["repository", "analysis", "metadata"],
  "properties": {
    "repository": {
      "type": "object",
      "required": ["url", "commit", "branch"],
      "properties": {
        "url": {"type": "string", "format": "uri"},
        "commit": {"type": "string", "pattern": "^[a-f0-9]{40}$"},
        "branch": {"type": "string"}
      }
    },
    "analysis": {
      "type": "object",
      "required": ["files", "metrics", "languages"],
      "properties": {
        "files": {
          "type": "array",
          "items": {"$ref": "#/definitions/FileAnalysis"}
        },
        "metrics": {"$ref": "#/definitions/RepositoryMetrics"},
        "languages": {"$ref": "#/definitions/LanguageBreakdown"}
      }
    },
    "metadata": {
      "type": "object",
      "required": ["version", "timestamp", "duration_ms"],
      "properties": {
        "version": {"type": "string"},
        "timestamp": {"type": "string", "format": "date-time"},
        "duration_ms": {"type": "integer", "minimum": 0}
      }
    }
  },
  "definitions": {
    "FileAnalysis": {
      "type": "object",
      "required": ["path", "language", "ast", "metrics"],
      "properties": {
        "path": {"type": "string"},
        "language": {"type": "string"},
        "ast": {"$ref": "#/definitions/ASTNode"},
        "metrics": {"$ref": "#/definitions/FileMetrics"}
      }
    },
    "ASTNode": {
      "type": "object",
      "required": ["type", "range"],
      "properties": {
        "type": {"type": "string"},
        "name": {"type": "string"},
        "range": {
          "type": "object",
          "required": ["start", "end"],
          "properties": {
            "start": {"$ref": "#/definitions/Position"},
            "end": {"$ref": "#/definitions/Position"}
          }
        },
        "children": {
          "type": "array",
          "items": {"$ref": "#/definitions/ASTNode"}
        },
        "properties": {
          "type": "object",
          "additionalProperties": true
        }
      }
    }
  }
}
```

#### B. `contracts/schemas/query-context-v1.json`
Context format for Query Intelligence:
```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "Query Context Input",
  "version": "1.0.0",
  "type": "object",
  "required": ["repository_id", "ast_summary", "embeddings"],
  "properties": {
    "repository_id": {"type": "string"},
    "ast_summary": {
      "type": "object",
      "description": "Processed AST data optimized for querying"
    },
    "embeddings": {
      "type": "array",
      "items": {
        "type": "object",
        "required": ["chunk_id", "content", "vector", "metadata"],
        "properties": {
          "chunk_id": {"type": "string"},
          "content": {"type": "string"},
          "vector": {
            "type": "array",
            "items": {"type": "number"},
            "minItems": 1536,
            "maxItems": 1536
          }
        }
      }
    }
  }
}
```

#### C. `contracts/schemas/pattern-input-v1.json`
#### D. `contracts/schemas/pattern-output-v1.json`
#### E. `contracts/schemas/marketplace-pattern-v1.json`

### 2. Example Payloads
Create these in `contracts/examples/`:

#### A. `contracts/examples/ast-output-example.json`
Complete example of Repository Analysis output with:
- Multiple files
- Different languages
- Nested AST structures
- Comprehensive metrics

#### B. `contracts/examples/query-context-example.json`
Example showing how AST is transformed for Query Intelligence

#### C. `contracts/examples/pattern-detection-flow.json`
End-to-end example from AST to detected patterns

### 3. Contract Validation Rules
Create `contracts/validation/validation-rules.md`:

```markdown
# Contract Validation Rules

## Version Management
- All schemas use semantic versioning
- Breaking changes require major version bump
- Backward compatibility for 2 major versions

## Required Field Rules
- Never remove required fields
- New required fields need migration plan
- Optional fields can become required with major version

## Data Size Limits
- AST depth: Maximum 50 levels
- File count: Maximum 10,000 per repository
- Individual file AST: Maximum 100MB
- Pattern results: Maximum 1000 per analysis

## Performance Contracts
- AST serialization: <1 second for 1MB
- Context building: <5 seconds for 1000 files
- Pattern detection: <30 seconds for standard repo
```

### 4. Integration Test Suite
Create `contracts/tests/contract-tests.md`:

```markdown
# Integration Contract Tests

## Test Categories

### 1. Schema Validation Tests
- Validate all examples against schemas
- Test edge cases (empty, minimal, maximal)
- Verify required fields

### 2. Cross-Service Compatibility
- Repository → Query Intelligence flow
- Repository → Pattern Detection flow
- Pattern → Marketplace flow

### 3. Version Compatibility
- Test v1 consumer with v2 producer
- Verify graceful degradation
- Migration path validation

### 4. Performance Tests
- Large payload handling
- Serialization/deserialization speed
- Memory usage validation
```

### 5. Contract Documentation
Create `contracts/README.md`:

```markdown
# CCL Service Integration Contracts

## Overview
This directory contains the formal contracts between CCL services.

## Contract Files
- `schemas/` - JSON Schema definitions
- `examples/` - Example payloads
- `validation/` - Validation rules and limits
- `tests/` - Contract test specifications

## Service Integration Map
[ASCII diagram showing data flow]

## Version History
- v1.0.0 - Initial release
- [Future versions]

## Breaking Change Policy
[Define how breaking changes are handled]
```

## Implementation Requirements

### Must Include
1. **Complete AST Schema** with all node types
2. **Transformation Logic** from AST to each consumer format
3. **Error Response Schemas** for all services
4. **Pagination Schemas** for large results
5. **Streaming Protocols** for real-time updates

### Critical Decisions to Make
1. **AST Format**: Tree-sitter native vs custom simplified
2. **Vector Dimensions**: 1536 (OpenAI) vs 768 (other models)
3. **Identifier Strategy**: UUIDs vs service-specific IDs
4. **Timestamp Format**: ISO 8601 vs Unix timestamps
5. **Null Handling**: Explicit nulls vs omission

### Performance Considerations
- Design for streaming large ASTs
- Optimize for JSON parsing speed
- Consider protobuf for internal communication
- Plan for compression strategies

## Validation Criteria

Your contracts are complete when:
- [ ] All service interactions have schemas
- [ ] Examples cover all use cases
- [ ] Validation rules are comprehensive
- [ ] Version strategy is defined
- [ ] Performance limits are specified
- [ ] Error handling is standardized
- [ ] Tests can validate compatibility
- [ ] Documentation is clear
- [ ] Migration paths exist
- [ ] Teams can work independently

## Common Pitfalls to Avoid

1. **Over-Specification**: Don't constrain future evolution
2. **Under-Specification**: Ensure unambiguous interpretation
3. **Ignoring Performance**: Large ASTs can be gigabytes
4. **Forgetting Errors**: Define all error responses
5. **No Versioning Plan**: Changes will happen
6. **Missing Examples**: Teams need concrete references
7. **Weak Validation**: Contracts without validation fail

## Time Estimate
- Initial schemas: 1 day
- Examples and validation: 1 day
- Testing framework: 1 day
- Documentation: 0.5 days
- Total: 3-4 days

## Success Metrics
- Zero integration failures in development
- All teams can work independently
- Contract changes follow defined process
- Performance SLAs are met
- Version migrations are smooth

Remember: These contracts are the foundation of CCL's microservices architecture. They must be precise enough to prevent integration issues but flexible enough to allow evolution. This is the most critical deliverable in Phase 1.