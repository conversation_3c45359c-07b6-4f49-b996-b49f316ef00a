# AI Agent Prompt: Set Up Local Development Environment

## Task Overview
Create a comprehensive local development environment that allows all engineers to run the complete CCL platform on their machines. This must support all 4 services (Rust, Python, Go, TypeScript) and integrate with Google Cloud Platform services.

## Context
CCL engineers need to:
- Run all microservices locally
- Test integrations between services
- Debug with production-like data
- Develop without internet (airplane mode)
- Switch between dev/staging/prod configs
- Share consistent environments across the team

## Files You MUST Read First

1. **Architecture Overview**:
   - `PLANNING.md` - System architecture
   - `CLAUDE.md` - Development conventions
   - `PRPs/implementation-guide.md` - Development patterns

2. **Service Requirements**:
   - `PRPs/services/analysis-engine.md` - Rust service
   - `PRPs/services/query-intelligence.md` - Python service
   - `PRPs/services/marketplace.md` - Go service
   - `PRPs/services/collaboration.md` - TypeScript service

3. **Infrastructure**:
   - `infrastructure/` - If exists, check current setup
   - `contracts/schemas/` - Service contracts

## Deliverables You Must Create

### 1. Docker Compose Configuration
Create `docker/`:

#### A. `docker/docker-compose.yml`
```yaml
version: '3.8'

services:
  # Core Infrastructure
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: ccl_dev
      POSTGRES_PASSWORD: dev_password
      POSTGRES_DB: ccl_local
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts/postgres:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ccl_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # GCP Emulators
  spanner-emulator:
    image: gcr.io/cloud-spanner-emulator/emulator:latest
    ports:
      - "9010:9010"
      - "9020:9020"

  firestore-emulator:
    image: google/cloud-sdk:latest
    command: gcloud beta emulators firestore start --host-port=0.0.0.0:8080
    ports:
      - "8080:8080"
    environment:
      FIRESTORE_PROJECT_ID: ccl-local

  pubsub-emulator:
    image: google/cloud-sdk:latest
    command: gcloud beta emulators pubsub start --host-port=0.0.0.0:8085
    ports:
      - "8085:8085"
    environment:
      PUBSUB_PROJECT_ID: ccl-local

  storage-emulator:
    image: fsouza/fake-gcs-server:latest
    command: -scheme http -public-host storage.ccl.local:4443
    ports:
      - "4443:4443"
    volumes:
      - ./data/gcs:/data

  # Observability
  otel-collector:
    image: otel/opentelemetry-collector-contrib:latest
    command: ["--config=/etc/otel-collector-config.yaml"]
    volumes:
      - ./config/otel-collector-config.yaml:/etc/otel-collector-config.yaml
    ports:
      - "4317:4317"  # OTLP gRPC
      - "4318:4318"  # OTLP HTTP
      - "9464:9464"  # Prometheus exporter
    depends_on:
      - jaeger
      - prometheus

  jaeger:
    image: jaegertracing/all-in-one:latest
    environment:
      COLLECTOR_OTLP_ENABLED: true
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Jaeger collector

  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
    ports:
      - "9090:9090"

  grafana:
    image: grafana/grafana:latest
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_INSTALL_PLUGINS: grafana-clock-panel
    volumes:
      - ./config/grafana/provisioning:/etc/grafana/provisioning
      - grafana_data:/var/lib/grafana
    ports:
      - "3000:3000"
    depends_on:
      - prometheus

  # CCL Services (built locally)
  analysis-engine:
    build:
      context: ../analysis-engine
      dockerfile: Dockerfile.dev
    volumes:
      - ../analysis-engine:/app
      - cargo_cache:/usr/local/cargo
      - target_cache:/app/target
    environment:
      RUST_LOG: debug
      DATABASE_URL: ***********************************************/ccl_local
      REDIS_URL: redis://redis:6379
      OTEL_EXPORTER_OTLP_ENDPOINT: http://otel-collector:4317
    ports:
      - "8001:8001"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: cargo watch -x run

  query-intelligence:
    build:
      context: ../query-intelligence
      dockerfile: Dockerfile.dev
    volumes:
      - ../query-intelligence:/app
      - pip_cache:/root/.cache/pip
    environment:
      PYTHONUNBUFFERED: 1
      DATABASE_URL: ***********************************************/ccl_local
      REDIS_URL: redis://redis:6379
      VERTEX_AI_PROJECT: ccl-local
      VERTEX_AI_LOCATION: us-central1
      GOOGLE_APPLICATION_CREDENTIALS: /app/credentials/dev-key.json
      OTEL_EXPORTER_OTLP_ENDPOINT: http://otel-collector:4317
    ports:
      - "8002:8002"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: python -m uvicorn main:app --reload --host 0.0.0.0 --port 8002

  pattern-mining:
    build:
      context: ../pattern-mining
      dockerfile: Dockerfile.dev
    volumes:
      - ../pattern-mining:/app
      - pip_cache:/root/.cache/pip
    environment:
      PYTHONUNBUFFERED: 1
      DATABASE_URL: ***********************************************/ccl_local
      BIGQUERY_PROJECT: ccl-local
      BIGQUERY_DATASET: patterns
      GOOGLE_APPLICATION_CREDENTIALS: /app/credentials/dev-key.json
      OTEL_EXPORTER_OTLP_ENDPOINT: http://otel-collector:4317
    ports:
      - "8003:8003"
    depends_on:
      postgres:
        condition: service_healthy
    command: python -m uvicorn main:app --reload --host 0.0.0.0 --port 8003

  marketplace:
    build:
      context: ../marketplace
      dockerfile: Dockerfile.dev
    volumes:
      - ../marketplace:/app
      - go_cache:/go/pkg/mod
    environment:
      DATABASE_URL: ***********************************************/ccl_local
      SPANNER_EMULATOR_HOST: spanner-emulator:9010
      STORAGE_EMULATOR_HOST: http://storage-emulator:4443
      STRIPE_SECRET_KEY: sk_test_dummy
      OTEL_EXPORTER_OTLP_ENDPOINT: http://otel-collector:4317
    ports:
      - "8004:8004"
    depends_on:
      postgres:
        condition: service_healthy
      spanner-emulator:
        condition: service_started
    command: air

  web:
    build:
      context: ../web
      dockerfile: Dockerfile.dev
    volumes:
      - ../web:/app
      - node_modules:/app/node_modules
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:8000
      NEXT_PUBLIC_WS_URL: ws://localhost:8005
    ports:
      - "3001:3000"
    command: npm run dev

  # API Gateway (nginx)
  api-gateway:
    image: nginx:alpine
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "8000:80"
    depends_on:
      - analysis-engine
      - query-intelligence
      - pattern-mining
      - marketplace

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
  cargo_cache:
  target_cache:
  pip_cache:
  go_cache:
  node_modules:

networks:
  default:
    name: ccl-dev-network
```

### 2. Development Scripts
Create `scripts/dev/`:

#### A. `scripts/dev/setup.sh`
```bash
#!/bin/bash
set -euo pipefail

echo "🚀 Setting up CCL development environment..."

# Check prerequisites
check_prerequisites() {
    echo "Checking prerequisites..."
    
    # Docker
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker not installed. Please install Docker Desktop."
        exit 1
    fi
    
    # Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose not installed."
        exit 1
    fi
    
    # Languages
    for cmd in rustc go python3 node; do
        if ! command -v $cmd &> /dev/null; then
            echo "⚠️  $cmd not installed. Some features may not work."
        fi
    done
    
    echo "✅ Prerequisites check passed"
}

# Create necessary directories
create_directories() {
    echo "Creating directories..."
    mkdir -p docker/config
    mkdir -p docker/init-scripts/postgres
    mkdir -p docker/data/gcs
    mkdir -p credentials
    echo "✅ Directories created"
}

# Generate development certificates
generate_certs() {
    echo "Generating development certificates..."
    if [ ! -f credentials/dev-cert.pem ]; then
        openssl req -x509 -newkey rsa:4096 -nodes \
            -keyout credentials/dev-key.pem \
            -out credentials/dev-cert.pem \
            -days 365 \
            -subj "/C=US/ST=State/L=City/O=CCL/CN=*.ccl.local"
    fi
    echo "✅ Certificates ready"
}

# Setup local DNS
setup_local_dns() {
    echo "Setting up local DNS..."
    if ! grep -q "ccl.local" /etc/hosts; then
        echo "Adding CCL domains to /etc/hosts (requires sudo)..."
        sudo tee -a /etc/hosts << EOF

# CCL Development
127.0.0.1 api.ccl.local
127.0.0.1 app.ccl.local
127.0.0.1 storage.ccl.local
EOF
    fi
    echo "✅ Local DNS configured"
}

# Initialize databases
init_databases() {
    echo "Initializing databases..."
    
    # PostgreSQL init script
    cat > docker/init-scripts/postgres/01-init.sql << 'EOF'
-- Create schemas
CREATE SCHEMA IF NOT EXISTS analysis;
CREATE SCHEMA IF NOT EXISTS marketplace;
CREATE SCHEMA IF NOT EXISTS patterns;

-- Create user for each service
CREATE USER analysis_service WITH PASSWORD 'analysis_dev';
CREATE USER query_service WITH PASSWORD 'query_dev';
CREATE USER pattern_service WITH PASSWORD 'pattern_dev';
CREATE USER marketplace_service WITH PASSWORD 'marketplace_dev';

-- Grant permissions
GRANT ALL ON SCHEMA analysis TO analysis_service;
GRANT ALL ON SCHEMA marketplace TO marketplace_service;
GRANT ALL ON SCHEMA patterns TO pattern_service;

-- Create development data
INSERT INTO analysis.repositories (url, status) VALUES
    ('https://github.com/golang/go', 'pending'),
    ('https://github.com/rust-lang/rust', 'completed');
EOF
    
    echo "✅ Database initialization scripts created"
}

# Create configuration files
create_configs() {
    echo "Creating configuration files..."
    
    # Nginx config
    cat > docker/config/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream analysis {
        server analysis-engine:8001;
    }
    
    upstream query {
        server query-intelligence:8002;
    }
    
    upstream patterns {
        server pattern-mining:8003;
    }
    
    upstream marketplace {
        server marketplace:8004;
    }
    
    server {
        listen 80;
        server_name api.ccl.local;
        
        location /api/v1/analysis {
            proxy_pass http://analysis;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /api/v1/query {
            proxy_pass http://query;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /api/v1/patterns {
            proxy_pass http://patterns;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /api/v1/marketplace {
            proxy_pass http://marketplace;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
EOF

    # Prometheus config
    cat > docker/config/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'ccl-services'
    static_configs:
      - targets:
        - analysis-engine:8001
        - query-intelligence:8002
        - pattern-mining:8003
        - marketplace:8004
        labels:
          env: 'development'
EOF

    echo "✅ Configuration files created"
}

# Create development environment file
create_env_file() {
    echo "Creating .env.development file..."
    cat > .env.development << 'EOF'
# CCL Development Environment
ENV=development

# Database
DATABASE_URL=postgresql://ccl_dev:dev_password@localhost:5432/ccl_local

# Redis
REDIS_URL=redis://localhost:6379

# GCP Emulators
SPANNER_EMULATOR_HOST=localhost:9010
FIRESTORE_EMULATOR_HOST=localhost:8080
PUBSUB_EMULATOR_HOST=localhost:8085
STORAGE_EMULATOR_HOST=http://localhost:4443

# Service URLs
ANALYSIS_SERVICE_URL=http://localhost:8001
QUERY_SERVICE_URL=http://localhost:8002
PATTERN_SERVICE_URL=http://localhost:8003
MARKETPLACE_SERVICE_URL=http://localhost:8004

# Frontend
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_URL=http://localhost:3001

# Observability
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4317
JAEGER_UI_URL=http://localhost:16686
GRAFANA_URL=http://localhost:3000

# Development Keys (DO NOT USE IN PRODUCTION)
JWT_SECRET=dev_jwt_secret_change_me
STRIPE_SECRET_KEY=sk_test_dummy
VERTEX_AI_API_KEY=dummy_key_for_dev
EOF
    
    echo "✅ Environment file created"
}

# Main setup flow
main() {
    check_prerequisites
    create_directories
    generate_certs
    setup_local_dns
    init_databases
    create_configs
    create_env_file
    
    echo ""
    echo "✨ CCL development environment setup complete!"
    echo ""
    echo "To start the development environment:"
    echo "  ./scripts/dev/start.sh"
    echo ""
    echo "Access points:"
    echo "  - API Gateway: http://localhost:8000"
    echo "  - Web App: http://localhost:3001"
    echo "  - Grafana: http://localhost:3000 (admin/admin)"
    echo "  - Jaeger: http://localhost:16686"
    echo ""
}

main "$@"
```

#### B. `scripts/dev/start.sh`
```bash
#!/bin/bash
set -euo pipefail

echo "🚀 Starting CCL development environment..."

# Load environment
if [ -f .env.development ]; then
    export $(cat .env.development | grep -v '^#' | xargs)
fi

# Start infrastructure first
echo "Starting infrastructure services..."
docker-compose -f docker/docker-compose.yml up -d \
    postgres redis spanner-emulator firestore-emulator \
    pubsub-emulator storage-emulator \
    otel-collector jaeger prometheus grafana

# Wait for infrastructure
echo "Waiting for infrastructure to be ready..."
sleep 10

# Start CCL services
echo "Starting CCL services..."
docker-compose -f docker/docker-compose.yml up -d \
    analysis-engine query-intelligence pattern-mining \
    marketplace web api-gateway

# Show status
echo ""
echo "✅ CCL development environment is running!"
echo ""
docker-compose -f docker/docker-compose.yml ps

echo ""
echo "Logs: docker-compose -f docker/docker-compose.yml logs -f [service]"
echo "Stop: ./scripts/dev/stop.sh"
```

### 3. Development Tools
Create `tools/dev/`:

#### A. `tools/dev/seed-data.sh`
```bash
#!/bin/bash
# Seed development data

echo "Seeding development data..."

# Upload sample repositories
curl -X POST http://localhost:8001/api/v1/analysis \
    -H "Content-Type: application/json" \
    -d '{
        "repository_url": "https://github.com/golang/go",
        "branch": "master"
    }'

# Create sample patterns
curl -X POST http://localhost:8003/api/v1/patterns \
    -H "Content-Type: application/json" \
    -d '{
        "name": "Singleton Pattern",
        "category": "design_pattern",
        "language": "java",
        "confidence": 0.95
    }'

echo "✅ Development data seeded"
```

### 4. IDE Configuration
Create `tools/ide/`:

#### A. `tools/ide/vscode-settings.json`
```json
{
    "ccl.development": {
        "services": {
            "analysis-engine": {
                "language": "rust",
                "debugPort": 9001,
                "testCommand": "cargo test"
            },
            "query-intelligence": {
                "language": "python",
                "debugPort": 9002,
                "testCommand": "pytest"
            },
            "pattern-mining": {
                "language": "python",
                "debugPort": 9003,
                "testCommand": "pytest"
            },
            "marketplace": {
                "language": "go",
                "debugPort": 9004,
                "testCommand": "go test ./..."
            }
        },
        "launch": {
            "compounds": [
                {
                    "name": "CCL Full Stack",
                    "configurations": [
                        "Analysis Engine",
                        "Query Intelligence",
                        "Pattern Mining",
                        "Marketplace",
                        "Web Frontend"
                    ]
                }
            ]
        }
    },
    "files.associations": {
        "*.proto": "proto3",
        "Dockerfile.*": "dockerfile"
    },
    "editor.formatOnSave": true,
    "python.linting.enabled": true,
    "go.lintTool": "golangci-lint",
    "rust-analyzer.checkOnSave.command": "clippy"
}
```

### 5. Troubleshooting Guide
Create `docs/development/troubleshooting.md`:

```markdown
# Development Environment Troubleshooting

## Common Issues

### Port Conflicts
```bash
# Check what's using a port
lsof -i :8001

# Kill process using port
kill -9 $(lsof -t -i:8001)
```

### Docker Issues
```bash
# Clean up everything
docker-compose down -v
docker system prune -a

# Rebuild specific service
docker-compose build --no-cache analysis-engine
```

### Database Connection
```bash
# Test PostgreSQL connection
docker exec -it ccl_postgres psql -U ccl_dev -d ccl_local

# Reset database
docker-compose down -v postgres
docker-compose up -d postgres
```

### Service Not Starting
1. Check logs: `docker-compose logs -f [service-name]`
2. Verify dependencies are running
3. Check environment variables
4. Ensure ports are free

## Performance Optimization

### For M1/M2 Macs
Add to docker-compose.yml:
```yaml
services:
  analysis-engine:
    platform: linux/amd64  # Force x86 emulation
```

### Resource Limits
```yaml
services:
  query-intelligence:
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
```
```

## Validation Criteria

Your development environment is complete when:
- [ ] All services start with one command
- [ ] Hot reloading works for all languages
- [ ] Debugging works in VS Code
- [ ] Services can communicate locally
- [ ] GCP emulators function correctly
- [ ] Monitoring dashboards show data
- [ ] Development data is seeded
- [ ] Works on Mac, Linux, Windows

## Success Metrics
- Setup time: <15 minutes
- Start time: <2 minutes
- Resource usage: <8GB RAM
- All services accessible
- Zero external dependencies

Remember: A great development environment accelerates the entire team. Make it fast, reliable, and pleasant to use.