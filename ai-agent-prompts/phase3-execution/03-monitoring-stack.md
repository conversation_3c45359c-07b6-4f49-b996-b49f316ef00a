# AI Agent Prompt: Implement Monitoring & Observability Stack

## Task Overview
Design and implement a comprehensive monitoring and observability stack for the CCL platform that provides real-time insights into system health, performance, and user experience across all microservices.

## Context
CCL is a distributed microservices platform where visibility is critical. The monitoring stack must:
- Handle metrics, logs, and traces from 4 different languages
- Provide unified dashboards across all services
- Enable rapid troubleshooting of issues
- Support SLO/SLA tracking
- Scale with platform growth

## Files You MUST Read First

1. **Architecture & Requirements**:
   - `PRPs/reviews/master-enhancement-plan.md` - Monitoring requirements
   - `PRPs/implementation-guide.md` - Observability standards
   - `PLANNING.md` - Performance targets

2. **Service Specifications**:
   - `PRPs/services/analysis-engine.md` - Rust service metrics
   - `PRPs/services/query-intelligence.md` - Python service metrics
   - `PRPs/services/pattern-mining.md` - ML metrics needs
   - `PRPs/services/marketplace.md` - Commerce metrics

3. **Operational Requirements**:
   - `PRPs/review-enhancement-strategy.md` - Operational excellence

## Deliverables You Must Create

### 1. Monitoring Architecture
Create `infrastructure/monitoring/`:

#### A. `infrastructure/monitoring/architecture.md`
```markdown
# CCL Monitoring Architecture

## Overview
```mermaid
graph TB
    subgraph "Applications"
        A[Rust Services]
        B[Python Services]
        C[Go Services]
        D[TypeScript Services]
    end
    
    subgraph "Collection Layer"
        E[OpenTelemetry Collectors]
        F[Prometheus Exporters]
        G[Fluent Bit]
    end
    
    subgraph "Storage Layer"
        H[Google Cloud Monitoring]
        I[BigQuery Logs]
        J[Cloud Trace]
    end
    
    subgraph "Visualization"
        K[Grafana Dashboards]
        L[Custom UI]
        M[Alerts]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    E --> H
    E --> J
    G --> I
    H --> K
    I --> L
    J --> K
```

## Components
- **Metrics**: Prometheus format → Cloud Monitoring
- **Logs**: Structured JSON → BigQuery
- **Traces**: OpenTelemetry → Cloud Trace
- **Dashboards**: Grafana + Custom UI
- **Alerts**: PagerDuty integration
```

### 2. OpenTelemetry Configuration
Create `infrastructure/monitoring/opentelemetry/`:

#### A. `infrastructure/monitoring/opentelemetry/collector-config.yaml`
```yaml
receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318
  
  prometheus:
    config:
      scrape_configs:
        - job_name: 'ccl-services'
          kubernetes_sd_configs:
            - role: pod
          relabel_configs:
            - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
              action: keep
              regex: true

processors:
  batch:
    timeout: 10s
    send_batch_size: 1024
  
  memory_limiter:
    check_interval: 1s
    limit_mib: 512
    spike_limit_mib: 128
  
  resource:
    attributes:
      - key: environment
        value: ${ENV}
        action: insert
      - key: service.namespace
        value: ccl
        action: insert
  
  filter/errors:
    error_mode: ignore
    traces:
      span:
        - 'attributes["http.status_code"] >= 500'

exporters:
  googlecloud:
    project: ccl-platform-${ENV}
    retry_on_failure:
      enabled: true
      initial_interval: 5s
      max_interval: 30s
    
  googlemanagedprometheus:
    project: ccl-platform-${ENV}
    
  bigquery:
    project: ccl-platform-${ENV}
    dataset: monitoring_logs
    table: service_logs

service:
  pipelines:
    traces:
      receivers: [otlp]
      processors: [memory_limiter, batch, resource]
      exporters: [googlecloud]
    
    metrics:
      receivers: [otlp, prometheus]
      processors: [memory_limiter, batch, resource]
      exporters: [googlemanagedprometheus]
    
    logs:
      receivers: [otlp]
      processors: [memory_limiter, batch, resource]
      exporters: [bigquery]
```

### 3. Service Instrumentation
Create `infrastructure/monitoring/instrumentation/`:

#### A. `infrastructure/monitoring/instrumentation/rust-instrumentation.rs`
```rust
// Instrumentation for Rust services
use opentelemetry::{global, sdk::propagation::TraceContextPropagator};
use opentelemetry_otlp::WithExportConfig;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

pub fn init_telemetry(service_name: &str) -> Result<(), Box<dyn std::error::Error>> {
    global::set_text_map_propagator(TraceContextPropagator::new());
    
    let tracer = opentelemetry_otlp::new_pipeline()
        .tracing()
        .with_exporter(
            opentelemetry_otlp::new_exporter()
                .tonic()
                .with_endpoint("http://otel-collector:4317")
        )
        .with_service_name(service_name)
        .install_batch(opentelemetry::runtime::Tokio)?;
    
    let telemetry = tracing_opentelemetry::layer().with_tracer(tracer);
    let fmt_layer = tracing_subscriber::fmt::layer()
        .json()
        .with_current_span(true)
        .with_span_list(true);
    
    tracing_subscriber::registry()
        .with(telemetry)
        .with(fmt_layer)
        .with(tracing_subscriber::EnvFilter::from_default_env())
        .init();
    
    Ok(())
}

// Metrics helpers
use prometheus::{register_histogram_vec, register_counter_vec, HistogramVec, CounterVec};

lazy_static! {
    pub static ref HTTP_REQUEST_DURATION: HistogramVec = register_histogram_vec!(
        "http_request_duration_seconds",
        "HTTP request latencies",
        &["method", "endpoint", "status"]
    ).unwrap();
    
    pub static ref HTTP_REQUEST_COUNT: CounterVec = register_counter_vec!(
        "http_requests_total",
        "Total HTTP requests",
        &["method", "endpoint", "status"]
    ).unwrap();
}
```

#### B. `infrastructure/monitoring/instrumentation/python-instrumentation.py`
```python
# Instrumentation for Python services
from opentelemetry import trace, metrics
from opentelemetry.exporter.otlp.proto.grpc import (
    trace_exporter,
    metric_exporter
)
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.metrics import MeterProvider
import logging
import json
from pythonjsonlogger import jsonlogger

def init_telemetry(service_name: str, version: str):
    """Initialize OpenTelemetry for Python services"""
    
    # Configure structured logging
    logHandler = logging.StreamHandler()
    formatter = jsonlogger.JsonFormatter(
        fmt="%(asctime)s %(levelname)s %(name)s %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    logHandler.setFormatter(formatter)
    logging.root.addHandler(logHandler)
    logging.root.setLevel(logging.INFO)
    
    # Configure tracing
    resource = Resource.create({
        "service.name": service_name,
        "service.version": version,
        "service.namespace": "ccl"
    })
    
    trace.set_tracer_provider(TracerProvider(resource=resource))
    tracer = trace.get_tracer(__name__)
    
    otlp_exporter = trace_exporter.OTLPSpanExporter(
        endpoint="otel-collector:4317",
        insecure=True
    )
    
    span_processor = BatchSpanProcessor(otlp_exporter)
    trace.get_tracer_provider().add_span_processor(span_processor)
    
    # Configure metrics
    metric_reader = PeriodicExportingMetricReader(
        exporter=metric_exporter.OTLPMetricExporter(
            endpoint="otel-collector:4317",
            insecure=True
        ),
        export_interval_millis=10000
    )
    
    metrics.set_meter_provider(
        MeterProvider(resource=resource, metric_readers=[metric_reader])
    )
    
    # Auto-instrument libraries
    FastAPIInstrumentor.instrument()
    RequestsInstrumentor.instrument()
    SQLAlchemyInstrumentor.instrument()
    
    return tracer

# Custom metrics
meter = metrics.get_meter(__name__)

query_latency = meter.create_histogram(
    name="query_processing_duration",
    description="Query processing latency",
    unit="ms"
)

pattern_detection_counter = meter.create_counter(
    name="patterns_detected_total",
    description="Total patterns detected",
    unit="1"
)
```

#### C. Similar files for Go and TypeScript

### 4. Dashboards
Create `infrastructure/monitoring/dashboards/`:

#### A. `infrastructure/monitoring/dashboards/service-overview.json`
```json
{
  "dashboard": {
    "title": "CCL Service Overview",
    "panels": [
      {
        "title": "Request Rate",
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
        "targets": [{
          "expr": "sum(rate(http_requests_total[5m])) by (service)",
          "legendFormat": "{{service}}"
        }]
      },
      {
        "title": "Error Rate",
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
        "targets": [{
          "expr": "sum(rate(http_requests_total{status=~\"5..\"}[5m])) by (service) / sum(rate(http_requests_total[5m])) by (service)",
          "legendFormat": "{{service}}"
        }]
      },
      {
        "title": "P95 Latency",
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8},
        "targets": [{
          "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (service, le))",
          "legendFormat": "{{service}}"
        }]
      },
      {
        "title": "Service Health",
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8},
        "type": "stat",
        "targets": [{
          "expr": "up{job=\"ccl-services\"}",
          "legendFormat": "{{service}}"
        }]
      }
    ]
  }
}
```

#### B. Create similar dashboards for:
- `repository-analysis-dashboard.json`
- `query-intelligence-dashboard.json`
- `pattern-detection-dashboard.json`
- `marketplace-dashboard.json`
- `slo-dashboard.json`

### 5. Alerting Rules
Create `infrastructure/monitoring/alerts/`:

#### A. `infrastructure/monitoring/alerts/service-alerts.yaml`
```yaml
groups:
  - name: service_health
    interval: 30s
    rules:
      - alert: ServiceDown
        expr: up{job="ccl-services"} == 0
        for: 2m
        labels:
          severity: critical
          team: platform
        annotations:
          summary: "Service {{ $labels.service }} is down"
          description: "{{ $labels.service }} has been down for more than 2 minutes"
      
      - alert: HighErrorRate
        expr: |
          sum(rate(http_requests_total{status=~"5.."}[5m])) by (service)
          /
          sum(rate(http_requests_total[5m])) by (service)
          > 0.05
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "High error rate for {{ $labels.service }}"
          description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.service }}"
      
      - alert: HighLatency
        expr: |
          histogram_quantile(0.95,
            sum(rate(http_request_duration_seconds_bucket[5m])) by (service, le)
          ) > 0.5
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "High latency for {{ $labels.service }}"
          description: "P95 latency is {{ $value }}s for {{ $labels.service }}"

  - name: infrastructure
    interval: 30s
    rules:
      - alert: HighMemoryUsage
        expr: |
          container_memory_usage_bytes{pod=~"ccl-.*"}
          /
          container_spec_memory_limit_bytes{pod=~"ccl-.*"}
          > 0.9
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "High memory usage for {{ $labels.pod }}"
          description: "Memory usage is {{ $value | humanizePercentage }}"
      
      - alert: PodCrashLooping
        expr: rate(kube_pod_container_status_restarts_total{pod=~"ccl-.*"}[15m]) > 0
        for: 5m
        labels:
          severity: critical
          team: platform
        annotations:
          summary: "Pod {{ $labels.pod }} is crash looping"
          description: "Pod has restarted {{ $value }} times in the last 15 minutes"
```

### 6. SLO Configuration
Create `infrastructure/monitoring/slos/`:

#### A. `infrastructure/monitoring/slos/slo-definitions.yaml`
```yaml
slos:
  - name: api-availability
    service: all
    sli:
      type: availability
      good_events: 'http_requests_total{status!~"5.."}'
      total_events: 'http_requests_total'
    objectives:
      - target: 0.999
        window: 30d
      - target: 0.995
        window: 7d
    
  - name: api-latency
    service: all
    sli:
      type: latency
      threshold: 100ms
      metric: 'http_request_duration_seconds'
    objectives:
      - target: 0.95
        window: 30d
    
  - name: query-intelligence-accuracy
    service: query-intelligence
    sli:
      type: custom
      good_events: 'queries_total{confidence>="0.8"}'
      total_events: 'queries_total'
    objectives:
      - target: 0.85
        window: 30d
```

### 7. Logging Configuration
Create `infrastructure/monitoring/logging/`:

#### A. `infrastructure/monitoring/logging/fluent-bit-config.yaml`
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-config
data:
  fluent-bit.conf: |
    [SERVICE]
        Flush         5
        Log_Level     info
        Daemon        off
        
    [INPUT]
        Name              tail
        Path              /var/log/containers/ccl-*.log
        Parser            docker
        Tag               kube.*
        Refresh_Interval  5
        
    [FILTER]
        Name                kubernetes
        Match               kube.*
        Kube_URL            https://kubernetes.default.svc:443
        Kube_CA_File        /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        Kube_Token_File     /var/run/secrets/kubernetes.io/serviceaccount/token
        Merge_Log           On
        K8S-Logging.Parser  On
        K8S-Logging.Exclude On
        
    [FILTER]
        Name                parser
        Match               kube.*
        Key_Name            log
        Parser              json
        Reserve_Data        On
        
    [OUTPUT]
        Name                  bigquery
        Match                 *
        project_id            ccl-platform-${ENV}
        dataset_id            logs
        table_id              application_logs
        google_service_credentials /etc/google/credentials/key.json
```

### 8. Documentation
Create `docs/monitoring/`:

#### A. `docs/monitoring/runbooks/high-error-rate.md`
```markdown
# Runbook: High Error Rate Alert

## Alert Description
The service is experiencing an error rate above 5%.

## Investigation Steps

1. **Check Recent Deployments**
   ```bash
   kubectl rollout history deployment/[service-name]
   ```

2. **View Error Logs**
   ```sql
   SELECT timestamp, message, error_details
   FROM `ccl-platform.logs.application_logs`
   WHERE service_name = '[service]'
     AND severity = 'ERROR'
     AND timestamp > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
   ORDER BY timestamp DESC
   LIMIT 100
   ```

3. **Check Dependencies**
   - Verify database connectivity
   - Check external API availability
   - Review service mesh health

4. **Examine Traces**
   - Open Cloud Trace
   - Filter by service and error status
   - Identify failing operations

## Remediation Steps

1. **Quick Fixes**
   - Restart pods if memory/connection issues
   - Scale up if under load
   - Enable circuit breakers

2. **Rollback if Needed**
   ```bash
   kubectl rollout undo deployment/[service-name]
   ```

3. **Long-term Fixes**
   - Fix root cause in code
   - Add better error handling
   - Improve test coverage
```

## Validation Criteria

Your monitoring stack is complete when:
- [ ] All services export metrics, logs, and traces
- [ ] Dashboards show real-time service health
- [ ] Alerts fire for critical issues
- [ ] SLOs are tracked automatically
- [ ] Runbooks exist for all alerts
- [ ] Logs are searchable and retained
- [ ] Traces show full request flow
- [ ] Cost is optimized (<$5k/month)

## Success Metrics
- Mean time to detection: <1 minute
- Mean time to resolution: <30 minutes
- Dashboard load time: <2 seconds
- Log search speed: <5 seconds
- Alert accuracy: >95% (low false positives)

Remember: Observability is not optional in microservices. This stack must provide instant visibility into any issue.