# CCL Platform Makefile
# Common development commands

.PHONY: help setup start stop restart logs health seed test lint format clean

# Default target
help:
	@echo "CCL Platform Development Commands"
	@echo "================================="
	@echo "Setup & Environment:"
	@echo "  make setup          - Set up development environment"
	@echo "  make start          - Start all services"
	@echo "  make stop           - Stop all services"
	@echo "  make restart        - Restart all services"
	@echo "  make reset          - Reset environment (data loss!)"
	@echo ""
	@echo "Development:"
	@echo "  make logs           - View service logs"
	@echo "  make health         - Check service health"
	@echo "  make seed           - Seed development data"
	@echo "  make shell SVC=name - Shell into service container"
	@echo ""
	@echo "Code Quality:"
	@echo "  make test           - Run all tests"
	@echo "  make lint           - Run all linters"
	@echo "  make format         - Format all code"
	@echo "  make security       - Run security scans"
	@echo ""
	@echo "Service Management:"
	@echo "  make build          - Build all services"
	@echo "  make build-svc SVC=name - Build specific service"
	@echo "  make up SVC=name    - Start specific service"
	@echo "  make down SVC=name  - Stop specific service"
	@echo ""
	@echo "Database:"
	@echo "  make db-shell       - PostgreSQL shell"
	@echo "  make redis-cli      - Redis CLI"
	@echo "  make migrate        - Run database migrations"
	@echo ""
	@echo "Monitoring:"
	@echo "  make grafana        - Open Grafana dashboard"
	@echo "  make jaeger         - Open Jaeger UI"
	@echo "  make prometheus     - Open Prometheus"

# Setup development environment
setup:
	@echo "Setting up CCL development environment..."
	@./scripts/dev/setup.sh

# Start services
start:
	@echo "Starting CCL services..."
	@./scripts/dev/start.sh

# Stop services
stop:
	@echo "Stopping CCL services..."
	@./scripts/dev/stop.sh

# Restart services
restart: stop start

# Reset environment
reset:
	@echo "Resetting CCL environment..."
	@./scripts/dev/reset.sh

# View logs
logs:
	@docker-compose -f docker/docker-compose.yml logs -f $(SVC)

# Health check
health:
	@./scripts/dev/health-check.sh

# Seed data
seed:
	@./scripts/dev/seed-data.sh

# Shell into service
shell:
ifndef SVC
	@echo "Error: Please specify service with SVC=name"
	@exit 1
endif
	@docker-compose -f docker/docker-compose.yml exec $(SVC) /bin/sh

# Build all services
build:
	@echo "Building all services..."
	@docker-compose -f docker/docker-compose.yml build

# Build specific service
build-svc:
ifndef SVC
	@echo "Error: Please specify service with SVC=name"
	@exit 1
endif
	@docker-compose -f docker/docker-compose.yml build $(SVC)

# Start specific service
up:
ifndef SVC
	@echo "Error: Please specify service with SVC=name"
	@exit 1
endif
	@docker-compose -f docker/docker-compose.yml up -d $(SVC)

# Stop specific service
down:
ifndef SVC
	@echo "Error: Please specify service with SVC=name"
	@exit 1
endif
	@docker-compose -f docker/docker-compose.yml stop $(SVC)

# Run all tests
test:
	@echo "Running all tests..."
	@make test-rust
	@make test-python
	@make test-go
	@make test-typescript

# Test Rust services
test-rust:
	@echo "Testing Rust services..."
	@cd services/analysis-engine && cargo test

# Test Python services
test-python:
	@echo "Testing Python services..."
	@cd services/query-intelligence && pytest
	@cd services/pattern-mining && pytest

# Test Go services
test-go:
	@echo "Testing Go services..."
	@cd services/marketplace && go test ./...

# Test TypeScript services
test-typescript:
	@echo "Testing TypeScript services..."
	@cd services/web && npm test
	@cd services/collaboration && npm test

# Run all linters
lint:
	@echo "Running linters..."
	@make lint-rust
	@make lint-python
	@make lint-go
	@make lint-typescript

# Lint Rust
lint-rust:
	@echo "Linting Rust code..."
	@cd services/analysis-engine && cargo clippy -- -D warnings

# Lint Python
lint-python:
	@echo "Linting Python code..."
	@cd services/query-intelligence && ruff check . && mypy .
	@cd services/pattern-mining && ruff check . && mypy .

# Lint Go
lint-go:
	@echo "Linting Go code..."
	@cd services/marketplace && golangci-lint run

# Lint TypeScript
lint-typescript:
	@echo "Linting TypeScript code..."
	@cd services/web && npm run lint
	@cd services/collaboration && npm run lint

# Format all code
format:
	@echo "Formatting code..."
	@make format-rust
	@make format-python
	@make format-go
	@make format-typescript

# Format Rust
format-rust:
	@cd services/analysis-engine && cargo fmt

# Format Python
format-python:
	@cd services/query-intelligence && black .
	@cd services/pattern-mining && black .

# Format Go
format-go:
	@cd services/marketplace && go fmt ./...

# Format TypeScript
format-typescript:
	@cd services/web && npm run format
	@cd services/collaboration && npm run format

# Security scan
security:
	@echo "Running security scans..."
	@cd services/analysis-engine && cargo audit
	@cd services/query-intelligence && pip-audit
	@cd services/marketplace && gosec ./...
	@cd services/web && npm audit

# Database shell
db-shell:
	@PGPASSWORD=dev_password psql -h localhost -U ccl_dev -d ccl_local

# Redis CLI
redis-cli:
	@redis-cli -h localhost -p 6379

# Run migrations
migrate:
	@echo "Running database migrations..."
	@docker-compose -f docker/docker-compose.yml exec postgres psql -U ccl_dev -d ccl_local -f /docker-entrypoint-initdb.d/01-init.sql

# Open Grafana
grafana:
	@open http://localhost:3000 || xdg-open http://localhost:3000

# Open Jaeger
jaeger:
	@open http://localhost:16686 || xdg-open http://localhost:16686

# Open Prometheus
prometheus:
	@open http://localhost:9090 || xdg-open http://localhost:9090

# Clean up
clean:
	@echo "Cleaning up..."
	@docker-compose -f docker/docker-compose.yml down -v
	@docker system prune -f
	@find . -name "__pycache__" -type d -exec rm -rf {} +
	@find . -name "*.pyc" -delete
	@find . -name ".pytest_cache" -type d -exec rm -rf {} +
	@find . -name "node_modules" -type d -exec rm -rf {} +
	@find . -name ".next" -type d -exec rm -rf {} +
	@find . -name "target" -type d -exec rm -rf {} +

# Development shortcuts
dev: start logs

# Quick restart with logs
dev-restart: restart logs

# Full test suite
test-all: lint test security

# Install git hooks
install-hooks:
	@echo "Installing git hooks..."
	@cp scripts/git-hooks/* .git/hooks/
	@chmod +x .git/hooks/*

# Check everything
check: lint test security health

# Update dependencies
update-deps:
	@echo "Updating dependencies..."
	@cd services/analysis-engine && cargo update
	@cd services/query-intelligence && pip install --upgrade -r requirements.txt
	@cd services/pattern-mining && pip install --upgrade -r requirements.txt
	@cd services/marketplace && go get -u ./...
	@cd services/web && npm update
	@cd services/collaboration && npm update