{
  "version": "2.0.0",
  "tasks": [
    // Development environment tasks
    {
      "label": "Start Development Environment",
      "type": "shell",
      "command": "./scripts/dev/start.sh",
      "group": {
        "kind": "build",
        "isDefault": true
      },
      "presentation": {
        "reveal": "always",
        "panel": "new"
      },
      "problemMatcher": []
    },
    {
      "label": "Stop Development Environment",
      "type": "shell",
      "command": "./scripts/dev/stop.sh",
      "presentation": {
        "reveal": "always",
        "panel": "new"
      },
      "problemMatcher": []
    },
    {
      "label": "Health Check",
      "type": "shell",
      "command": "./scripts/dev/health-check.sh",
      "presentation": {
        "reveal": "always",
        "panel": "new"
      },
      "problemMatcher": []
    },
    {
      "label": "Seed Development Data",
      "type": "shell",
      "command": "./scripts/dev/seed-data.sh",
      "presentation": {
        "reveal": "always",
        "panel": "new"
      },
      "problemMatcher": []
    },
    
    // Service-specific tasks
    {
      "label": "Test: Analysis Engine",
      "type": "shell",
      "command": "cargo test",
      "options": {
        "cwd": "${workspaceFolder}/services/analysis-engine"
      },
      "group": "test",
      "problemMatcher": ["$rustc"]
    },
    {
      "label": "Test: Query Intelligence",
      "type": "shell",
      "command": "pytest",
      "options": {
        "cwd": "${workspaceFolder}/services/query-intelligence"
      },
      "group": "test",
      "problemMatcher": []
    },
    {
      "label": "Test: Pattern Mining",
      "type": "shell",
      "command": "pytest",
      "options": {
        "cwd": "${workspaceFolder}/services/pattern-mining"
      },
      "group": "test",
      "problemMatcher": []
    },
    {
      "label": "Test: Marketplace",
      "type": "shell",
      "command": "go test ./...",
      "options": {
        "cwd": "${workspaceFolder}/services/marketplace"
      },
      "group": "test",
      "problemMatcher": ["$go"]
    },
    {
      "label": "Test: Web",
      "type": "shell",
      "command": "npm test",
      "options": {
        "cwd": "${workspaceFolder}/services/web"
      },
      "group": "test",
      "problemMatcher": ["$eslint-stylish"]
    },
    
    // Linting tasks
    {
      "label": "Lint: All Services",
      "dependsOn": [
        "Lint: Rust",
        "Lint: Python",
        "Lint: Go",
        "Lint: TypeScript"
      ],
      "group": "test",
      "problemMatcher": []
    },
    {
      "label": "Lint: Rust",
      "type": "shell",
      "command": "cargo clippy -- -D warnings",
      "options": {
        "cwd": "${workspaceFolder}/services/analysis-engine"
      },
      "problemMatcher": ["$rustc"]
    },
    {
      "label": "Lint: Python",
      "type": "shell",
      "command": "ruff check . && mypy .",
      "options": {
        "cwd": "${workspaceFolder}/services"
      },
      "problemMatcher": []
    },
    {
      "label": "Lint: Go",
      "type": "shell",
      "command": "golangci-lint run",
      "options": {
        "cwd": "${workspaceFolder}/services/marketplace"
      },
      "problemMatcher": ["$go"]
    },
    {
      "label": "Lint: TypeScript",
      "type": "shell",
      "command": "npm run lint",
      "options": {
        "cwd": "${workspaceFolder}/services/web"
      },
      "problemMatcher": ["$eslint-stylish"]
    },
    
    // Docker tasks
    {
      "label": "Docker: View Logs",
      "type": "shell",
      "command": "docker-compose -f docker/docker-compose.yml logs -f",
      "presentation": {
        "reveal": "always",
        "panel": "new"
      },
      "problemMatcher": []
    },
    {
      "label": "Docker: Rebuild Services",
      "type": "shell",
      "command": "docker-compose -f docker/docker-compose.yml build",
      "presentation": {
        "reveal": "always",
        "panel": "new"
      },
      "problemMatcher": []
    },
    {
      "label": "Docker: Clean Up",
      "type": "shell",
      "command": "docker system prune -f",
      "presentation": {
        "reveal": "always",
        "panel": "new"
      },
      "problemMatcher": []
    },
    
    // Database tasks
    {
      "label": "Database: Connect PostgreSQL",
      "type": "shell",
      "command": "PGPASSWORD=dev_password psql -h localhost -U ccl_dev -d ccl_local",
      "presentation": {
        "reveal": "always",
        "panel": "new"
      },
      "problemMatcher": []
    },
    {
      "label": "Database: Connect Redis",
      "type": "shell",
      "command": "redis-cli",
      "presentation": {
        "reveal": "always",
        "panel": "new"
      },
      "problemMatcher": []
    },
    
    // Monitoring tasks
    {
      "label": "Open: Grafana Dashboard",
      "type": "shell",
      "command": "open http://localhost:3000 || xdg-open http://localhost:3000",
      "problemMatcher": []
    },
    {
      "label": "Open: Jaeger UI",
      "type": "shell",
      "command": "open http://localhost:16686 || xdg-open http://localhost:16686",
      "problemMatcher": []
    },
    {
      "label": "Open: Prometheus",
      "type": "shell",
      "command": "open http://localhost:9090 || xdg-open http://localhost:9090",
      "problemMatcher": []
    }
  ]
}