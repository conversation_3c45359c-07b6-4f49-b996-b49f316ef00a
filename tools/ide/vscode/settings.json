{
  "ccl.development": {
    "services": {
      "analysis-engine": {
        "language": "rust",
        "port": 8001,
        "debugPort": 9001,
        "testCommand": "cargo test",
        "lintCommand": "cargo clippy",
        "formatCommand": "cargo fmt"
      },
      "query-intelligence": {
        "language": "python",
        "port": 8002,
        "debugPort": 9002,
        "testCommand": "pytest",
        "lintCommand": "ruff check",
        "formatCommand": "black ."
      },
      "pattern-mining": {
        "language": "python",
        "port": 8003,
        "debugPort": 9003,
        "testCommand": "pytest",
        "lintCommand": "ruff check",
        "formatCommand": "black ."
      },
      "marketplace": {
        "language": "go",
        "port": 8004,
        "debugPort": 9004,
        "testCommand": "go test ./...",
        "lintCommand": "golangci-lint run",
        "formatCommand": "go fmt ./..."
      },
      "collaboration": {
        "language": "typescript",
        "port": 8005,
        "debugPort": 9005,
        "testCommand": "npm test",
        "lintCommand": "npm run lint",
        "formatCommand": "npm run format"
      },
      "web": {
        "language": "typescript",
        "port": 3001,
        "debugPort": 9006,
        "testCommand": "npm test",
        "lintCommand": "npm run lint",
        "formatCommand": "npm run format"
      }
    }
  },
  // File associations
  "files.associations": {
    "*.proto": "proto3",
    "Dockerfile.*": "dockerfile",
    "*.sql": "sql",
    "*.md": "markdown"
  },
  // Editor settings - Fixed to prevent text selection issues
  "editor.formatOnSave": false,
  "editor.formatOnType": false,
  "editor.formatOnPaste": false,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "never",
    "source.organizeImports": "never"
  },
  "editor.quickSuggestions": {
    "other": true,
    "comments": false,
    "strings": false
  },
  "editor.acceptSuggestionOnCommitCharacter": false,
  "editor.acceptSuggestionOnEnter": "smart",
  "editor.rulers": [
    80,
    120
  ],
  "editor.tabSize": 2,
  "editor.accessibilitySupport": "off",
  "editor.selectionHighlight": false,
  "editor.smartSelect.selectLeadingAndTrailingWhitespace": false,
  "editor.smartSelect.selectSubwords": false,
  "editor.wordBasedSuggestions": false,
  "editor.find.autoFindInSelection": "never",
  "editor.suggest.snippetsPreventQuickSuggestions": false,
  "editor.hover.enabled": false,
  "editor.links": false,
  // Language-specific settings - disabled auto-formatting
  "[rust]": {
    "editor.tabSize": 4,
    "editor.defaultFormatter": "rust-lang.rust-analyzer",
    "editor.formatOnSave": false
  },
  "[python]": {
    "editor.tabSize": 4,
    "editor.defaultFormatter": "ms-python.black-formatter",
    "editor.formatOnSave": false
  },
  "[go]": {
    "editor.tabSize": 4,
    "editor.insertSpaces": false,
    "editor.defaultFormatter": "golang.go",
    "editor.formatOnSave": false
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": false
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": false
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": false
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": false
  },
  "[yaml]": {
    "editor.tabSize": 2,
    "editor.defaultFormatter": "redhat.vscode-yaml",
    "editor.formatOnSave": false
  },
  // Python settings - disabled aggressive linting
  "python.linting.enabled": false,
  "python.linting.pylintEnabled": false,
  "python.linting.flake8Enabled": false,
  "python.formatting.provider": "black",
  "python.testing.pytestEnabled": true,
  "python.defaultInterpreterPath": "./venv/bin/python",
  // Go settings - reduced aggressiveness
  "go.useLanguageServer": true,
  "go.lintTool": "golangci-lint",
  "go.lintFlags": [
    "--fast"
  ],
  "go.testFlags": [
    "-v"
  ],
  "go.testTimeout": "30s",
  "go.formatTool": "goimports",
  "go.lintOnSave": "off",
  // Rust settings - reduced inline hints
  "rust-analyzer.cargo.features": "all",
  "rust-analyzer.checkOnSave.command": "clippy",
  "rust-analyzer.inlayHints.enable": false,
  "rust-analyzer.lens.enable": false,
  // TypeScript settings
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.updateImportsOnFileMove.enabled": "prompt",
  "typescript.suggest.autoImports": false,
  // ESLint settings - disabled auto-fix
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "eslint.autoFixOnSave": false,
  "eslint.run": "onSave",
  // Docker settings
  "docker.defaultRegistryPath": "gcr.io/ccl-platform",
  // Terminal settings
  "terminal.integrated.defaultProfile.linux": "bash",
  "terminal.integrated.defaultProfile.osx": "zsh",
  "terminal.integrated.env.linux": {
    "CCL_ENV": "development"
  },
  "terminal.integrated.env.osx": {
    "CCL_ENV": "development"
  },
  // Search exclusions
  "search.exclude": {
    "**/node_modules": true,
    "**/bower_components": true,
    "**/*.code-search": true,
    "**/target": true,
    "**/__pycache__": true,
    "**/.pytest_cache": true,
    "**/venv": true,
    "**/.venv": true,
    "**/dist": true,
    "**/build": true,
    "**/.next": true
  },
  // File exclusions
  "files.exclude": {
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true,
    "**/__pycache__": true,
    "**/.pytest_cache": true
  },
  // Git settings
  "git.autofetch": true,
  "git.confirmSync": false,
  // Better Comments
  "better-comments.tags": [
    {
      "tag": "!",
      "color": "#FF2D00",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "?",
      "color": "#3498DB",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "//",
      "color": "#474747",
      "strikethrough": true,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "todo",
      "color": "#FF8C00",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "*",
      "color": "#98C379",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    }
  ]
}
