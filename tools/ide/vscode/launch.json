{"version": "0.2.0", "configurations": [{"name": "Analysis Engine (Rust)", "type": "lldb", "request": "launch", "cargo": {"args": ["build", "--bin=analysis-engine", "--package=analysis-engine"], "filter": {"name": "analysis-engine", "kind": "bin"}}, "args": [], "cwd": "${workspaceFolder}/services/analysis-engine", "env": {"RUST_LOG": "debug", "DATABASE_URL": "postgresql://ccl_dev:dev_password@localhost:5432/ccl_local", "REDIS_URL": "redis://localhost:6379"}}, {"name": "Query Intelligence (Python)", "type": "python", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["main:app", "--reload", "--port", "8002"], "cwd": "${workspaceFolder}/services/query-intelligence", "env": {"PYTHONPATH": "${workspaceFolder}/services/query-intelligence", "DATABASE_URL": "postgresql://ccl_dev:dev_password@localhost:5432/ccl_local", "REDIS_URL": "redis://localhost:6379"}, "console": "integratedTerminal"}, {"name": "Pattern Mining (Python)", "type": "python", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["main:app", "--reload", "--port", "8003"], "cwd": "${workspaceFolder}/services/pattern-mining", "env": {"PYTHONPATH": "${workspaceFolder}/services/pattern-mining", "DATABASE_URL": "postgresql://ccl_dev:dev_password@localhost:5432/ccl_local"}, "console": "integratedTerminal"}, {"name": "Marketplace (Go)", "type": "go", "request": "launch", "mode": "auto", "program": "${workspaceFolder}/services/marketplace", "env": {"DATABASE_URL": "postgresql://ccl_dev:dev_password@localhost:5432/ccl_local", "PORT": "8004"}, "args": []}, {"name": "Collaboration (Node.js)", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "cwd": "${workspaceFolder}/services/collaboration", "env": {"NODE_ENV": "development", "PORT": "8005", "DATABASE_URL": "postgresql://ccl_dev:dev_password@localhost:5432/ccl_local", "REDIS_URL": "redis://localhost:6379"}, "console": "integratedTerminal"}, {"name": "Web (Next.js)", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "cwd": "${workspaceFolder}/services/web", "env": {"NODE_ENV": "development", "NEXT_PUBLIC_API_URL": "http://localhost:8000", "NEXT_PUBLIC_WS_URL": "ws://localhost:8005"}, "serverReadyAction": {"pattern": "started server on .+, url: (https?://.+)", "uriFormat": "%s", "action": "openExternally"}, "console": "integratedTerminal"}, {"name": "Docker: Attach to Node", "type": "node", "request": "attach", "port": 9229, "address": "localhost", "localRoot": "${workspaceFolder}", "remoteRoot": "/app", "protocol": "inspector", "restart": true}, {"name": "Docker: Attach to Python", "type": "python", "request": "attach", "port": 5678, "host": "localhost", "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "/app"}]}], "compounds": [{"name": "All Services", "configurations": ["Analysis Engine (Rust)", "Query Intelligence (Python)", "Pattern Mining (Python)", "Marketplace (Go)", "Collaboration (Node.js)", "Web (Next.js)"], "stopAll": true}, {"name": "Backend Services", "configurations": ["Analysis Engine (Rust)", "Query Intelligence (Python)", "Pattern Mining (Python)", "Marketplace (Go)"], "stopAll": true}, {"name": "Frontend Services", "configurations": ["Collaboration (Node.js)", "Web (Next.js)"], "stopAll": true}]}