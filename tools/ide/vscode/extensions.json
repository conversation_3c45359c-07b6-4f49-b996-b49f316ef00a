{
  "recommendations": [
    // Rust
    "rust-lang.rust-analyzer",
    "vadimcn.vscode-lldb",
    "serayuzgur.crates",
    "tamasfe.even-better-toml",
    
    // Python
    "ms-python.python",
    "ms-python.vscode-pylance",
    "ms-python.black-formatter",
    "charliermarsh.ruff",
    "ms-python.debugpy",
    
    // Go
    "golang.go",
    
    // TypeScript/JavaScript
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "bradlc.vscode-tailwindcss",
    "dsznajder.es7-react-js-snippets",
    "prisma.prisma",
    
    // Docker
    "ms-azuretools.vscode-docker",
    "ms-vscode-remote.remote-containers",
    
    // Google Cloud
    "googlecloudtools.cloudcode",
    
    // Database
    "mtxr.sqltools",
    "mtxr.sqltools-driver-pg",
    
    // Git
    "eamodio.gitlens",
    "mhutchie.git-graph",
    "donjayamanne.githistory",
    
    // API Development
    "humao.rest-client",
    "42crunch.vscode-openapi",
    "gruntfuggly.todo-tree",
    
    // YAML/JSON
    "redhat.vscode-yaml",
    "zainchen.json",
    
    // Markdown
    "yzhang.markdown-all-in-one",
    "davidanson.vscode-markdownlint",
    "bierner.markdown-mermaid",
    
    // Code Quality
    "sonarsource.sonarlint-vscode",
    "aaron-bond.better-comments",
    "streetsidesoftware.code-spell-checker",
    
    // Productivity
    "wayou.vscode-todo-highlight",
    "alefragnani.bookmarks",
    "alefragnani.project-manager",
    "vscodevim.vim",
    
    // Themes (optional)
    "pkief.material-icon-theme",
    "zhuangtongfa.material-theme",
    
    // Remote Development
    "ms-vscode-remote.remote-ssh",
    "ms-vscode.remote-explorer",
    
    // Testing
    "hbenl.vscode-test-explorer",
    "littlefoxteam.vscode-python-test-adapter",
    "hbenl.vscode-test-explorer",
    
    // Protocol Buffers
    "zxh404.vscode-proto3",
    
    // Environment Variables
    "mikestead.dotenv",
    
    // OpenTelemetry
    "sumanthdonthireddy.vscode-opentelemetry"
  ]
}