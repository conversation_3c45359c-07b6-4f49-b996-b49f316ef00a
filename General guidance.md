To effectively prepare the foundation documents for Claude Code to use as a reference for any project, you should focus on a practice known as **Context Engineering**. This approach is significantly more effective than traditional prompt engineering for ensuring that AI coding assistants perform well.

Here are the best practices for preparing these foundation documents and what they should include:

### 1. Embrace Context Engineering as a System
Context Engineering is a comprehensive system designed to provide AI coding assistants with all the necessary information to complete tasks end-to-end. It's crucial because it **reduces AI failures** (which are often context failures, not model failures), **ensures consistency** with project patterns, **enables complex, multi-step features**, and supports **self-correction** through validation loops.

### 2. Utilize Multiple Markdown Files for Comprehensive Context
A common mistake is thinking a single Markdown file is sufficient; instead, **you need multiple MD files** to properly structure your project's context. Key files include:
*   **`CLAUDE.md`**: The most important file, serving as your project's main persistent memory.
*   **`implementation.md`**: Details technical implementation aspects.
*   **`design.md` or `feature.md`**: For design specifics or new feature details.
*   **`INITIAL.md`**: A template for describing new feature requests.
*   **Product Requirements Prompts (PRPs)**: Comprehensive implementation blueprints that are often generated by <PERSON> itself.
*   **`examples/` folder**: A critical location for providing high-quality code examples.

### 3. Structure and Content for `CLAUDE.md`
Think of your `CLAUDE.md` file as a constantly refined, carefully crafted prompt. Claude automatically reads its content and includes it in its context at the start of every session.

**Best Practices for `CLAUDE.md`:**
*   **Conciseness and Structure:** Use **Markdown headings and bullet points** to keep the file organized and readable.
*   **Project Overview:** Include a general overview of your project.
*   **Global Rules and Standards:** This file contains **project-wide rules** that Claude will follow in every conversation.
    *   **Project Awareness:** Include information about reading planning documents and checking tasks.
    *   **Code Structure:** Define file size limits, module organization, and general code structure.
    *   **Testing Requirements:** Specify unit test patterns and coverage expectations.
    *   **Style Conventions:** Explicitly state language preferences and formatting rules.
    *   **Documentation Standards:** Outline docstring formats and commenting practices.
*   **Common Commands:** List frequently used build, test, and lint commands specific to your project.
*   **Key Files:** Point Claude to **core architectural files** (e.g., `src/services/main_service.py`).
*   **Project Workflows:** Detail processes such as Git branching or testing strategies.
*   **Modularity with Imports:** You can keep your main `CLAUDE.md` clean by importing other files directly using the `@` syntax (e.g., `@docs/api_conventions.md`).
*   **Persistence:** It is crucial to **tell Claude in its memory to reference other important `.md` files**, such as `implementation.md`, because it might otherwise forget them after a `/compact` command. You can do this by using `/memory project memory` and instructing it to "always refer to implementation.md" and "keep a task list of what you've done and what's next".
*   **Location:** For shared project-specific instructions, place `CLAUDE.md` in the **root of your project** and check it into Git. For personal preferences, use `~/.claude/CLAUDE.md` in your home directory.

### 4. Create Detailed Feature Request Documents (`INITIAL.md`)
When requesting a new feature or task, edit `INITIAL.md` (or a similar document) to clearly describe your requirements.

**Key Sections for `INITIAL.md`:**
*   **`FEATURE`**: Be **specific and comprehensive** about the desired functionality and requirements. Avoid vague instructions.
*   **`EXAMPLES`**: **Reference specific example files** from your `examples/` folder and explain how they should be used or mimicked.
*   **`DOCUMENTATION`**: Include links to relevant API documentation, library guides, or any custom Model Context Protocol (MCP) server resources.
*   **`OTHER CONSIDERATIONS`**: Mention any "gotchas," specific constraints (e.g., authentication, rate limits), or common pitfalls that AI assistants might miss.
*   **Motivation:** Always explain *why* you're asking for something; this context helps Claude understand the goal and deliver better-suited responses.
*   **Role Assignment:** Giving Claude a specific role (e.g., "You are an expert in cybersecurity...") at the beginning of a conversation can filter its responses through that lens.
*   **XML Tags:** Use **XML tags** to demarcate different parts of your prompt, improving clarity and structure for Claude.

### 5. Generate and Utilize Product Requirements Prompts (PRPs) / Documents (PRDs)
PRPs are comprehensive implementation blueprints designed specifically for AI coding assistants.
*   **Generation:** After Claude has gained a sense of your repository, ask it to **create a PRD** for a new feature. Claude can research existing UI components, understand conversation storage, and even perform web searches for compliance requirements (like GDPR) to produce a detailed document.
*   **Content:** PRPs/PRDs should include problem statements, user pain points, core functionalities, success metrics, potential data structures, and even design considerations (e.g., for a consent model).
*   **Workflow Integration:** These documents can be exported to project management tools, and Claude can track its progress against the defined requirements.

### 6. Leverage High-Quality Code Examples in an `examples/` Folder
The `examples/` folder is **critical for success** because AI coding assistants perform much better when they have patterns to follow.
*   **What to Include:** Place examples of your preferred **code structure patterns** (module organization, import conventions, class/function patterns), **testing patterns** (file structure, mocking, assertion styles), **integration patterns** (API clients, database connections, authentication flows), and **CLI patterns** (argument parsing, output formatting).
*   **Demonstrate Error Handling:** Show both what to do *and* what not to do, including patterns for error handling.

### 7. Establish a Dedicated Learning Phase for Claude
When starting a new project or a significant new feature, **do not jump straight into coding**. First, let Claude learn your repository.
*   **Process:** Ask Claude to **analyze the codebase and create a Markdown file explaining the app's flow**, including details on how payments or other data are handled. This initial document then becomes a foundational "knowledge base" for Claude to build future features upon.
*   **Treat Claude as a Junior Engineer:** This phase mimics a junior developer onboarding to a new codebase, learning its structure before attempting to add new features.

### 8. Incorporate Validation and Iteration Loops with Human Oversight
*   **Validation Gates:** Your PRPs should include test commands that Claude must pass. Claude will iterate on its code until all validations succeed, ensuring working code.
*   **Active Collaboration:** Treat Claude as an intelligent partner. AI agents are self-repairing but require human orchestration to guide them, especially for new or complex projects.
*   **Detailed Feedback:** If a feature doesn't work, don't just state the problem. **Investigate the issue yourself** (e.g., using browser network tabs, checking the database) to pinpoint the exact failure point. Provide Claude with **specific, detailed context** and even examples of stored data; this dramatically improves its ability to fix issues accurately and faster.

### 9. Consider a Solid Project Template
Starting with a **well-structured template** (e.g., a React or Next.js starter kit that already handles common components like payments, database integration, and authentication) can provide a strong foundation and significantly accelerate development when using Claude Code. It also helps Claude by providing a consistent codebase to learn from.

To effectively prepare foundation documents for Claude Code to use as a reference for any project, you should adopt a comprehensive approach known as **Context Engineering**. This method goes beyond simple prompt engineering by providing a complete system of documentation, examples, rules, patterns, and validation, which significantly reduces AI failures and ensures consistency.

Here's how you should set up your repository, detailing the essential files, their content, and their structure:

### 1. The Core Principle: Context Engineering
Context Engineering is paramount because most AI coding assistant failures stem from a lack of sufficient context, not from the model itself. By engineering the context, you empower Claude to handle complex, multi-step features, ensure consistency with project patterns, and even self-correct through validation loops.

### 2. Repository Setup: Essential Files and Their Content

Instead of a single Markdown file, **you need multiple Markdown files** to properly structure your project's context.

#### a. `CLAUDE.md`
This is arguably **the most important file**, serving as your project's main persistent memory. Claude automatically reads its contents and includes it in its context at the start of every session in that directory.

*   **Location:**
    *   For **project-specific instructions** shared with your team, place `CLAUDE.md` in the **root of your project** and check it into Git.
    *   For **personal preferences** (e.g., preferred coding style or custom tool shortcuts), use `~/.claude/CLAUDE.md` in your home directory.
    *   Claude recursively looks for `CLAUDE.md` files, loading them from the root and current directories, as well as child directories on-demand.
*   **Content and Best Practices:**
    *   **Project Overview:** Include a general overview of your project.
    *   **Global Rules and Standards:** This file contains **project-wide rules** Claude will follow.
        *   **Project Awareness:** Instruct Claude to read planning documents and check tasks.
        *   **Code Structure:** Define file size limits and module organization.
        *   **Testing Requirements:** Specify unit test patterns and coverage expectations.
        *   **Style Conventions:** Explicitly state language preferences and formatting rules (e.g., "Use ES modules (import/export), not CommonJS (require)").
        *   **Documentation Standards:** Outline docstring formats and commenting practices.
    *   **Common Commands:** List frequently used build, test, and lint commands specific to your project (e.g., `npm run build`).
    *   **Key Architectural Files:** Point Claude to core architectural files (e.g., `src/services/main_service.py`).
    *   **Project Workflows:** Detail processes such as Git branching or testing strategies (e.g., "Always create feature branches from `develop`").
    *   **Conciseness and Structure:** Use **Markdown headings and bullet points** to keep the file organized and readable.
    *   **Modularity with Imports:** Keep your main `CLAUDE.md` clean by importing other files directly using the `@` syntax (e.g., `@docs/api_conventions.md`).
    *   **Persistence in Claude's Memory:** It is **crucial to tell Claude in its memory to explicitly reference other important `.md` files**, such as `implementation.md`. This prevents Claude from forgetting them after a `/compact` command. You can do this using `/memory project memory` and instructing it to "always refer to implementation.md" and "keep a task list of what you've done and what's next".

#### b. `implementation.md`, `design.md`, or `feature.md`
These files detail technical implementation aspects, design specifics, or new feature details.

*   **Generation:** These documents are often **generated by Claude itself** after a thorough research phase. For instance, you can ask Claude to "make an implementation.md file" and ensure it does "extremely detailed research into each of these topics".
*   **Content:** They should contain specific examples, detailed research, and a comprehensive breakdown of the implementation.
*   **Reference:** As mentioned above, it's essential to **tell Claude in its memory to refer to these files**.

#### c. `INITIAL.md` (or similar for Feature Requests)
This file serves as a **template for describing new feature requests** or tasks.

*   **Key Sections (as outlined in the template):**
    *   **`FEATURE`**: **Be specific and comprehensive** about the desired functionality and requirements. Avoid vague instructions.
    *   **`EXAMPLES`**: **Reference specific example files** from your `examples/` folder and explain how they should be used or mimicked.
    *   **`DOCUMENTATION`**: Include links to relevant API documentation, library guides, or any custom Model Context Protocol (MCP) server resources.
    *   **`OTHER CONSIDERATIONS`**: Mention any "gotchas," specific constraints (e.g., authentication, rate limits), or common pitfalls that AI assistants might miss.
*   **Best Practices for Prompting within `INITIAL.md`:**
    *   **Be Explicit and Direct:** Tell Claude exactly what you want.
    *   **Provide Context and Motivation:** Explain *why* you're asking for something (e.g., accessibility purposes, GDPR compliance). This helps Claude understand the goal and deliver better-suited responses.
    *   **Use XML Tags:** Employ **XML tags** to demarcate different parts of your prompt, improving clarity and structure for Claude.

#### d. `PRPs/` Folder (Product Requirements Prompts/Documents)
Product Requirements Prompts (PRPs) are comprehensive implementation blueprints specifically **designed to instruct AI coding assistants**.

*   **Generation:** After Claude has learned your repository, you can ask it to **create a PRD/PRP** for a new feature. Claude can research existing UI components, understand conversation storage, and even perform web searches for compliance requirements (like GDPR) to produce a detailed document. The `/generate-prp INITIAL.md` custom command can automate this.
*   **Content:** PRPs/PRDs should include complete context and documentation, implementation steps with validation, error handling patterns, and test requirements. They are similar to traditional PRDs but are crafted to guide an AI. They can include problem statements, user pain points, core functionalities, success metrics, potential data structures, and even design considerations.
*   **Workflow Integration:** These documents can be exported to project management tools, and Claude can track its progress against the defined requirements.
*   **Execution:** Once a PRP is generated, you can use a command like `/execute-prp PRPs/your-feature-name.md` to have Claude implement the feature.

#### e. `examples/` Folder
This folder is **critical for success** because AI coding assistants perform much better when they have high-quality patterns to follow.

*   **What to Include:**
    *   **Code Structure Patterns:** Examples of your preferred module organization, import conventions, and class/function patterns.
    *   **Testing Patterns:** Test file structure, mocking approaches, and assertion styles.
    *   **Integration Patterns:** API client implementations, database connections, and authentication flows.
    *   **CLI Patterns:** Argument parsing, output formatting, and error handling.
*   **Demonstrate Error Handling:** Show both what to do *and* what not to do, including patterns for error handling.
*   **Reference:** These examples should be referenced liberally within your `INITIAL.md` files.

#### f. `.claude/` Folder
This hidden directory often contains custom commands and configurations for Claude Code.

*   **`commands/`:** Store your custom slash commands here (e.g., `generate-prp.md`, `execute-prp.md`). These allow you to encapsulate complex multi-step instructions into single commands.
*   **`settings.json` (or `settings.local.json`):** Customize Claude Code's behavior, permissions (e.g., allowing file edits or Git commits), and themes. These settings can be shared with your team.

### 3. Overall Structure, Workflow, and Saving Practices

*   **Start with a Learning Phase:** When beginning a new project or a significant feature, **do not jump straight into coding**. First, let Claude learn your repository. Ask it to analyze the codebase and create a Markdown file (e.g., in `CLAUDE.md` or a separate `architecture.md`) explaining the app's flow, how data is handled, etc.. This mimics a junior developer onboarding and provides a foundational "knowledge base" for Claude.
*   **Solid Project Template:** Start with a well-structured project template (e.g., a React or Next.js starter kit that already handles common components like payments, database integration, and authentication). This provides a strong foundation and a consistent codebase for Claude to learn from.
*   **Orchestration Prompt for Multi-Agent Workflows:** If orchestrating multiple AI agents (e.g., Claude Code, GitHub Copilot, Gemini CLI), create a detailed orchestration prompt at the very beginning. This prompt dictates roles for each agent (e.g., Claude for backend, Copilot for frontend, Gemini for testing) and outlines the project goals and communication methods (e.g., using Git commits to communicate). This helps align their work from the start.
*   **Validation and Iteration Loops with Human Oversight:**
    *   **Human as Orchestrator:** AI agents are self-repairing but **require human intervention and guidance**. You are the "puppet master".
    *   **Validation Gates:** Ensure your PRPs include test commands that Claude must pass. Claude will iterate on its code until all validations succeed, helping ensure working code.
    *   **Detailed Feedback:** If a feature doesn't work, **do not just state the problem** (e.g., "the feature doesn't work"). Instead, **investigate the issue yourself** (e.g., using browser network tabs, checking the database for stored data) to pinpoint the exact failure point. Provide Claude with **specific, detailed context** and even examples of stored data; this dramatically improves its ability to fix issues accurately and faster.
*   **Saving and Persistence:**
    *   All these foundation documents (e.g., `CLAUDE.md`, `INITIAL.md`, files in `examples/`, `PRPs/`) should be **saved within your project repository** and, where appropriate, **checked into your version control system (Git)**. This ensures they are persistent, shared with the team, and contribute to Claude's consistent understanding of the project over time.
    *   For Claude to maintain context even after sessions or compact commands, use the `/memory` command to explicitly instruct it to reference key documents.

By adhering to these practices, you provide Claude Code with the comprehensive context it needs to operate as an intelligent, agentic coding assistant, significantly enhancing code quality and development speed.

The best Claude Code workflow is centered around a comprehensive approach called **Context Engineering**, which is considered significantly more effective than traditional prompt engineering because it provides a complete system of documentation, examples, rules, patterns, and validation. This method helps to reduce AI failures, ensure consistency with project patterns, enable complex multi-step features, and allow the AI to self-correct.

Here’s how to set up your repository and the recommended workflow:

### I. Repository Structure and Key Files

Instead of relying on a single Markdown file, the workflow emphasizes the use of **multiple Markdown files** to properly structure your project's context.

*   **`CLAUDE.md`**:
    *   This is considered the **most important file**, acting as your project's main persistent memory. Claude automatically reads its contents and includes it in its context at the start of every session in that directory.
    *   It should contain a **project overview** and **global project-wide rules and standards** that Claude will follow. This includes project awareness (reading planning docs, checking tasks), code structure (file size limits, module organization), testing requirements (unit test patterns, coverage), style conventions (language preferences, formatting rules like ES modules over CommonJS), and documentation standards.
    *   You can also list **common commands** (e.g., `npm run build`) and point Claude to **key architectural files** (e.g., `src/services/main_service.py`).
    *   For modularity, you can **import other files** directly into `CLAUDE.md` using the `@` syntax (e.g., `@docs/api_conventions.md`) to keep it clean.
    *   **Crucially, you must tell Claude in its memory to explicitly refer to other important `.md` files**, such as `implementation.md`. This prevents Claude from forgetting them after a `/compact` command, ensuring persistence. You can do this using `/memory project memory` and instructing it to "always refer to implementation.md" and "keep a task list of what you've done and what's next".
    *   You can bootstrap this file with `/init` in the `claude-code` CLI or edit it with `/memory`.

*   **`implementation.md`, `design.md`, or `feature.md`**:
    *   These files contain **detailed technical implementation aspects, design specifics, or new feature details**.
    *   They are often **generated by Claude itself** after a thorough research phase. You can prompt Claude to "make an implementation.md file" and instruct it to do "extremely detailed research into each of these topics". This research should go beyond surface-level scraping, delving deep into multiple relevant pages and outputting specific examples.
    *   These documents should be explicitly referenced in Claude's memory.

*   **`INITIAL.md`**:
    *   This file serves as a **template for describing new feature requests** or tasks.
    *   It should include key sections:
        *   **`FEATURE`**: A **specific and comprehensive description** of the desired functionality and requirements, avoiding vague instructions.
        *   **`EXAMPLES`**: **References to specific example files** from your `examples/` folder, explaining how they should be used or mimicked.
        *   **`DOCUMENTATION`**: Links to relevant API documentation, library guides, or custom Model Context Protocol (MCP) server resources.
        *   **`OTHER CONSIDERATIONS`**: Any "gotchas," specific constraints (e.g., authentication, rate limits), or common pitfalls that AI assistants might miss.
    *   When writing prompts, be **explicit and direct**, provide **context and motivation** (explaining *why* a constraint exists), use **high-quality examples** (few-shot prompting) for pattern recognition, and structure your requests with **XML tags** for clarity.

*   **`PRPs/` Folder (Product Requirements Prompts/Documents)**:
    *   PRPs are **comprehensive implementation blueprints specifically designed to instruct AI coding assistants**.
    *   They are similar to traditional Product Requirements Documents (PRDs) but are crafted to guide an AI, including complete context, detailed implementation steps with validation, error handling patterns, and test requirements.
    *   Claude can generate PRPs for new features after learning your repository. A custom command like `/generate-prp INITIAL.md` can automate this, where Claude researches UI components, conversation storage, and compliance requirements (e.g., GDPR) to produce a detailed document.
    *   Once a PRP is generated, you can use a command like `/execute-prp PRPs/your-feature-name.md` to have Claude implement the feature.

*   **`examples/` Folder**:
    *   This folder is **critical for success**, as AI coding assistants perform much better when provided with high-quality patterns to follow.
    *   Include examples of your preferred code structure, testing patterns (e.g., file structure, mocking, assertion styles), integration patterns (API clients, database connections, authentication), and CLI patterns (argument parsing, output formatting).
    *   Demonstrate both what to do *and* what not to do, and include patterns for error handling.

*   **`.claude/` Folder**:
    *   This hidden directory typically contains **custom slash commands** (`commands/`) and configurations (`settings.json`) for Claude Code.
    *   Custom commands allow you to encapsulate complex multi-step instructions into single commands (e.g., `generate-prp.md`, `execute-prp.md`).
    *   Settings files can customize Claude Code's behavior, such as permissions for file modifications or Git commits, and can be shared with your team.

### II. Overall Workflow and Saving Practices

1.  **Start with a Learning Phase (Codebase Q&A)**:
    *   **Do not jump straight into coding** for a new project or significant feature. First, allow Claude to learn your repository by asking it to analyze the codebase and create a Markdown file (e.g., in `CLAUDE.md` or a separate `architecture.md`) explaining the app's flow, data handling, and existing components. This serves as a foundational "knowledge base" for Claude.
    *   Treat Claude like a **junior engineer** who needs to learn the repository before building new features.

2.  **Explore, Plan, Code, Commit Workflow**:
    *   This is a versatile, foundational workflow.
    *   **Explore**: Instruct Claude to read relevant files, images, or URLs, **but crucially, tell it not to write any code yet**. The goal is information gathering.
    *   **Plan**: Ask Claude to create a **detailed, step-by-step plan**. Using the word "think" encourages deeper consideration. Review this plan carefully. Skipping this step is a common mistake that reduces code quality.
    *   **Code**: Once the plan is approved, instruct Claude to implement the solution based on the agreed-upon steps.
    *   **Commit**: After implementation and verification, ask Claude to commit the result, write a descriptive commit message, and even create a pull request using the `gh` CLI.

3.  **Test-Driven Development (TDD) with Claude**:
    *   Claude excels with clear, verifiable targets.
    *   **Write Tests**: Describe the desired functionality and ask Claude to write tests first, explicitly stating they should fail initially.
    *   **Confirm Failure**: Instruct Claude to run tests and confirm they fail as expected.
    *   **Commit Tests**: Have Claude commit the tests once you're satisfied they capture requirements.
    *   **Write Code**: Instruct Claude to write the implementation to make tests pass, telling it *not* to modify the tests. Claude will iterate (write, run, analyze, adjust) until success.
    *   **Commit Code**: Once tests pass, have Claude commit the implementation.

4.  **Multi-Agent Workflows and Human Oversight**:
    *   For complex projects, you can orchestrate multiple AI agents (e.g., Claude Code for backend, GitHub Copilot for frontend, Gemini CLI for testing).
    *   Create a detailed **orchestration prompt** at the beginning, defining roles, project goals, and communication methods (e.g., using Git commits).
    *   These AI agents are **self-repairing but require human intervention and guidance**. You act as the "puppet master," checking their progress and helping them resolve issues.
    *   While parallel work is powerful, **be prepared to switch to sequential work** if agents get out of sync (e.g., tests becoming outdated as frontend work progresses).

5.  **Validation and Detailed Feedback**:
    *   **Do not simply state "the feature doesn't work"**. Instead, **investigate the issue yourself** (e.g., using browser network tabs to check requests, examining the database for stored data) to pinpoint the exact failure point.
    *   Provide Claude with **specific, detailed context** and even examples of stored data; this dramatically improves its ability to fix issues accurately and faster.
    *   Validation gates within PRPs, such as requiring tests to pass, ensure working code.

### III. Saving and Persistence

*   All foundation documents (e.g., `CLAUDE.md`, `INITIAL.md`, files in `examples/`, `PRPs/`, generated `implementation.md` files) should be **saved within your project repository**.
*   They should be **checked into your version control system (Git)** where appropriate. This ensures they are persistent, shared with your team, and contribute to Claude's consistent understanding of the project over time.
*   Explicitly instructing Claude via the `/memory` command to "always refer to" key documents like `implementation.md` ensures it maintains context even after sessions or compact commands.