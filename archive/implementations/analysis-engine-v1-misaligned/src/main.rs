use actix_web::{get, App, HttpR<PERSON>ponse, HttpServer, Responder};
use opentelemetry::global;
use actix_web_opentelemetry::RequestTracing;
use tracing::{subscriber::set_global_default, Subscriber};
use tracing_subscriber::{layer::SubscriberExt, EnvFilter, Registry};
use tracing_opentelemetry::OpenTelemetryLayer;

mod api;
mod git;
mod metrics;
mod parser;
mod storage;

#[get("/health")]
async fn health() -> impl Responder {
    HttpResponse::Ok().body("OK")
}

fn init_tracer() {
    global::set_text_map_propagator(opentelemetry::sdk::propagation::TraceContextPropagator::new());
    let tracer = opentelemetry::sdk::export::trace::stdout::new_pipeline()
        .with_pretty_print(true)
        .install_simple();
    let telemetry = tracing_opentelemetry::layer().with_tracer(tracer);
    let subscriber = Registry::default().with(telemetry);
    set_global_default(subscriber).expect("Failed to set global default subscriber");
}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    std::env::set_var("RUST_LOG", "debug");
    env_logger::init();
    init_tracer();

    HttpServer::new(|| {
        App::new()
            .wrap(RequestTracing::new())
            .service(health)
    })
    .bind("127.0.0.1:8001")?
    .run()
    .await
}