use anyhow::Result;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{RwLock, Semaphore};
use tracing::{info, warn, error};

use crate::models::AnalysisError;

#[derive(Debug, <PERSON><PERSON>)]
pub struct JobManager {
    // Concurrent job limit
    semaphore: Arc<Semaphore>,
    // Active jobs tracking
    active_jobs: Arc<RwLock<HashMap<String, JobInfo>>>,
    // Maximum concurrent analyses
    max_concurrent: usize,
}

#[derive(Debug, <PERSON>lone)]
pub struct JobInfo {
    pub analysis_id: String,
    pub repository_url: String,
    pub status: JobStatus,
    pub started_at: chrono::DateTime<chrono::Utc>,
    pub progress: JobProgress,
}

#[derive(Debug, <PERSON>lone)]
pub enum JobStatus {
    Queued,
    Running,
    Completed,
    Failed(String),
}

#[derive(Debug, <PERSON>lone)]
pub struct JobProgress {
    pub current_phase: String,
    pub phase_progress: f32,
    pub overall_progress: f32,
    pub message: String,
}

impl JobProgress {
    pub fn new(phase: &str, message: &str) -> Self {
        Self {
            current_phase: phase.to_string(),
            phase_progress: 0.0,
            overall_progress: 0.0,
            message: message.to_string(),
        }
    }

    pub fn update(&mut self, phase: &str, phase_progress: f32, overall_progress: f32, message: &str) {
        self.current_phase = phase.to_string();
        self.phase_progress = phase_progress;
        self.overall_progress = overall_progress;
        self.message = message.to_string();
    }
}

#[derive(Clone)]
pub struct JobHandle {
    pub analysis_id: String,
    manager: Arc<JobManager>,
}

impl JobHandle {
    pub async fn update_progress(&self, phase: &str, phase_progress: f32, overall_progress: f32, message: &str) -> Result<()> {
        let mut jobs = self.manager.active_jobs.write().await;
        if let Some(job) = jobs.get_mut(&self.analysis_id) {
            job.progress.update(phase, phase_progress, overall_progress, message);
            info!(
                "Analysis {} progress: {} - {}% (overall: {}%)",
                self.analysis_id, phase, phase_progress * 100.0, overall_progress * 100.0
            );
        }
        Ok(())
    }

    pub async fn complete(&self) -> Result<()> {
        let mut jobs = self.manager.active_jobs.write().await;
        if let Some(job) = jobs.get_mut(&self.analysis_id) {
            job.status = JobStatus::Completed;
            job.progress.overall_progress = 1.0;
            info!("Analysis {} completed", self.analysis_id);
        }
        Ok(())
    }

    pub async fn fail(&self, error: String) -> Result<()> {
        let mut jobs = self.manager.active_jobs.write().await;
        if let Some(job) = jobs.get_mut(&self.analysis_id) {
            job.status = JobStatus::Failed(error.clone());
            error!("Analysis {} failed: {}", self.analysis_id, error);
        }
        Ok(())
    }
}

impl Drop for JobHandle {
    fn drop(&mut self) {
        let analysis_id = self.analysis_id.clone();
        let manager = self.manager.clone();
        
        // Clean up job from active jobs when handle is dropped
        tokio::spawn(async move {
            let mut jobs = manager.active_jobs.write().await;
            jobs.remove(&analysis_id);
            // Release semaphore permit
            drop(manager.semaphore.acquire().await);
        });
    }
}

impl JobManager {
    pub fn new(max_concurrent: usize) -> Self {
        info!("Initializing JobManager with max concurrent jobs: {}", max_concurrent);
        
        Self {
            semaphore: Arc::new(Semaphore::new(max_concurrent)),
            active_jobs: Arc::new(RwLock::new(HashMap::new())),
            max_concurrent,
        }
    }

    pub async fn acquire_job_slot(
        &self,
        analysis_id: String,
        repository_url: String,
    ) -> Result<JobHandle, AnalysisError> {
        // Check if we're at capacity
        let active_count = self.active_jobs.read().await.len();
        if active_count >= self.max_concurrent {
            warn!(
                "Job queue at capacity ({}/{}), analysis {} will wait",
                active_count, self.max_concurrent, analysis_id
            );
        }

        // Acquire semaphore permit (will wait if at capacity)
        let _permit = self.semaphore.acquire().await
            .map_err(|e| AnalysisError::ConfigError(format!("Failed to acquire job slot: {}", e)))?;
        
        // Create job info
        let job_info = JobInfo {
            analysis_id: analysis_id.clone(),
            repository_url: repository_url.clone(),
            status: JobStatus::Running,
            started_at: chrono::Utc::now(),
            progress: JobProgress::new("initializing", "Starting analysis"),
        };

        // Add to active jobs
        let mut jobs = self.active_jobs.write().await;
        jobs.insert(analysis_id.clone(), job_info);
        
        info!(
            "Acquired job slot for analysis {} ({}/{} active)",
            analysis_id,
            jobs.len(),
            self.max_concurrent
        );

        Ok(JobHandle {
            analysis_id,
            manager: Arc::new(self.clone()),
        })
    }

    pub async fn get_job_status(&self, analysis_id: &str) -> Option<JobInfo> {
        let jobs = self.active_jobs.read().await;
        jobs.get(analysis_id).cloned()
    }

    pub async fn get_active_jobs(&self) -> Vec<JobInfo> {
        let jobs = self.active_jobs.read().await;
        jobs.values().cloned().collect()
    }

    pub async fn get_queue_status(&self) -> (usize, usize) {
        let active = self.active_jobs.read().await.len();
        let available = self.max_concurrent.saturating_sub(active);
        (active, available)
    }

    pub async fn cancel_job(&self, analysis_id: &str) -> Result<(), AnalysisError> {
        let mut jobs = self.active_jobs.write().await;
        if jobs.remove(analysis_id).is_some() {
            info!("Cancelled job: {}", analysis_id);
            Ok(())
        } else {
            Err(AnalysisError::NotFound(format!("Job {} not found", analysis_id)))
        }
    }
}

// Progress tracking phases for repository analysis
pub struct AnalysisPhases;

impl AnalysisPhases {
    pub const CLONING: (&'static str, f32) = ("cloning", 0.1);
    pub const PARSING: (&'static str, f32) = ("parsing", 0.3);
    pub const ANALYZING: (&'static str, f32) = ("analyzing", 0.6);
    pub const EMBEDDINGS: (&'static str, f32) = ("embeddings", 0.8);
    pub const STORING: (&'static str, f32) = ("storing", 0.9);
    pub const PUBLISHING: (&'static str, f32) = ("publishing", 1.0);

    pub fn get_phase_weight(phase: &str) -> f32 {
        match phase {
            "cloning" => 0.1,
            "parsing" => 0.2,
            "analyzing" => 0.3,
            "embeddings" => 0.2,
            "storing" => 0.1,
            "publishing" => 0.1,
            _ => 0.0,
        }
    }

    pub fn calculate_overall_progress(phase: &str, phase_progress: f32) -> f32 {
        let base_progress = match phase {
            "cloning" => 0.0,
            "parsing" => 0.1,
            "analyzing" => 0.3,
            "embeddings" => 0.6,
            "storing" => 0.8,
            "publishing" => 0.9,
            _ => 0.0,
        };
        
        let phase_weight = Self::get_phase_weight(phase);
        base_progress + (phase_weight * phase_progress)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_job_manager_initialization() {
        let manager = JobManager::new(10);
        assert_eq!(manager.max_concurrent, 10);
        
        let (active, available) = manager.get_queue_status().await;
        assert_eq!(active, 0);
        assert_eq!(available, 10);
    }

    #[tokio::test]
    async fn test_job_acquisition_and_release() {
        let manager = JobManager::new(2);
        
        // Acquire first job
        let job1 = manager.acquire_job_slot(
            "analysis_001".to_string(),
            "https://github.com/test/repo1".to_string(),
        ).await.unwrap();
        
        let (active, available) = manager.get_queue_status().await;
        assert_eq!(active, 1);
        assert_eq!(available, 1);
        
        // Acquire second job
        let job2 = manager.acquire_job_slot(
            "analysis_002".to_string(),
            "https://github.com/test/repo2".to_string(),
        ).await.unwrap();
        
        let (active, available) = manager.get_queue_status().await;
        assert_eq!(active, 2);
        assert_eq!(available, 0);
        
        // Release first job
        drop(job1);
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        
        let (active, available) = manager.get_queue_status().await;
        assert_eq!(active, 1);
        assert_eq!(available, 1);
    }

    #[tokio::test]
    async fn test_job_progress_tracking() {
        let manager = JobManager::new(5);
        
        let job = manager.acquire_job_slot(
            "analysis_003".to_string(),
            "https://github.com/test/repo3".to_string(),
        ).await.unwrap();
        
        // Update progress through phases
        job.update_progress("cloning", 0.5, 0.05, "Cloning repository...").await.unwrap();
        job.update_progress("cloning", 1.0, 0.1, "Clone complete").await.unwrap();
        
        job.update_progress("parsing", 0.5, 0.2, "Parsing files...").await.unwrap();
        job.update_progress("parsing", 1.0, 0.3, "Parsing complete").await.unwrap();
        
        // Check job status
        let status = manager.get_job_status("analysis_003").await.unwrap();
        assert_eq!(status.progress.current_phase, "parsing");
        assert_eq!(status.progress.overall_progress, 0.3);
        
        // Complete the job
        job.complete().await.unwrap();
        
        let status = manager.get_job_status("analysis_003").await.unwrap();
        match status.status {
            JobStatus::Completed => (),
            _ => panic!("Job should be completed"),
        }
    }

    #[tokio::test]
    async fn test_phase_progress_calculation() {
        assert_eq!(AnalysisPhases::calculate_overall_progress("cloning", 0.0), 0.0);
        assert_eq!(AnalysisPhases::calculate_overall_progress("cloning", 0.5), 0.05);
        assert_eq!(AnalysisPhases::calculate_overall_progress("cloning", 1.0), 0.1);
        
        assert_eq!(AnalysisPhases::calculate_overall_progress("analyzing", 0.0), 0.3);
        assert_eq!(AnalysisPhases::calculate_overall_progress("analyzing", 0.5), 0.45);
        assert_eq!(AnalysisPhases::calculate_overall_progress("analyzing", 1.0), 0.6);
        
        assert_eq!(AnalysisPhases::calculate_overall_progress("publishing", 1.0), 1.0);
    }
}