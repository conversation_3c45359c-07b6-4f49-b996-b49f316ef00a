use anyhow::Result;
use git2::{Repository, Oid, BranchType};
use std::path::{Path, PathBuf};
use std::time::{Duration, Instant};
use tokio::fs;
use tracing::{info, warn};
use uuid::Uuid;

use crate::config::GitConfig;
use crate::models::{AnalysisError, RepositoryInfo};

#[derive(Clone)]
pub struct GitService {
    config: GitConfig,
}

pub struct ClonedRepository {
    pub path: PathBuf,
    pub info: RepositoryInfo,
    pub cleanup_handle: CleanupHandle,
}

pub struct CleanupHandle {
    path: PathBuf,
    cleanup_after: Duration,
}

impl GitService {
    pub fn new(config: &GitConfig) -> Result<Self> {
        // Ensure temp directory exists
        std::fs::create_dir_all(&config.temp_dir)?;
        
        Ok(Self {
            config: config.clone(),
        })
    }

    pub async fn clone_repository(
        &self,
        url: &str,
        branch: Option<&str>,
        commit: Option<&str>,
    ) -> Result<ClonedRepository, AnalysisError> {
        let start_time = Instant::now();
        let clone_id = Uuid::new_v4().simple().to_string()[..8].to_string();
        let clone_path = PathBuf::from(&self.config.temp_dir).join(format!("repo_{}", clone_id));

        info!("Cloning repository {} to {:?}", url, clone_path);

        // Clone repository with timeout
        let repo = tokio::task::spawn_blocking({
            let url = url.to_string();
            let clone_path = clone_path.clone();
            let timeout = Duration::from_secs(self.config.clone_timeout_seconds);
            
            move || -> Result<Repository, AnalysisError> {
                let start = Instant::now();
                
                // Set up clone options
                let mut builder = git2::build::RepoBuilder::new();
                builder.bare(false);
                
                // Clone the repository
                let repo = builder.clone(&url, &clone_path)
                    .map_err(|e| AnalysisError::GitError(e))?;
                
                // Check timeout
                if start.elapsed() > timeout {
                    return Err(AnalysisError::TimeoutError);
                }
                
                Ok(repo)
            }
        }).await.map_err(|e| AnalysisError::ConfigError(format!("Clone task failed: {}", e)))??;

        let clone_time_ms = start_time.elapsed().as_millis() as u64;

        // Checkout specific branch or commit if requested
        let (final_commit, final_branch) = if let Some(commit_sha) = commit {
            self.checkout_commit(&repo, commit_sha)?
        } else if let Some(branch_name) = branch {
            self.checkout_branch(&repo, branch_name)?
        } else {
            self.get_current_head(&repo)?
        };

        // Check repository size
        let repo_size = self.calculate_repository_size(&clone_path).await?;
        if repo_size > self.config.max_repository_size_mb * 1024 * 1024 {
            // Cleanup and return error
            let _ = fs::remove_dir_all(&clone_path).await;
            return Err(AnalysisError::RepositoryTooLarge {
                size_mb: repo_size / (1024 * 1024),
                limit_mb: self.config.max_repository_size_mb,
            });
        }

        let repo_info = RepositoryInfo {
            id: format!("repo_{}", Uuid::new_v4().simple().to_string()[..16].to_string()),
            url: url.to_string(),
            commit: final_commit,
            branch: final_branch,
            size_bytes: repo_size,
            clone_time_ms,
        };

        let cleanup_handle = CleanupHandle {
            path: clone_path.clone(),
            cleanup_after: Duration::from_secs(self.config.cleanup_after_seconds),
        };

        Ok(ClonedRepository {
            path: clone_path,
            info: repo_info,
            cleanup_handle,
        })
    }

    fn checkout_commit(&self, repo: &Repository, commit_sha: &str) -> Result<(String, String), AnalysisError> {
        let oid = Oid::from_str(commit_sha)
            .map_err(|e| AnalysisError::ValidationError(format!("Invalid commit SHA: {}", e)))?;
        
        let commit = repo.find_commit(oid)
            .map_err(|e| AnalysisError::GitError(e))?;
        
        let tree = commit.tree()
            .map_err(|e| AnalysisError::GitError(e))?;
        
        repo.checkout_tree(tree.as_object(), None)
            .map_err(|e| AnalysisError::GitError(e))?;
        
        repo.set_head_detached(oid)
            .map_err(|e| AnalysisError::GitError(e))?;

        Ok((commit_sha.to_string(), "HEAD".to_string()))
    }

    fn checkout_branch(&self, repo: &Repository, branch_name: &str) -> Result<(String, String), AnalysisError> {
        let branch = repo.find_branch(branch_name, BranchType::Local)
            .or_else(|_| repo.find_branch(&format!("origin/{}", branch_name), BranchType::Remote))
            .map_err(|e| AnalysisError::GitError(e))?;
        
        let commit = branch.get().peel_to_commit()
            .map_err(|e| AnalysisError::GitError(e))?;
        
        let tree = commit.tree()
            .map_err(|e| AnalysisError::GitError(e))?;
        
        repo.checkout_tree(tree.as_object(), None)
            .map_err(|e| AnalysisError::GitError(e))?;
        
        repo.set_head(&format!("refs/heads/{}", branch_name))
            .map_err(|e| AnalysisError::GitError(e))?;

        Ok((commit.id().to_string(), branch_name.to_string()))
    }

    fn get_current_head(&self, repo: &Repository) -> Result<(String, String), AnalysisError> {
        let head = repo.head()
            .map_err(|e| AnalysisError::GitError(e))?;
        
        let commit = head.peel_to_commit()
            .map_err(|e| AnalysisError::GitError(e))?;
        
        let branch_name = if head.is_branch() {
            head.shorthand().unwrap_or("main").to_string()
        } else {
            "HEAD".to_string()
        };

        Ok((commit.id().to_string(), branch_name))
    }

    async fn calculate_repository_size(&self, path: &Path) -> Result<u64, AnalysisError> {
        let mut total_size = 0u64;
        
        let mut entries = fs::read_dir(path).await
            .map_err(|e| AnalysisError::IoError(e))?;
        
        while let Some(entry) = entries.next_entry().await
            .map_err(|e| AnalysisError::IoError(e))? {
            
            let metadata = entry.metadata().await
                .map_err(|e| AnalysisError::IoError(e))?;
            
            if metadata.is_file() {
                total_size += metadata.len();
            } else if metadata.is_dir() {
                // Skip .git directory for size calculation
                if entry.file_name() != ".git" {
                    total_size += self.calculate_directory_size(&entry.path()).await?;
                }
            }
        }
        
        Ok(total_size)
    }

    async fn calculate_directory_size(&self, path: &Path) -> Result<u64, AnalysisError> {
        let mut total_size = 0u64;
        
        let mut entries = fs::read_dir(path).await
            .map_err(|e| AnalysisError::IoError(e))?;
        
        while let Some(entry) = entries.next_entry().await
            .map_err(|e| AnalysisError::IoError(e))? {
            
            let metadata = entry.metadata().await
                .map_err(|e| AnalysisError::IoError(e))?;
            
            if metadata.is_file() {
                total_size += metadata.len();
            } else if metadata.is_dir() {
                total_size += Box::pin(self.calculate_directory_size(&entry.path())).await?;
            }
        }
        
        Ok(total_size)
    }
}

impl Drop for CleanupHandle {
    fn drop(&mut self) {
        let path = self.path.clone();
        tokio::spawn(async move {
            if let Err(e) = fs::remove_dir_all(&path).await {
                warn!("Failed to cleanup repository at {:?}: {}", path, e);
            } else {
                info!("Cleaned up repository at {:?}", path);
            }
        });
    }
}
