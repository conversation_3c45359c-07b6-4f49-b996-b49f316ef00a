use anyhow::Result;
use uuid::Uuid;
use std::time::{SystemTime, UNIX_EPOCH};

/// Generate a unique analysis ID with the format "analysis_[16_char_hex]"
pub fn generate_analysis_id() -> String {
    format!("analysis_{}", Uuid::new_v4().simple().to_string()[..16].to_string())
}

/// Generate a unique repository ID with the format "repo_[16_char_hex]"
pub fn generate_repository_id() -> String {
    format!("repo_{}", Uuid::new_v4().simple().to_string()[..16].to_string())
}

/// Generate a unique chunk ID with the format "chunk_[16_char_hex]"
pub fn generate_chunk_id() -> String {
    format!("chunk_{}", Uuid::new_v4().simple().to_string()[..16].to_string())
}

/// Generate a unique pattern ID with the format "pattern_[8_char_hex]"
pub fn generate_pattern_id() -> String {
    format!("pattern_{:08x}", rand::random::<u32>())
}

/// Get current timestamp in milliseconds since Unix epoch
pub fn current_timestamp_ms() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_millis() as u64
}

/// Validate URL format for repository URLs
pub fn validate_repository_url(url: &str) -> Result<()> {
    if url.is_empty() {
        return Err(anyhow::anyhow!("Repository URL cannot be empty"));
    }

    if !url.starts_with("https://") && !url.starts_with("git@") {
        return Err(anyhow::anyhow!("Invalid repository URL format. Must start with 'https://' or 'git@'"));
    }

    Ok(())
}

/// Format file size in human readable format
pub fn format_file_size(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", bytes, UNITS[unit_index])
    } else {
        format!("{:.2} {}", size, UNITS[unit_index])
    }
}

/// Sanitize filename for safe storage
pub fn sanitize_filename(filename: &str) -> String {
    filename
        .chars()
        .map(|c| {
            if c.is_alphanumeric() || c == '.' || c == '-' || c == '_' {
                c
            } else {
                '_'
            }
        })
        .collect::<String>()
        .trim_matches('_')
        .to_string()
}

/// Calculate percentage with proper rounding
pub fn calculate_percentage(part: u64, total: u64) -> f64 {
    if total == 0 {
        0.0
    } else {
        (part as f64 / total as f64) * 100.0
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_ids() {
        let analysis_id = generate_analysis_id();
        assert!(analysis_id.starts_with("analysis_"));
        assert_eq!(analysis_id.len(), 9 + 16); // "analysis_" + 16 chars

        let repo_id = generate_repository_id();
        assert!(repo_id.starts_with("repo_"));
        assert_eq!(repo_id.len(), 5 + 16); // "repo_" + 16 chars

        let chunk_id = generate_chunk_id();
        assert!(chunk_id.starts_with("chunk_"));
        assert_eq!(chunk_id.len(), 6 + 16); // "chunk_" + 16 chars
    }

    #[test]
    fn test_validate_repository_url() {
        assert!(validate_repository_url("https://github.com/user/repo").is_ok());
        assert!(validate_repository_url("**************:user/repo.git").is_ok());
        assert!(validate_repository_url("http://github.com/user/repo").is_err());
        assert!(validate_repository_url("").is_err());
        assert!(validate_repository_url("invalid-url").is_err());
    }

    #[test]
    fn test_format_file_size() {
        assert_eq!(format_file_size(0), "0 B");
        assert_eq!(format_file_size(512), "512 B");
        assert_eq!(format_file_size(1024), "1.00 KB");
        assert_eq!(format_file_size(1536), "1.50 KB");
        assert_eq!(format_file_size(1048576), "1.00 MB");
        assert_eq!(format_file_size(1073741824), "1.00 GB");
    }

    #[test]
    fn test_sanitize_filename() {
        assert_eq!(sanitize_filename("normal.rs"), "normal.rs");
        assert_eq!(sanitize_filename("file with spaces.txt"), "file_with_spaces.txt");
        assert_eq!(sanitize_filename("file/with\\slashes"), "file_with_slashes");
        assert_eq!(sanitize_filename("file@#$%^&*().rs"), "file________.rs");
    }

    #[test]
    fn test_calculate_percentage() {
        assert_eq!(calculate_percentage(0, 100), 0.0);
        assert_eq!(calculate_percentage(50, 100), 50.0);
        assert_eq!(calculate_percentage(100, 100), 100.0);
        assert_eq!(calculate_percentage(25, 100), 25.0);
        assert_eq!(calculate_percentage(1, 0), 0.0); // Division by zero
    }

    #[test]
    fn test_current_timestamp_ms() {
        let timestamp = current_timestamp_ms();
        assert!(timestamp > 0);
        
        // Should be a reasonable timestamp (after 2020)
        assert!(timestamp > 1_577_836_800_000); // 2020-01-01 00:00:00 UTC in milliseconds
    }
}