use axum::{
    extract::{Path, State},
    http::<PERSON><PERSON><PERSON>,
    Json,
};
use serde::{Deserialize, Serialize};
use tracing::{info, error, warn};
use uuid::Uuid;
use std::sync::Arc;
use tokio::sync::Mutex;
use std::collections::HashMap;

use super::AppState;
use crate::models::{AnalysisRequest, AnalysisStatus, AnalysisState, AnalysisProgress, RepositoryAnalysisOutput, ErrorResponse, AnalysisError};
use crate::services::embeddings::EmbeddingsService;
use crate::services::job_manager::AnalysisPhases;
use crate::clients::spanner::AnalysisStatus as SpannerAnalysisStatus;
use crate::utils::{generate_analysis_id, generate_repository_id, validate_repository_url};

// Custom analysis tracking for API
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisTracking {
    pub analysis_id: String,
    pub status: String,
    pub repository_url: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    pub error_message: Option<String>,
    pub storage_path: Option<String>,
    pub progress_percentage: u8,
}

// Temporary in-memory storage for analysis status
lazy_static::lazy_static! {
    static ref ANALYSIS_STATUS_CACHE: Arc<Mutex<HashMap<String, AnalysisTracking>>> = 
        Arc::new(Mutex::new(HashMap::new()));
}

#[derive(Serialize, Deserialize)]
pub struct StartAnalysisResponse {
    pub analysis_id: String,
    pub status: String,
    pub message: String,
}

#[derive(Serialize, Deserialize)]
pub struct SupportedLanguagesResponse {
    pub languages: Vec<LanguageInfo>,
    pub total_count: usize,
}

#[derive(Serialize, Deserialize)]
pub struct LanguageInfo {
    pub name: String,
    pub extensions: Vec<String>,
    pub features: Vec<String>,
}

#[derive(Serialize, Deserialize)]
pub struct JobQueueStatus {
    pub active_jobs: usize,
    pub available_slots: usize,
    pub max_concurrent: usize,
    pub jobs: Vec<JobSummary>,
}

#[derive(Serialize, Deserialize)]
pub struct JobSummary {
    pub analysis_id: String,
    pub repository_url: String,
    pub status: String,
    pub progress: f32,
    pub started_at: chrono::DateTime<chrono::Utc>,
}

pub async fn start_analysis(
    State(state): State<AppState>,
    Json(request): Json<AnalysisRequest>,
) -> Result<Json<StartAnalysisResponse>, (StatusCode, Json<ErrorResponse>)> {
    let correlation_id = Uuid::new_v4().to_string();
    info!("Starting analysis for repository: {}", request.repository_url);

    // Validate request
    if let Err(e) = validate_analysis_request(&request) {
        let error_response = ErrorResponse::from_analysis_error(e, correlation_id);
        return Err((StatusCode::BAD_REQUEST, Json(error_response)));
    }

    // Generate analysis ID
    let analysis_id = generate_analysis_id();

    // Create initial status
    let initial_status = AnalysisTracking {
        analysis_id: analysis_id.clone(),
        status: "pending".to_string(),
        repository_url: request.repository_url.clone(),
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
        completed_at: None,
        error_message: None,
        storage_path: None,
        progress_percentage: 0,
    };

    // Add to cache
    {
        let mut cache = ANALYSIS_STATUS_CACHE.lock().await;
        cache.insert(analysis_id.clone(), initial_status);
    }

    // Start analysis asynchronously
    let state_clone = state.clone();
    let request_clone = request.clone();
    let analysis_id_clone = analysis_id.clone();
    
    tokio::spawn(async move {
        if let Err(e) = perform_analysis(state_clone, request_clone, analysis_id_clone).await {
            error!("Analysis failed: {}", e);
        }
    });

    let response = StartAnalysisResponse {
        analysis_id,
        status: "queued".to_string(),
        message: "Analysis has been queued and will begin shortly".to_string(),
    };

    Ok(Json(response))
}

pub async fn get_analysis_status(
    State(state): State<AppState>,
    Path(analysis_id): Path<String>,
) -> Result<Json<AnalysisStatus>, (StatusCode, Json<ErrorResponse>)> {
    info!("Getting status for analysis: {}", analysis_id);

    // Check in-memory cache first
    let tracking = {
        let cache = ANALYSIS_STATUS_CACHE.lock().await;
        cache.get(&analysis_id).cloned()
    };

    if let Some(tracking) = tracking {
        // Convert tracking to AnalysisStatus
        let status = AnalysisStatus {
            analysis_id: tracking.analysis_id,
            status: match tracking.status.as_str() {
                "pending" => AnalysisState::Queued,
                "cloning" => AnalysisState::Cloning,
                "parsing" => AnalysisState::Parsing,
                "analyzing" => AnalysisState::Analyzing,
                "generating" => AnalysisState::Generating,
                "completed" => AnalysisState::Completed,
                "failed" => AnalysisState::Failed,
                _ => AnalysisState::Queued,
            },
            progress: AnalysisProgress {
                current_step: tracking.status.clone(),
                files_processed: 0,
                total_files: 0,
                percentage: tracking.progress_percentage as f32,
            },
            created_at: tracking.created_at,
            updated_at: tracking.updated_at,
            error: tracking.error_message,
        };
        return Ok(Json(status));
    }

    // Retrieve from Spanner
    match state.spanner_client.get_analysis(&analysis_id).await {
        Ok(record) => {
            let tracking = AnalysisTracking {
                analysis_id: record.analysis_id.clone(),
                status: match record.status {
                    SpannerAnalysisStatus::Pending => "pending".to_string(),
                    SpannerAnalysisStatus::InProgress => "processing".to_string(),
                    SpannerAnalysisStatus::Completed => "completed".to_string(),
                    SpannerAnalysisStatus::Failed => "failed".to_string(),
                },
                repository_url: record.repository_url,
                created_at: record.created_at,
                updated_at: record.updated_at,
                completed_at: record.completed_at,
                error_message: record.error_message,
                storage_path: record.storage_path,
                progress_percentage: match record.status {
                    SpannerAnalysisStatus::Pending => 0,
                    SpannerAnalysisStatus::InProgress => 50,
                    SpannerAnalysisStatus::Completed => 100,
                    SpannerAnalysisStatus::Failed => 0,
                },
            };
            
            // Update cache
            {
                let mut cache = ANALYSIS_STATUS_CACHE.lock().await;
                cache.insert(analysis_id.clone(), tracking.clone());
            }
            
            // Convert to AnalysisStatus
            let status = AnalysisStatus {
                analysis_id: tracking.analysis_id,
                status: match tracking.status.as_str() {
                    "pending" => AnalysisState::Queued,
                    "processing" => AnalysisState::Analyzing,
                    "completed" => AnalysisState::Completed,
                    "failed" => AnalysisState::Failed,
                    _ => AnalysisState::Queued,
                },
                progress: AnalysisProgress {
                    current_step: tracking.status,
                    files_processed: 0,
                    total_files: record.total_files.unwrap_or(0) as usize,
                    percentage: tracking.progress_percentage as f32,
                },
                created_at: tracking.created_at,
                updated_at: tracking.updated_at,
                error: tracking.error_message,
            };
            
            Ok(Json(status))
        }
        Err(_e) => {
            let correlation_id = Uuid::new_v4().to_string();
            let error_response = ErrorResponse::from_analysis_error(
                AnalysisError::NotFound(format!("Analysis {} not found", analysis_id)),
                correlation_id,
            );
            Err((StatusCode::NOT_FOUND, Json(error_response)))
        }
    }
}

pub async fn get_analysis_results(
    State(state): State<AppState>,
    Path(analysis_id): Path<String>,
) -> Result<Json<RepositoryAnalysisOutput>, (StatusCode, Json<ErrorResponse>)> {
    info!("Getting results for analysis: {}", analysis_id);

    // First check if analysis exists and is completed
    let analysis_record = match state.spanner_client.get_analysis(&analysis_id).await {
        Ok(record) => record,
        Err(_e) => {
            let correlation_id = Uuid::new_v4().to_string();
            let error_response = ErrorResponse::from_analysis_error(
                AnalysisError::NotFound(format!("Analysis {} not found", analysis_id)),
                correlation_id,
            );
            return Err((StatusCode::NOT_FOUND, Json(error_response)));
        }
    };

    // Check if analysis is completed
    match analysis_record.status {
        SpannerAnalysisStatus::Completed => {
            // Retrieve results from storage
            match state.storage_client.download_analysis_results(&analysis_id).await {
                Ok(results) => Ok(Json(results)),
                Err(e) => {
                    error!("Failed to retrieve analysis results: {}", e);
                    let correlation_id = Uuid::new_v4().to_string();
                    let error_response = ErrorResponse::from_analysis_error(e, correlation_id);
                    Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)))
                }
            }
        }
        SpannerAnalysisStatus::Failed => {
            let correlation_id = Uuid::new_v4().to_string();
            let error_response = ErrorResponse::from_analysis_error(
                AnalysisError::ValidationError(format!(
                    "Analysis failed: {}",
                    analysis_record.error_message.unwrap_or_else(|| "Unknown error".to_string())
                )),
                correlation_id,
            );
            Err((StatusCode::UNPROCESSABLE_ENTITY, Json(error_response)))
        }
        _ => {
            let correlation_id = Uuid::new_v4().to_string();
            let error_response = ErrorResponse::from_analysis_error(
                AnalysisError::ValidationError(format!(
                    "Analysis is still {}",
                    match analysis_record.status {
                        SpannerAnalysisStatus::Pending => "pending",
                        SpannerAnalysisStatus::InProgress => "in progress",
                        _ => "in unknown state",
                    }
                )),
                correlation_id,
            );
            Err((StatusCode::ACCEPTED, Json(error_response)))
        }
    }
}

pub async fn webhook_handler(
    State(_state): State<AppState>,
    Json(payload): Json<serde_json::Value>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    info!("Webhook received: {:?}", payload);

    // TODO: Handle GitHub webhook for repository changes
    
    Ok(Json(serde_json::json!({
        "status": "received",
        "message": "Webhook processed successfully"
    })))
}

pub async fn list_supported_languages(
    State(_state): State<AppState>,
) -> Json<SupportedLanguagesResponse> {
    let languages = vec![
        LanguageInfo {
            name: "Rust".to_string(),
            extensions: vec!["rs".to_string()],
            features: vec!["ast".to_string(), "metrics".to_string(), "patterns".to_string()],
        },
        LanguageInfo {
            name: "Python".to_string(),
            extensions: vec!["py".to_string(), "pyw".to_string()],
            features: vec!["ast".to_string(), "metrics".to_string(), "patterns".to_string()],
        },
        LanguageInfo {
            name: "JavaScript".to_string(),
            extensions: vec!["js".to_string(), "jsx".to_string()],
            features: vec!["ast".to_string(), "metrics".to_string(), "patterns".to_string()],
        },
        LanguageInfo {
            name: "TypeScript".to_string(),
            extensions: vec!["ts".to_string(), "tsx".to_string()],
            features: vec!["ast".to_string(), "metrics".to_string(), "patterns".to_string()],
        },
        LanguageInfo {
            name: "Go".to_string(),
            extensions: vec!["go".to_string()],
            features: vec!["ast".to_string(), "metrics".to_string(), "patterns".to_string()],
        },
    ];

    Json(SupportedLanguagesResponse {
        total_count: languages.len(),
        languages,
    })
}

pub async fn get_job_queue_status(
    State(state): State<AppState>,
) -> Json<JobQueueStatus> {
    let (active, available) = state.job_manager.get_queue_status().await;
    let active_jobs = state.job_manager.get_active_jobs().await;
    
    let jobs: Vec<JobSummary> = active_jobs.into_iter().map(|job| {
        JobSummary {
            analysis_id: job.analysis_id,
            repository_url: job.repository_url,
            status: match job.status {
                crate::services::job_manager::JobStatus::Queued => "queued".to_string(),
                crate::services::job_manager::JobStatus::Running => "running".to_string(),
                crate::services::job_manager::JobStatus::Completed => "completed".to_string(),
                crate::services::job_manager::JobStatus::Failed(ref err) => format!("failed: {}", err),
            },
            progress: job.progress.overall_progress,
            started_at: job.started_at,
        }
    }).collect();
    
    Json(JobQueueStatus {
        active_jobs: active,
        available_slots: available,
        max_concurrent: 10, // This should match the value in main.rs
        jobs,
    })
}

fn validate_analysis_request(request: &AnalysisRequest) -> Result<(), AnalysisError> {
    validate_repository_url(&request.repository_url)
        .map_err(|e| AnalysisError::ValidationError(e.to_string()))?;
    
    Ok(())
}

async fn perform_analysis(
    state: AppState,
    request: AnalysisRequest,
    analysis_id: String,
) -> Result<(), AnalysisError> {
    info!("Performing analysis {} for {}", analysis_id, request.repository_url);
    let start_time = std::time::Instant::now();

    // Acquire job slot
    let job_handle = state.job_manager.acquire_job_slot(
        analysis_id.clone(),
        request.repository_url.clone(),
    ).await?;

    // Create analysis record in Spanner
    let repo_id = generate_repository_id();
    let repository_info = crate::models::RepositoryInfo {
        id: repo_id,
        url: request.repository_url.clone(),
        commit: request.commit.as_deref().unwrap_or("HEAD").to_string(),
        branch: request.branch.as_deref().unwrap_or("main").to_string(),
        size_bytes: 0, // Will be updated later
        clone_time_ms: 0, // Will be updated later
    };
    let created_analysis_id = state.spanner_client.create_analysis(&repository_info).await?;
    
    // Verify the analysis_id matches
    if created_analysis_id != analysis_id {
        warn!("Analysis ID mismatch: expected {}, got {}", analysis_id, created_analysis_id);
    }

    // Update status to in-progress
    state.spanner_client.update_analysis_status(
        &analysis_id,
        SpannerAnalysisStatus::InProgress,
        None,
    ).await?;

    // Publish analysis started event
    state.pubsub_client.publish_analysis_started(
        &analysis_id,
        &request.repository_url,
        request.commit.as_deref().unwrap_or("HEAD"),
        request.branch.as_deref().unwrap_or("main"),
    ).await?;

    // 1. Clone repository
    info!("Cloning repository: {}", request.repository_url);
    job_handle.update_progress(
        AnalysisPhases::CLONING.0,
        0.0,
        AnalysisPhases::calculate_overall_progress(AnalysisPhases::CLONING.0, 0.0),
        "Starting repository clone",
    ).await?;
    let cloned_repo = match state.git_service.clone_repository(
        &request.repository_url,
        request.branch.as_deref(),
        request.commit.as_deref(),
    ).await {
        Ok(repo) => {
            job_handle.update_progress(
                AnalysisPhases::CLONING.0,
                1.0,
                AnalysisPhases::calculate_overall_progress(AnalysisPhases::CLONING.0, 1.0),
                "Repository cloned successfully",
            ).await?;
            repo
        },
        Err(e) => {
            error!("Failed to clone repository: {}", e);
            job_handle.fail(format!("Failed to clone repository: {}", e)).await?;
            state.spanner_client.update_analysis_status(
                &analysis_id,
                SpannerAnalysisStatus::Failed,
                Some(format!("Failed to clone repository: {}", e)),
            ).await?;
            
            state.pubsub_client.publish_analysis_failed(
                &analysis_id,
                &request.repository_url,
                "CLONE_FAILED",
                &format!("Clone failed: {}", e),
            ).await?;
            
            return Err(e);
        }
    };

    // 2. Analyze repository
    info!("Analyzing repository at: {:?}", cloned_repo.path);
    job_handle.update_progress(
        AnalysisPhases::ANALYZING.0,
        0.0,
        AnalysisPhases::calculate_overall_progress(AnalysisPhases::ANALYZING.0, 0.0),
        "Starting repository analysis",
    ).await?;
    
    let embeddings_service = EmbeddingsService::new(&state.settings.embeddings)?;
    
    let analysis_result = match state.analyzer.analyze_repository(
        &cloned_repo.path,
        &embeddings_service,
    ).await {
        Ok(result) => {
            job_handle.update_progress(
                AnalysisPhases::ANALYZING.0,
                1.0,
                AnalysisPhases::calculate_overall_progress(AnalysisPhases::ANALYZING.0, 1.0),
                "Analysis completed successfully",
            ).await?;
            result
        },
        Err(e) => {
            error!("Failed to analyze repository: {}", e);
            job_handle.fail(format!("Failed to analyze repository: {}", e)).await?;
            state.spanner_client.update_analysis_status(
                &analysis_id,
                SpannerAnalysisStatus::Failed,
                Some(format!("Analysis failed: {}", e)),
            ).await?;
            
            state.pubsub_client.publish_analysis_failed(
                &analysis_id,
                &request.repository_url,
                "ANALYSIS_FAILED",
                &format!("Analysis failed: {}", e),
            ).await?;
            
            return Err(e);
        }
    };

    // 3. Prepare output
    let mut output = RepositoryAnalysisOutput::new(
        request.repository_url.clone(),
        cloned_repo.info.commit.clone(),
        cloned_repo.info.branch.clone(),
    );
    
    output.analysis.files = analysis_result.files;
    output.analysis.metrics = analysis_result.metrics;
    output.analysis.languages = analysis_result.languages;
    output.analysis.embeddings = analysis_result.embeddings;
    output.analysis.patterns = Some(analysis_result.patterns);

    // 4. Store results
    info!("Storing analysis results");
    job_handle.update_progress(
        AnalysisPhases::STORING.0,
        0.0,
        AnalysisPhases::calculate_overall_progress(AnalysisPhases::STORING.0, 0.0),
        "Storing analysis results",
    ).await?;
    
    let storage_path = match state.storage_client.upload_analysis_results(
        &analysis_id,
        &output,
    ).await {
        Ok(path) => {
            job_handle.update_progress(
                AnalysisPhases::STORING.0,
                1.0,
                AnalysisPhases::calculate_overall_progress(AnalysisPhases::STORING.0, 1.0),
                "Results stored successfully",
            ).await?;
            path
        },
        Err(e) => {
            error!("Failed to store analysis results: {}", e);
            job_handle.fail(format!("Failed to store results: {}", e)).await?;
            state.spanner_client.update_analysis_status(
                &analysis_id,
                SpannerAnalysisStatus::Failed,
                Some(format!("Failed to store results: {}", e)),
            ).await?;
            
            state.pubsub_client.publish_analysis_failed(
                &analysis_id,
                &request.repository_url,
                "STORAGE_FAILED",
                &format!("Storage failed: {}", e),
            ).await?;
            
            return Err(e);
        }
    };

    // 5. Update analysis record with results
    state.spanner_client.update_analysis_results(
        &analysis_id,
        &storage_path,
        output.analysis.metrics.total_files,
        output.analysis.metrics.total_lines as u32,
    ).await?;

    // 6. Update status to completed
    state.spanner_client.update_analysis_status(
        &analysis_id,
        SpannerAnalysisStatus::Completed,
        None,
    ).await?;

    // 7. Publish completion event
    job_handle.update_progress(
        AnalysisPhases::PUBLISHING.0,
        0.0,
        AnalysisPhases::calculate_overall_progress(AnalysisPhases::PUBLISHING.0, 0.0),
        "Publishing completion event",
    ).await?;
    
    let duration_ms = start_time.elapsed().as_millis() as u64;
    state.pubsub_client.publish_analysis_completed(
        &analysis_id,
        &request.repository_url,
        cloned_repo.info.commit.as_str(),
        cloned_repo.info.branch.as_str(),
        &storage_path,
        output.analysis.metrics.total_files,
        output.analysis.metrics.total_lines as u32,
        &output.analysis.languages.primary_language,
        duration_ms,
    ).await?;

    // Update progress to 100% and mark job as complete
    job_handle.update_progress(
        AnalysisPhases::PUBLISHING.0,
        1.0,
        1.0,
        "Analysis completed successfully",
    ).await?;
    job_handle.complete().await?;

    info!("Analysis {} completed successfully in {}ms", analysis_id, duration_ms);
    Ok(())
}
