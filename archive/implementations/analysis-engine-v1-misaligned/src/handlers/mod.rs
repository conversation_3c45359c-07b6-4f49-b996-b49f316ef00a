pub mod health;
pub mod analysis;

use crate::config::Setting<PERSON>;
use crate::services::{analyzer::AnalyzerService, parser::ParserService, git::GitService, job_manager::JobManager};
use crate::clients::{spanner::SpannerClient, storage::StorageClient, pubsub::PubSubClient};

#[derive(Clone)]
pub struct AppState {
    pub analyzer: AnalyzerService,
    pub parser: ParserService,
    pub git_service: GitService,
    pub spanner_client: SpannerClient,
    pub storage_client: StorageClient,
    pub pubsub_client: PubSubClient,
    pub job_manager: JobManager,
    pub settings: Settings,
}
