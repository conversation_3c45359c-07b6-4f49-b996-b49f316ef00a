use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::{debug, info};
use uuid::Uuid;

use crate::models::AnalysisError;

#[derive(Clone)]
pub struct PubSubClient {
    project_id: String,
    // Topic configurations
    analysis_complete_topic: String,
    analysis_failed_topic: String,
    // In production, this would be the actual Pub/Sub client
    mock_publisher: Arc<Mutex<Vec<PublishedMessage>>>,
}

#[derive(Debug, Clone)]
struct PublishedMessage {
    topic: String,
    message: PubSubMessage,
    published_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PubSubMessage {
    pub message_id: String,
    pub data: serde_json::Value,
    pub attributes: HashMap<String, String>,
    pub publish_time: DateTime<Utc>,
}

// Event types
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(tag = "event_type")]
pub enum AnalysisEvent {
    #[serde(rename = "analysis.started")]
    AnalysisStarted {
        analysis_id: String,
        repository_url: String,
        commit_sha: String,
        branch: String,
        started_at: DateTime<Utc>,
    },
    
    #[serde(rename = "analysis.completed")]
    AnalysisCompleted {
        analysis_id: String,
        repository_url: String,
        commit_sha: String,
        branch: String,
        storage_path: String,
        total_files: u32,
        total_lines: u32,
        primary_language: String,
        duration_ms: u64,
        completed_at: DateTime<Utc>,
    },
    
    #[serde(rename = "analysis.failed")]
    AnalysisFailed {
        analysis_id: String,
        repository_url: String,
        error_code: String,
        error_message: String,
        failed_at: DateTime<Utc>,
    },
    
    #[serde(rename = "analysis.progress")]
    AnalysisProgress {
        analysis_id: String,
        stage: String,
        progress_percentage: f32,
        message: String,
        updated_at: DateTime<Utc>,
    },
}

impl PubSubClient {
    pub fn new(project_id: &str) -> Result<Self> {
        info!("Initializing Pub/Sub client for project: {}", project_id);

        Ok(Self {
            project_id: project_id.to_string(),
            analysis_complete_topic: "repository-analysis-complete".to_string(),
            analysis_failed_topic: "repository-analysis-failed".to_string(),
            mock_publisher: Arc::new(Mutex::new(Vec::new())),
        })
    }

    pub async fn publish_analysis_started(
        &self,
        analysis_id: &str,
        repository_url: &str,
        commit_sha: &str,
        branch: &str,
    ) -> Result<String, AnalysisError> {
        let event = AnalysisEvent::AnalysisStarted {
            analysis_id: analysis_id.to_string(),
            repository_url: repository_url.to_string(),
            commit_sha: commit_sha.to_string(),
            branch: branch.to_string(),
            started_at: Utc::now(),
        };

        self.publish_event(&self.analysis_complete_topic, event).await
    }

    pub async fn publish_analysis_completed(
        &self,
        analysis_id: &str,
        repository_url: &str,
        commit_sha: &str,
        branch: &str,
        storage_path: &str,
        total_files: u32,
        total_lines: u32,
        primary_language: &str,
        duration_ms: u64,
    ) -> Result<String, AnalysisError> {
        let event = AnalysisEvent::AnalysisCompleted {
            analysis_id: analysis_id.to_string(),
            repository_url: repository_url.to_string(),
            commit_sha: commit_sha.to_string(),
            branch: branch.to_string(),
            storage_path: storage_path.to_string(),
            total_files,
            total_lines,
            primary_language: primary_language.to_string(),
            duration_ms,
            completed_at: Utc::now(),
        };

        self.publish_event(&self.analysis_complete_topic, event).await
    }

    pub async fn publish_analysis_failed(
        &self,
        analysis_id: &str,
        repository_url: &str,
        error_code: &str,
        error_message: &str,
    ) -> Result<String, AnalysisError> {
        let event = AnalysisEvent::AnalysisFailed {
            analysis_id: analysis_id.to_string(),
            repository_url: repository_url.to_string(),
            error_code: error_code.to_string(),
            error_message: error_message.to_string(),
            failed_at: Utc::now(),
        };

        self.publish_event(&self.analysis_failed_topic, event).await
    }

    pub async fn publish_analysis_progress(
        &self,
        analysis_id: &str,
        stage: &str,
        progress_percentage: f32,
        message: &str,
    ) -> Result<String, AnalysisError> {
        let event = AnalysisEvent::AnalysisProgress {
            analysis_id: analysis_id.to_string(),
            stage: stage.to_string(),
            progress_percentage,
            message: message.to_string(),
            updated_at: Utc::now(),
        };

        self.publish_event(&self.analysis_complete_topic, event).await
    }

    async fn publish_event<T: Serialize>(
        &self,
        topic: &str,
        event: T,
    ) -> Result<String, AnalysisError> {
        let message_id = format!("msg_{}", Uuid::new_v4().simple().to_string()[..16].to_string());
        
        let data = serde_json::to_value(&event)
            .map_err(|e| AnalysisError::ConfigError(format!("Failed to serialize event: {}", e)))?;
        
        let mut attributes = HashMap::new();
        attributes.insert("service".to_string(), "analysis-engine".to_string());
        attributes.insert("version".to_string(), "1.0.0".to_string());
        
        let message = PubSubMessage {
            message_id: message_id.clone(),
            data,
            attributes,
            publish_time: Utc::now(),
        };

        info!("Publishing message {} to topic {}", message_id, topic);

        // In production, this would publish to actual Pub/Sub
        self.mock_publish(topic, message).await?;

        Ok(message_id)
    }

    async fn mock_publish(&self, topic: &str, message: PubSubMessage) -> Result<(), AnalysisError> {
        let mut publisher = self.mock_publisher.lock().await;
        
        publisher.push(PublishedMessage {
            topic: topic.to_string(),
            message,
            published_at: Utc::now(),
        });

        debug!("Mock published message to topic: {}", topic);
        Ok(())
    }

    // Test helper methods
    #[cfg(test)]
    pub async fn get_published_messages(&self) -> Vec<PublishedMessage> {
        self.mock_publisher.lock().await.clone()
    }
}

// Production Pub/Sub implementation skeleton (commented out)
/*
use google_cloud_pubsub::client::{Client, ClientConfig};
use google_cloud_pubsub::publisher::Publisher;
use google_cloud_pubsub::subscription::Message;

impl PubSubClient {
    async fn real_publish_event<T: Serialize>(
        &self,
        topic_name: &str,
        event: T,
    ) -> Result<String, AnalysisError> {
        let config = ClientConfig::default()
            .with_project_id(Some(self.project_id.clone()));
        
        let client = Client::new(config).await
            .map_err(|e| AnalysisError::ExternalServiceError { 
                service: "pubsub".to_string(), 
                message: format!("PubSub client error: {}", e) 
            })?;
        
        let topic = client.topic(topic_name);
        let publisher = topic.new_publisher(None);
        
        let data = serde_json::to_vec(&event)
            .map_err(|e| AnalysisError::SerializationError(format!("Failed to serialize: {}", e)))?;
        
        let message_id = publisher
            .publish(data)
            .await
            .map_err(|e| AnalysisError::ExternalServiceError { 
                service: "pubsub".to_string(), 
                message: format!("Publish failed: {}", e) 
            })?.
        
        Ok(message_id)
    }
}
*/

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_pubsub_client_initialization() {
        let client = PubSubClient::new("test-project").unwrap();
        assert_eq!(client.project_id, "test-project");
        assert_eq!(client.analysis_complete_topic, "repository-analysis-complete");
        assert_eq!(client.analysis_failed_topic, "repository-analysis-failed");
    }

    #[tokio::test]
    async fn test_publish_analysis_started() {
        let client = PubSubClient::new("test-project").unwrap();
        
        let message_id = client.publish_analysis_started(
            "analysis_123",
            "https://github.com/test/repo",
            "abcdef123456",
            "main",
        ).await.unwrap();
        
        assert!(message_id.starts_with("msg_"));
        
        let messages = client.get_published_messages().await;
        assert_eq!(messages.len(), 1);
        
        let message = &messages[0];
        assert_eq!(message.topic, "repository-analysis-complete");
        
        if let serde_json::Value::Object(data) = &message.message.data {
            assert_eq!(data.get("event_type").unwrap(), "analysis.started");
            assert_eq!(data.get("analysis_id").unwrap(), "analysis_123");
        } else {
            panic!("Expected object data");
        }
    }

    #[tokio::test]
    async fn test_publish_analysis_completed() {
        let client = PubSubClient::new("test-project").unwrap();
        
        let message_id = client.publish_analysis_completed(
            "analysis_123",
            "https://github.com/test/repo",
            "abcdef123456",
            "main",
            "gs://bucket/analysis_123/results.json",
            100,
            10000,
            "rust",
            5000,
        ).await.unwrap();
        
        assert!(message_id.starts_with("msg_"));
        
        let messages = client.get_published_messages().await;
        assert_eq!(messages.len(), 1);
        
        let message = &messages[0];
        if let serde_json::Value::Object(data) = &message.message.data {
            assert_eq!(data.get("event_type").unwrap(), "analysis.completed");
            assert_eq!(data.get("total_files").unwrap(), 100);
            assert_eq!(data.get("primary_language").unwrap(), "rust");
        } else {
            panic!("Expected object data");
        }
    }

    #[tokio::test]
    async fn test_publish_analysis_failed() {
        let client = PubSubClient::new("test-project").unwrap();
        
        let message_id = client.publish_analysis_failed(
            "analysis_123",
            "https://github.com/test/repo",
            "CLONE_FAILED",
            "Repository not found",
        ).await.unwrap();
        
        assert!(message_id.starts_with("msg_"));
        
        let messages = client.get_published_messages().await;
        assert_eq!(messages.len(), 1);
        
        let message = &messages[0];
        assert_eq!(message.topic, "repository-analysis-failed");
        
        if let serde_json::Value::Object(data) = &message.message.data {
            assert_eq!(data.get("event_type").unwrap(), "analysis.failed");
            assert_eq!(data.get("error_code").unwrap(), "CLONE_FAILED");
        } else {
            panic!("Expected object data");
        }
    }

    #[tokio::test]
    async fn test_message_attributes() {
        let client = PubSubClient::new("test-project").unwrap();
        
        client.publish_analysis_progress(
            "analysis_123",
            "parsing",
            50.0,
            "Parsing files...",
        ).await.unwrap();
        
        let messages = client.get_published_messages().await;
        let message = &messages[0];
        
        assert_eq!(message.message.attributes.get("service").unwrap(), "analysis-engine");
        assert_eq!(message.message.attributes.get("version").unwrap(), "1.0.0");
    }
}