use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::{debug, info};
use uuid::Uuid;

use crate::models::{AnalysisError, RepositoryInfo};

#[derive(Clone)]
pub struct SpannerClient {
    project_id: String,
    instance_id: String,
    database_id: String,
    // In production, this would be the actual Spanner client
    mock_storage: Arc<Mutex<Vec<AnalysisRecord>>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisRecord {
    pub analysis_id: String,
    pub repository_id: String,
    pub repository_url: String,
    pub commit_sha: String,
    pub branch: String,
    pub status: AnalysisStatus,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
    pub error_message: Option<String>,
    pub storage_path: Option<String>,
    pub total_files: Option<i64>,
    pub total_lines: Option<i64>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum AnalysisStatus {
    Pending,
    InProgress,
    Completed,
    Failed,
}

impl SpannerClient {
    pub fn new(project_id: &str, instance_id: &str, database_id: &str) -> Result<Self> {
        info!(
            "Initializing Spanner client for project: {}, instance: {}, database: {}",
            project_id, instance_id, database_id
        );

        Ok(Self {
            project_id: project_id.to_string(),
            instance_id: instance_id.to_string(),
            database_id: database_id.to_string(),
            mock_storage: Arc::new(Mutex::new(Vec::new())),
        })
    }

    pub async fn create_analysis(
        &self,
        repository_info: &RepositoryInfo,
    ) -> Result<String, AnalysisError> {
        let analysis_id = format!("analysis_{}", Uuid::new_v4().simple().to_string()[..16].to_string());
        let now = Utc::now();

        let record = AnalysisRecord {
            analysis_id: analysis_id.clone(),
            repository_id: repository_info.id.clone(),
            repository_url: repository_info.url.clone(),
            commit_sha: repository_info.commit.clone(),
            branch: repository_info.branch.clone(),
            status: AnalysisStatus::Pending,
            created_at: now,
            updated_at: now,
            completed_at: None,
            error_message: None,
            storage_path: None,
            total_files: None,
            total_lines: None,
        };

        // In production, this would execute a Spanner mutation
        self.mock_insert_record(record).await?;

        info!("Created analysis record: {}", analysis_id);
        Ok(analysis_id)
    }

    pub async fn update_analysis_status(
        &self,
        analysis_id: &str,
        status: AnalysisStatus,
        error_message: Option<String>,
    ) -> Result<(), AnalysisError> {
        debug!("Updating analysis {} status to {:?}", analysis_id, status);

        // In production, this would be a Spanner transaction
        let mut storage = self.mock_storage.lock().await;
        
        if let Some(record) = storage.iter_mut().find(|r| r.analysis_id == analysis_id) {
            record.status = status.clone();
            record.updated_at = Utc::now();
            
            if status == AnalysisStatus::Completed || status == AnalysisStatus::Failed {
                record.completed_at = Some(Utc::now());
            }
            
            if let Some(err_msg) = error_message {
                record.error_message = Some(err_msg);
            }
            
            Ok(())
        } else {
            Err(AnalysisError::NotFound(format!("Analysis {} not found", analysis_id)))
        }
    }

    pub async fn update_analysis_results(
        &self,
        analysis_id: &str,
        storage_path: &str,
        total_files: u32,
        total_lines: u32,
    ) -> Result<(), AnalysisError> {
        debug!("Updating analysis {} results", analysis_id);

        let mut storage = self.mock_storage.lock().await;
        
        if let Some(record) = storage.iter_mut().find(|r| r.analysis_id == analysis_id) {
            record.storage_path = Some(storage_path.to_string());
            record.total_files = Some(total_files as i64);
            record.total_lines = Some(total_lines as i64);
            record.updated_at = Utc::now();
            Ok(())
        } else {
            Err(AnalysisError::NotFound(format!("Analysis {} not found", analysis_id)))
        }
    }

    pub async fn get_analysis(
        &self,
        analysis_id: &str,
    ) -> Result<AnalysisRecord, AnalysisError> {
        let storage = self.mock_storage.lock().await;
        
        storage
            .iter()
            .find(|r| r.analysis_id == analysis_id)
            .cloned()
            .ok_or_else(|| AnalysisError::NotFound(format!("Analysis {} not found", analysis_id)))
    }

    pub async fn list_analyses_for_repository(
        &self,
        repository_url: &str,
        limit: i64,
    ) -> Result<Vec<AnalysisRecord>, AnalysisError> {
        let storage = self.mock_storage.lock().await;
        
        let mut analyses: Vec<_> = storage
            .iter()
            .filter(|r| r.repository_url == repository_url)
            .cloned()
            .collect();
        
        // Sort by created_at descending
        analyses.sort_by(|a, b| b.created_at.cmp(&a.created_at));
        
        // Apply limit
        analyses.truncate(limit as usize);
        
        Ok(analyses)
    }

    pub async fn check_duplicate_analysis(
        &self,
        repository_url: &str,
        commit_sha: &str,
    ) -> Result<Option<String>, AnalysisError> {
        let storage = self.mock_storage.lock().await;
        
        let existing = storage
            .iter()
            .find(|r| {
                r.repository_url == repository_url && 
                r.commit_sha == commit_sha &&
                r.status == AnalysisStatus::Completed
            })
            .map(|r| r.analysis_id.clone());
        
        Ok(existing)
    }

    // Mock implementation helpers
    async fn mock_insert_record(&self, record: AnalysisRecord) -> Result<(), AnalysisError> {
        let mut storage = self.mock_storage.lock().await;
        storage.push(record);
        Ok(())
    }
}

// Production Spanner implementation skeleton (commented out)
/*
use google_cloud_spanner::client::{Client, ClientConfig};
use google_cloud_spanner::mutation::insert;
use google_cloud_spanner::statement::Statement;
use google_cloud_spanner::transaction::Transaction;

impl SpannerClient {
    async fn real_create_analysis(&self, repository_info: &RepositoryInfo) -> Result<String, AnalysisError> {
        let config = ClientConfig::default()
            .with_project_id(self.project_id.clone())
            .with_instance_id(self.instance_id.clone())
            .with_database_id(self.database_id.clone());
        
        let client = Client::new(config).await
            .map_err(|e| AnalysisError::DatabaseError(format!("Failed to create client: {}", e)))?;
        
        let analysis_id = format!("analysis_{}", Uuid::new_v4().simple().to_string()[..16].to_string());
        let now = Utc::now();
        
        let mutation = insert(
            "analyses",
            vec!["analysis_id", "repository_id", "repository_url", "commit_sha", "branch", "status", "created_at", "updated_at"],
            vec![vec![
                analysis_id.clone(),
                repository_info.id.clone(),
                repository_info.url.clone(),
                repository_info.commit.clone(),
                repository_info.branch.clone(),
                "pending".to_string(),
                now.to_rfc3339(),
                now.to_rfc3339(),
            ]],
        );
        
        client.apply(vec![mutation]).await
            .map_err(|e| AnalysisError::DatabaseError(format!("Failed to insert: {}", e)))?;
        
        Ok(analysis_id)
    }
}
*/

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_spanner_client_initialization() {
        let client = SpannerClient::new("test-project", "test-instance", "test-db").unwrap();
        assert_eq!(client.project_id, "test-project");
        assert_eq!(client.instance_id, "test-instance");
        assert_eq!(client.database_id, "test-db");
    }

    #[tokio::test]
    async fn test_create_and_get_analysis() {
        let client = SpannerClient::new("test-project", "test-instance", "test-db").unwrap();
        
        let repo_info = RepositoryInfo {
            id: "repo_1234567890abcdef".to_string(),
            url: "https://github.com/test/repo".to_string(),
            commit: "abcdef1234567890abcdef1234567890abcdef12".to_string(),
            branch: "main".to_string(),
            size_bytes: 1000000,
            clone_time_ms: 5000,
        };
        
        let analysis_id = client.create_analysis(&repo_info).await.unwrap();
        assert!(analysis_id.starts_with("analysis_"));
        
        let record = client.get_analysis(&analysis_id).await.unwrap();
        assert_eq!(record.repository_url, repo_info.url);
        assert_eq!(record.status, AnalysisStatus::Pending);
    }

    #[tokio::test]
    async fn test_update_analysis_status() {
        let client = SpannerClient::new("test-project", "test-instance", "test-db").unwrap();
        
        let repo_info = RepositoryInfo {
            id: "repo_1234567890abcdef".to_string(),
            url: "https://github.com/test/repo".to_string(),
            commit: "abcdef1234567890abcdef1234567890abcdef12".to_string(),
            branch: "main".to_string(),
            size_bytes: 1000000,
            clone_time_ms: 5000,
        };
        
        let analysis_id = client.create_analysis(&repo_info).await.unwrap();
        
        // Update to in progress
        client.update_analysis_status(&analysis_id, AnalysisStatus::InProgress, None).await.unwrap();
        let record = client.get_analysis(&analysis_id).await.unwrap();
        assert_eq!(record.status, AnalysisStatus::InProgress);
        
        // Update to completed
        client.update_analysis_status(&analysis_id, AnalysisStatus::Completed, None).await.unwrap();
        let record = client.get_analysis(&analysis_id).await.unwrap();
        assert_eq!(record.status, AnalysisStatus::Completed);
        assert!(record.completed_at.is_some());
    }

    #[tokio::test]
    async fn test_check_duplicate_analysis() {
        let client = SpannerClient::new("test-project", "test-instance", "test-db").unwrap();
        
        let repo_info = RepositoryInfo {
            id: "repo_1234567890abcdef".to_string(),
            url: "https://github.com/test/repo".to_string(),
            commit: "abcdef1234567890abcdef1234567890abcdef12".to_string(),
            branch: "main".to_string(),
            size_bytes: 1000000,
            clone_time_ms: 5000,
        };
        
        // No duplicate initially
        let duplicate = client.check_duplicate_analysis(&repo_info.url, &repo_info.commit).await.unwrap();
        assert!(duplicate.is_none());
        
        // Create analysis
        let analysis_id = client.create_analysis(&repo_info).await.unwrap();
        client.update_analysis_status(&analysis_id, AnalysisStatus::Completed, None).await.unwrap();
        
        // Now should find duplicate
        let duplicate = client.check_duplicate_analysis(&repo_info.url, &repo_info.commit).await.unwrap();
        assert_eq!(duplicate, Some(analysis_id));
    }
}