use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::path::Path;
use std::sync::Arc;
use tokio::fs;
use tokio::sync::Mutex;
use tracing::{debug, info};

use crate::models::{AnalysisError, RepositoryAnalysisOutput};

#[derive(Clone)]
pub struct StorageClient {
    bucket_name: String,
    project_id: String,
    // In production, this would be the actual GCS client
    mock_storage: Arc<Mutex<MockStorage>>,
}

struct MockStorage {
    objects: std::collections::HashMap<String, Vec<u8>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageMetadata {
    pub content_type: String,
    pub size_bytes: u64,
    pub created_at: DateTime<Utc>,
    pub etag: String,
    pub content_encoding: Option<String>,
}

impl StorageClient {
    pub fn new(project_id: &str, bucket_name: &str) -> Result<Self> {
        info!(
            "Initializing Storage client for project: {}, bucket: {}",
            project_id, bucket_name
        );

        Ok(Self {
            project_id: project_id.to_string(),
            bucket_name: bucket_name.to_string(),
            mock_storage: Arc::new(Mutex::new(MockStorage {
                objects: std::collections::HashMap::new(),
            })),
        })
    }

    pub async fn upload_analysis_results(
        &self,
        analysis_id: &str,
        results: &RepositoryAnalysisOutput,
    ) -> Result<String, AnalysisError> {
        let object_path = format!("analyses/{}/results.json", analysis_id);
        
        info!("Uploading analysis results to: {}", object_path);

        // Serialize results to JSON
        let json_data = serde_json::to_vec_pretty(results)
            .map_err(|e| AnalysisError::ConfigError(format!("Failed to serialize: {}", e)))?;

        // Compress with gzip
        let compressed_data = self.compress_data(&json_data).await?;

        // Upload to storage
        self.upload_object(&object_path, &compressed_data, "application/json", Some("gzip")).await?;

        let full_path = format!("gs://{}/{}", self.bucket_name, object_path);
        info!("Successfully uploaded analysis results to: {}", full_path);

        Ok(full_path)
    }

    pub async fn download_analysis_results(
        &self,
        analysis_id: &str,
    ) -> Result<RepositoryAnalysisOutput, AnalysisError> {
        let object_path = format!("analyses/{}/results.json", analysis_id);
        
        debug!("Downloading analysis results from: {}", object_path);

        // Download from storage
        let compressed_data = self.download_object(&object_path).await?;

        // Decompress
        let json_data = self.decompress_data(&compressed_data).await?;

        // Deserialize
        let results: RepositoryAnalysisOutput = serde_json::from_slice(&json_data)
            .map_err(|e| AnalysisError::ConfigError(format!("Failed to deserialize: {}", e)))?;

        Ok(results)
    }

    pub async fn upload_file(
        &self,
        local_path: &Path,
        remote_path: &str,
    ) -> Result<String, AnalysisError> {
        debug!("Uploading file from {:?} to {}", local_path, remote_path);

        // Read file
        let data = fs::read(local_path).await
            .map_err(|e| AnalysisError::IoError(e))?;

        // Determine content type
        let content_type = self.detect_content_type(local_path);

        // Upload
        self.upload_object(remote_path, &data, &content_type, None).await?;

        let full_path = format!("gs://{}/{}", self.bucket_name, remote_path);
        Ok(full_path)
    }

    pub async fn list_analyses(
        &self,
        prefix: &str,
        limit: usize,
    ) -> Result<Vec<String>, AnalysisError> {
        debug!("Listing objects with prefix: {}", prefix);

        // In production, this would use GCS list API
        let storage = self.mock_storage.lock().await;
        
        let objects: Vec<String> = storage.objects.keys()
            .filter(|k| k.starts_with(prefix))
            .take(limit)
            .cloned()
            .collect();

        Ok(objects)
    }

    pub async fn delete_analysis_results(
        &self,
        analysis_id: &str,
    ) -> Result<(), AnalysisError> {
        let object_path = format!("analyses/{}/results.json", analysis_id);
        
        info!("Deleting analysis results: {}", object_path);

        // In production, this would use GCS delete API
        let mut storage = self.mock_storage.lock().await;
        storage.objects.remove(&object_path);

        Ok(())
    }

    async fn upload_object(
        &self,
        path: &str,
        data: &[u8],
        content_type: &str,
        _content_encoding: Option<&str>,
    ) -> Result<(), AnalysisError> {
        // In production, this would use GCS upload API
        let mut storage = self.mock_storage.lock().await;
        storage.objects.insert(path.to_string(), data.to_vec());

        debug!(
            "Uploaded object: {} (size: {} bytes, type: {})",
            path,
            data.len(),
            content_type
        );

        Ok(())
    }

    async fn download_object(&self, path: &str) -> Result<Vec<u8>, AnalysisError> {
        // In production, this would use GCS download API
        let storage = self.mock_storage.lock().await;
        
        storage.objects.get(path)
            .cloned()
            .ok_or_else(|| AnalysisError::NotFound(format!("Object not found: {}", path)))
    }

    async fn compress_data(&self, data: &[u8]) -> Result<Vec<u8>, AnalysisError> {
        use flate2::write::GzEncoder;
        use flate2::Compression;
        use std::io::Write;

        let mut encoder = GzEncoder::new(Vec::new(), Compression::default());
        encoder.write_all(data)
            .map_err(|e| AnalysisError::IoError(e.into()))?;
        
        encoder.finish()
            .map_err(|e| AnalysisError::IoError(e.into()))
    }

    async fn decompress_data(&self, data: &[u8]) -> Result<Vec<u8>, AnalysisError> {
        use flate2::read::GzDecoder;
        use std::io::Read;

        let mut decoder = GzDecoder::new(data);
        let mut decompressed = Vec::new();
        decoder.read_to_end(&mut decompressed)
            .map_err(|e| AnalysisError::IoError(e.into()))?;
        
        Ok(decompressed)
    }

    fn detect_content_type(&self, path: &Path) -> String {
        match path.extension().and_then(|ext| ext.to_str()) {
            Some("json") => "application/json",
            Some("txt") => "text/plain",
            Some("rs") => "text/x-rust",
            Some("py") => "text/x-python",
            Some("js") => "text/javascript",
            Some("ts") => "text/typescript",
            Some("go") => "text/x-go",
            _ => "application/octet-stream",
        }.to_string()
    }
}

// Production GCS implementation skeleton (commented out)
/*
use google_cloud_storage::{client::Client, http::objects::upload::UploadObjectRequest};
use google_cloud_storage::http::objects::download::DownloadObjectRequest;
use google_cloud_storage::http::objects::delete::DeleteObjectRequest;
use google_cloud_storage::http::objects::list::ListObjectsRequest;

impl StorageClient {
    async fn real_upload_object(
        &self,
        path: &str,
        data: Vec<u8>,
        content_type: &str,
        content_encoding: Option<&str>,
    ) -> Result<(), AnalysisError> {
        let client = Client::default().await
            .map_err(|e| AnalysisError::ExternalServiceError { 
                service: "gcs".to_string(), 
                message: format!("GCS client error: {}", e) 
            })?;
        
        let upload_request = UploadObjectRequest {
            bucket: self.bucket_name.clone(),
            name: path.to_string(),
            content_type: Some(content_type.to_string()),
            content_encoding: content_encoding.map(|s| s.to_string()),
            ..Default::default()
        };
        
        client.upload_object(&upload_request, data, None).await
            .map_err(|e| AnalysisError::ExternalServiceError { 
                service: "gcs".to_string(), 
                message: format!("Upload failed: {}", e) 
            })?;
        
        Ok(())
    }
    
    async fn real_download_object(&self, path: &str) -> Result<Vec<u8>, AnalysisError> {
        let client = Client::default().await
            .map_err(|e| AnalysisError::ExternalServiceError { 
                service: "gcs".to_string(), 
                message: format!("GCS client error: {}", e) 
            })?;
        
        let download_request = DownloadObjectRequest {
            bucket: self.bucket_name.clone(),
            name: path.to_string(),
            ..Default::default()
        };
        
        let data = client.download_object(&download_request, None).await
            .map_err(|e| AnalysisError::ExternalServiceError { 
                service: "gcs".to_string(), 
                message: format!("Download failed: {}", e) 
            })?.
        
        Ok(data)
    }
}
*/

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::{RepositoryMetrics, LanguageBreakdown};
    use std::collections::HashMap;

    #[tokio::test]
    async fn test_storage_client_initialization() {
        let client = StorageClient::new("test-project", "test-bucket").unwrap();
        assert_eq!(client.project_id, "test-project");
        assert_eq!(client.bucket_name, "test-bucket");
    }

    #[tokio::test]
    async fn test_upload_and_download_analysis() {
        let client = StorageClient::new("test-project", "test-bucket").unwrap();
        
        let results = RepositoryAnalysisOutput {
            files: vec![],
            metrics: RepositoryMetrics {
                total_files: 10,
                total_lines: 1000,
                total_complexity: 50,
                average_complexity: 5.0,
                maintainability_score: 75.0,
                technical_debt_minutes: 120,
                test_coverage_estimate: 0.8,
            },
            languages: LanguageBreakdown {
                primary_language: "rust".to_string(),
                languages: HashMap::new(),
            },
            embeddings: vec![],
            patterns: vec![],
        };
        
        let analysis_id = "test_analysis_123";
        
        // Upload
        let upload_path = client.upload_analysis_results(analysis_id, &results).await.unwrap();
        assert!(upload_path.contains(analysis_id));
        
        // Download
        let downloaded = client.download_analysis_results(analysis_id).await.unwrap();
        assert_eq!(downloaded.metrics.total_files, results.metrics.total_files);
        assert_eq!(downloaded.metrics.total_lines, results.metrics.total_lines);
    }

    #[tokio::test]
    async fn test_compress_decompress() {
        let client = StorageClient::new("test-project", "test-bucket").unwrap();
        
        let original_data = b"This is test data for compression";
        
        let compressed = client.compress_data(original_data).await.unwrap();
        assert!(compressed.len() < original_data.len()); // Should be smaller
        
        let decompressed = client.decompress_data(&compressed).await.unwrap();
        assert_eq!(decompressed, original_data);
    }

    #[tokio::test]
    async fn test_content_type_detection() {
        let client = StorageClient::new("test-project", "test-bucket").unwrap();
        
        assert_eq!(client.detect_content_type(Path::new("test.json")), "application/json");
        assert_eq!(client.detect_content_type(Path::new("main.rs")), "text/x-rust");
        assert_eq!(client.detect_content_type(Path::new("app.py")), "text/x-python");
        assert_eq!(client.detect_content_type(Path::new("unknown.xyz")), "application/octet-stream");
    }

    #[tokio::test]
    async fn test_list_analyses() {
        let client = StorageClient::new("test-project", "test-bucket").unwrap();
        
        // Upload some test objects
        client.upload_object("analyses/test1/results.json", b"data1", "application/json", None).await.unwrap();
        client.upload_object("analyses/test2/results.json", b"data2", "application/json", None).await.unwrap();
        client.upload_object("other/file.txt", b"data3", "text/plain", None).await.unwrap();
        
        // List with prefix
        let analyses = client.list_analyses("analyses/", 10).await.unwrap();
        assert_eq!(analyses.len(), 2);
        assert!(analyses.contains(&"analyses/test1/results.json".to_string()));
        assert!(analyses.contains(&"analyses/test2/results.json".to_string()));
    }
}