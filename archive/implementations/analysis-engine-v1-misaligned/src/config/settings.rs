use anyhow::Result;
use serde::{Deserialize, Serial<PERSON>};
use std::env;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Settings {
    pub server: ServerConfig,
    pub gcp: GcpConfig,
    pub git: GitConfig,
    pub analysis: AnalysisConfig,
    pub performance: PerformanceConfig,
    pub embeddings: EmbeddingsConfig,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ServerConfig {
    pub port: u16,
    pub host: String,
    pub workers: usize,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct GcpConfig {
    pub project_id: String,
    pub spanner_instance: String,
    pub spanner_database: String,
    pub storage_bucket: String,
    pub pubsub_topic: String,
    pub service_account_path: Option<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct GitConfig {
    pub clone_timeout_seconds: u64,
    pub max_repository_size_mb: u64,
    pub temp_dir: String,
    pub cleanup_after_seconds: u64,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct AnalysisConfig {
    pub max_file_size_mb: u64,
    pub max_concurrent_analyses: usize,
    pub supported_languages: Vec<String>,
    pub enable_embeddings: bool,
    pub enable_pattern_detection: bool,
    pub skip_binary_files: bool,
    pub batch_size: Option<usize>,
    pub parallel_workers: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmbeddingsConfig {
    pub vertex_ai_project_id: String,
    pub vertex_ai_location: String,
    pub embedding_model: String,
    pub batch_size: usize,
    pub max_chunk_size: usize,
    pub rate_limit_per_minute: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    pub max_memory_mb: u64,
    pub analysis_timeout_seconds: u64,
    pub parallel_file_processing: bool,
    pub cache_enabled: bool,
}

impl Settings {
    pub fn new() -> Result<Self> {
        let settings = Self {
            server: ServerConfig {
                port: env::var("PORT")
                    .unwrap_or_else(|_| "8001".to_string())
                    .parse()
                    .unwrap_or(8001),
                host: env::var("HOST").unwrap_or_else(|_| "0.0.0.0".to_string()),
                workers: env::var("WORKERS")
                    .unwrap_or_else(|_| "4".to_string())
                    .parse()
                    .unwrap_or(4),
            },
            gcp: GcpConfig {
                project_id: env::var("GCP_PROJECT_ID")
                    .unwrap_or_else(|_| "ccl-development".to_string()),
                spanner_instance: env::var("SPANNER_INSTANCE")
                    .unwrap_or_else(|_| "ccl-instance".to_string()),
                spanner_database: env::var("SPANNER_DATABASE")
                    .unwrap_or_else(|_| "ccl-database".to_string()),
                storage_bucket: env::var("STORAGE_BUCKET")
                    .unwrap_or_else(|_| "ccl-analysis-results".to_string()),
                pubsub_topic: env::var("PUBSUB_TOPIC")
                    .unwrap_or_else(|_| "repository-analysis-complete".to_string()),
                service_account_path: env::var("GOOGLE_APPLICATION_CREDENTIALS").ok(),
            },
            git: GitConfig {
                clone_timeout_seconds: env::var("GIT_CLONE_TIMEOUT")
                    .unwrap_or_else(|_| "300".to_string())
                    .parse()
                    .unwrap_or(300),
                max_repository_size_mb: env::var("MAX_REPO_SIZE_MB")
                    .unwrap_or_else(|_| "10240".to_string())
                    .parse()
                    .unwrap_or(10240),
                temp_dir: env::var("TEMP_DIR")
                    .unwrap_or_else(|_| "/tmp/ccl-analysis".to_string()),
                cleanup_after_seconds: env::var("CLEANUP_AFTER")
                    .unwrap_or_else(|_| "3600".to_string())
                    .parse()
                    .unwrap_or(3600),
            },
            analysis: AnalysisConfig {
                max_file_size_mb: env::var("MAX_FILE_SIZE_MB")
                    .unwrap_or_else(|_| "50".to_string())
                    .parse()
                    .unwrap_or(50),
                max_concurrent_analyses: env::var("MAX_CONCURRENT_ANALYSES")
                    .unwrap_or_else(|_| "10".to_string())
                    .parse()
                    .unwrap_or(10),
                supported_languages: vec![
                    "Rust".to_string(),
                    "Python".to_string(),
                    "JavaScript".to_string(),
                    "TypeScript".to_string(),
                    "Go".to_string(),
                ],
                enable_embeddings: env::var("ENABLE_EMBEDDINGS")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                enable_pattern_detection: env::var("ENABLE_PATTERN_DETECTION")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                skip_binary_files: env::var("SKIP_BINARY_FILES")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                batch_size: env::var("ANALYSIS_BATCH_SIZE")
                    .ok()
                    .and_then(|s| s.parse().ok()),
                parallel_workers: env::var("PARALLEL_WORKERS")
                    .unwrap_or_else(|_| "4".to_string())
                    .parse()
                    .unwrap_or(4),
            },
            performance: PerformanceConfig {
                max_memory_mb: env::var("MAX_MEMORY_MB")
                    .unwrap_or_else(|_| "4096".to_string())
                    .parse()
                    .unwrap_or(4096),
                analysis_timeout_seconds: env::var("ANALYSIS_TIMEOUT")
                    .unwrap_or_else(|_| "300".to_string())
                    .parse()
                    .unwrap_or(300),
                parallel_file_processing: env::var("PARALLEL_PROCESSING")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                cache_enabled: env::var("CACHE_ENABLED")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
            },
            embeddings: EmbeddingsConfig {
                vertex_ai_project_id: env::var("VERTEX_AI_PROJECT_ID")
                    .unwrap_or_else(|_| "ccl-development".to_string()),
                vertex_ai_location: env::var("VERTEX_AI_LOCATION")
                    .unwrap_or_else(|_| "us-central1".to_string()),
                embedding_model: env::var("EMBEDDING_MODEL")
                    .unwrap_or_else(|_| "text-embedding-004".to_string()),
                batch_size: env::var("EMBEDDING_BATCH_SIZE")
                    .unwrap_or_else(|_| "10".to_string())
                    .parse()
                    .unwrap_or(10),
                max_chunk_size: env::var("MAX_CHUNK_SIZE")
                    .unwrap_or_else(|_| "8192".to_string())
                    .parse()
                    .unwrap_or(8192),
                rate_limit_per_minute: env::var("EMBEDDING_RATE_LIMIT")
                    .unwrap_or_else(|_| "600".to_string())
                    .parse()
                    .unwrap_or(600),
            },
        };

        Ok(settings)
    }
}

impl Default for AnalysisConfig {
    fn default() -> Self {
        Self {
            max_file_size_mb: 50,
            max_concurrent_analyses: 10,
            supported_languages: vec![
                "Rust".to_string(),
                "Python".to_string(),
                "JavaScript".to_string(),
                "TypeScript".to_string(),
                "Go".to_string(),
            ],
            enable_embeddings: true,
            enable_pattern_detection: true,
            skip_binary_files: true,
            batch_size: Some(10),
            parallel_workers: 4,
        }
    }
}

impl Default for EmbeddingsConfig {
    fn default() -> Self {
        Self {
            vertex_ai_project_id: "ccl-development".to_string(),
            vertex_ai_location: "us-central1".to_string(),
            embedding_model: "text-embedding-004".to_string(),
            batch_size: 10,
            max_chunk_size: 8192,
            rate_limit_per_minute: 600,
        }
    }
}

impl Default for Settings {
    fn default() -> Self {
        Self::new().expect("Failed to create default settings")
    }
}
