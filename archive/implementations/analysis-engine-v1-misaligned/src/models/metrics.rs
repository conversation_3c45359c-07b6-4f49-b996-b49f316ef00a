use serde::{Deserialize, Serialize};

/// Code metrics for a single file
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, <PERSON><PERSON>ult)]
pub struct FileMetrics {
    pub lines_of_code: u32,
    pub total_lines: u32,
    pub complexity: u32,
    pub maintainability_index: f64,
    pub function_count: u32,
    pub class_count: u32,
    pub comment_ratio: f64,
}

/// Aggregate repository metrics
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, <PERSON><PERSON>ult)]
pub struct RepositoryMetrics {
    pub total_files: u32,
    pub total_lines: u64,
    pub total_complexity: u32,
    pub average_complexity: f64,
    pub maintainability_score: f64,
    pub technical_debt_minutes: u32,
    pub test_coverage_estimate: f64,
}

/// Analysis performance metrics
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct PerformanceMetrics {
    pub parsing_time_ms: u64,
    pub embedding_time_ms: u64,
    pub total_memory_mb: f64,
    pub files_per_second: f64,
    pub cache_hit_ratio: f64,
}

impl FileMetrics {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn calculate_maintainability_index(&mut self) {
        // Simplified maintainability index calculation
        // Based on Halstead Volume, Cyclomatic Complexity, and Lines of Code
        let volume = (self.lines_of_code as f64).ln();
        let complexity_penalty = self.complexity as f64;
        let size_penalty = (self.lines_of_code as f64 / 1000.0).max(1.0);
        
        self.maintainability_index = (171.0 - 5.2 * volume - 0.23 * complexity_penalty - 16.2 * size_penalty.ln())
            .max(0.0)
            .min(100.0);
    }
}

impl RepositoryMetrics {
    pub fn aggregate_from_files(&mut self, file_metrics: &[FileMetrics]) {
        self.total_files = file_metrics.len() as u32;
        self.total_lines = file_metrics.iter().map(|m| m.total_lines as u64).sum();
        self.total_complexity = file_metrics.iter().map(|m| m.complexity).sum();
        
        if !file_metrics.is_empty() {
            self.average_complexity = self.total_complexity as f64 / file_metrics.len() as f64;
            self.maintainability_score = file_metrics.iter()
                .map(|m| m.maintainability_index)
                .sum::<f64>() / file_metrics.len() as f64;
        }
        
        // Estimate technical debt (simplified calculation)
        self.technical_debt_minutes = (self.total_complexity as f64 * 0.5) as u32;
        
        // Estimate test coverage based on file patterns (simplified)
        let test_files = file_metrics.iter()
            .filter(|_| false) // TODO: Implement test file detection
            .count();
        self.test_coverage_estimate = if self.total_files > 0 {
            (test_files as f64 / self.total_files as f64).min(1.0)
        } else {
            0.0
        };
    }
}
