use serde::{Deserialize, Serialize};
use thiserror::Error;
use chrono::{DateTime, Utc};
use uuid::Uuid;

/// Unified error response format matching contracts/schemas/error-response-v1.json
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorResponse {
    pub error_id: String,
    pub service: String,
    pub error_type: ErrorType,
    pub message: String,
    pub retryable: bool,
    pub user_message: String,
    pub correlation_id: String,
    pub timestamp: DateTime<Utc>,
    pub context: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum ErrorType {
    Validation,
    Timeout,
    Internal,
    External,
}

#[derive(Error, Debug)]
pub enum AnalysisError {
    #[error("Git operation failed: {0}")]
    GitError(#[from] git2::Error),
    
    #[error("Tree-sitter parsing failed: {0}")]
    ParseError(String),
    
    #[error("File I/O error: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("Serialization error: {0}")]
    SerializationError(#[from] serde_json::Error),
    
    #[error("Configuration error: {0}")]
    ConfigError(String),
    
    #[error("Validation error: {0}")]
    ValidationError(String),
    
    #[error("Timeout error: operation took too long")]
    TimeoutError,
    
    #[error("Repository too large: {size_mb}MB exceeds limit of {limit_mb}MB")]
    RepositoryTooLarge { size_mb: u64, limit_mb: u64 },
    
    #[error("File too large: {size_mb}MB exceeds limit of {limit_mb}MB")]
    FileTooLarge { size_mb: u64, limit_mb: u64 },
    
    #[error("Unsupported language: {language}")]
    UnsupportedLanguage { language: String },
    
    #[error("Analysis capacity exceeded: {current} concurrent analyses, limit is {limit}")]
    CapacityExceeded { current: usize, limit: usize },
    
    #[error("External service error: {service} - {message}")]
    ExternalServiceError { service: String, message: String },
    
    #[error("Resource not found: {0}")]
    NotFound(String),
    
    #[error(transparent)]
    GeneralError(#[from] anyhow::Error),
}

impl ErrorResponse {
    pub fn from_analysis_error(error: AnalysisError, correlation_id: String) -> Self {
        let error_id = format!("error_{}", Uuid::new_v4().simple());
        let (error_type, retryable, user_message) = match &error {
            AnalysisError::GitError(_) => (
                ErrorType::External,
                true,
                "Failed to access repository. Please check the URL and try again.".to_string(),
            ),
            AnalysisError::ParseError(_) => (
                ErrorType::Internal,
                false,
                "Failed to parse code. The repository may contain unsupported syntax.".to_string(),
            ),
            AnalysisError::IoError(_) => (
                ErrorType::Internal,
                true,
                "File system error occurred. Please try again.".to_string(),
            ),
            AnalysisError::ValidationError(_) => (
                ErrorType::Validation,
                false,
                "Invalid request parameters. Please check your input and try again.".to_string(),
            ),
            AnalysisError::TimeoutError => (
                ErrorType::Timeout,
                true,
                "Analysis took too long to complete. Please try again with a smaller repository.".to_string(),
            ),
            AnalysisError::RepositoryTooLarge { .. } => (
                ErrorType::Validation,
                false,
                "Repository is too large for analysis. Please try with a smaller repository.".to_string(),
            ),
            AnalysisError::FileTooLarge { .. } => (
                ErrorType::Validation,
                false,
                "Repository contains files that are too large for analysis.".to_string(),
            ),
            AnalysisError::UnsupportedLanguage { .. } => (
                ErrorType::Validation,
                false,
                "Repository contains unsupported programming languages.".to_string(),
            ),
            AnalysisError::CapacityExceeded { .. } => (
                ErrorType::External,
                true,
                "Analysis service is currently at capacity. Please try again later.".to_string(),
            ),
            AnalysisError::ExternalServiceError { .. } => (
                ErrorType::External,
                true,
                "External service temporarily unavailable. Please try again later.".to_string(),
            ),
            AnalysisError::NotFound(_) => (
                ErrorType::Validation,
                false,
                "The requested resource was not found.".to_string(),
            ),
            AnalysisError::GeneralError(_) => (
                ErrorType::Internal,
                false,
                "An unexpected error occurred. Please contact support if the problem persists.".to_string(),
            ),
            _ => (
                ErrorType::Internal,
                false,
                "An unexpected error occurred. Please contact support if the problem persists.".to_string(),
            ),
        };

        Self {
            error_id,
            service: "analysis-engine".to_string(),
            error_type,
            message: error.to_string(),
            retryable,
            user_message,
            correlation_id,
            timestamp: Utc::now(),
            context: serde_json::json!({}),
        }
    }
}

pub type Result<T> = std::result::Result<T, AnalysisError>;
