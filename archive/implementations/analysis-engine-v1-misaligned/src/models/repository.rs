use serde::{Deserialize, Serialize};

/// Repository information and context
#[derive(<PERSON>bug, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct RepositoryInfo {
    pub id: String,
    pub url: String,
    pub commit: String,
    pub branch: String,
    pub size_bytes: u64,
    pub clone_time_ms: u64,
}

/// Programming language distribution
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct LanguageBreakdown {
    pub primary_language: String,
    pub languages: std::collections::HashMap<String, LanguageStats>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LanguageStats {
    pub lines: u64,
    pub files: u64,
    pub percentage: f64,
    pub bytes: u64,
}
