[package]
name = "analysis-engine"
version = "0.1.0"
edition = "2021"

[dependencies]
actix-web = "4"
tokio = { version = "1", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
env_logger = "0.11"
futures-util = "0.3"
opentelemetry = "0.22.0"
actix-web-opentelemetry = "0.22.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-opentelemetry = "0.23.0"
git2 = "0.18"
tree-sitter = "0.20"
anyhow = "1.0"
thiserror = "1.0"

[dev-dependencies]
actix-rt = "2"
mockito = "1.2"
