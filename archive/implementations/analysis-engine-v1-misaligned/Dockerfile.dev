FROM rust:1.75-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    git \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Install development tools
RUN cargo install cargo-watch
RUN cargo install cargo-expand
RUN cargo install cargo-edit

# Create non-root user
RUN useradd -m -s /bin/bash rustdev
RUN chown -R rustdev:rustdev /app

USER rustdev

# Pre-install common dependencies to speed up builds
RUN cargo init --name analysis-engine
COPY --chown=rustdev:rustdev Cargo.toml* ./
RUN cargo fetch || true

# Copy source code
COPY --chown=rustdev:rustdev . .

# Set environment for better error messages
ENV RUST_BACKTRACE=1
ENV RUST_LOG=debug

# Development command with hot reloading
CMD ["cargo", "watch", "-x", "run", "-w", "src"]