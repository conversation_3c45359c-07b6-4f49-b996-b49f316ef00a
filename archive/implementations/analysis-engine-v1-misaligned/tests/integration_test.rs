#[cfg(test)]
mod tests {
    use analysis_engine::models::{AnalysisRequest, AnalysisOptions};
    
    #[test]
    fn test_analysis_request_serialization() {
        let request = AnalysisRequest {
            repository_url: "https://github.com/rust-lang/rust-clippy".to_string(),
            branch: Some("master".to_string()),
            commit: None,
            options: AnalysisOptions::default(),
        };
        
        let json = serde_json::to_string(&request).unwrap();
        assert!(json.contains("repository_url"));
        assert!(json.contains("rust-clippy"));
        
        let deserialized: AnalysisRequest = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.repository_url, request.repository_url);
    }
}