# Query Intelligence Service

Natural language processing service for code queries using Vertex AI and RAG (Retrieval-Augmented Generation).

## Overview

The Query Intelligence service enables users to ask natural language questions about their codebase and receive intelligent, context-aware responses. It uses Google's Gemini 2.5 model for language understanding and generation, combined with vector embeddings for semantic code search.

## Features

- **Natural Language Understanding**: Process queries like "How does the authentication system work?"
- **Intent Detection**: Automatically classify queries (explain, find, debug, etc.)
- **Semantic Code Search**: Find relevant code using vector embeddings
- **RAG Pipeline**: Combine retrieved code context with LLM generation
- **Caching**: Redis-based caching for performance
- **Async Processing**: Full async/await support for scalability
- **Monitoring**: OpenTelemetry instrumentation

## Tech Stack

- **Language**: Python 3.11+
- **Framework**: FastAPI
- **AI/ML**: Vertex AI (Gemini 2.5, text-embedding-004)
- **Cache**: Redis
- **Database**: Google Cloud Spanner
- **Message Queue**: Google Cloud Pub/Sub

## Project Structure

```
services/query-intelligence/
├── src/
│   └── query_intelligence/
│       ├── api/              # REST API endpoints
│       │   └── v1/           # API v1 routes
│       ├── integrations/     # External service integrations
│       ├── models/           # Pydantic models
│       ├── services/         # Business logic
│       ├── config.py         # Configuration management
│       ├── exceptions.py     # Custom exceptions
│       └── main.py           # FastAPI application
├── tests/                    # Test suite
├── requirements.txt          # Python dependencies
├── requirements-dev.txt      # Development dependencies
├── Dockerfile               # Production container
├── Dockerfile.dev           # Development container
└── .env.example             # Environment variables example
```

## Setup

### Prerequisites

- Python 3.11+
- Redis
- Google Cloud Project with Vertex AI enabled
- Service account with appropriate permissions

### Local Development

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/episteme.git
   cd episteme/services/query-intelligence
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt
   ```

4. **Set up environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Set up Google Cloud credentials**
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json
   ```

6. **Start Redis** (using Docker)
   ```bash
   docker run -d -p 6379:6379 redis:alpine
   ```

7. **Run the service**
   ```bash
   uvicorn query_intelligence.main:app --reload --port 8001
   ```

## API Documentation

Once running, access the interactive API documentation at:
- Swagger UI: http://localhost:8001/docs
- ReDoc: http://localhost:8001/redoc

### Key Endpoints

- `POST /api/v1/query` - Submit a natural language query
- `GET /api/v1/query/{query_id}` - Get query result
- `POST /api/v1/feedback` - Submit feedback on results
- `GET /api/v1/suggestions` - Get query suggestions
- `POST /api/v1/index` - Trigger repository indexing

### Example Query

```bash
curl -X POST http://localhost:8001/api/v1/query \
  -H "Content-Type: application/json" \
  -d '{
    "query": "How does the authentication middleware work?",
    "repository_id": "repo_abc123def456ghi7",
    "include_code_snippets": true,
    "max_results": 5
  }'
```

## Testing

Run the test suite:

```bash
# All tests
pytest

# Unit tests only
pytest tests/unit/

# With coverage
pytest --cov=query_intelligence --cov-report=html

# Type checking
mypy src/

# Linting
ruff check src/
black --check src/
```

## Configuration

Key configuration options (see `.env.example` for full list):

- `QUERY_INTELLIGENCE_GCP_PROJECT_ID`: Your GCP project ID
- `QUERY_INTELLIGENCE_VERTEX_AI_MODEL`: Gemini model to use (default: gemini-2.5-flash)
- `QUERY_INTELLIGENCE_EMBEDDING_MODEL`: Embedding model (default: text-embedding-004)
- `QUERY_INTELLIGENCE_REDIS_URL`: Redis connection URL
- `QUERY_INTELLIGENCE_CONFIDENCE_THRESHOLD`: Minimum confidence score (default: 0.7)

## Performance Optimization

The service includes several optimizations:

- **Caching**: Query results and embeddings are cached in Redis
- **Async Processing**: All I/O operations are async
- **Connection Pooling**: Redis and database connections are pooled
- **Batch Processing**: Embeddings can be generated in batches
- **Token Management**: Automatic context window management

## Monitoring

The service includes comprehensive monitoring:

- **Metrics**: Query latency, cache hit rates, confidence scores
- **Tracing**: Distributed tracing with OpenTelemetry
- **Logging**: Structured JSON logging
- **Health Checks**: `/health`, `/liveness`, `/readiness` endpoints

## Development Guidelines

1. **Code Style**: Follow PEP 8, use Black for formatting
2. **Type Hints**: Use type annotations for all functions
3. **Testing**: Maintain >90% test coverage
4. **Documentation**: Update docs for API changes
5. **Error Handling**: Use custom exceptions from `exceptions.py`

## Deployment

### Docker

Build and run with Docker:

```bash
# Build image
docker build -t query-intelligence .

# Run container
docker run -p 8001:8001 --env-file .env query-intelligence
```

### Google Cloud Run

Deploy to Cloud Run:

```bash
# Build and push image
gcloud builds submit --tag gcr.io/PROJECT_ID/query-intelligence

# Deploy service
gcloud run deploy query-intelligence \
  --image gcr.io/PROJECT_ID/query-intelligence \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

## Troubleshooting

### Common Issues

1. **"Model not found" error**
   - Ensure Vertex AI API is enabled in your GCP project
   - Check that the model name is correct in configuration

2. **Redis connection errors**
   - Verify Redis is running and accessible
   - Check REDIS_URL in configuration

3. **Authentication errors**
   - Ensure GOOGLE_APPLICATION_CREDENTIALS is set correctly
   - Verify service account has necessary permissions

## License

See the main repository LICENSE file.