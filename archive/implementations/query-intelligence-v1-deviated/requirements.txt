# Web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Data validation
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
sqlalchemy==2.0.23
asyncpg==0.29.0
alembic==1.13.0

# Caching
redis==5.0.1
hiredis==2.3.2

# HTTP client
httpx==0.25.2

# AI/ML
numpy==1.26.2
scikit-learn==1.3.2

# Google Cloud & Vertex AI
google-cloud-aiplatform==1.38.1
google-cloud-storage==2.10.0
google-cloud-spanner==3.48.0
google-cloud-pubsub==2.18.4
google-cloud-secretmanager==2.16.4
google-auth==2.25.2
vertexai==1.38.1

# Vector stores and embeddings
faiss-cpu==1.7.4
sentence-transformers==2.2.2

# Text processing
tiktoken==0.5.2
nltk==3.8.1
spacy==3.7.2

# Additional utilities
tenacity==8.2.3
pydantic-extra-types==2.1.0

# Async utilities
aiocache==0.12.2

# Monitoring
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0
opentelemetry-exporter-otlp==1.21.0
prometheus-fastapi-instrumentator==6.1.0

# Development
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.12.0
ruff==0.1.8
mypy==1.7.1