"""
Pydantic models for Query Intelligence service
"""

from .health import (
    DependencyStatus,
    HealthResponse,
    LivenessResponse,
    ReadinessResponse,
    ServiceStatus,
)
from .query import (
    CodeReference,
    IndexingRequest,
    IndexingResponse,
    QueryContext,
    QueryFeedback,
    QueryIntent,
    QueryRequest,
    QueryResponse,
    QueryScope,
    QueryStatus,
    QueryStatusResponse,
    QuerySuggestion,
    QuerySuggestionsResponse,
)

__all__ = [
    # Health models
    "ServiceStatus",
    "DependencyStatus",
    "HealthResponse",
    "ReadinessResponse",
    "LivenessResponse",
    # Query models
    "QueryIntent",
    "QueryScope",
    "QueryContext",
    "QueryRequest",
    "CodeReference",
    "QueryResponse",
    "QueryStatus",
    "QueryStatusResponse",
    "QueryFeedback",
    "QuerySuggestion",
    "QuerySuggestionsResponse",
    "IndexingRequest",
    "IndexingResponse",
]