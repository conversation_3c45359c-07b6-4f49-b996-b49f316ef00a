"""
Query-related Pydantic models for request/response validation
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field, field_validator


class QueryIntent(str, Enum):
    """Types of query intents"""
    EXPLAIN = "explain"
    FIND = "find"
    DEBUG = "debug"
    REFACTOR = "refactor"
    SECURITY = "security"
    PERFORMANCE = "performance"
    ARCHITECTURE = "architecture"
    DOCUMENTATION = "documentation"
    TESTING = "testing"
    GENERAL = "general"


class QueryScope(str, Enum):
    """Scope of the query"""
    FILE = "file"
    MODULE = "module"
    FUNCTION = "function"
    CLASS = "class"
    REPOSITORY = "repository"


class QueryContext(BaseModel):
    """Context for query processing"""
    
    model_config = ConfigDict(str_strip_whitespace=True)
    
    repository_id: str = Field(
        ...,
        pattern="^repo_[a-zA-Z0-9]{16}$",
        description="Repository identifier"
    )
    user_id: Optional[str] = Field(
        None,
        pattern="^user_[a-zA-Z0-9]{16}$",
        description="User identifier"
    )
    session_id: Optional[str] = Field(
        None,
        pattern="^session_[a-zA-Z0-9]{16}$",
        description="Session identifier for conversation continuity"
    )
    history: List[Dict[str, Any]] = Field(
        default_factory=list,
        max_length=10,
        description="Previous queries and responses in the session"
    )
    filters: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional filters for query processing"
    )
    
    @field_validator("history")
    @classmethod
    def validate_history(cls, v):
        """Ensure history items have required fields"""
        for item in v:
            if not all(key in item for key in ["query", "response", "timestamp"]):
                raise ValueError("History items must contain query, response, and timestamp")
        return v


class QueryRequest(BaseModel):
    """Request model for submitting a query"""
    
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "query": "How does the authentication middleware work?",
                "repository_id": "repo_abc123def456ghi7",
                "context": {
                    "repository_id": "repo_abc123def456ghi7",
                    "session_id": "session_xyz789abc123def"
                }
            }
        }
    )
    
    query: str = Field(
        ...,
        min_length=1,
        max_length=1000,
        description="Natural language query about the codebase"
    )
    repository_id: str = Field(
        ...,
        pattern="^repo_[a-zA-Z0-9]{16}$",
        description="Repository to query against"
    )
    context: Optional[QueryContext] = Field(
        None,
        description="Optional context for the query"
    )
    include_code_snippets: bool = Field(
        True,
        description="Whether to include code snippets in the response"
    )
    max_results: int = Field(
        5,
        ge=1,
        le=20,
        description="Maximum number of code references to return"
    )
    
    @field_validator("query")
    @classmethod
    def validate_query(cls, v):
        """Validate query content"""
        # Remove excessive whitespace
        v = " ".join(v.split())
        
        # Check for minimum meaningful content
        if len(v.split()) < 2:
            raise ValueError("Query must contain at least 2 words")
        
        return v


class CodeReference(BaseModel):
    """Reference to a specific code location"""
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "file_path": "src/middleware/auth.py",
                "start_line": 45,
                "end_line": 72,
                "snippet": "def authenticate_request(request):\n    ...",
                "relevance_score": 0.92,
                "symbol": "authenticate_request",
                "language": "python"
            }
        }
    )
    
    file_path: str = Field(..., description="Path to the file")
    start_line: int = Field(..., ge=1, description="Starting line number")
    end_line: int = Field(..., ge=1, description="Ending line number")
    snippet: Optional[str] = Field(None, description="Code snippet")
    relevance_score: float = Field(
        ...,
        ge=0.0,
        le=1.0,
        description="Relevance score for the reference"
    )
    symbol: Optional[str] = Field(None, description="Symbol name (function, class, etc.)")
    language: Optional[str] = Field(None, description="Programming language")
    
    @field_validator("end_line")
    @classmethod
    def validate_line_range(cls, v, values):
        """Ensure end_line >= start_line"""
        if "start_line" in values.data and v < values.data["start_line"]:
            raise ValueError("end_line must be greater than or equal to start_line")
        return v


class QueryResponse(BaseModel):
    """Response model for a query"""
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "query_id": "query_abc123def456ghi7",
                "answer": "The authentication middleware works by...",
                "confidence": 0.89,
                "intent": "explain",
                "references": [
                    {
                        "file_path": "src/middleware/auth.py",
                        "start_line": 45,
                        "end_line": 72,
                        "relevance_score": 0.92
                    }
                ],
                "follow_up_questions": [
                    "How is the JWT token validated?",
                    "What happens when authentication fails?"
                ],
                "execution_time_ms": 234.5,
                "cached": False
            }
        }
    )
    
    query_id: str = Field(
        ...,
        pattern="^query_[a-zA-Z0-9]{16}$",
        description="Unique query identifier"
    )
    answer: str = Field(..., description="Natural language answer to the query")
    confidence: float = Field(
        ...,
        ge=0.0,
        le=1.0,
        description="Confidence score of the response"
    )
    intent: QueryIntent = Field(..., description="Detected intent of the query")
    references: List[CodeReference] = Field(
        ...,
        description="Relevant code references"
    )
    follow_up_questions: List[str] = Field(
        default_factory=list,
        max_length=5,
        description="Suggested follow-up questions"
    )
    execution_time_ms: float = Field(
        ...,
        ge=0.0,
        description="Query execution time in milliseconds"
    )
    cached: bool = Field(False, description="Whether the response was served from cache")
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata about the query processing"
    )


class QueryStatus(str, Enum):
    """Status of a query"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class QueryStatusResponse(BaseModel):
    """Response model for query status check"""
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "query_id": "query_abc123def456ghi7",
                "status": "completed",
                "created_at": "2025-07-07T10:30:00Z",
                "updated_at": "2025-07-07T10:30:05Z",
                "progress": 100,
                "result_available": True
            }
        }
    )
    
    query_id: str = Field(..., pattern="^query_[a-zA-Z0-9]{16}$")
    status: QueryStatus = Field(..., description="Current status of the query")
    created_at: datetime = Field(..., description="When the query was created")
    updated_at: datetime = Field(..., description="When the query was last updated")
    progress: int = Field(
        0,
        ge=0,
        le=100,
        description="Progress percentage"
    )
    result_available: bool = Field(
        False,
        description="Whether results are available"
    )
    error: Optional[str] = Field(None, description="Error message if failed")


class QueryFeedback(BaseModel):
    """Feedback for a query response"""
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "query_id": "query_abc123def456ghi7",
                "helpful": True,
                "rating": 4,
                "comment": "The answer was clear but could include more examples"
            }
        }
    )
    
    query_id: str = Field(
        ...,
        pattern="^query_[a-zA-Z0-9]{16}$",
        description="Query identifier"
    )
    helpful: bool = Field(..., description="Whether the response was helpful")
    rating: Optional[int] = Field(
        None,
        ge=1,
        le=5,
        description="Rating from 1-5"
    )
    comment: Optional[str] = Field(
        None,
        max_length=500,
        description="Additional feedback comment"
    )


class QuerySuggestion(BaseModel):
    """Query suggestion model"""
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "suggestion": "How does error handling work in the API?",
                "category": "architecture",
                "relevance_score": 0.85
            }
        }
    )
    
    suggestion: str = Field(..., description="Suggested query")
    category: str = Field(..., description="Category of the suggestion")
    relevance_score: float = Field(
        ...,
        ge=0.0,
        le=1.0,
        description="Relevance score"
    )


class QuerySuggestionsResponse(BaseModel):
    """Response model for query suggestions"""
    
    suggestions: List[QuerySuggestion] = Field(
        ...,
        max_length=10,
        description="List of query suggestions"
    )
    based_on: Optional[str] = Field(
        None,
        description="What the suggestions are based on"
    )


class IndexingRequest(BaseModel):
    """Request to trigger repository indexing"""
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "repository_id": "repo_abc123def456ghi7",
                "force_reindex": False,
                "specific_paths": ["src/", "lib/"]
            }
        }
    )
    
    repository_id: str = Field(
        ...,
        pattern="^repo_[a-zA-Z0-9]{16}$",
        description="Repository to index"
    )
    force_reindex: bool = Field(
        False,
        description="Force reindexing even if already indexed"
    )
    specific_paths: Optional[List[str]] = Field(
        None,
        description="Specific paths to index"
    )


class IndexingResponse(BaseModel):
    """Response for indexing request"""
    
    indexing_id: str = Field(
        ...,
        pattern="^indexing_[a-zA-Z0-9]{16}$",
        description="Indexing job identifier"
    )
    status: str = Field(..., description="Status of the indexing job")
    message: str = Field(..., description="Status message")
    estimated_time_seconds: Optional[int] = Field(
        None,
        description="Estimated completion time"
    )