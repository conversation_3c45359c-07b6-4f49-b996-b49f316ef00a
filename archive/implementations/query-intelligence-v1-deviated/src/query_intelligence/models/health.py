"""
Health check and service status models
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional

from pydantic import BaseModel, Field


class ServiceStatus(str, Enum):
    """Service health status"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"


class DependencyStatus(BaseModel):
    """Status of a service dependency"""
    
    name: str = Field(..., description="Dependency name")
    status: ServiceStatus = Field(..., description="Dependency status")
    latency_ms: Optional[float] = Field(None, description="Response latency in milliseconds")
    error: Optional[str] = Field(None, description="Error message if unhealthy")
    last_check: datetime = Field(..., description="Last health check timestamp")


class HealthResponse(BaseModel):
    """Health check response"""
    
    status: ServiceStatus = Field(..., description="Overall service status")
    service: str = Field("query-intelligence", description="Service name")
    version: str = Field(..., description="Service version")
    environment: str = Field(..., description="Environment name")
    uptime_seconds: float = Field(..., description="Service uptime in seconds")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    # Dependencies
    dependencies: Dict[str, DependencyStatus] = Field(
        default_factory=dict,
        description="Status of service dependencies"
    )
    
    # Metrics
    metrics: Dict[str, float] = Field(
        default_factory=dict,
        description="Key service metrics"
    )


class ReadinessResponse(BaseModel):
    """Readiness check response"""
    
    ready: bool = Field(..., description="Whether service is ready to handle requests")
    checks: Dict[str, bool] = Field(
        default_factory=dict,
        description="Individual readiness checks"
    )
    message: Optional[str] = Field(None, description="Additional information")


class LivenessResponse(BaseModel):
    """Liveness check response"""
    
    alive: bool = Field(True, description="Whether service is alive")
    timestamp: datetime = Field(default_factory=datetime.utcnow)