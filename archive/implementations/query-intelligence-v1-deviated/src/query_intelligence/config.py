"""
Configuration management for Query Intelligence service using Pydantic Settings
"""

from functools import lru_cache
from typing import List, Optional

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """
    Application settings with validation and environment variable support
    """
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        env_prefix="QUERY_INTELLIGENCE_"
    )
    
    # Service configuration
    service_name: str = "query-intelligence"
    service_version: str = "0.1.0"
    environment: str = Field("development", pattern="^(development|staging|production)$")
    debug: bool = False
    
    # API configuration
    api_v1_prefix: str = "/api/v1"
    api_host: str = "0.0.0.0"
    api_port: int = Field(8001, ge=1, le=65535)
    api_workers: int = Field(4, ge=1)
    cors_origins: List[str] = ["*"]
    
    # Google Cloud configuration
    gcp_project_id: str = Field(..., description="GCP project ID")
    gcp_location: str = Field("us-central1", description="GCP region")
    
    # Vertex AI configuration
    vertex_ai_model: str = Field("gemini-2.5-flash", description="Gemini model name")
    vertex_ai_temperature: float = Field(0.3, ge=0.0, le=1.0)
    vertex_ai_max_output_tokens: int = Field(2048, ge=1)
    vertex_ai_timeout_seconds: int = Field(30, ge=1)
    embedding_model: str = Field("text-embedding-004", description="Embedding model name")
    embedding_dimensions: int = Field(768, description="Embedding vector dimensions")
    
    # Vector Search configuration
    vector_search_index_endpoint: Optional[str] = None
    vector_search_deployed_index_id: Optional[str] = None
    vector_search_similarity_threshold: float = Field(0.7, ge=0.0, le=1.0)
    vector_search_top_k: int = Field(20, ge=1, le=100)
    
    # Redis configuration
    redis_url: str = Field("redis://localhost:6379/0", description="Redis connection URL")
    redis_password: Optional[str] = None
    redis_max_connections: int = Field(10, ge=1)
    redis_socket_timeout: int = Field(30, ge=1)
    redis_socket_connect_timeout: int = Field(30, ge=1)
    cache_ttl_seconds: int = Field(3600, ge=0)
    
    # Spanner configuration
    spanner_instance: str = Field(..., description="Spanner instance ID")
    spanner_database: str = Field(..., description="Spanner database name")
    spanner_pool_size: int = Field(10, ge=1)
    
    # Pub/Sub configuration
    pubsub_subscription: str = Field(
        "repository-analysis-complete-sub",
        description="Pub/Sub subscription name"
    )
    pubsub_max_messages: int = Field(10, ge=1)
    pubsub_ack_deadline_seconds: int = Field(600, ge=10)
    
    # Security configuration
    api_key_header: str = "X-API-Key"
    enable_api_key_auth: bool = True
    trusted_hosts: List[str] = ["*"]
    
    # Rate limiting
    rate_limit_enabled: bool = True
    rate_limit_requests_per_minute: int = Field(60, ge=1)
    rate_limit_burst: int = Field(10, ge=1)
    
    # Monitoring configuration
    enable_telemetry: bool = True
    otlp_endpoint: str = Field("http://localhost:4317", description="OTLP collector endpoint")
    otlp_insecure: bool = True
    sampling_ratio: float = Field(0.1, ge=0.0, le=1.0)
    log_level: str = Field("INFO", pattern="^(DEBUG|INFO|WARNING|ERROR|CRITICAL)$")
    
    # Query processing configuration
    max_query_length: int = Field(1000, ge=1)
    max_context_chunks: int = Field(10, ge=1)
    max_follow_up_questions: int = Field(3, ge=0)
    confidence_threshold: float = Field(0.7, ge=0.0, le=1.0)
    query_timeout_seconds: int = Field(30, ge=1)
    
    # Code chunking configuration
    chunk_size: int = Field(512, ge=128)
    chunk_overlap: int = Field(50, ge=0)
    max_chunk_tokens: int = Field(8192, ge=100)
    
    # Performance configuration
    enable_query_cache: bool = True
    enable_embedding_cache: bool = True
    batch_size: int = Field(10, ge=1)
    max_concurrent_queries: int = Field(50, ge=1)
    
    @field_validator("cors_origins", mode="before")
    @classmethod
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @field_validator("trusted_hosts", mode="before")
    @classmethod
    def parse_trusted_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @property
    def redis_url_with_password(self) -> str:
        """Get Redis URL with password if configured"""
        if self.redis_password:
            # Parse and inject password into URL
            from urllib.parse import urlparse, urlunparse
            parsed = urlparse(self.redis_url)
            netloc = f":{self.redis_password}@{parsed.hostname}"
            if parsed.port:
                netloc += f":{parsed.port}"
            return urlunparse((
                parsed.scheme,
                netloc,
                parsed.path,
                parsed.params,
                parsed.query,
                parsed.fragment
            ))
        return self.redis_url
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.environment == "production"
    
    @property
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.environment == "development"


@lru_cache()
def get_settings() -> Settings:
    """
    Get cached settings instance
    
    Returns:
        Settings: Application settings
    """
    return Settings()


# Convenience function for importing
settings = get_settings()