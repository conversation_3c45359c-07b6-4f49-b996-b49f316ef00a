"""
Embedding service for generating text embeddings using Vertex AI text-embedding-004
"""

import asyncio
import hashlib
from typing import Dict, List, Optional, Tuple

import numpy as np
import structlog
import vertexai
from google.api_core import exceptions as google_exceptions
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_random_exponential,
)
from vertexai.language_models import TextEmbeddingModel

from query_intelligence.config import get_settings
from query_intelligence.exceptions import EmbeddingGenerationError, VertexAIError

logger = structlog.get_logger()
settings = get_settings()


class EmbeddingService:
    """
    Service for generating text embeddings with caching support
    """
    
    def __init__(
        self,
        model_name: Optional[str] = None,
        project_id: Optional[str] = None,
        location: Optional[str] = None,
    ):
        self.model_name = model_name or settings.embedding_model
        self.project_id = project_id or settings.gcp_project_id
        self.location = location or settings.gcp_location
        self.dimensions = settings.embedding_dimensions
        
        # Initialize Vertex AI
        vertexai.init(project=self.project_id, location=self.location)
        
        # Load embedding model
        self.model = TextEmbeddingModel.from_pretrained(self.model_name)
        
        # Cache for embeddings (in production, use Redis)
        self._cache: Dict[str, np.ndarray] = {}
        
        logger.info(
            "Embedding service initialized",
            model=self.model_name,
            dimensions=self.dimensions
        )
    
    def _get_cache_key(self, text: str) -> str:
        """
        Generate cache key for text
        
        Args:
            text: Input text
            
        Returns:
            Cache key
        """
        # Use SHA256 hash of text as cache key
        return hashlib.sha256(f"{self.model_name}:{text}".encode()).hexdigest()
    
    @retry(
        retry=retry_if_exception_type((
            google_exceptions.ResourceExhausted,
            google_exceptions.ServiceUnavailable,
            google_exceptions.DeadlineExceeded,
        )),
        wait=wait_random_exponential(multiplier=1, max=60),
        stop=stop_after_attempt(3),
    )
    async def embed_text(
        self,
        text: str,
        task_type: Optional[str] = None,
        use_cache: bool = True,
    ) -> np.ndarray:
        """
        Generate embedding for a single text
        
        Args:
            text: Text to embed
            task_type: Optional task type (e.g., 'RETRIEVAL_QUERY', 'RETRIEVAL_DOCUMENT')
            use_cache: Whether to use cache
            
        Returns:
            Embedding vector as numpy array
            
        Raises:
            EmbeddingGenerationError: If embedding generation fails
        """
        # Check cache first
        if use_cache:
            cache_key = self._get_cache_key(text)
            if cache_key in self._cache:
                logger.debug("Embedding cache hit", cache_key=cache_key[:8])
                return self._cache[cache_key]
        
        try:
            # Prepare text (truncate if needed)
            processed_text = self._prepare_text(text)
            
            # Generate embedding
            logger.debug(
                "Generating embedding",
                text_length=len(processed_text),
                task_type=task_type
            )
            
            # Use asyncio.to_thread for CPU-bound operation
            embeddings = await asyncio.to_thread(
                self.model.get_embeddings,
                [processed_text],
                task_type=task_type
            )
            
            if not embeddings or not embeddings[0].values:
                raise EmbeddingGenerationError(
                    text=text,
                    reason="Empty embedding response"
                )
            
            # Convert to numpy array
            embedding = np.array(embeddings[0].values, dtype=np.float32)
            
            # Validate dimensions
            if embedding.shape[0] != self.dimensions:
                raise EmbeddingGenerationError(
                    text=text,
                    reason=f"Expected {self.dimensions} dimensions, got {embedding.shape[0]}"
                )
            
            # Cache the result
            if use_cache:
                self._cache[cache_key] = embedding
            
            return embedding
            
        except google_exceptions.ResourceExhausted as e:
            logger.warning(
                "Embedding API quota exceeded",
                error=str(e)
            )
            raise VertexAIError(
                operation="embed_text",
                reason="Quota exceeded. Please try again later."
            )
            
        except Exception as e:
            logger.error(
                "Embedding generation failed",
                error=str(e),
                text_length=len(text)
            )
            raise EmbeddingGenerationError(
                text=text,
                reason=str(e)
            )
    
    async def embed_texts(
        self,
        texts: List[str],
        task_type: Optional[str] = None,
        batch_size: int = 100,
        use_cache: bool = True,
    ) -> List[np.ndarray]:
        """
        Generate embeddings for multiple texts with batching
        
        Args:
            texts: List of texts to embed
            task_type: Optional task type
            batch_size: Batch size for API calls
            use_cache: Whether to use cache
            
        Returns:
            List of embedding vectors
        """
        embeddings = []
        
        # Process in batches
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            
            # Check cache for each text
            batch_embeddings = []
            texts_to_embed = []
            text_indices = []
            
            for idx, text in enumerate(batch):
                if use_cache:
                    cache_key = self._get_cache_key(text)
                    if cache_key in self._cache:
                        batch_embeddings.append((idx, self._cache[cache_key]))
                        continue
                
                texts_to_embed.append(text)
                text_indices.append(idx)
            
            # Generate embeddings for uncached texts
            if texts_to_embed:
                try:
                    # Prepare texts
                    processed_texts = [self._prepare_text(t) for t in texts_to_embed]
                    
                    # Generate embeddings
                    response = await asyncio.to_thread(
                        self.model.get_embeddings,
                        processed_texts,
                        task_type=task_type
                    )
                    
                    # Process results
                    for idx, (text, embedding_data) in enumerate(zip(texts_to_embed, response)):
                        if embedding_data.values:
                            embedding = np.array(embedding_data.values, dtype=np.float32)
                            batch_embeddings.append((text_indices[idx], embedding))
                            
                            # Cache the result
                            if use_cache:
                                cache_key = self._get_cache_key(text)
                                self._cache[cache_key] = embedding
                        else:
                            logger.warning(
                                "Empty embedding for text",
                                text_preview=text[:50]
                            )
                            # Use zero vector as fallback
                            batch_embeddings.append(
                                (text_indices[idx], np.zeros(self.dimensions, dtype=np.float32))
                            )
                            
                except Exception as e:
                    logger.error(
                        "Batch embedding failed",
                        batch_size=len(texts_to_embed),
                        error=str(e)
                    )
                    # Use zero vectors for failed batch
                    for idx in text_indices:
                        batch_embeddings.append(
                            (idx, np.zeros(self.dimensions, dtype=np.float32))
                        )
            
            # Sort by original index and extract embeddings
            batch_embeddings.sort(key=lambda x: x[0])
            embeddings.extend([emb for _, emb in batch_embeddings])
        
        return embeddings
    
    async def embed_code(
        self,
        code: str,
        language: str,
        context: Optional[str] = None,
        use_cache: bool = True,
    ) -> np.ndarray:
        """
        Generate embedding for code with language context
        
        Args:
            code: Code snippet
            language: Programming language
            context: Additional context (e.g., function name, class)
            use_cache: Whether to use cache
            
        Returns:
            Embedding vector
        """
        # Format code with metadata
        formatted_text = f"[{language.upper()}]\n"
        if context:
            formatted_text += f"Context: {context}\n"
        formatted_text += code
        
        return await self.embed_text(
            formatted_text,
            task_type="RETRIEVAL_DOCUMENT",
            use_cache=use_cache
        )
    
    async def embed_query(
        self,
        query: str,
        use_cache: bool = True,
    ) -> np.ndarray:
        """
        Generate embedding for a search query
        
        Args:
            query: Search query
            use_cache: Whether to use cache
            
        Returns:
            Embedding vector
        """
        return await self.embed_text(
            query,
            task_type="RETRIEVAL_QUERY",
            use_cache=use_cache
        )
    
    def _prepare_text(self, text: str, max_length: int = 8192) -> str:
        """
        Prepare text for embedding generation
        
        Args:
            text: Input text
            max_length: Maximum text length
            
        Returns:
            Prepared text
        """
        # Clean up text
        text = text.strip()
        
        # Truncate if too long
        if len(text) > max_length:
            logger.warning(
                "Text truncated for embedding",
                original_length=len(text),
                max_length=max_length
            )
            text = text[:max_length]
        
        return text
    
    async def compute_similarity(
        self,
        embedding1: np.ndarray,
        embedding2: np.ndarray,
        metric: str = "cosine",
    ) -> float:
        """
        Compute similarity between two embeddings
        
        Args:
            embedding1: First embedding
            embedding2: Second embedding
            metric: Similarity metric ('cosine', 'euclidean', 'dot')
            
        Returns:
            Similarity score
        """
        if metric == "cosine":
            # Cosine similarity
            dot_product = np.dot(embedding1, embedding2)
            norm1 = np.linalg.norm(embedding1)
            norm2 = np.linalg.norm(embedding2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            return float(dot_product / (norm1 * norm2))
            
        elif metric == "euclidean":
            # Euclidean distance (inverted for similarity)
            distance = np.linalg.norm(embedding1 - embedding2)
            return float(1.0 / (1.0 + distance))
            
        elif metric == "dot":
            # Dot product
            return float(np.dot(embedding1, embedding2))
            
        else:
            raise ValueError(f"Unknown metric: {metric}")
    
    async def find_similar(
        self,
        query_embedding: np.ndarray,
        candidate_embeddings: List[np.ndarray],
        top_k: int = 10,
        threshold: float = 0.0,
        metric: str = "cosine",
    ) -> List[Tuple[int, float]]:
        """
        Find most similar embeddings from candidates
        
        Args:
            query_embedding: Query embedding
            candidate_embeddings: List of candidate embeddings
            top_k: Number of top results to return
            threshold: Minimum similarity threshold
            metric: Similarity metric
            
        Returns:
            List of (index, similarity_score) tuples
        """
        similarities = []
        
        # Compute similarities
        for idx, candidate in enumerate(candidate_embeddings):
            similarity = await self.compute_similarity(
                query_embedding,
                candidate,
                metric=metric
            )
            
            if similarity >= threshold:
                similarities.append((idx, similarity))
        
        # Sort by similarity (descending)
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        # Return top k
        return similarities[:top_k]
    
    def clear_cache(self):
        """Clear the embedding cache"""
        self._cache.clear()
        logger.info("Embedding cache cleared")
    
    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache statistics"""
        return {
            "size": len(self._cache),
            "memory_bytes": sum(
                embedding.nbytes for embedding in self._cache.values()
            )
        }


# Singleton instance
_embedding_service: Optional[EmbeddingService] = None


def get_embedding_service() -> EmbeddingService:
    """
    Get or create embedding service singleton
    
    Returns:
        EmbeddingService instance
    """
    global _embedding_service
    if _embedding_service is None:
        _embedding_service = EmbeddingService()
    return _embedding_service