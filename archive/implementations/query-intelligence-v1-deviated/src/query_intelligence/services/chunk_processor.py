"""
Code chunking strategy for optimal embedding and retrieval
"""

import re
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple

import structlog
import tiktoken

from query_intelligence.config import get_settings

logger = structlog.get_logger()
settings = get_settings()


@dataclass
class CodeChunk:
    """Represents a chunk of code with metadata"""
    
    chunk_id: str
    content: str
    file_path: str
    start_line: int
    end_line: int
    chunk_type: str  # function, class, method, block, comment, import
    language: str
    symbols: List[str]  # Symbol names contained in chunk
    parent_symbol: Optional[str] = None
    context: Optional[str] = None  # Additional context (imports, class definition)
    token_count: int = 0
    importance_score: float = 1.0


class ChunkProcessor:
    """
    Process code into optimal chunks for embedding and retrieval
    """
    
    def __init__(
        self,
        chunk_size: Optional[int] = None,
        chunk_overlap: Optional[int] = None,
        max_chunk_tokens: Optional[int] = None,
    ):
        self.chunk_size = chunk_size or settings.chunk_size
        self.chunk_overlap = chunk_overlap or settings.chunk_overlap
        self.max_chunk_tokens = max_chunk_tokens or settings.max_chunk_tokens
        
        # Initialize tokenizer for accurate token counting
        try:
            self.tokenizer = tiktoken.encoding_for_model("gpt-3.5-turbo")
        except Exception:
            self.tokenizer = tiktoken.get_encoding("cl100k_base")
        
        logger.info(
            "Chunk processor initialized",
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap
        )
    
    def process_ast_output(
        self,
        ast_data: Dict[str, Any],
        repository_id: str,
    ) -> List[CodeChunk]:
        """
        Process AST output from Repository Analysis into chunks
        
        Args:
            ast_data: AST analysis output
            repository_id: Repository identifier
            
        Returns:
            List of code chunks
        """
        chunks = []
        
        # Process each file in the AST
        for file_data in ast_data.get("files", []):
            file_path = file_data["path"]
            language = file_data.get("language", "unknown")
            content = file_data.get("content", "")
            symbols = file_data.get("symbols", [])
            
            # Create chunks based on symbol boundaries
            file_chunks = self._chunk_by_symbols(
                content=content,
                symbols=symbols,
                file_path=file_path,
                language=language,
                repository_id=repository_id
            )
            
            chunks.extend(file_chunks)
        
        # Calculate importance scores
        self._calculate_importance_scores(chunks)
        
        logger.info(
            "AST processing completed",
            repository_id=repository_id,
            total_chunks=len(chunks)
        )
        
        return chunks
    
    def _chunk_by_symbols(
        self,
        content: str,
        symbols: List[Dict[str, Any]],
        file_path: str,
        language: str,
        repository_id: str,
    ) -> List[CodeChunk]:
        """
        Create chunks based on symbol boundaries
        """
        chunks = []
        lines = content.split('\n')
        
        # Sort symbols by start line
        sorted_symbols = sorted(symbols, key=lambda s: s.get("start_line", 0))
        
        # Get file-level context (imports, module docstring)
        file_context = self._extract_file_context(lines, language)
        
        # Process each symbol
        for symbol in sorted_symbols:
            symbol_name = symbol.get("name", "")
            symbol_type = symbol.get("type", "unknown")
            start_line = symbol.get("start_line", 1) - 1  # Convert to 0-based
            end_line = symbol.get("end_line", len(lines))
            
            # Extract symbol content
            symbol_lines = lines[start_line:end_line]
            symbol_content = '\n'.join(symbol_lines)
            
            # Check token count
            token_count = self._count_tokens(symbol_content)
            
            if token_count <= self.max_chunk_tokens:
                # Create single chunk for symbol
                chunk = CodeChunk(
                    chunk_id=f"{repository_id}_{file_path}_{start_line}",
                    content=symbol_content,
                    file_path=file_path,
                    start_line=start_line + 1,  # Convert back to 1-based
                    end_line=end_line,
                    chunk_type=self._map_symbol_type(symbol_type),
                    language=language,
                    symbols=[symbol_name],
                    parent_symbol=symbol.get("parent", None),
                    context=file_context,
                    token_count=token_count
                )
                chunks.append(chunk)
            else:
                # Split large symbols into smaller chunks
                sub_chunks = self._split_large_symbol(
                    symbol_content=symbol_content,
                    symbol_name=symbol_name,
                    symbol_type=symbol_type,
                    start_line=start_line,
                    file_path=file_path,
                    language=language,
                    repository_id=repository_id,
                    context=file_context
                )
                chunks.extend(sub_chunks)
        
        # Handle code not covered by symbols
        uncovered_chunks = self._chunk_uncovered_code(
            lines=lines,
            symbols=sorted_symbols,
            file_path=file_path,
            language=language,
            repository_id=repository_id,
            context=file_context
        )
        chunks.extend(uncovered_chunks)
        
        return chunks
    
    def _split_large_symbol(
        self,
        symbol_content: str,
        symbol_name: str,
        symbol_type: str,
        start_line: int,
        file_path: str,
        language: str,
        repository_id: str,
        context: str,
    ) -> List[CodeChunk]:
        """
        Split large symbols into smaller chunks
        """
        chunks = []
        lines = symbol_content.split('\n')
        
        # Try to split at logical boundaries
        logical_chunks = self._find_logical_boundaries(lines, language)
        
        if logical_chunks:
            for i, (chunk_start, chunk_end) in enumerate(logical_chunks):
                chunk_lines = lines[chunk_start:chunk_end]
                chunk_content = '\n'.join(chunk_lines)
                
                chunk = CodeChunk(
                    chunk_id=f"{repository_id}_{file_path}_{start_line + chunk_start}_{i}",
                    content=chunk_content,
                    file_path=file_path,
                    start_line=start_line + chunk_start + 1,
                    end_line=start_line + chunk_end,
                    chunk_type=self._map_symbol_type(symbol_type),
                    language=language,
                    symbols=[f"{symbol_name}_part{i}"],
                    parent_symbol=symbol_name,
                    context=context,
                    token_count=self._count_tokens(chunk_content)
                )
                chunks.append(chunk)
        else:
            # Fall back to simple line-based chunking
            chunk_lines = []
            current_tokens = 0
            chunk_start = 0
            
            for i, line in enumerate(lines):
                line_tokens = self._count_tokens(line)
                
                if current_tokens + line_tokens > self.chunk_size and chunk_lines:
                    # Create chunk
                    chunk_content = '\n'.join(chunk_lines)
                    chunk = CodeChunk(
                        chunk_id=f"{repository_id}_{file_path}_{start_line + chunk_start}",
                        content=chunk_content,
                        file_path=file_path,
                        start_line=start_line + chunk_start + 1,
                        end_line=start_line + i,
                        chunk_type=self._map_symbol_type(symbol_type),
                        language=language,
                        symbols=[symbol_name],
                        parent_symbol=None,
                        context=context,
                        token_count=current_tokens
                    )
                    chunks.append(chunk)
                    
                    # Reset for next chunk
                    chunk_lines = []
                    current_tokens = 0
                    chunk_start = i
                
                chunk_lines.append(line)
                current_tokens += line_tokens
            
            # Handle remaining lines
            if chunk_lines:
                chunk_content = '\n'.join(chunk_lines)
                chunk = CodeChunk(
                    chunk_id=f"{repository_id}_{file_path}_{start_line + chunk_start}",
                    content=chunk_content,
                    file_path=file_path,
                    start_line=start_line + chunk_start + 1,
                    end_line=start_line + len(lines),
                    chunk_type=self._map_symbol_type(symbol_type),
                    language=language,
                    symbols=[symbol_name],
                    parent_symbol=None,
                    context=context,
                    token_count=current_tokens
                )
                chunks.append(chunk)
        
        return chunks
    
    def _chunk_uncovered_code(
        self,
        lines: List[str],
        symbols: List[Dict[str, Any]],
        file_path: str,
        language: str,
        repository_id: str,
        context: str,
    ) -> List[CodeChunk]:
        """
        Create chunks for code not covered by symbols
        """
        chunks = []
        covered_lines = set()
        
        # Mark lines covered by symbols
        for symbol in symbols:
            start = symbol.get("start_line", 1) - 1
            end = symbol.get("end_line", len(lines))
            covered_lines.update(range(start, end))
        
        # Find uncovered regions
        uncovered_regions = []
        region_start = None
        
        for i in range(len(lines)):
            if i not in covered_lines:
                if region_start is None:
                    region_start = i
            else:
                if region_start is not None:
                    uncovered_regions.append((region_start, i))
                    region_start = None
        
        # Handle last region
        if region_start is not None:
            uncovered_regions.append((region_start, len(lines)))
        
        # Create chunks for uncovered regions
        for start, end in uncovered_regions:
            region_lines = lines[start:end]
            region_content = '\n'.join(region_lines)
            
            # Skip if mostly empty
            if not region_content.strip():
                continue
            
            # Determine chunk type
            chunk_type = self._detect_chunk_type(region_content, language)
            
            chunk = CodeChunk(
                chunk_id=f"{repository_id}_{file_path}_{start}_uncovered",
                content=region_content,
                file_path=file_path,
                start_line=start + 1,
                end_line=end,
                chunk_type=chunk_type,
                language=language,
                symbols=[],
                parent_symbol=None,
                context=context,
                token_count=self._count_tokens(region_content)
            )
            chunks.append(chunk)
        
        return chunks
    
    def _extract_file_context(self, lines: List[str], language: str) -> str:
        """
        Extract file-level context (imports, module docstring)
        """
        context_lines = []
        
        if language.lower() in ["python", "py"]:
            # Extract imports and module docstring
            in_docstring = False
            docstring_quotes = None
            
            for line in lines[:50]:  # Check first 50 lines
                stripped = line.strip()
                
                # Handle docstrings
                if not in_docstring and (stripped.startswith('"""') or stripped.startswith("'''")):
                    in_docstring = True
                    docstring_quotes = '"""' if stripped.startswith('"""') else "'''"
                    context_lines.append(line)
                    if stripped.endswith(docstring_quotes) and len(stripped) > 3:
                        in_docstring = False
                elif in_docstring:
                    context_lines.append(line)
                    if stripped.endswith(docstring_quotes):
                        in_docstring = False
                # Handle imports
                elif stripped.startswith(("import ", "from ")):
                    context_lines.append(line)
                elif not stripped or stripped.startswith("#"):
                    continue
                else:
                    # Stop at first non-import, non-docstring line
                    break
        
        elif language.lower() in ["javascript", "js", "typescript", "ts"]:
            # Extract imports
            for line in lines[:30]:
                stripped = line.strip()
                if stripped.startswith(("import ", "const ", "require(", "export ")):
                    context_lines.append(line)
                elif not stripped or stripped.startswith("//"):
                    continue
                else:
                    break
        
        return '\n'.join(context_lines) if context_lines else ""
    
    def _find_logical_boundaries(
        self,
        lines: List[str],
        language: str
    ) -> List[Tuple[int, int]]:
        """
        Find logical boundaries for splitting code
        """
        boundaries = []
        
        if language.lower() in ["python", "py"]:
            # Look for method boundaries in classes
            method_starts = []
            for i, line in enumerate(lines):
                if re.match(r'^\s{4,}def\s+\w+', line):  # Indented def
                    method_starts.append(i)
            
            # Create chunks for each method
            for i, start in enumerate(method_starts):
                end = method_starts[i + 1] if i + 1 < len(method_starts) else len(lines)
                boundaries.append((start, end))
        
        return boundaries
    
    def _detect_chunk_type(self, content: str, language: str) -> str:
        """
        Detect the type of code chunk
        """
        stripped = content.strip()
        
        # Check for imports
        if language.lower() in ["python", "py"]:
            if re.match(r'^(import|from)\s+', stripped):
                return "import"
        elif language.lower() in ["javascript", "js", "typescript", "ts"]:
            if re.match(r'^(import|const.*require|export)\s+', stripped):
                return "import"
        
        # Check for comments
        comment_ratio = self._calculate_comment_ratio(content, language)
        if comment_ratio > 0.7:
            return "comment"
        
        return "block"
    
    def _calculate_comment_ratio(self, content: str, language: str) -> float:
        """
        Calculate ratio of comment lines to total lines
        """
        lines = content.split('\n')
        comment_lines = 0
        
        for line in lines:
            stripped = line.strip()
            if not stripped:
                continue
            
            if language.lower() in ["python", "py"]:
                if stripped.startswith("#"):
                    comment_lines += 1
            elif language.lower() in ["javascript", "js", "typescript", "ts", "java", "c", "cpp"]:
                if stripped.startswith("//") or stripped.startswith("/*") or stripped.startswith("*"):
                    comment_lines += 1
        
        total_lines = len([l for l in lines if l.strip()])
        return comment_lines / total_lines if total_lines > 0 else 0
    
    def _map_symbol_type(self, symbol_type: str) -> str:
        """
        Map AST symbol type to chunk type
        """
        mapping = {
            "function": "function",
            "method": "method",
            "class": "class",
            "interface": "class",
            "struct": "class",
            "enum": "class",
            "module": "block",
            "namespace": "block",
        }
        return mapping.get(symbol_type.lower(), "block")
    
    def _calculate_importance_scores(self, chunks: List[CodeChunk]):
        """
        Calculate importance scores for chunks
        """
        for chunk in chunks:
            score = 1.0
            
            # Boost score for certain chunk types
            if chunk.chunk_type in ["function", "method", "class"]:
                score += 0.3
            
            # Boost score for chunks with symbols
            if chunk.symbols:
                score += 0.2 * len(chunk.symbols)
            
            # Reduce score for comments
            if chunk.chunk_type == "comment":
                score *= 0.5
            
            # Reduce score for imports
            if chunk.chunk_type == "import":
                score *= 0.7
            
            # Consider token count (prefer medium-sized chunks)
            if 100 <= chunk.token_count <= 300:
                score += 0.1
            
            chunk.importance_score = min(score, 2.0)  # Cap at 2.0
    
    def _count_tokens(self, text: str) -> int:
        """
        Count tokens in text
        """
        try:
            return len(self.tokenizer.encode(text))
        except Exception:
            # Fallback to word count estimation
            return len(text.split())
    
    def merge_chunks(
        self,
        chunks: List[CodeChunk],
        max_size: Optional[int] = None
    ) -> List[CodeChunk]:
        """
        Merge small adjacent chunks
        """
        if not chunks:
            return []
        
        max_size = max_size or self.chunk_size
        merged = []
        current_group = [chunks[0]]
        current_tokens = chunks[0].token_count
        
        for chunk in chunks[1:]:
            # Check if chunks can be merged
            if (chunk.file_path == current_group[-1].file_path and
                chunk.start_line == current_group[-1].end_line + 1 and
                current_tokens + chunk.token_count <= max_size):
                
                current_group.append(chunk)
                current_tokens += chunk.token_count
            else:
                # Create merged chunk
                if len(current_group) > 1:
                    merged_chunk = self._create_merged_chunk(current_group)
                    merged.append(merged_chunk)
                else:
                    merged.append(current_group[0])
                
                # Start new group
                current_group = [chunk]
                current_tokens = chunk.token_count
        
        # Handle last group
        if current_group:
            if len(current_group) > 1:
                merged_chunk = self._create_merged_chunk(current_group)
                merged.append(merged_chunk)
            else:
                merged.append(current_group[0])
        
        return merged
    
    def _create_merged_chunk(self, chunks: List[CodeChunk]) -> CodeChunk:
        """
        Create a merged chunk from multiple chunks
        """
        # Combine content
        content = '\n'.join(chunk.content for chunk in chunks)
        
        # Combine symbols
        symbols = []
        for chunk in chunks:
            symbols.extend(chunk.symbols)
        
        # Use first chunk's metadata
        first = chunks[0]
        last = chunks[-1]
        
        return CodeChunk(
            chunk_id=f"{first.chunk_id}_merged",
            content=content,
            file_path=first.file_path,
            start_line=first.start_line,
            end_line=last.end_line,
            chunk_type="block",
            language=first.language,
            symbols=list(set(symbols)),  # Remove duplicates
            parent_symbol=first.parent_symbol,
            context=first.context,
            token_count=sum(c.token_count for c in chunks),
            importance_score=max(c.importance_score for c in chunks)
        )