"""
Feedback API endpoints
"""

import structlog
from fastapi import APIRouter, HTTPException, status

from query_intelligence.models import QueryFeedback

logger = structlog.get_logger()
router = APIRouter()


@router.post(
    "/",
    status_code=status.HTTP_201_CREATED,
    summary="Submit feedback",
    description="Submit feedback for a query response",
    responses={
        201: {"description": "Feedback submitted successfully"},
        404: {"description": "Query not found"},
        500: {"description": "Internal server error"},
    }
)
async def submit_feedback(
    feedback: QueryFeedback
) -> dict:
    """
    Submit feedback for a query response
    
    Args:
        feedback: Feedback data
        
    Returns:
        Success message
        
    Raises:
        HTTPException: If query not found
    """
    try:
        logger.info(
            "Feedback received",
            query_id=feedback.query_id,
            helpful=feedback.helpful,
            rating=feedback.rating
        )
        
        # TODO: Store feedback in Spanner for analysis
        # For now, just log it
        
        # Verify query exists
        from query_intelligence.integrations.redis_cache import CacheKeyBuilder, get_redis_cache
        
        cache = await get_redis_cache()
        cache_key = CacheKeyBuilder.result_key(feedback.query_id)
        result = await cache.get(cache_key)
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error_code": "QUERY_NOT_FOUND",
                    "message": f"Query '{feedback.query_id}' not found",
                    "details": {"query_id": feedback.query_id}
                }
            )
        
        # Log feedback metrics
        if feedback.rating:
            logger.info(
                "Feedback metrics",
                query_id=feedback.query_id,
                helpful=feedback.helpful,
                rating=feedback.rating,
                has_comment=bool(feedback.comment)
            )
        
        return {
            "message": "Feedback submitted successfully",
            "query_id": feedback.query_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Error submitting feedback",
            query_id=feedback.query_id,
            error=str(e),
            exc_info=e
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error_code": "INTERNAL_ERROR",
                "message": "An unexpected error occurred",
                "details": {}
            }
        )