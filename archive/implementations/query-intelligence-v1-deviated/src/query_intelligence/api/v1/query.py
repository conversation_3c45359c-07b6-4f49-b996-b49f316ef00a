"""
Query API endpoints
"""

from typing import Optional

import structlog
from fastapi import APIRouter, Depends, HTTPException, Query, status

from query_intelligence.exceptions import QueryIntelligenceHTTPError
from query_intelligence.models import (
    QueryRequest,
    QueryResponse,
    QueryStatus,
    QueryStatusResponse,
)
from query_intelligence.services.query_processor import QueryProcessor

logger = structlog.get_logger()
router = APIRouter()

# Create query processor instance
query_processor = QueryProcessor()


@router.post(
    "/",
    response_model=QueryResponse,
    status_code=status.HTTP_200_OK,
    summary="Submit a query",
    description="Submit a natural language query about code in the repository",
    responses={
        200: {"description": "Query processed successfully"},
        400: {"description": "Invalid query"},
        404: {"description": "Repository not found or not indexed"},
        422: {"description": "Query processing failed"},
        429: {"description": "Rate limit exceeded"},
        500: {"description": "Internal server error"},
    }
)
async def submit_query(
    request: QueryRequest
) -> QueryResponse:
    """
    Submit a natural language query about code
    
    Args:
        request: Query request with query text and context
        
    Returns:
        Query response with answer and code references
        
    Raises:
        Various HTTP exceptions for different error conditions
    """
    try:
        logger.info(
            "Query received",
            query=request.query[:100],  # Log first 100 chars
            repository_id=request.repository_id
        )
        
        # Process the query
        response = await query_processor.process_query(request)
        
        return response
        
    except QueryIntelligenceHTTPError:
        # Re-raise our custom HTTP errors
        raise
    except Exception as e:
        logger.error(
            "Unexpected error processing query",
            error=str(e),
            exc_info=e
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error_code": "INTERNAL_ERROR",
                "message": "An unexpected error occurred",
                "details": {}
            }
        )


@router.get(
    "/{query_id}",
    response_model=QueryResponse,
    status_code=status.HTTP_200_OK,
    summary="Get query result",
    description="Get the result of a previously submitted query",
    responses={
        200: {"description": "Query result found"},
        404: {"description": "Query not found"},
        500: {"description": "Internal server error"},
    }
)
async def get_query_result(
    query_id: str
) -> QueryResponse:
    """
    Get the result of a previously submitted query
    
    Args:
        query_id: Unique query identifier
        
    Returns:
        Query response if found
        
    Raises:
        HTTPException: If query not found
    """
    try:
        # Check cache for result
        from query_intelligence.integrations.redis_cache import CacheKeyBuilder, get_redis_cache
        
        cache = await get_redis_cache()
        cache_key = CacheKeyBuilder.result_key(query_id)
        result = await cache.get(cache_key)
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error_code": "QUERY_NOT_FOUND",
                    "message": f"Query '{query_id}' not found",
                    "details": {"query_id": query_id}
                }
            )
        
        return QueryResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Error retrieving query result",
            query_id=query_id,
            error=str(e),
            exc_info=e
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error_code": "INTERNAL_ERROR",
                "message": "An unexpected error occurred",
                "details": {}
            }
        )


@router.get(
    "/{query_id}/status",
    response_model=QueryStatusResponse,
    status_code=status.HTTP_200_OK,
    summary="Get query status",
    description="Get the processing status of a query",
    responses={
        200: {"description": "Query status retrieved"},
        404: {"description": "Query not found"},
        500: {"description": "Internal server error"},
    }
)
async def get_query_status(
    query_id: str
) -> QueryStatusResponse:
    """
    Get the processing status of a query
    
    Args:
        query_id: Unique query identifier
        
    Returns:
        Query status information
        
    Raises:
        HTTPException: If query not found
    """
    try:
        # For now, return completed status if result exists
        from query_intelligence.integrations.redis_cache import CacheKeyBuilder, get_redis_cache
        from datetime import datetime
        
        cache = await get_redis_cache()
        cache_key = CacheKeyBuilder.result_key(query_id)
        result = await cache.get(cache_key)
        
        if result:
            return QueryStatusResponse(
                query_id=query_id,
                status=QueryStatus.COMPLETED,
                created_at=datetime.utcnow(),  # Should store actual time
                updated_at=datetime.utcnow(),
                progress=100,
                result_available=True
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error_code": "QUERY_NOT_FOUND",
                    "message": f"Query '{query_id}' not found",
                    "details": {"query_id": query_id}
                }
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Error retrieving query status",
            query_id=query_id,
            error=str(e),
            exc_info=e
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error_code": "INTERNAL_ERROR",
                "message": "An unexpected error occurred",
                "details": {}
            }
        )