"""
Custom exception hierarchy for Query Intelligence service
"""

from typing import Any, Dict, Optional

from fastapi import HTTPException, status


class QueryIntelligenceError(Exception):
    """Base exception for Query Intelligence service"""
    
    def __init__(
        self,
        message: str,
        error_code: str,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class QueryIntelligenceHTTPError(HTTPException):
    """Base HTTP exception for Query Intelligence service"""
    
    def __init__(
        self,
        status_code: int,
        error_code: str,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ):
        detail = {
            "error_code": error_code,
            "message": message,
            "details": details or {}
        }
        super().__init__(
            status_code=status_code,
            detail=detail,
            headers=headers
        )


# Query-related exceptions

class QueryTooLongError(QueryIntelligenceHTTPError):
    """Raised when query exceeds maximum length"""
    
    def __init__(self, query_length: int, max_length: int):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            error_code="QUERY_TOO_LONG",
            message=f"Query length ({query_length}) exceeds maximum allowed length ({max_length})",
            details={
                "query_length": query_length,
                "max_length": max_length
            }
        )


class QueryTooComplexError(QueryIntelligenceHTTPError):
    """Raised when query is too complex to process"""
    
    def __init__(self, complexity_score: float, threshold: float):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            error_code="QUERY_TOO_COMPLEX",
            message="Query exceeds complexity threshold",
            details={
                "complexity_score": complexity_score,
                "threshold": threshold,
                "suggestion": "Try breaking the query into smaller, more specific questions"
            }
        )


class InvalidQueryError(QueryIntelligenceHTTPError):
    """Raised when query is invalid or malformed"""
    
    def __init__(self, reason: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            error_code="INVALID_QUERY",
            message=f"Invalid query: {reason}",
            details={"reason": reason}
        )


# Repository-related exceptions

class RepositoryNotFoundError(QueryIntelligenceHTTPError):
    """Raised when repository is not found"""
    
    def __init__(self, repository_id: str):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            error_code="REPOSITORY_NOT_FOUND",
            message=f"Repository '{repository_id}' not found",
            details={"repository_id": repository_id}
        )


class RepositoryNotIndexedError(QueryIntelligenceHTTPError):
    """Raised when repository hasn't been indexed yet"""
    
    def __init__(self, repository_id: str):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            error_code="REPOSITORY_NOT_INDEXED",
            message=f"Repository '{repository_id}' has not been indexed yet",
            details={
                "repository_id": repository_id,
                "suggestion": "Please wait for repository analysis to complete"
            }
        )


# Context-related exceptions

class InsufficientContextError(QueryIntelligenceHTTPError):
    """Raised when there's not enough context to answer the query"""
    
    def __init__(self, query: str, available_chunks: int):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            error_code="INSUFFICIENT_CONTEXT",
            message="Not enough context available to answer this query",
            details={
                "query": query,
                "available_chunks": available_chunks,
                "suggestion": "Try a more specific query or ensure the repository is fully indexed"
            }
        )


class ContextWindowExceededError(QueryIntelligenceError):
    """Raised when context exceeds model's context window"""
    
    def __init__(self, context_tokens: int, max_tokens: int):
        super().__init__(
            message="Context window exceeded",
            error_code="CONTEXT_WINDOW_EXCEEDED",
            details={
                "context_tokens": context_tokens,
                "max_tokens": max_tokens
            }
        )


# AI/ML-related exceptions

class EmbeddingGenerationError(QueryIntelligenceError):
    """Raised when embedding generation fails"""
    
    def __init__(self, text: str, reason: str):
        super().__init__(
            message=f"Failed to generate embedding: {reason}",
            error_code="EMBEDDING_GENERATION_ERROR",
            details={
                "text_length": len(text),
                "reason": reason
            }
        )


class ModelInferenceError(QueryIntelligenceError):
    """Raised when model inference fails"""
    
    def __init__(self, model: str, reason: str):
        super().__init__(
            message=f"Model inference failed: {reason}",
            error_code="MODEL_INFERENCE_ERROR",
            details={
                "model": model,
                "reason": reason
            }
        )


class LowConfidenceError(QueryIntelligenceHTTPError):
    """Raised when response confidence is below threshold"""
    
    def __init__(self, confidence: float, threshold: float):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            error_code="LOW_CONFIDENCE",
            message="Response confidence below acceptable threshold",
            details={
                "confidence": confidence,
                "threshold": threshold,
                "suggestion": "Try rephrasing your query or providing more context"
            }
        )


# Vector search exceptions

class VectorSearchError(QueryIntelligenceError):
    """Raised when vector search fails"""
    
    def __init__(self, reason: str):
        super().__init__(
            message=f"Vector search failed: {reason}",
            error_code="VECTOR_SEARCH_ERROR",
            details={"reason": reason}
        )


class NoMatchingChunksError(QueryIntelligenceHTTPError):
    """Raised when no matching chunks are found"""
    
    def __init__(self, query: str, repository_id: str):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            error_code="NO_MATCHING_CHUNKS",
            message="No relevant code chunks found for the query",
            details={
                "query": query,
                "repository_id": repository_id,
                "suggestion": "Try using different keywords or a broader query"
            }
        )


# Cache-related exceptions

class CacheError(QueryIntelligenceError):
    """Raised when cache operation fails"""
    
    def __init__(self, operation: str, reason: str):
        super().__init__(
            message=f"Cache {operation} failed: {reason}",
            error_code="CACHE_ERROR",
            details={
                "operation": operation,
                "reason": reason
            }
        )


# Rate limiting exceptions

class RateLimitExceededError(QueryIntelligenceHTTPError):
    """Raised when rate limit is exceeded"""
    
    def __init__(self, limit: int, window: str, retry_after: int):
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            error_code="RATE_LIMIT_EXCEEDED",
            message=f"Rate limit exceeded: {limit} requests per {window}",
            details={
                "limit": limit,
                "window": window,
                "retry_after_seconds": retry_after
            },
            headers={"Retry-After": str(retry_after)}
        )


# External service exceptions

class VertexAIError(QueryIntelligenceError):
    """Raised when Vertex AI service fails"""
    
    def __init__(self, operation: str, reason: str):
        super().__init__(
            message=f"Vertex AI {operation} failed: {reason}",
            error_code="VERTEX_AI_ERROR",
            details={
                "operation": operation,
                "reason": reason
            }
        )


class SpannerError(QueryIntelligenceError):
    """Raised when Spanner operation fails"""
    
    def __init__(self, operation: str, reason: str):
        super().__init__(
            message=f"Spanner {operation} failed: {reason}",
            error_code="SPANNER_ERROR",
            details={
                "operation": operation,
                "reason": reason
            }
        )


class PubSubError(QueryIntelligenceError):
    """Raised when Pub/Sub operation fails"""
    
    def __init__(self, operation: str, reason: str):
        super().__init__(
            message=f"Pub/Sub {operation} failed: {reason}",
            error_code="PUBSUB_ERROR",
            details={
                "operation": operation,
                "reason": reason
            }
        )


# Validation exceptions

class ValidationError(QueryIntelligenceHTTPError):
    """Raised when validation fails"""
    
    def __init__(self, field: str, reason: str):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            error_code="VALIDATION_ERROR",
            message=f"Validation failed for field '{field}': {reason}",
            details={
                "field": field,
                "reason": reason
            }
        )


# Authentication/Authorization exceptions

class UnauthorizedError(QueryIntelligenceHTTPError):
    """Raised when authentication fails"""
    
    def __init__(self, reason: str = "Invalid or missing authentication credentials"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            error_code="UNAUTHORIZED",
            message=reason,
            headers={"WWW-Authenticate": "Bearer"}
        )


class ForbiddenError(QueryIntelligenceHTTPError):
    """Raised when user lacks permissions"""
    
    def __init__(self, resource: str, action: str):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            error_code="FORBIDDEN",
            message=f"Permission denied: cannot {action} {resource}",
            details={
                "resource": resource,
                "action": action
            }
        )


# Timeout exceptions

class QueryTimeoutError(QueryIntelligenceHTTPError):
    """Raised when query processing times out"""
    
    def __init__(self, timeout_seconds: int):
        super().__init__(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            error_code="QUERY_TIMEOUT",
            message=f"Query processing timed out after {timeout_seconds} seconds",
            details={
                "timeout_seconds": timeout_seconds,
                "suggestion": "Try a simpler query or contact support if the issue persists"
            }
        )