"""
Vertex AI client wrapper with async support and retry logic
"""

import asyncio
from typing import Any, Dict, List, Optional, Union

import structlog
import vertexai
from google.api_core import exceptions as google_exceptions
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_random_exponential,
)
from vertexai.generative_models import (
    GenerationConfig,
    GenerativeModel,
    HarmBlockThreshold,
    HarmCategory,
)

from query_intelligence.config import get_settings
from query_intelligence.exceptions import ModelInferenceError, VertexAIError

logger = structlog.get_logger()
settings = get_settings()


class VertexAIClient:
    """
    Async wrapper for Vertex AI with retry logic and error handling
    """
    
    def __init__(
        self,
        project_id: Optional[str] = None,
        location: Optional[str] = None,
        model_name: Optional[str] = None,
    ):
        self.project_id = project_id or settings.gcp_project_id
        self.location = location or settings.gcp_location
        self.model_name = model_name or settings.vertex_ai_model
        
        # Initialize Vertex AI
        vertexai.init(project=self.project_id, location=self.location)
        
        # Create generative model
        self.model = GenerativeModel(self.model_name)
        
        # Default generation config
        self.default_generation_config = GenerationConfig(
            temperature=settings.vertex_ai_temperature,
            max_output_tokens=settings.vertex_ai_max_output_tokens,
            top_p=0.95,
            top_k=40,
        )
        
        # Safety settings - allow most content for code analysis
        self.safety_settings = {
            HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_ONLY_HIGH,
            HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_ONLY_HIGH,
            HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_ONLY_HIGH,
            HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_ONLY_HIGH,
        }
        
        logger.info(
            "Vertex AI client initialized",
            project_id=self.project_id,
            location=self.location,
            model=self.model_name
        )
    
    @retry(
        retry=retry_if_exception_type((
            google_exceptions.ResourceExhausted,
            google_exceptions.ServiceUnavailable,
            google_exceptions.DeadlineExceeded,
        )),
        wait=wait_random_exponential(multiplier=1, max=120),
        stop=stop_after_attempt(3),
    )
    async def generate_content_async(
        self,
        prompt: Union[str, List[str]],
        generation_config: Optional[GenerationConfig] = None,
        stream: bool = False,
        timeout: Optional[float] = None,
    ) -> str:
        """
        Generate content asynchronously with retry logic
        
        Args:
            prompt: Text prompt or list of prompts
            generation_config: Optional custom generation config
            stream: Whether to stream the response
            timeout: Request timeout in seconds
            
        Returns:
            Generated text content
            
        Raises:
            ModelInferenceError: If generation fails
            VertexAIError: For other Vertex AI errors
        """
        try:
            config = generation_config or self.default_generation_config
            timeout = timeout or settings.vertex_ai_timeout_seconds
            
            # Log the request
            logger.debug(
                "Generating content",
                model=self.model_name,
                prompt_length=len(prompt) if isinstance(prompt, str) else sum(len(p) for p in prompt),
                stream=stream
            )
            
            # Use asyncio.to_thread for CPU-bound operation
            response = await asyncio.wait_for(
                asyncio.to_thread(
                    self.model.generate_content,
                    prompt,
                    generation_config=config,
                    safety_settings=self.safety_settings,
                    stream=stream,
                ),
                timeout=timeout
            )
            
            if stream:
                # Handle streaming response
                chunks = []
                async for chunk in response:
                    if chunk.text:
                        chunks.append(chunk.text)
                return "".join(chunks)
            else:
                # Handle non-streaming response
                if not response.text:
                    raise ModelInferenceError(
                        model=self.model_name,
                        reason="Empty response from model"
                    )
                return response.text
                
        except asyncio.TimeoutError:
            logger.error(
                "Vertex AI timeout",
                model=self.model_name,
                timeout_seconds=timeout
            )
            raise VertexAIError(
                operation="generate_content",
                reason=f"Request timed out after {timeout} seconds"
            )
            
        except google_exceptions.ResourceExhausted as e:
            logger.warning(
                "Vertex AI quota exceeded",
                model=self.model_name,
                error=str(e)
            )
            raise VertexAIError(
                operation="generate_content",
                reason="Quota exceeded. Please try again later."
            )
            
        except google_exceptions.InvalidArgument as e:
            logger.error(
                "Invalid argument to Vertex AI",
                model=self.model_name,
                error=str(e)
            )
            raise ModelInferenceError(
                model=self.model_name,
                reason=f"Invalid argument: {str(e)}"
            )
            
        except Exception as e:
            logger.exception(
                "Unexpected Vertex AI error",
                model=self.model_name,
                error=str(e)
            )
            raise VertexAIError(
                operation="generate_content",
                reason=f"Unexpected error: {str(e)}"
            )
    
    async def generate_with_context(
        self,
        prompt: str,
        context: List[Dict[str, Any]],
        system_instruction: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Generate content with additional context
        
        Args:
            prompt: Main prompt
            context: List of context messages
            system_instruction: Optional system instruction
            **kwargs: Additional arguments for generate_content_async
            
        Returns:
            Generated text content
        """
        # Build conversation history
        messages = []
        
        if system_instruction:
            messages.append(f"System: {system_instruction}")
        
        for ctx in context:
            role = ctx.get("role", "user")
            content = ctx.get("content", "")
            messages.append(f"{role.capitalize()}: {content}")
        
        messages.append(f"User: {prompt}")
        
        # Combine into single prompt
        full_prompt = "\n\n".join(messages)
        
        return await self.generate_content_async(full_prompt, **kwargs)
    
    async def count_tokens(self, text: str) -> int:
        """
        Count tokens in text
        
        Args:
            text: Text to count tokens for
            
        Returns:
            Number of tokens
        """
        try:
            response = await asyncio.to_thread(
                self.model.count_tokens,
                text
            )
            return response.total_tokens
        except Exception as e:
            logger.error(
                "Token counting failed",
                error=str(e)
            )
            # Fallback to rough estimation
            return len(text) // 4
    
    async def batch_generate(
        self,
        prompts: List[str],
        generation_config: Optional[GenerationConfig] = None,
        max_concurrent: int = 5,
    ) -> List[str]:
        """
        Generate content for multiple prompts concurrently
        
        Args:
            prompts: List of prompts
            generation_config: Optional custom generation config
            max_concurrent: Maximum concurrent requests
            
        Returns:
            List of generated texts
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def generate_with_semaphore(prompt: str) -> str:
            async with semaphore:
                return await self.generate_content_async(
                    prompt,
                    generation_config=generation_config
                )
        
        tasks = [generate_with_semaphore(prompt) for prompt in prompts]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle any exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(
                    "Batch generation failed for prompt",
                    prompt_index=i,
                    error=str(result)
                )
                processed_results.append("")
            else:
                processed_results.append(result)
        
        return processed_results
    
    def update_generation_config(self, **kwargs):
        """
        Update default generation configuration
        
        Args:
            **kwargs: Generation config parameters to update
        """
        for key, value in kwargs.items():
            if hasattr(self.default_generation_config, key):
                setattr(self.default_generation_config, key, value)
        
        logger.info(
            "Generation config updated",
            config=vars(self.default_generation_config)
        )


# Singleton instance
_vertex_ai_client: Optional[VertexAIClient] = None


def get_vertex_ai_client() -> VertexAIClient:
    """
    Get or create Vertex AI client singleton
    
    Returns:
        VertexAIClient instance
    """
    global _vertex_ai_client
    if _vertex_ai_client is None:
        _vertex_ai_client = VertexAIClient()
    return _vertex_ai_client