/**
 * Generic API Performance Test
 * Tests basic API endpoints across all CCL services
 */

import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';

// Custom metrics
export const errorRate = new Rate('error_rate');
export const responseTime = new Trend('response_time');
export const requests = new Counter('total_requests');

// Test configuration
export const options = {
  stages: [
    { duration: '30s', target: 5 },   // Ramp up to 5 users
    { duration: '1m', target: 10 },   // Stay at 10 users
    { duration: '30s', target: 20 },  // Ramp up to 20 users
    { duration: '1m', target: 20 },   // Stay at 20 users
    { duration: '30s', target: 0 },   // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests should be below 500ms
    http_req_failed: ['rate<0.01'],   // Error rate should be less than 1%
    error_rate: ['rate<0.01'],
    response_time: ['p(95)<500'],
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:8000';
const SERVICE_NAME = __ENV.SERVICE_NAME || 'unknown';

// Service endpoint mappings
const SERVICE_ENDPOINTS = {
  'analysis-engine': [
    '/api/v1/analysis/health',
    '/api/v1/analysis/repositories',
    '/api/v1/analysis/status'
  ],
  'query-intelligence': [
    '/api/v1/query/health',
    '/api/v1/query/capabilities'
  ],
  'pattern-mining': [
    '/api/v1/patterns/health',
    '/api/v1/patterns/categories'
  ],
  'marketplace': [
    '/api/v1/marketplace/health',
    '/api/v1/marketplace/patterns'
  ],
  'collaboration': [
    '/api/v1/collaboration/health',
    '/api/v1/collaboration/sessions'
  ],
  'web': [
    '/health',
    '/api/status'
  ]
};

export function setup() {
  console.log(`Starting performance test for service: ${SERVICE_NAME}`);
  console.log(`Base URL: ${BASE_URL}`);
  
  // Warm up the service
  const warmupResponse = http.get(`${BASE_URL}/health`);
  console.log(`Warmup response status: ${warmupResponse.status}`);
  
  return { baseUrl: BASE_URL, serviceName: SERVICE_NAME };
}

export default function(data) {
  const endpoints = SERVICE_ENDPOINTS[data.serviceName] || ['/health'];
  
  // Test each endpoint for the service
  endpoints.forEach(endpoint => {
    const url = `${data.baseUrl}${endpoint}`;
    
    const response = http.get(url, {
      timeout: '30s',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'k6-performance-test',
      },
    });
    
    requests.add(1);
    responseTime.add(response.timings.duration);
    
    const success = check(response, {
      'status is 200': (r) => r.status === 200,
      'response time < 1000ms': (r) => r.timings.duration < 1000,
      'response has content': (r) => r.body.length > 0,
    });
    
    if (!success) {
      errorRate.add(1);
      console.log(`Failed request to ${url}: status=${response.status}, duration=${response.timings.duration}ms`);
    } else {
      errorRate.add(0);
    }
  });
  
  // Simulate realistic user behavior
  sleep(Math.random() * 2 + 1); // Random sleep between 1-3 seconds
}

export function teardown(data) {
  console.log('Performance test completed');
  console.log(`Service: ${data.serviceName}`);
  console.log(`Base URL: ${data.baseUrl}`);
}