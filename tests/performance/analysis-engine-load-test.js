/**
 * Analysis Engine Load Test
 * Comprehensive performance testing for the Rust-based analysis engine
 */

import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter, Gauge } from 'k6/metrics';

// Custom metrics
export const errorRate = new Rate('analysis_error_rate');
export const analysisTime = new Trend('analysis_duration');
export const memoryUsage = new Gauge('memory_usage_mb');
export const concurrentAnalyses = new Gauge('concurrent_analyses');
export const repositoryProcessingRate = new Rate('repository_processing_success');

export const options = {
  stages: [
    { duration: '1m', target: 2 },   // Warm up with 2 concurrent analyses
    { duration: '2m', target: 5 },   // Increase to 5 concurrent analyses
    { duration: '3m', target: 8 },   // Peak load with 8 concurrent analyses
    { duration: '2m', target: 5 },   // Scale back down
    { duration: '1m', target: 0 },   // Cool down
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'],  // Analysis requests should complete in <2s
    http_req_failed: ['rate<0.05'],     // Less than 5% error rate
    analysis_error_rate: ['rate<0.05'],
    analysis_duration: ['p(95)<5000'],  // 95% of analyses should complete in <5s
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:8001';

// Test repositories of varying sizes
const TEST_REPOSITORIES = [
  {
    name: 'small-repo',
    url: 'https://github.com/rust-lang/rustlings',
    expectedDuration: 1000, // ~1 second
    size: 'small'
  },
  {
    name: 'medium-repo',
    url: 'https://github.com/tokio-rs/tokio',
    expectedDuration: 5000, // ~5 seconds
    size: 'medium'
  },
  {
    name: 'large-repo',
    url: 'https://github.com/rust-lang/rust',
    expectedDuration: 15000, // ~15 seconds
    size: 'large'
  }
];

export function setup() {
  console.log('Setting up Analysis Engine performance test');
  
  // Health check
  const healthResponse = http.get(`${BASE_URL}/health`);
  check(healthResponse, {
    'analysis engine is healthy': (r) => r.status === 200,
  });
  
  if (healthResponse.status !== 200) {
    throw new Error('Analysis engine is not healthy, aborting test');
  }
  
  console.log('Analysis Engine is ready for testing');
  return { repositories: TEST_REPOSITORIES };
}

export default function(data) {
  const repository = data.repositories[Math.floor(Math.random() * data.repositories.length)];
  
  // Start repository analysis
  const analysisPayload = JSON.stringify({
    repository_url: repository.url,
    analysis_type: 'full',
    include_patterns: true,
    include_metrics: true,
    cache_results: true
  });
  
  const analysisStart = Date.now();
  
  const analysisResponse = http.post(`${BASE_URL}/api/v1/analysis/analyze`, analysisPayload, {
    headers: {
      'Content-Type': 'application/json',
    },
    timeout: '30s',
  });
  
  const analysisSuccess = check(analysisResponse, {
    'analysis request accepted': (r) => r.status === 202 || r.status === 200,
    'analysis response has job id': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.job_id !== undefined;
      } catch (e) {
        return false;
      }
    }
  });
  
  if (!analysisSuccess) {
    errorRate.add(1);
    console.log(`Analysis failed for ${repository.name}: ${analysisResponse.status} ${analysisResponse.body}`);
    return;
  }
  
  errorRate.add(0);
  
  // Extract job ID for status polling
  let jobId;
  try {
    const responseBody = JSON.parse(analysisResponse.body);
    jobId = responseBody.job_id;
  } catch (e) {
    console.log('Failed to parse analysis response:', e);
    return;
  }
  
  // Poll for analysis completion
  let analysisComplete = false;\n  let pollAttempts = 0;\n  const maxPollAttempts = 30; // 30 attempts with 2s intervals = 1 minute timeout\n  \n  while (!analysisComplete && pollAttempts < maxPollAttempts) {\n    sleep(2); // Wait 2 seconds between polls\n    pollAttempts++;\n    \n    const statusResponse = http.get(`${BASE_URL}/api/v1/analysis/status/${jobId}`);\n    \n    const statusCheck = check(statusResponse, {\n      'status check successful': (r) => r.status === 200,\n    });\n    \n    if (!statusCheck) {\n      console.log(`Status check failed for job ${jobId}`);\n      break;\n    }\n    \n    try {\n      const statusBody = JSON.parse(statusResponse.body);\n      const status = statusBody.status;\n      \n      if (status === 'completed') {\n        analysisComplete = true;\n        const totalTime = Date.now() - analysisStart;\n        analysisTime.add(totalTime);\n        repositoryProcessingRate.add(1);\n        \n        console.log(`Analysis completed for ${repository.name} in ${totalTime}ms`);\n        \n        // Check if analysis time is within expected range\n        if (totalTime > repository.expectedDuration * 2) {\n          console.log(`WARNING: Analysis took longer than expected: ${totalTime}ms vs expected ${repository.expectedDuration}ms`);\n        }\n        \n      } else if (status === 'failed' || status === 'error') {\n        console.log(`Analysis failed for ${repository.name}: ${statusBody.error || 'Unknown error'}`);\n        repositoryProcessingRate.add(0);\n        break;\n      } else if (status === 'in_progress' || status === 'queued') {\n        // Continue polling\n        console.log(`Analysis in progress for ${repository.name} (attempt ${pollAttempts})`);\n      }\n      \n    } catch (e) {\n      console.log(`Failed to parse status response: ${e}`);\n      break;\n    }\n  }\n  \n  if (!analysisComplete) {\n    console.log(`Analysis timed out for ${repository.name} after ${pollAttempts} attempts`);\n    repositoryProcessingRate.add(0);\n  }\n  \n  // Test concurrent capabilities\n  testConcurrentAnalysis();\n  \n  // Test memory monitoring\n  testMemoryUsage();\n  \n  // Random sleep to simulate realistic usage\n  sleep(Math.random() * 3 + 1);\n}\n\nfunction testConcurrentAnalysis() {\n  // Test the system's ability to handle concurrent analysis requests\n  const concurrentRequests = [];\n  const concurrentCount = Math.floor(Math.random() * 3) + 1; // 1-3 concurrent requests\n  \n  for (let i = 0; i < concurrentCount; i++) {\n    const payload = JSON.stringify({\n      repository_url: 'https://github.com/rust-lang/book',\n      analysis_type: 'quick',\n      priority: 'low'\n    });\n    \n    const response = http.post(`${BASE_URL}/api/v1/analysis/analyze`, payload, {\n      headers: { 'Content-Type': 'application/json' },\n      timeout: '10s',\n    });\n    \n    concurrentRequests.push(response);\n  }\n  \n  concurrentAnalyses.add(concurrentCount);\n  \n  // Check that all concurrent requests were accepted\n  concurrentRequests.forEach((response, index) => {\n    check(response, {\n      [`concurrent request ${index + 1} accepted`]: (r) => r.status === 202 || r.status === 200,\n    });\n  });\n}\n\nfunction testMemoryUsage() {\n  // Check memory usage metrics\n  const metricsResponse = http.get(`${BASE_URL}/metrics`);\n  \n  if (metricsResponse.status === 200) {\n    try {\n      // Parse Prometheus metrics format\n      const metricsText = metricsResponse.body;\n      const memoryMatch = metricsText.match(/process_resident_memory_bytes (\\d+)/);\n      \n      if (memoryMatch) {\n        const memoryBytes = parseInt(memoryMatch[1]);\n        const memoryMB = memoryBytes / (1024 * 1024);\n        memoryUsage.add(memoryMB);\n        \n        // Alert if memory usage is very high (>2GB)\n        if (memoryMB > 2048) {\n          console.log(`WARNING: High memory usage detected: ${memoryMB.toFixed(2)}MB`);\n        }\n      }\n    } catch (e) {\n      console.log('Failed to parse memory metrics:', e);\n    }\n  }\n}\n\nexport function teardown(data) {\n  console.log('Analysis Engine performance test completed');\n  \n  // Final health check\n  const finalHealthResponse = http.get(`${BASE_URL}/health`);\n  check(finalHealthResponse, {\n    'analysis engine still healthy after test': (r) => r.status === 200,\n  });\n  \n  if (finalHealthResponse.status !== 200) {\n    console.log('WARNING: Analysis engine appears unhealthy after performance test');\n  }\n}