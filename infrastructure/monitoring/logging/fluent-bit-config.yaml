apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-config
  namespace: ccl-system
data:
  # Main Fluent Bit configuration
  fluent-bit.conf: |
    [SERVICE]
        Flush         5
        Log_Level     info
        Daemon        off
        Parsers_File  parsers.conf
        
    # Collect logs from all containers
    [INPUT]
        Name              tail
        Path              /var/log/containers/ccl-*.log
        Parser            docker
        Tag               kube.*
        Refresh_Interval  5
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On
        
    # Enrich logs with Kubernetes metadata
    [FILTER]
        Name                kubernetes
        Match               kube.*
        Kube_URL            https://kubernetes.default.svc:443
        Kube_CA_File        /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        Kube_Token_File     /var/run/secrets/kubernetes.io/serviceaccount/token
        Merge_Log           On
        K8S-Logging.Parser  On
        K8S-Logging.Exclude On
        Keep_Log            Off
        Annotations         Off
        Labels              On
        
    # Parse JSON logs
    [FILTER]
        Name                parser
        Match               kube.*
        Key_Name            log
        Parser              json
        Reserve_Data        On
        Preserve_Key        On
        
    # Add standard fields
    [FILTER]
        Name                modify
        Match               kube.*
        Add                 cluster ${CLUSTER_NAME}
        Add                 environment ${ENVIRONMENT}
        Add                 region ${GCP_REGION}
        
    # Remove sensitive data
    [FILTER]
        Name                lua
        Match               kube.*
        script              sanitize_logs.lua
        call                sanitize_sensitive_data
        
    # Route logs based on service
    [FILTER]
        Name                rewrite_tag
        Match               kube.*
        Rule                $kubernetes['labels']['app'] ^(analysis-engine)$ analysis.$TAG false
        Rule                $kubernetes['labels']['app'] ^(query-intelligence)$ query.$TAG false
        Rule                $kubernetes['labels']['app'] ^(pattern-mining)$ pattern.$TAG false
        Rule                $kubernetes['labels']['app'] ^(marketplace)$ marketplace.$TAG false
        Rule                $kubernetes['labels']['app'] ^(web)$ web.$TAG false
        
    # Output to BigQuery for long-term storage
    [OUTPUT]
        Name                  bigquery
        Match                 *
        project_id            ccl-platform-${ENVIRONMENT}
        dataset_id            logs
        table_id              application_logs
        google_service_credentials /etc/google/credentials/key.json
        
        # Schema
        schema                |
          [
            {"name": "timestamp", "type": "TIMESTAMP"},
            {"name": "severity", "type": "STRING"},
            {"name": "service_name", "type": "STRING"},
            {"name": "pod_name", "type": "STRING"},
            {"name": "container_name", "type": "STRING"},
            {"name": "namespace", "type": "STRING"},
            {"name": "cluster", "type": "STRING"},
            {"name": "environment", "type": "STRING"},
            {"name": "region", "type": "STRING"},
            {"name": "message", "type": "STRING"},
            {"name": "trace_id", "type": "STRING"},
            {"name": "span_id", "type": "STRING"},
            {"name": "user_id", "type": "STRING"},
            {"name": "request_id", "type": "STRING"},
            {"name": "error_type", "type": "STRING"},
            {"name": "stack_trace", "type": "STRING"},
            {"name": "metadata", "type": "JSON"}
          ]
        
        # Partitioning
        time_partitioning_type DAY
        time_partitioning_field timestamp
        
        # Clustering
        clustering_fields     service_name,severity,environment
        
    # Output to Cloud Logging for real-time access
    [OUTPUT]
        Name                  stackdriver
        Match                 *
        project_id            ccl-platform-${ENVIRONMENT}
        resource              k8s_container
        k8s_cluster_name      ${CLUSTER_NAME}
        k8s_cluster_location  ${GCP_REGION}
        labels_key            labels
        severity_key          severity
        
    # Output critical errors to Cloud Pub/Sub for alerting
    [OUTPUT]
        Name                  pubsub
        Match                 *.error
        project_id            ccl-platform-${ENVIRONMENT}
        topic                 critical-errors
        credentials_file      /etc/google/credentials/key.json

  # Parser definitions
  parsers.conf: |
    [PARSER]
        Name        docker
        Format      json
        Time_Key    time
        Time_Format %Y-%m-%dT%H:%M:%S.%LZ
        Time_Keep   On
        
    [PARSER]
        Name        json
        Format      json
        Time_Key    timestamp
        Time_Format %Y-%m-%dT%H:%M:%S.%LZ
        Time_Keep   On
        
    [PARSER]
        Name        rust_log
        Format      regex
        Regex       ^(?<timestamp>\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z)\s+(?<severity>\w+)\s+\[(?<target>[^\]]+)\]\s+(?<message>.*)$
        Time_Key    timestamp
        Time_Format %Y-%m-%dT%H:%M:%S.%LZ
        
    [PARSER]
        Name        python_log
        Format      json
        Time_Key    asctime
        Time_Format %Y-%m-%d %H:%M:%S
        
    [PARSER]
        Name        go_log
        Format      json
        Time_Key    time
        Time_Format %Y-%m-%dT%H:%M:%S.%LZ
        
    [PARSER]
        Name        nginx_access
        Format      regex
        Regex       ^(?<remote>[^ ]*) (?<host>[^ ]*) (?<user>[^ ]*) \[(?<time>[^\]]*)\] "(?<method>\S+)(?: +(?<path>[^\"]*?)(?: +\S*)?)?" (?<code>[^ ]*) (?<size>[^ ]*)(?: "(?<referer>[^\"]*)" "(?<agent>[^\"]*)")?$
        Time_Key    time
        Time_Format %d/%b/%Y:%H:%M:%S %z

  # Lua script for sanitizing sensitive data
  sanitize_logs.lua: |
    function sanitize_sensitive_data(tag, timestamp, record)
        -- List of sensitive field patterns
        local sensitive_patterns = {
            "password", "token", "secret", "key", "authorization",
            "api_key", "private_key", "credit_card", "ssn", "email"
        }
        
        -- Recursively sanitize record
        local function sanitize_value(value)
            if type(value) == "string" then
                -- Mask credit card numbers
                value = string.gsub(value, "%d%d%d%d[-%s]?%d%d%d%d[-%s]?%d%d%d%d[-%s]?%d%d%d%d", "****-****-****-****")
                -- Mask email addresses
                value = string.gsub(value, "[%w._%%-]+@[%w.-]+%.[%w]+", "****@****.***")
                -- Mask JWT tokens
                value = string.gsub(value, "eyJ[%w%-_]+%.[%w%-_]+%.[%w%-_]+", "****JWT****")
                -- Mask API keys (common patterns)
                value = string.gsub(value, "[%w%-]{32,}", "****API_KEY****")
            end
            return value
        end
        
        local function sanitize_table(tbl)
            for key, value in pairs(tbl) do
                -- Check if key contains sensitive pattern
                local key_lower = string.lower(key)
                for _, pattern in ipairs(sensitive_patterns) do
                    if string.find(key_lower, pattern) then
                        tbl[key] = "****REDACTED****"
                        break
                    end
                end
                
                -- Recursively sanitize nested tables
                if type(value) == "table" then
                    sanitize_table(value)
                elseif type(value) == "string" then
                    tbl[key] = sanitize_value(value)
                end
            end
        end
        
        -- Sanitize the record
        sanitize_table(record)
        
        -- Add processing timestamp
        record["processed_at"] = os.date("!%Y-%m-%dT%H:%M:%SZ")
        
        return 1, timestamp, record
    end

---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluent-bit
  namespace: ccl-system
  labels:
    app: fluent-bit
spec:
  selector:
    matchLabels:
      app: fluent-bit
  template:
    metadata:
      labels:
        app: fluent-bit
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "2020"
        prometheus.io/path: "/api/v1/metrics/prometheus"
    spec:
      serviceAccountName: fluent-bit
      containers:
      - name: fluent-bit
        image: fluent/fluent-bit:2.1.10
        imagePullPolicy: Always
        resources:
          limits:
            memory: 200Mi
            cpu: 500m
          requests:
            memory: 100Mi
            cpu: 100m
        env:
        - name: CLUSTER_NAME
          value: "ccl-cluster"
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: cluster-config
              key: environment
        - name: GCP_REGION
          valueFrom:
            configMapKeyRef:
              name: cluster-config
              key: region
        volumeMounts:
        - name: varlog
          mountPath: /var/log
        - name: varlibdockercontainers
          mountPath: /var/lib/docker/containers
          readOnly: true
        - name: config
          mountPath: /fluent-bit/etc/
        - name: google-service-account
          mountPath: /etc/google/credentials
          readOnly: true
      volumes:
      - name: varlog
        hostPath:
          path: /var/log
      - name: varlibdockercontainers
        hostPath:
          path: /var/lib/docker/containers
      - name: config
        configMap:
          name: fluent-bit-config
      - name: google-service-account
        secret:
          secretName: google-service-account
      tolerations:
      - key: node-role.kubernetes.io/master
        operator: Exists
        effect: NoSchedule
      - operator: "Exists"
        effect: "NoExecute"
      - operator: "Exists"
        effect: "NoSchedule"

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: fluent-bit
  namespace: ccl-system

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: fluent-bit
rules:
- apiGroups: [""]
  resources:
  - pods
  - namespaces
  verbs:
  - get
  - list
  - watch

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: fluent-bit
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: fluent-bit
subjects:
- kind: ServiceAccount
  name: fluent-bit
  namespace: ccl-system

---
apiVersion: v1
kind: Service
metadata:
  name: fluent-bit
  namespace: ccl-system
  labels:
    app: fluent-bit
spec:
  type: ClusterIP
  ports:
  - port: 2020
    targetPort: 2020
    name: metrics
  selector:
    app: fluent-bit