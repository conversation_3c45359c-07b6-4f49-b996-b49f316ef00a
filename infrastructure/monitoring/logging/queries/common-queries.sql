-- Common BigQuery queries for log analysis in CCL platform

-- 1. Recent errors by service
WITH recent_errors AS (
  SELECT
    timestamp,
    service_name,
    severity,
    message,
    error_type,
    trace_id,
    user_id,
    JSON_EXTRACT_SCALAR(metadata, '$.request_id') AS request_id,
    JSON_EXTRACT_SCALAR(metadata, '$.endpoint') AS endpoint
  FROM
    `ccl-platform-production.logs.application_logs`
  WHERE
    timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
    AND severity IN ('ERROR', 'CRITICAL')
)
SELECT
  service_name,
  COUNT(*) AS error_count,
  COUNT(DISTINCT user_id) AS affected_users,
  ARRAY_AGG(DISTINCT error_type IGNORE NULLS LIMIT 10) AS error_types,
  ARRAY_AGG(STRUCT(timestamp, message, trace_id) ORDER BY timestamp DESC LIMIT 5) AS recent_examples
FROM
  recent_errors
GROUP BY
  service_name
ORDER BY
  error_count DESC;

-- 2. Query performance analysis
SELECT
  service_name,
  JSON_EXTRACT_SCALAR(metadata, '$.query_type') AS query_type,
  COUNT(*) AS query_count,
  AVG(CAST(JSON_EXTRACT_SCALAR(metadata, '$.duration_ms') AS FLOAT64)) AS avg_duration_ms,
  APPROX_QUANTILES(CAST(JSON_EXTRACT_SCALAR(metadata, '$.duration_ms') AS FLOAT64), 100)[OFFSET(95)] AS p95_duration_ms,
  APPROX_QUANTILES(CAST(JSON_EXTRACT_SCALAR(metadata, '$.duration_ms') AS FLOAT64), 100)[OFFSET(99)] AS p99_duration_ms,
  AVG(CAST(JSON_EXTRACT_SCALAR(metadata, '$.confidence_score') AS FLOAT64)) AS avg_confidence
FROM
  `ccl-platform-production.logs.application_logs`
WHERE
  timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOURS)
  AND service_name = 'query-intelligence'
  AND JSON_EXTRACT_SCALAR(metadata, '$.event_type') = 'query_processed'
GROUP BY
  service_name, query_type
ORDER BY
  query_count DESC;

-- 3. Pattern detection performance
SELECT
  DATE(timestamp) AS date,
  JSON_EXTRACT_SCALAR(metadata, '$.language') AS language,
  COUNT(*) AS patterns_detected,
  AVG(CAST(JSON_EXTRACT_SCALAR(metadata, '$.detection_time_ms') AS FLOAT64)) AS avg_detection_time_ms,
  SUM(CAST(JSON_EXTRACT_SCALAR(metadata, '$.file_count') AS INT64)) AS total_files_processed,
  COUNT(DISTINCT JSON_EXTRACT_SCALAR(metadata, '$.repository_id')) AS unique_repositories
FROM
  `ccl-platform-production.logs.application_logs`
WHERE
  timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAYS)
  AND service_name = 'pattern-mining'
  AND JSON_EXTRACT_SCALAR(metadata, '$.event_type') = 'pattern_detected'
GROUP BY
  date, language
ORDER BY
  date DESC, patterns_detected DESC;

-- 4. User activity tracking
WITH user_sessions AS (
  SELECT
    user_id,
    MIN(timestamp) AS session_start,
    MAX(timestamp) AS session_end,
    COUNT(*) AS action_count,
    COUNT(DISTINCT service_name) AS services_used,
    ARRAY_AGG(DISTINCT JSON_EXTRACT_SCALAR(metadata, '$.action_type') IGNORE NULLS) AS actions
  FROM
    `ccl-platform-production.logs.application_logs`
  WHERE
    timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
    AND user_id IS NOT NULL
  GROUP BY
    user_id
)
SELECT
  COUNT(DISTINCT user_id) AS unique_users,
  AVG(TIMESTAMP_DIFF(session_end, session_start, MINUTE)) AS avg_session_duration_minutes,
  AVG(action_count) AS avg_actions_per_session,
  APPROX_TOP_COUNT(UNNEST(actions), 10) AS top_actions
FROM
  user_sessions;

-- 5. API endpoint performance
SELECT
  JSON_EXTRACT_SCALAR(metadata, '$.endpoint') AS endpoint,
  JSON_EXTRACT_SCALAR(metadata, '$.method') AS method,
  COUNT(*) AS request_count,
  SUM(CASE WHEN CAST(JSON_EXTRACT_SCALAR(metadata, '$.status_code') AS INT64) >= 500 THEN 1 ELSE 0 END) AS error_count,
  AVG(CAST(JSON_EXTRACT_SCALAR(metadata, '$.response_time_ms') AS FLOAT64)) AS avg_response_time_ms,
  APPROX_QUANTILES(CAST(JSON_EXTRACT_SCALAR(metadata, '$.response_time_ms') AS FLOAT64), 100)[OFFSET(95)] AS p95_response_time_ms
FROM
  `ccl-platform-production.logs.application_logs`
WHERE
  timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
  AND JSON_EXTRACT_SCALAR(metadata, '$.event_type') = 'http_request'
GROUP BY
  endpoint, method
HAVING
  request_count > 10
ORDER BY
  request_count DESC;

-- 6. Error correlation analysis
WITH error_windows AS (
  SELECT
    timestamp,
    service_name,
    error_type,
    trace_id,
    LAG(timestamp) OVER (PARTITION BY service_name ORDER BY timestamp) AS prev_error_time
  FROM
    `ccl-platform-production.logs.application_logs`
  WHERE
    timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 6 HOURS)
    AND severity IN ('ERROR', 'CRITICAL')
),
error_clusters AS (
  SELECT
    service_name,
    error_type,
    COUNT(*) AS error_count,
    AVG(TIMESTAMP_DIFF(timestamp, prev_error_time, SECOND)) AS avg_seconds_between_errors,
    ARRAY_AGG(trace_id LIMIT 10) AS sample_trace_ids
  FROM
    error_windows
  WHERE
    prev_error_time IS NOT NULL
  GROUP BY
    service_name, error_type
)
SELECT
  *
FROM
  error_clusters
WHERE
  error_count > 5
  AND avg_seconds_between_errors < 60
ORDER BY
  error_count DESC;

-- 7. Slow query investigation
SELECT
  timestamp,
  user_id,
  JSON_EXTRACT_SCALAR(metadata, '$.query_text') AS query_text,
  CAST(JSON_EXTRACT_SCALAR(metadata, '$.duration_ms') AS FLOAT64) AS duration_ms,
  JSON_EXTRACT_SCALAR(metadata, '$.cache_hit') AS cache_hit,
  trace_id,
  JSON_EXTRACT(metadata, '$.analyzed_files') AS analyzed_files
FROM
  `ccl-platform-production.logs.application_logs`
WHERE
  timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOURS)
  AND service_name = 'query-intelligence'
  AND CAST(JSON_EXTRACT_SCALAR(metadata, '$.duration_ms') AS FLOAT64) > 1000
ORDER BY
  duration_ms DESC
LIMIT 100;

-- 8. Payment failure analysis
SELECT
  DATE(timestamp) AS date,
  JSON_EXTRACT_SCALAR(metadata, '$.payment_provider') AS provider,
  JSON_EXTRACT_SCALAR(metadata, '$.error_code') AS error_code,
  COUNT(*) AS failure_count,
  COUNT(DISTINCT user_id) AS affected_users,
  SUM(CAST(JSON_EXTRACT_SCALAR(metadata, '$.amount') AS FLOAT64)) AS total_failed_amount,
  ARRAY_AGG(DISTINCT JSON_EXTRACT_SCALAR(metadata, '$.pattern_id') IGNORE NULLS LIMIT 10) AS affected_patterns
FROM
  `ccl-platform-production.logs.application_logs`
WHERE
  timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAYS)
  AND service_name = 'marketplace'
  AND JSON_EXTRACT_SCALAR(metadata, '$.event_type') = 'payment_failed'
GROUP BY
  date, provider, error_code
ORDER BY
  date DESC, failure_count DESC;

-- 9. Repository analysis performance by size
SELECT
  JSON_EXTRACT_SCALAR(metadata, '$.size_category') AS size_category,
  COUNT(*) AS analysis_count,
  AVG(CAST(JSON_EXTRACT_SCALAR(metadata, '$.duration_seconds') AS FLOAT64)) AS avg_duration_seconds,
  AVG(CAST(JSON_EXTRACT_SCALAR(metadata, '$.file_count') AS INT64)) AS avg_file_count,
  AVG(CAST(JSON_EXTRACT_SCALAR(metadata, '$.patterns_found') AS INT64)) AS avg_patterns_found,
  ARRAY_AGG(DISTINCT JSON_EXTRACT_SCALAR(metadata, '$.primary_language') IGNORE NULLS) AS languages
FROM
  `ccl-platform-production.logs.application_logs`
WHERE
  timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOURS)
  AND service_name = 'analysis-engine'
  AND JSON_EXTRACT_SCALAR(metadata, '$.event_type') = 'analysis_completed'
GROUP BY
  size_category
ORDER BY
  CASE size_category
    WHEN 'small' THEN 1
    WHEN 'medium' THEN 2
    WHEN 'large' THEN 3
    WHEN 'xlarge' THEN 4
  END;

-- 10. Cross-service trace analysis
WITH service_traces AS (
  SELECT
    trace_id,
    ARRAY_AGG(DISTINCT service_name) AS services_involved,
    MIN(timestamp) AS trace_start,
    MAX(timestamp) AS trace_end,
    COUNT(*) AS span_count,
    SUM(CASE WHEN severity IN ('ERROR', 'CRITICAL') THEN 1 ELSE 0 END) AS error_count
  FROM
    `ccl-platform-production.logs.application_logs`
  WHERE
    timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
    AND trace_id IS NOT NULL
  GROUP BY
    trace_id
)
SELECT
  ARRAY_TO_STRING(services_involved, ' -> ') AS service_flow,
  COUNT(*) AS trace_count,
  AVG(TIMESTAMP_DIFF(trace_end, trace_start, MILLISECOND)) AS avg_trace_duration_ms,
  SUM(error_count) AS total_errors,
  ARRAY_AGG(trace_id LIMIT 5) AS sample_trace_ids
FROM
  service_traces
WHERE
  ARRAY_LENGTH(services_involved) > 1
GROUP BY
  service_flow
ORDER BY
  trace_count DESC;