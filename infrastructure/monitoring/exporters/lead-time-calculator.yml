# Lead Time Calculator Service
# This service calculates DORA lead time metrics from Git and deployment events

apiVersion: apps/v1
kind: Deployment
metadata:
  name: lead-time-calculator
  namespace: monitoring
  labels:
    app: lead-time-calculator
    component: cicd-monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lead-time-calculator
  template:
    metadata:
      labels:
        app: lead-time-calculator
        component: cicd-monitoring
    spec:
      containers:
      - name: lead-time-calculator
        image: ghcr.io/ccl-platform/lead-time-calculator:v1.0.0
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: PORT
          value: "8080"
        - name: GITHUB_TOKEN
          valueFrom:
            secretKeyRef:
              name: github-credentials
              key: token
        - name: PROMETHEUS_URL
          value: "http://prometheus:9090"
        - name: LOG_LEVEL
          value: "info"
        - name: CALCULATION_INTERVAL
          value: "5m"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
        volumeMounts:
        - name: config
          mountPath: /etc/calculator
          readOnly: true
      volumes:
      - name: config
        configMap:
          name: lead-time-calculator-config

---
apiVersion: v1
kind: Service
metadata:
  name: lead-time-calculator
  namespace: monitoring
  labels:
    app: lead-time-calculator
    component: cicd-monitoring
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"
    prometheus.io/path: "/metrics"
spec:
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  selector:
    app: lead-time-calculator

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: lead-time-calculator-config
  namespace: monitoring
data:
  config.yml: |
    # Lead Time Calculator Configuration
    
    # Data sources
    sources:
      github:
        token: "${GITHUB_TOKEN}"
        organization: "ccl-platform"
        repositories:
          - "episteme"
        api_url: "https://api.github.com"
        
      prometheus:
        url: "${PROMETHEUS_URL}"
        queries:
          deployments: 'ccl_deployment_status_total{status="SUCCESS"}'
          builds: 'ccl_build_status_total{status="SUCCESS"}'
          
    # Calculation settings
    calculation:
      interval: "${CALCULATION_INTERVAL}"
      lookback_window: "7d"
      batch_size: 100
      
    # Lead time stages
    stages:
      - name: "commit_to_build"
        start_event: "commit"
        end_event: "build_start"
        
      - name: "build_to_test"
        start_event: "build_success"
        end_event: "test_start"
        
      - name: "test_to_deploy"
        start_event: "test_success"
        end_event: "deployment_start"
        
      - name: "deploy_to_production"
        start_event: "deployment_start"
        end_event: "deployment_success"
        
      - name: "total_lead_time"
        start_event: "commit"
        end_event: "deployment_success"
        
    # Metric definitions
    metrics:
      lead_time_seconds:
        help: "Lead time from commit to production"
        type: "histogram"
        labels:
          - service_name
          - change_type
          - stage
        buckets: [300, 600, 1800, 3600, 7200, 14400, 28800, 43200, 86400]
        
      lead_time_stage_seconds:
        help: "Lead time for individual stages"
        type: "histogram"
        labels:
          - service_name
          - stage_name
        buckets: [60, 300, 600, 1800, 3600, 7200, 14400]
        
    # Change type classification
    change_types:
      feature:
        patterns:
          - "feat:"
          - "feature:"
          - "add:"
          
      bugfix:
        patterns:
          - "fix:"
          - "bug:"
          - "bugfix:"
          
      hotfix:
        patterns:
          - "hotfix:"
          - "urgent:"
          - "critical:"
          
      refactor:
        patterns:
          - "refactor:"
          - "refact:"
          - "cleanup:"
          
    # Service mapping
    service_mapping:
      # Map repository paths to service names
      "analysis-engine/": "analysis-engine"
      "query-intelligence/": "query-intelligence"
      "pattern-mining/": "pattern-mining"
      "marketplace/": "marketplace"
      "web/": "web"
      "collaboration/": "collaboration"
      
    # Exclusions
    exclusions:
      # Exclude certain commits from lead time calculation
      commit_patterns:
        - "^Merge pull request"
        - "^Merge branch"
        - "^Revert"
        - "^docs:"
        - "^ci:"
        
      # Exclude certain branches
      branches:
        - "dependabot/*"
        - "renovate/*"
        
    # Caching
    cache:
      ttl: "1h"
      max_size: 10000
      
    # Logging
    logging:
      level: "info"
      format: "json"

---
# CronJob for periodic lead time calculation
apiVersion: batch/v1
kind: CronJob
metadata:
  name: lead-time-calculation
  namespace: monitoring
spec:
  schedule: "*/5 * * * *"  # Every 5 minutes
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: lead-time-calculator
            image: ghcr.io/ccl-platform/lead-time-calculator:v1.0.0
            command: ["/app/calculate"]
            env:
            - name: GITHUB_TOKEN
              valueFrom:
                secretKeyRef:
                  name: github-credentials
                  key: token
            - name: PROMETHEUS_URL
              value: "http://prometheus:9090"
            volumeMounts:
            - name: config
              mountPath: /etc/calculator
              readOnly: true
          volumes:
          - name: config
            configMap:
              name: lead-time-calculator-config
          restartPolicy: OnFailure
