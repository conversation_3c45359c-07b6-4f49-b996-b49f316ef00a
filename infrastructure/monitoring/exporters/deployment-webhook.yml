# Deployment Webhook Service
# This service receives deployment events and exposes them as Prometheus metrics

apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment-webhook
  namespace: monitoring
  labels:
    app: deployment-webhook
    component: cicd-monitoring
spec:
  replicas: 2
  selector:
    matchLabels:
      app: deployment-webhook
  template:
    metadata:
      labels:
        app: deployment-webhook
        component: cicd-monitoring
    spec:
      containers:
      - name: deployment-webhook
        image: ghcr.io/ccl-platform/deployment-webhook:v1.0.0
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 9090
          name: metrics
        env:
        - name: PORT
          value: "8080"
        - name: METRICS_PORT
          value: "9090"
        - name: LOG_LEVEL
          value: "info"
        - name: WEBHOOK_SECRET
          valueFrom:
            secretKeyRef:
              name: webhook-credentials
              key: secret
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
        volumeMounts:
        - name: config
          mountPath: /etc/webhook
          readOnly: true
      volumes:
      - name: config
        configMap:
          name: deployment-webhook-config

---
apiVersion: v1
kind: Service
metadata:
  name: deployment-webhook
  namespace: monitoring
  labels:
    app: deployment-webhook
    component: cicd-monitoring
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
    prometheus.io/path: "/metrics"
spec:
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  - port: 9090
    targetPort: 9090
    name: metrics
  selector:
    app: deployment-webhook
  type: LoadBalancer

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: deployment-webhook-config
  namespace: monitoring
data:
  config.yml: |
    # Deployment Webhook Configuration
    server:
      port: 8080
      metrics_port: 9090
      
    webhook:
      secret: "${WEBHOOK_SECRET}"
      path: "/webhook/deployment"
      
    # Metric definitions
    metrics:
      deployment_events_total:
        help: "Total number of deployment events"
        type: "counter"
        labels:
          - service_name
          - environment
          - version
          - deployment_strategy
          - triggered_by
          
      deployment_status_total:
        help: "Deployment status outcomes"
        type: "counter"
        labels:
          - service_name
          - environment
          - version
          - status
          - failure_reason
          
      deployment_duration_seconds:
        help: "Time taken for deployments to complete"
        type: "histogram"
        labels:
          - service_name
          - environment
          - deployment_strategy
        buckets: [30, 60, 120, 300, 600, 1200, 1800, 3600]
        
      rollback_events_total:
        help: "Number of rollback events"
        type: "counter"
        labels:
          - service_name
          - environment
          - rollback_reason
          
      lead_time_seconds:
        help: "Lead time from commit to production"
        type: "histogram"
        labels:
          - service_name
          - change_type
        buckets: [300, 600, 1800, 3600, 7200, 14400, 28800, 43200, 86400]
        
    # Event processing
    processing:
      # GitHub webhook events
      github:
        events:
          - deployment
          - deployment_status
          - workflow_run
        mapping:
          deployment:
            service_name: "payload.deployment.environment"
            environment: "payload.deployment.environment"
            version: "payload.deployment.sha"
            status: "payload.deployment_status.state"
            
      # Cloud Run deployment events
      cloud_run:
        events:
          - service.deployed
          - service.failed
        mapping:
          service_name: "resource.labels.service_name"
          environment: "resource.labels.location"
          version: "protoPayload.request.spec.template.metadata.name"
          
    # Storage for metrics
    storage:
      type: "memory"
      retention: "24h"
      
    # Logging
    logging:
      level: "info"
      format: "json"

---
apiVersion: v1
kind: Secret
metadata:
  name: webhook-credentials
  namespace: monitoring
type: Opaque
data:
  # Base64 encoded webhook secret
  # kubectl create secret generic webhook-credentials --from-literal=secret=YOUR_WEBHOOK_SECRET -n monitoring
  secret: ""

---
# Ingress for external webhook access
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: deployment-webhook-ingress
  namespace: monitoring
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - webhook.ccl-platform.com
    secretName: webhook-tls
  rules:
  - host: webhook.ccl-platform.com
    http:
      paths:
      - path: /webhook
        pathType: Prefix
        backend:
          service:
            name: deployment-webhook
            port:
              number: 8080
