# GitHub Actions Exporter Configuration
# This service exports GitHub Actions workflow and job metrics to Prometheus

apiVersion: apps/v1
kind: Deployment
metadata:
  name: github-actions-exporter
  namespace: monitoring
  labels:
    app: github-actions-exporter
    component: cicd-monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: github-actions-exporter
  template:
    metadata:
      labels:
        app: github-actions-exporter
        component: cicd-monitoring
    spec:
      containers:
      - name: github-actions-exporter
        image: ghcr.io/ccl-platform/github-actions-exporter:v1.0.0
        ports:
        - containerPort: 9090
          name: metrics
        env:
        - name: GITHUB_TOKEN
          valueFrom:
            secretKeyRef:
              name: github-credentials
              key: token
        - name: GITHUB_ORG
          value: "ccl-platform"
        - name: GITHUB_REPOS
          value: "episteme"
        - name: METRICS_PORT
          value: "9090"
        - name: SCRAPE_INTERVAL
          value: "30s"
        - name: LOG_LEVEL
          value: "info"
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        livenessProbe:
          httpGet:
            path: /health
            port: 9090
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /ready
            port: 9090
          initialDelaySeconds: 5
          periodSeconds: 10
        volumeMounts:
        - name: config
          mountPath: /etc/exporter
          readOnly: true
      volumes:
      - name: config
        configMap:
          name: github-actions-exporter-config
      serviceAccountName: github-actions-exporter

---
apiVersion: v1
kind: Service
metadata:
  name: github-actions-exporter
  namespace: monitoring
  labels:
    app: github-actions-exporter
    component: cicd-monitoring
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
    prometheus.io/path: "/metrics"
spec:
  ports:
  - port: 9090
    targetPort: 9090
    name: metrics
  selector:
    app: github-actions-exporter

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: github-actions-exporter-config
  namespace: monitoring
data:
  config.yml: |
    # GitHub Actions Exporter Configuration
    github:
      token: "${GITHUB_TOKEN}"
      organization: "${GITHUB_ORG}"
      repositories:
        - "${GITHUB_REPOS}"
      
    metrics:
      port: 9090
      path: "/metrics"
      
    # Workflow metrics to collect
    workflows:
      - name: "CI Pipeline"
        file: "ci-common.yml"
        metrics:
          - duration
          - conclusion
          - jobs_count
          - steps_count
          
      - name: "Deployment Pipeline"
        file: "cd-deploy.yml"
        metrics:
          - duration
          - conclusion
          - environment
          - service_name
          
    # Job metrics to collect
    jobs:
      metrics:
        - duration
        - conclusion
        - runner_type
        - steps_count
        
    # Custom metric mappings
    metric_mappings:
      workflow_run_duration_seconds:
        help: "Duration of GitHub Actions workflow runs"
        type: "histogram"
        labels:
          - workflow_name
          - repository
          - branch
          - conclusion
          - environment
          - service_name
        buckets: [30, 60, 120, 300, 600, 900, 1200, 1800, 3600]
        
      workflow_run_conclusion_total:
        help: "Total number of workflow runs by conclusion"
        type: "counter"
        labels:
          - workflow_name
          - repository
          - branch
          - conclusion
          - environment
          - service_name
          
      job_duration_seconds:
        help: "Duration of individual jobs"
        type: "histogram"
        labels:
          - job_name
          - workflow_name
          - repository
          - runner_type
          - conclusion
        buckets: [10, 30, 60, 120, 300, 600, 900, 1200]
        
      job_conclusion_total:
        help: "Total number of jobs by conclusion"
        type: "counter"
        labels:
          - job_name
          - workflow_name
          - repository
          - runner_type
          - conclusion
          
    # Rate limiting
    rate_limit:
      requests_per_hour: 4000
      burst: 100
      
    # Caching
    cache:
      ttl: "5m"
      max_size: 1000
      
    # Logging
    logging:
      level: "info"
      format: "json"

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: github-actions-exporter
  namespace: monitoring

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: github-actions-exporter
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: github-actions-exporter
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: github-actions-exporter
subjects:
- kind: ServiceAccount
  name: github-actions-exporter
  namespace: monitoring

---
# Secret for GitHub credentials (to be created separately)
apiVersion: v1
kind: Secret
metadata:
  name: github-credentials
  namespace: monitoring
type: Opaque
data:
  # Base64 encoded GitHub token
  # kubectl create secret generic github-credentials --from-literal=token=YOUR_GITHUB_TOKEN -n monitoring
  token: ""
