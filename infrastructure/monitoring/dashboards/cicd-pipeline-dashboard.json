{"dashboard": {"id": null, "uid": "ccl-cicd-pipeline", "title": "CCL CI/CD Pipeline Dashboard", "tags": ["ccl", "cicd", "deployment", "devops"], "timezone": "browser", "schemaVersion": 30, "version": 1, "refresh": "30s", "time": {"from": "now-24h", "to": "now"}, "templating": {"list": [{"name": "datasource", "type": "datasource", "query": "prometheus", "current": {"text": "CCL-Prometheus", "value": "CCL-Prometheus"}}, {"name": "environment", "type": "query", "datasource": "$datasource", "query": "label_values(ccl_deployments_total, environment)", "current": {"text": "All", "value": "$__all"}, "multi": true, "includeAll": true}, {"name": "service", "type": "query", "datasource": "$datasource", "query": "label_values(ccl_deployments_total{environment=~\"$environment\"}, service_name)", "current": {"text": "All", "value": "$__all"}, "multi": true, "includeAll": true}]}, "annotations": {"list": [{"datasource": "$datasource", "enable": true, "expr": "ccl_deployment_status{status=\"FAILED\", environment=~\"$environment\"}", "iconColor": "red", "name": "Failed Deployments", "step": "10s", "tagKeys": "service_name,version", "titleFormat": "Deployment Failed: {{ service_name }}", "textFormat": "Version {{ version }} failed to deploy"}, {"datasource": "$datasource", "enable": true, "expr": "ccl_rollback_events{environment=~\"$environment\"}", "iconColor": "orange", "name": "Rollbacks", "step": "10s", "tagKeys": "service_name,rollback_reason", "titleFormat": "Rollback: {{ service_name }}", "textFormat": "Reason: {{ rollback_reason }}"}]}, "panels": [{"title": "DORA Metrics Overview", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "collapsed": false}, {"title": "Deployment Frequency", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 0, "y": 1}, "targets": [{"expr": "sum(rate(ccl_deployments_total{environment=~\"$environment\", service_name=~\"$service\"}[24h])) * 86400", "legendFormat": "Deployments/Day", "refId": "A"}], "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "orientation": "auto", "textMode": "auto", "graphMode": "area"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 1}, {"color": "green", "value": 5}]}, "unit": "short", "decimals": 1}}}, {"title": "Lead Time for Changes", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 6, "y": 1}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(ccl_lead_time_bucket{environment=~\"$environment\", service_name=~\"$service\"}[24h])) by (le)) / 3600", "legendFormat": "P95 Lead Time (hours)", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 2}, {"color": "red", "value": 8}]}, "unit": "h", "decimals": 1}}}, {"title": "Mean Time to Recovery", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 12, "y": 1}, "targets": [{"expr": "histogram_quantile(0.50, sum(rate(ccl_mttr_bucket{environment=~\"$environment\", service_name=~\"$service\"}[7d])) by (le)) / 60", "legendFormat": "MTTR (minutes)", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 60}, {"color": "red", "value": 240}]}, "unit": "m", "decimals": 0}}}, {"title": "Change Failure Rate", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 18, "y": 1}, "targets": [{"expr": "sum(rate(ccl_deployment_status{status=\"FAILED\", environment=~\"$environment\", service_name=~\"$service\"}[7d])) / sum(rate(ccl_deployments_total{environment=~\"$environment\", service_name=~\"$service\"}[7d])) * 100", "legendFormat": "Failure Rate %", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5}, {"color": "red", "value": 15}]}, "unit": "percent", "decimals": 1}}}, {"title": "Pipeline Performance", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "collapsed": false}, {"title": "Build Success Rate by Service", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "targets": [{"expr": "sum(rate(ccl_build_success_rate{environment=~\"$environment\", service_name=~\"$service\"}[5m])) by (service_name)", "legendFormat": "{{service_name}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "unit": "percentunit", "min": 0, "max": 1}}}, {"title": "Pipeline Duration by Stage", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(ccl_pipeline_duration_bucket{environment=~\"$environment\", service_name=~\"$service\"}[5m])) by (le, pipeline_stage))", "legendFormat": "{{pipeline_stage}} P95", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "s"}}}, {"title": "Security & Quality", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "collapsed": false}, {"title": "Security Issues by Severity", "type": "timeseries", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 19}, "targets": [{"expr": "sum(ccl_security_issues{environment=~\"$environment\", service_name=~\"$service\"}) by (severity)", "legendFormat": "{{severity}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}}, {"title": "Test Coverage by Service", "type": "timeseries", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 19}, "targets": [{"expr": "ccl_test_coverage{environment=~\"$environment\", service_name=~\"$service\"}", "legendFormat": "{{service_name}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent", "min": 0, "max": 100}}}, {"title": "Code Quality Score", "type": "gauge", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 19}, "targets": [{"expr": "avg(ccl_code_quality_score{environment=~\"$environment\", service_name=~\"$service\"})", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 6}, {"color": "green", "value": 8}]}, "unit": "short", "min": 0, "max": 10}}}, {"title": "Deployment Details", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 27}, "collapsed": false}, {"title": "Recent Deployments", "type": "table", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 28}, "targets": [{"expr": "sort_desc(ccl_deployment_status{environment=~\"$environment\", service_name=~\"$service\"})", "format": "table", "instant": true, "refId": "A"}], "transformations": [{"id": "organize", "options": {"excludeByName": {"__name__": true, "job": true, "instance": true}, "indexByName": {}, "renameByName": {"service_name": "Service", "environment": "Environment", "version": "Version", "status": "Status", "deployment_strategy": "Strategy"}}}], "fieldConfig": {"defaults": {"custom": {"align": "auto", "displayMode": "auto"}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Status"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}, {"id": "mappings", "value": [{"options": {"SUCCESS": {"color": "green", "index": 0}}, "type": "value"}, {"options": {"FAILED": {"color": "red", "index": 1}}, "type": "value"}, {"options": {"ROLLBACK": {"color": "orange", "index": 2}}, "type": "value"}]}]}]}}]}}