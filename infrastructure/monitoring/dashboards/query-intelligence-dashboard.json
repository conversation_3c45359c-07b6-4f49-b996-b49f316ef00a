{"dashboard": {"id": null, "uid": "ccl-query-intelligence", "title": "CCL Query Intelligence Dashboard", "tags": ["ccl", "query-intelligence", "ai", "performance"], "timezone": "browser", "schemaVersion": 30, "version": 1, "refresh": "30s", "time": {"from": "now-3h", "to": "now"}, "templating": {"list": [{"name": "datasource", "type": "datasource", "query": "prometheus", "current": {"text": "CCL-Prometheus", "value": "CCL-Prometheus"}}, {"name": "environment", "type": "query", "datasource": "$datasource", "query": "label_values(queries_total, environment)", "current": {"text": "production", "value": "production"}, "multi": false}, {"name": "query_type", "type": "query", "datasource": "$datasource", "query": "label_values(queries_total{environment=\"$environment\"}, query_type)", "current": {"text": "All", "value": "$__all"}, "multi": true, "includeAll": true}]}, "panels": [{"title": "Query Response Time (P95)", "type": "gauge", "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(query_processing_duration_bucket{environment=\"$environment\", query_type=~\"$query_type\"}[5m])) by (le))", "refId": "A"}], "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": true, "showThresholdMarkers": true}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}, "unit": "ms", "decimals": 0, "min": 0, "max": 200}}}, {"title": "Query Rate", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 6, "y": 0}, "targets": [{"expr": "sum(rate(queries_total{environment=\"$environment\", query_type=~\"$query_type\"}[5m]))", "refId": "A"}], "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "orientation": "auto", "text": {}, "textMode": "auto", "graphMode": "area"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps", "decimals": 2}}}, {"title": "Active Queries", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 12, "y": 0}, "targets": [{"expr": "sum(active_queries{environment=\"$environment\"})", "refId": "A"}], "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "orientation": "auto", "text": {}, "textMode": "auto", "graphMode": "area"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100}, {"color": "red", "value": 500}]}, "unit": "short"}}}, {"title": "<PERSON><PERSON> Hit Rate", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 18, "y": 0}, "targets": [{"expr": "sum(rate(cache_hits_total{environment=\"$environment\", cache_type=\"query\"}[5m])) / (sum(rate(cache_hits_total{environment=\"$environment\", cache_type=\"query\"}[5m])) + sum(rate(cache_misses_total{environment=\"$environment\", cache_type=\"query\"}[5m]))) * 100", "refId": "A"}], "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "orientation": "auto", "text": {}, "textMode": "auto", "graphMode": "area"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 60}, {"color": "green", "value": 80}]}, "unit": "percent", "decimals": 1, "min": 0, "max": 100}}}, {"title": "Average Confidence Score", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 6, "y": 4}, "targets": [{"expr": "avg(query_confidence_score{environment=\"$environment\", query_type=~\"$query_type\"})", "refId": "A"}], "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "orientation": "auto", "text": {}, "textMode": "auto", "graphMode": "area"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 0.7}, {"color": "green", "value": 0.85}]}, "unit": "percentunit", "decimals": 2, "min": 0, "max": 1}}}, {"title": "Query Success Rate", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 12, "y": 4}, "targets": [{"expr": "(1 - (sum(rate(errors_total{environment=\"$environment\", type=\"query_error\"}[5m])) / sum(rate(queries_total{environment=\"$environment\"}[5m])))) * 100", "refId": "A"}], "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "orientation": "auto", "text": {}, "textMode": "auto", "graphMode": "area"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 95}, {"color": "green", "value": 98}]}, "unit": "percent", "decimals": 2, "min": 0, "max": 100}}}, {"title": "Query Latency Distribution", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(query_processing_duration_bucket{environment=\"$environment\", query_type=~\"$query_type\"}[5m])) by (le))", "legendFormat": "P99", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(query_processing_duration_bucket{environment=\"$environment\", query_type=~\"$query_type\"}[5m])) by (le))", "legendFormat": "P95", "refId": "B"}, {"expr": "histogram_quantile(0.50, sum(rate(query_processing_duration_bucket{environment=\"$environment\", query_type=~\"$query_type\"}[5m])) by (le))", "legendFormat": "P50", "refId": "C"}], "yaxes": [{"format": "ms", "label": "Latency", "show": true}, {"show": false}], "thresholds": [{"value": 100, "op": "gt", "fill": true, "line": true, "colorMode": "critical"}]}, {"title": "Confidence Score Distribution", "type": "histogram", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "query_confidence_score{environment=\"$environment\", query_type=~\"$query_type\"}", "format": "time_series", "refId": "A"}], "options": {"bucketSize": 0.05, "bucketOffset": 0}, "fieldConfig": {"defaults": {"unit": "percentunit"}}}, {"title": "Queries by Type", "type": "piechart", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 16}, "targets": [{"expr": "sum(increase(queries_total{environment=\"$environment\", query_type=~\"$query_type\"}[1h])) by (query_type)", "legendFormat": "{{query_type}}", "refId": "A"}], "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "pieType": "donut", "displayLabels": ["name", "percent"], "legend": {"displayMode": "table", "placement": "right", "values": ["value", "percent"]}}}, {"title": "ML Inference Performance", "type": "graph", "gridPos": {"h": 8, "w": 16, "x": 8, "y": 16}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(ml_inference_duration_bucket{environment=\"$environment\", model_name=\"gemini-2.5\"}[5m])) by (le))", "legendFormat": "Gemini 2.5 P95", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(ml_inference_duration_bucket{environment=\"$environment\", model_name=\"embeddings\"}[5m])) by (le))", "legendFormat": "Embeddings P95", "refId": "B"}], "yaxes": [{"format": "ms", "label": "Inference Time", "show": true}, {"show": false}]}, {"title": "Vertex AI Quota Usage", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "sum(rate(vertex_ai_requests_total{environment=\"$environment\"}[5m])) by (model)", "legendFormat": "{{model}}", "refId": "A"}], "yaxes": [{"format": "reqps", "label": "Requests/sec", "show": true}, {"show": false}], "thresholds": [{"value": 1, "op": "gt", "fill": true, "line": true, "colorMode": "warning"}]}, {"title": "Query <PERSON>", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "sum(rate(cache_hits_total{environment=\"$environment\", cache_type=\"query\"}[5m]))", "legendFormat": "<PERSON><PERSON>", "refId": "A"}, {"expr": "sum(rate(cache_misses_total{environment=\"$environment\", cache_type=\"query\"}[5m]))", "legendFormat": "<PERSON><PERSON>", "refId": "B"}], "yaxes": [{"format": "ops", "label": "Operations/sec", "show": true}, {"show": false}]}, {"title": "Query Service Health", "type": "table", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}, "targets": [{"expr": "up{job=\"ccl-services\", service_name=\"query-intelligence\", environment=\"$environment\"}", "format": "table", "instant": true, "legendFormat": "Status", "refId": "A"}, {"expr": "rate(queries_total{environment=\"$environment\"}[5m])", "format": "table", "instant": true, "legendFormat": "QPS", "refId": "B"}, {"expr": "histogram_quantile(0.95, sum(rate(query_processing_duration_bucket{environment=\"$environment\"}[5m])) by (le))", "format": "table", "instant": true, "legendFormat": "P95 Latency", "refId": "C"}, {"expr": "avg(query_confidence_score{environment=\"$environment\"})", "format": "table", "instant": true, "legendFormat": "Avg Confidence", "refId": "D"}], "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true}, "renameByName": {"instance": "Instance", "Value #A": "Status", "Value #B": "QPS", "Value #C": "P95 Latency (ms)", "Value #D": "Avg Confidence"}}}], "fieldConfig": {"overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Status"}, "properties": [{"id": "mappings", "value": [{"type": "value", "value": "0", "text": "DOWN", "color": "red"}, {"type": "value", "value": "1", "text": "UP", "color": "green"}]}, {"id": "custom.displayMode", "value": "color-background"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "P95 Latency (ms)"}, "properties": [{"id": "unit", "value": "ms"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}}, {"id": "custom.displayMode", "value": "color-background"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Avg Confidence"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "decimals", "value": 2}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 0.7}, {"color": "green", "value": 0.85}]}}, {"id": "custom.displayMode", "value": "color-background"}]}]}}]}}