{"dashboard": {"id": null, "uid": "ccl-slo-dashboard", "title": "CCL SLO Dashboard", "tags": ["ccl", "slo", "reliability"], "timezone": "browser", "schemaVersion": 30, "version": 1, "refresh": "1m", "time": {"from": "now-7d", "to": "now"}, "templating": {"list": [{"name": "datasource", "type": "datasource", "query": "prometheus", "current": {"text": "CCL-Prometheus", "value": "CCL-Prometheus"}}, {"name": "environment", "type": "query", "datasource": "$datasource", "query": "label_values(up{job=\"ccl-services\"}, environment)", "current": {"text": "production", "value": "production"}, "multi": false}]}, "panels": [{"title": "Overall Platform Availability (30d)", "type": "gauge", "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}, "targets": [{"expr": "avg_over_time((1 - (sum(rate(http_requests_total{environment=\"$environment\", status=~\"5..\"}[5m])) / sum(rate(http_requests_total{environment=\"$environment\"}[5m]))))[30d:5m]) * 100", "refId": "A"}], "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": true, "showThresholdMarkers": true}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 99.5}, {"color": "green", "value": 99.9}]}, "unit": "percent", "decimals": 3, "min": 95, "max": 100}}}, {"title": "API Latency SLO (P95 < 100ms)", "type": "gauge", "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}, "targets": [{"expr": "(sum(rate(http_request_duration_seconds_bucket{environment=\"$environment\", le=\"0.1\"}[30d])) / sum(rate(http_request_duration_seconds_count{environment=\"$environment\"}[30d]))) * 100", "refId": "A"}], "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": true, "showThresholdMarkers": true}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 90}, {"color": "green", "value": 95}]}, "unit": "percent", "decimals": 2, "min": 0, "max": 100}}}, {"title": "Query Intelligence Accuracy SLO", "type": "gauge", "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}, "targets": [{"expr": "(sum(increase(queries_total{environment=\"$environment\", confidence>=\"0.8\"}[30d])) / sum(increase(queries_total{environment=\"$environment\"}[30d]))) * 100", "refId": "A"}], "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": true, "showThresholdMarkers": true}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 80}, {"color": "green", "value": 85}]}, "unit": "percent", "decimals": 1, "min": 0, "max": 100}}}, {"title": "Error Budget Remaining", "type": "gauge", "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}, "targets": [{"expr": "(1 - ((1 - avg_over_time((1 - (sum(rate(http_requests_total{environment=\"$environment\", status=~\"5..\"}[5m])) / sum(rate(http_requests_total{environment=\"$environment\"}[5m]))))[30d:5m])) / (1 - 0.999))) * 100", "refId": "A"}], "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": true, "showThresholdMarkers": true}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 25}, {"color": "green", "value": 50}]}, "unit": "percent", "decimals": 1, "min": 0, "max": 100}}}, {"title": "Service-Level Availability Trends", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "(1 - (sum(rate(http_requests_total{environment=\"$environment\", service_name=\"analysis-engine\", status=~\"5..\"}[1h])) / sum(rate(http_requests_total{environment=\"$environment\", service_name=\"analysis-engine\"}[1h])))) * 100", "legendFormat": "Analysis Engine", "refId": "A"}, {"expr": "(1 - (sum(rate(http_requests_total{environment=\"$environment\", service_name=\"query-intelligence\", status=~\"5..\"}[1h])) / sum(rate(http_requests_total{environment=\"$environment\", service_name=\"query-intelligence\"}[1h])))) * 100", "legendFormat": "Query Intelligence", "refId": "B"}, {"expr": "(1 - (sum(rate(http_requests_total{environment=\"$environment\", service_name=\"pattern-mining\", status=~\"5..\"}[1h])) / sum(rate(http_requests_total{environment=\"$environment\", service_name=\"pattern-mining\"}[1h])))) * 100", "legendFormat": "Pattern Mining", "refId": "C"}, {"expr": "(1 - (sum(rate(http_requests_total{environment=\"$environment\", service_name=\"marketplace\", status=~\"5..\"}[1h])) / sum(rate(http_requests_total{environment=\"$environment\", service_name=\"marketplace\"}[1h])))) * 100", "legendFormat": "Marketplace", "refId": "D"}], "yaxes": [{"format": "percent", "label": "Availability %", "show": true, "min": 95, "max": 100}, {"show": false}], "thresholds": [{"value": 99.9, "op": "lt", "fill": true, "line": true, "colorMode": "critical"}]}, {"title": "Service Latency Percentiles", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{environment=\"$environment\"}[5m])) by (service_name, le))", "legendFormat": "{{service_name}} - P95", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(http_request_duration_seconds_bucket{environment=\"$environment\"}[5m])) by (service_name, le))", "legendFormat": "{{service_name}} - P99", "refId": "B"}], "yaxes": [{"format": "s", "label": "Latency", "show": true, "decimals": 3}, {"show": false}], "thresholds": [{"value": 0.1, "op": "gt", "fill": true, "line": true, "colorMode": "warning"}]}, {"title": "Error Budget Burn Rate", "type": "graph", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}, "targets": [{"expr": "sum(rate(http_requests_total{environment=\"$environment\", status=~\"5..\"}[1h])) / sum(rate(http_requests_total{environment=\"$environment\"}[1h])) / (1 - 0.999)", "legendFormat": "Burn Rate (1x = normal)", "refId": "A"}], "yaxes": [{"format": "short", "label": "Burn Rate Multiple", "show": true, "min": 0}, {"show": false}], "thresholds": [{"value": 1, "op": "gt", "fill": true, "line": true, "colorMode": "warning"}, {"value": 2, "op": "gt", "fill": true, "line": true, "colorMode": "critical"}]}, {"title": "SLO Compliance Summary", "type": "table", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}, "targets": [{"expr": "vector(99.9)", "format": "table", "instant": true, "legendFormat": "API Availability Target", "refId": "A"}, {"expr": "avg_over_time((1 - (sum(rate(http_requests_total{environment=\"$environment\", status=~\"5..\"}[5m])) / sum(rate(http_requests_total{environment=\"$environment\"}[5m]))))[30d:5m]) * 100", "format": "table", "instant": true, "legendFormat": "API Availability Actual", "refId": "B"}, {"expr": "vector(95)", "format": "table", "instant": true, "legendFormat": "Latency SLO Target", "refId": "C"}, {"expr": "(sum(rate(http_request_duration_seconds_bucket{environment=\"$environment\", le=\"0.1\"}[30d])) / sum(rate(http_request_duration_seconds_count{environment=\"$environment\"}[30d]))) * 100", "format": "table", "instant": true, "legendFormat": "Latency SLO Actual", "refId": "D"}, {"expr": "vector(85)", "format": "table", "instant": true, "legendFormat": "Query Accuracy Target", "refId": "E"}, {"expr": "(sum(increase(queries_total{environment=\"$environment\", confidence>=\"0.8\"}[30d])) / sum(increase(queries_total{environment=\"$environment\"}[30d]))) * 100", "format": "table", "instant": true, "legendFormat": "Query Accuracy Actual", "refId": "F"}], "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true}, "renameByName": {"Value #A": "Availability Target %", "Value #B": "Availability Actual %", "Value #C": "Latency Target %", "Value #D": "Latency Actual %", "Value #E": "Accuracy Target %", "Value #F": "Accuracy Actual %"}}}], "fieldConfig": {"defaults": {"custom": {"align": "center"}}, "overrides": [{"matcher": {"id": "byRegexp", "options": ".*Actual.*"}, "properties": [{"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 90}, {"color": "green", "value": 95}]}}, {"id": "custom.displayMode", "value": "color-background"}, {"id": "decimals", "value": 2}, {"id": "unit", "value": "percent"}]}, {"matcher": {"id": "byRegexp", "options": ".*Target.*"}, "properties": [{"id": "custom.displayMode", "value": "color-text"}, {"id": "color", "value": {"mode": "fixed", "fixedColor": "blue"}}, {"id": "decimals", "value": 1}, {"id": "unit", "value": "percent"}]}]}}]}}