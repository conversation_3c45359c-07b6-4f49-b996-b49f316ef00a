{"dashboard": {"id": null, "uid": "ccl-query-intelligence-dev", "title": "CCL Query Intelligence - Development", "tags": ["ccl", "query-intelligence", "development", "ai"], "timezone": "browser", "schemaVersion": 30, "version": 1, "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m"], "time_options": ["15m", "30m", "1h", "2h", "6h", "12h"]}, "templating": {"list": [{"name": "datasource", "type": "datasource", "query": "prometheus", "current": {"text": "Prometheus", "value": "Prometheus"}}]}, "panels": [{"id": 1, "title": "Query Latency Distribution", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"expr": "histogram_quantile(0.99, rate(query_processing_duration_seconds_bucket{environment=\"development\", service=\"query-intelligence\"}[5m]))", "legendFormat": "99th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(query_processing_duration_seconds_bucket{environment=\"development\", service=\"query-intelligence\"}[5m]))", "legendFormat": "95th percentile", "refId": "B"}, {"expr": "histogram_quantile(0.50, rate(query_processing_duration_seconds_bucket{environment=\"development\", service=\"query-intelligence\"}[5m]))", "legendFormat": "50th percentile", "refId": "C"}], "yAxes": [{"label": "Duration (seconds)", "min": 0}, {"show": false}], "tooltip": {"shared": true, "sort": 2}}, {"id": 2, "title": "Gemini API Usage", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"expr": "rate(gemini_api_requests_total{environment=\"development\"}[5m])", "legendFormat": "Requests/sec", "refId": "A"}, {"expr": "rate(gemini_api_tokens_total{environment=\"development\"}[5m])", "legendFormat": "Tokens/sec", "refId": "B"}], "yAxes": [{"label": "Rate", "min": 0}, {"show": false}], "alert": {"conditions": [{"evaluator": {"params": [50], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "for": "2m", "frequency": "30s", "handler": 1, "name": "Gemini API Rate Limit Warning", "noDataState": "no_data", "notifications": []}}, {"id": 3, "title": "<PERSON><PERSON>", "type": "stat", "gridPos": {"h": 4, "w": 8, "x": 0, "y": 8}, "targets": [{"expr": "query_cache_hits_total{environment=\"development\"} / (query_cache_hits_total{environment=\"development\"} + query_cache_misses_total{environment=\"development\"}) * 100", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}}}, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "horizontal"}}, {"id": 4, "title": "Active Queries", "type": "stat", "gridPos": {"h": 4, "w": 8, "x": 8, "y": 8}, "targets": [{"expr": "query_active_requests{environment=\"development\", service=\"query-intelligence\"}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "min": 0, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 20}]}}}}, {"id": 5, "title": "Response Confidence Score", "type": "stat", "gridPos": {"h": 4, "w": 8, "x": 16, "y": 8}, "targets": [{"expr": "avg(query_confidence_score{environment=\"development\"})", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percentunit", "min": 0, "max": 1, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 0.7}, {"color": "green", "value": 0.85}]}}}}, {"id": 6, "title": "Popular Query Types", "type": "piechart", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "targets": [{"expr": "sum by (query_type) (increase(query_requests_total{environment=\"development\"}[1h]))", "legendFormat": "{{query_type}}", "refId": "A"}], "options": {"pieType": "pie", "tooltip": {"mode": "single"}, "legend": {"displayMode": "list", "placement": "bottom"}}}, {"id": 7, "title": "Error Analysis", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}, "targets": [{"expr": "sum by (error_type, error_message) (increase(query_errors_total{environment=\"development\"}[1h]))", "format": "table", "instant": true, "refId": "A"}], "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true}, "indexByName": {}, "renameByName": {"error_type": "Error Type", "error_message": "Error Message", "Value": "Count"}}}]}, {"id": 8, "title": "Recent Query Log", "type": "logs", "gridPos": {"h": 10, "w": 24, "x": 0, "y": 20}, "targets": [{"expr": "{environment=\"development\", service=\"query-intelligence\"}", "refId": "A"}], "options": {"showTime": true, "showLabels": true, "showCommonLabels": false, "wrapLogMessage": true, "dedupStrategy": "exact"}}, {"id": 9, "title": "Memory Usage", "type": "graph", "gridPos": {"h": 6, "w": 12, "x": 0, "y": 30}, "targets": [{"expr": "process_resident_memory_bytes{environment=\"development\", service=\"query-intelligence\"}", "legendFormat": "RSS Memory", "refId": "A"}, {"expr": "go_memstats_heap_inuse_bytes{environment=\"development\", service=\"query-intelligence\"}", "legendFormat": "Heap Memory", "refId": "B"}], "yAxes": [{"label": "Bytes", "min": 0}, {"show": false}]}, {"id": 10, "title": "Embedding Generation Rate", "type": "graph", "gridPos": {"h": 6, "w": 12, "x": 12, "y": 30}, "targets": [{"expr": "rate(embeddings_generated_total{environment=\"development\"}[5m])", "legendFormat": "Embeddings/sec", "refId": "A"}], "yAxes": [{"label": "Rate", "min": 0}, {"show": false}]}]}}