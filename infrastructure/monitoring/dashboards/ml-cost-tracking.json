{"dashboard": {"id": null, "uid": "ccl-ml-cost-tracking", "title": "CCL ML Cost Tracking Dashboard", "tags": ["ccl", "ml", "costs", "big<PERSON>y", "budget"], "timezone": "browser", "schemaVersion": 30, "version": 1, "refresh": "5m", "time": {"from": "now-30d", "to": "now"}, "timepicker": {"refresh_intervals": ["1m", "5m", "15m", "30m", "1h"], "time_options": ["1h", "6h", "12h", "24h", "7d", "30d"]}, "templating": {"list": [{"name": "service", "type": "query", "datasource": "<PERSON><PERSON><PERSON><PERSON>", "query": "SELECT DISTINCT service FROM `ccl-platform.ml_operations.cost_tracking` ORDER BY service", "current": {"text": "All", "value": "$__all"}, "multi": true, "includeAll": true}, {"name": "operation_type", "type": "query", "datasource": "<PERSON><PERSON><PERSON><PERSON>", "query": "SELECT DISTINCT operation_type FROM `ccl-platform.ml_operations.cost_tracking` ORDER BY operation_type", "current": {"text": "All", "value": "$__all"}, "multi": true, "includeAll": true}]}, "panels": [{"id": 1, "title": "💰 Total Monthly Cost", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 0, "y": 0}, "targets": [{"datasource": "<PERSON><PERSON><PERSON><PERSON>", "rawSql": "SELECT SUM(actual_cost_usd) as total_cost FROM `ccl-platform.ml_operations.cost_tracking` WHERE operation_date >= DATE_TRUNC(CURRENT_DATE(), MONTH) AND service IN ($service) AND operation_type IN ($operation_type)", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5000}, {"color": "red", "value": 10000}]}}}, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center"}}, {"id": 2, "title": "📊 Operations This Month", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 6, "y": 0}, "targets": [{"datasource": "<PERSON><PERSON><PERSON><PERSON>", "rawSql": "SELECT COUNT(*) as operation_count FROM `ccl-platform.ml_operations.cost_tracking` WHERE operation_date >= DATE_TRUNC(CURRENT_DATE(), MONTH) AND service IN ($service) AND operation_type IN ($operation_type)", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}}, {"id": 3, "title": "⚡ Avg Cost per Operation", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 12, "y": 0}, "targets": [{"datasource": "<PERSON><PERSON><PERSON><PERSON>", "rawSql": "SELECT AVG(actual_cost_usd) as avg_cost FROM `ccl-platform.ml_operations.cost_tracking` WHERE operation_date >= DATE_TRUNC(CURRENT_DATE(), MONTH) AND service IN ($service) AND operation_type IN ($operation_type)", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "decimals": 4}}}, {"id": 4, "title": "💾 Data Processed (TB)", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 18, "y": 0}, "targets": [{"datasource": "<PERSON><PERSON><PERSON><PERSON>", "rawSql": "SELECT SUM(bytes_processed) / (1024*1024*1024*1024) as tb_processed FROM `ccl-platform.ml_operations.cost_tracking` WHERE operation_date >= DATE_TRUNC(CURRENT_DATE(), MONTH) AND service IN ($service) AND operation_type IN ($operation_type)", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "decbytes", "decimals": 2}}}, {"id": 5, "title": "📈 Daily Cost Trend", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}, "targets": [{"datasource": "<PERSON><PERSON><PERSON><PERSON>", "rawSql": "SELECT operation_date as time, service, SUM(actual_cost_usd) as daily_cost FROM `ccl-platform.ml_operations.cost_tracking` WHERE operation_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY) AND service IN ($service) AND operation_type IN ($operation_type) GROUP BY operation_date, service ORDER BY operation_date", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "custom": {"drawStyle": "line", "lineInterpolation": "smooth", "barAlignment": 0, "lineWidth": 2, "fillOpacity": 10, "gradientMode": "opacity", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}}}}, "options": {"tooltip": {"mode": "multi", "sort": "desc"}, "legend": {"displayMode": "list", "placement": "bottom"}}}, {"id": 6, "title": "🥧 Cost by Operation Type", "type": "piechart", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}, "targets": [{"datasource": "<PERSON><PERSON><PERSON><PERSON>", "rawSql": "SELECT operation_type, SUM(actual_cost_usd) as total_cost FROM `ccl-platform.ml_operations.cost_tracking` WHERE operation_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY) AND service IN ($service) AND operation_type IN ($operation_type) GROUP BY operation_type ORDER BY total_cost DESC", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}}}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "pieType": "pie", "tooltip": {"mode": "single", "sort": "none"}, "legend": {"displayMode": "list", "placement": "right"}}}, {"id": 7, "title": "💰 Budget Utilization Status", "type": "bargauge", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 12}, "targets": [{"datasource": "<PERSON><PERSON><PERSON><PERSON>", "rawSql": "SELECT CONCAT(service, ' - ', COALESCE(operation_type, 'All')) as budget_name, monthly_utilization_percent, budget_status FROM `ccl-platform.ml_operations.budget_utilization` WHERE service IN ($service) ORDER BY monthly_utilization_percent DESC", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 75}, {"color": "orange", "value": 90}, {"color": "red", "value": 100}]}, "custom": {"displayMode": "gradient", "orientation": "horizontal"}}}, "options": {"showUnfilled": true}}, {"id": 8, "title": "⚠️ Cost <PERSON>s", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}, "targets": [{"datasource": "<PERSON><PERSON><PERSON><PERSON>", "rawSql": "SELECT service, operation_type, alert_level, today_cost, avg_daily_cost, cost_change_percent FROM `ccl-platform.ml_operations.cost_alerts` WHERE alert_level IN ('HIGH_COST_ALERT', 'MODERATE_COST_ALERT') AND service IN ($service) ORDER BY ABS(cost_change_percent) DESC", "refId": "A"}], "fieldConfig": {"defaults": {"custom": {"align": "auto", "displayMode": "auto"}, "mappings": [{"options": {"HIGH_COST_ALERT": {"text": "🔴 High Cost", "color": "red"}, "MODERATE_COST_ALERT": {"text": "🟡 Moderate", "color": "orange"}}, "type": "value"}]}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "today_cost"}, "properties": [{"id": "unit", "value": "currencyUSD"}, {"id": "displayName", "value": "Today's Cost"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "avg_daily_cost"}, "properties": [{"id": "unit", "value": "currencyUSD"}, {"id": "displayName", "value": "Avg Daily Cost"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "cost_change_percent"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "displayName", "value": "% Change"}]}]}}, {"id": 9, "title": "⚡ Cost Efficiency Metrics", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "targets": [{"datasource": "<PERSON><PERSON><PERSON><PERSON>", "rawSql": "SELECT service, operation_type, bytes_per_dollar, avg_duration_seconds, total_operations, total_cost FROM `ccl-platform.ml_operations.cost_efficiency` WHERE service IN ($service) AND operation_type IN ($operation_type) ORDER BY total_cost DESC", "refId": "A"}], "fieldConfig": {"defaults": {"custom": {"align": "auto", "displayMode": "auto"}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "bytes_per_dollar"}, "properties": [{"id": "unit", "value": "decbytes"}, {"id": "displayName", "value": "Bytes per $"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "avg_duration_seconds"}, "properties": [{"id": "unit", "value": "s"}, {"id": "displayName", "value": "Avg Duration"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "total_cost"}, "properties": [{"id": "unit", "value": "currencyUSD"}, {"id": "displayName", "value": "Total Cost"}]}]}}, {"id": 10, "title": "📊 Monthly Cost Comparison", "type": "barchart", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 28}, "targets": [{"datasource": "<PERSON><PERSON><PERSON><PERSON>", "rawSql": "SELECT CONCAT(CAST(year AS STRING), '-', LPAD(CAST(month AS STRING), 2, '0')) as month_year, service, monthly_cost FROM `ccl-platform.ml_operations.monthly_cost_trends` WHERE service IN ($service) AND operation_type IN ($operation_type) ORDER BY year, month, monthly_cost DESC", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "custom": {"axisPlacement": "auto", "axisLabel": "", "axisWidth": 80, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}}}, "options": {"orientation": "auto", "xTickLabelRotation": -45, "xTickLabelSpacing": 0, "legend": {"displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}}], "annotations": {"list": [{"name": "Budget Alerts", "datasource": "<PERSON><PERSON><PERSON><PERSON>", "enable": true, "iconColor": "red", "query": "SELECT operation_date as time, CONCAT('Budget Alert: ', service, ' - ', operation_type) as text FROM `ccl-platform.ml_operations.cost_alerts` WHERE alert_level = 'HIGH_COST_ALERT'", "textColumn": "text", "timeColumn": "time"}]}}}