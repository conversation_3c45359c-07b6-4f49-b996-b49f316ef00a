{"dashboard": {"id": null, "uid": "ccl-service-overview", "title": "CCL Service Overview", "tags": ["ccl", "overview", "sre"], "timezone": "browser", "schemaVersion": 30, "version": 1, "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "templating": {"list": [{"name": "datasource", "type": "datasource", "query": "prometheus", "current": {"text": "CCL-Prometheus", "value": "CCL-Prometheus"}}, {"name": "environment", "type": "query", "datasource": "$datasource", "query": "label_values(up{job=\"ccl-services\"}, environment)", "current": {"text": "production", "value": "production"}, "multi": false, "includeAll": false}, {"name": "service", "type": "query", "datasource": "$datasource", "query": "label_values(up{job=\"ccl-services\", environment=\"$environment\"}, service_name)", "current": {"text": "All", "value": "$__all"}, "multi": true, "includeAll": true}]}, "annotations": {"list": [{"datasource": "$datasource", "enable": true, "expr": "ALERTS{alertstate=\"firing\", environment=\"$environment\"}", "iconColor": "red", "name": "Active Alerts", "step": "10s", "tagKeys": "severity,service", "titleFormat": "{{ alertname }}", "textFormat": "{{ description }}"}, {"datasource": "$datasource", "enable": true, "expr": "deployment_info{environment=\"$environment\"}", "iconColor": "blue", "name": "Deployments", "step": "10s", "tagKeys": "service,version", "titleFormat": "Deployment: {{ service }}", "textFormat": "Version {{ version }} deployed"}]}, "panels": [{"title": "Service Health Status", "type": "stat", "gridPos": {"h": 4, "w": 24, "x": 0, "y": 0}, "targets": [{"expr": "up{job=\"ccl-services\", environment=\"$environment\", service_name=~\"$service\"}", "legendFormat": "{{service_name}}", "refId": "A"}], "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"type": "value", "value": 0, "text": "DOWN", "color": "red"}, {"type": "value", "value": 1, "text": "UP", "color": "green"}], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}, "unit": "none"}}}, {"title": "Request Rate by Service", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}, "targets": [{"expr": "sum(rate(http_requests_total{environment=\"$environment\", service_name=~\"$service\"}[5m])) by (service_name)", "legendFormat": "{{service_name}}", "refId": "A"}], "yaxes": [{"format": "reqps", "label": "Requests/sec", "show": true}, {"show": false}], "xaxis": {"show": true}, "legend": {"show": true, "alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": false, "total": false, "values": true}, "options": {"alertThreshold": true}}, {"title": "Error Rate by Service", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}, "targets": [{"expr": "sum(rate(http_requests_total{environment=\"$environment\", service_name=~\"$service\", status=~\"5..\"}[5m])) by (service_name) / sum(rate(http_requests_total{environment=\"$environment\", service_name=~\"$service\"}[5m])) by (service_name) * 100", "legendFormat": "{{service_name}}", "refId": "A"}], "yaxes": [{"format": "percent", "label": "Error %", "show": true, "min": 0, "max": 10}, {"show": false}], "thresholds": [{"value": 1, "op": "gt", "fill": true, "line": true, "colorMode": "critical"}], "alert": {"name": "High Error Rate", "conditions": [{"evaluator": {"params": [5], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "frequency": "1m", "handler": 1, "message": "Service error rate is above 5%", "noDataState": "no_data", "notifications": []}}, {"title": "P95 Latency by Service", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{environment=\"$environment\", service_name=~\"$service\"}[5m])) by (service_name, le))", "legendFormat": "{{service_name}}", "refId": "A"}], "yaxes": [{"format": "s", "label": "Latency", "show": true, "decimals": 3}, {"show": false}], "thresholds": [{"value": 0.1, "op": "gt", "fill": true, "line": true, "colorMode": "warning"}, {"value": 0.5, "op": "gt", "fill": true, "line": true, "colorMode": "critical"}]}, {"title": "Active Requests by Service", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}, "targets": [{"expr": "sum(active_requests{environment=\"$environment\", service_name=~\"$service\"}) by (service_name)", "legendFormat": "{{service_name}}", "refId": "A"}], "yaxes": [{"format": "short", "label": "Active Requests", "show": true, "min": 0}, {"show": false}]}, {"title": "CPU Usage by Service", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}, "targets": [{"expr": "avg(rate(container_cpu_usage_seconds_total{namespace=\"ccl-$environment\", pod=~\".*$service.*\"}[5m])) by (pod) * 100", "legendFormat": "{{pod}}", "refId": "A"}], "yaxes": [{"format": "percent", "label": "CPU %", "show": true, "min": 0, "max": 100}, {"show": false}], "thresholds": [{"value": 80, "op": "gt", "fill": true, "line": true, "colorMode": "warning"}]}, {"title": "Memory Usage by Service", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "targets": [{"expr": "avg(container_memory_usage_bytes{namespace=\"ccl-$environment\", pod=~\".*$service.*\"}) by (pod) / avg(container_spec_memory_limit_bytes{namespace=\"ccl-$environment\", pod=~\".*$service.*\"}) by (pod) * 100", "legendFormat": "{{pod}}", "refId": "A"}], "yaxes": [{"format": "percent", "label": "Memory %", "show": true, "min": 0, "max": 100}, {"show": false}], "thresholds": [{"value": 90, "op": "gt", "fill": true, "line": true, "colorMode": "critical"}]}, {"title": "Service Metrics Summary", "type": "table", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 28}, "targets": [{"expr": "sum(rate(http_requests_total{environment=\"$environment\", service_name=~\"$service\"}[5m])) by (service_name)", "format": "table", "instant": true, "legendFormat": "Request Rate", "refId": "A"}, {"expr": "sum(rate(http_requests_total{environment=\"$environment\", service_name=~\"$service\", status=~\"5..\"}[5m])) by (service_name) / sum(rate(http_requests_total{environment=\"$environment\", service_name=~\"$service\"}[5m])) by (service_name) * 100", "format": "table", "instant": true, "legendFormat": "Error Rate %", "refId": "B"}, {"expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{environment=\"$environment\", service_name=~\"$service\"}[5m])) by (service_name, le))", "format": "table", "instant": true, "legendFormat": "P95 Latency", "refId": "C"}, {"expr": "up{job=\"ccl-services\", environment=\"$environment\", service_name=~\"$service\"}", "format": "table", "instant": true, "legendFormat": "Status", "refId": "D"}], "options": {"showHeader": true, "sortBy": [{"displayName": "Service", "desc": false}]}, "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true}, "renameByName": {"service_name": "Service", "Value #A": "Req/s", "Value #B": "Error %", "Value #C": "P95 (ms)", "Value #D": "Status"}}}], "fieldConfig": {"overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Req/s"}, "properties": [{"id": "unit", "value": "reqps"}, {"id": "decimals", "value": 2}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Error %"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "decimals", "value": 2}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}, {"id": "custom.displayMode", "value": "color-background"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "P95 (ms)"}, "properties": [{"id": "unit", "value": "ms"}, {"id": "decimals", "value": 0}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100}, {"color": "red", "value": 500}]}}, {"id": "custom.displayMode", "value": "color-background"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Status"}, "properties": [{"id": "mappings", "value": [{"type": "value", "value": "0", "text": "DOWN", "color": "red"}, {"type": "value", "value": "1", "text": "UP", "color": "green"}]}, {"id": "custom.displayMode", "value": "color-background"}]}]}}, {"title": "CI/CD Metrics", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 20}, "collapsed": false}, {"title": "Recent Deployments", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 0, "y": 21}, "targets": [{"expr": "sum(increase(ccl_deployment_status_total{service_name=~\"$service\", environment=\"$environment\"}[24h]))", "legendFormat": "Deployments (24h)", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 1}, {"color": "green", "value": 3}]}, "unit": "short"}}}, {"title": "Deployment Success Rate", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 6, "y": 21}, "targets": [{"expr": "sum(rate(ccl_deployment_status_total{service_name=~\"$service\", environment=\"$environment\", status=\"SUCCESS\"}[24h])) / sum(rate(ccl_deployment_status_total{service_name=~\"$service\", environment=\"$environment\"}[24h])) * 100", "legendFormat": "Success Rate", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 90}, {"color": "green", "value": 95}]}, "unit": "percent", "min": 0, "max": 100}}}, {"title": "Build Status", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 12, "y": 21}, "targets": [{"expr": "ccl_build_success_rate{service_name=~\"$service\"} * 100", "legendFormat": "Build Success Rate", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}, "unit": "percent", "min": 0, "max": 100}}}, {"title": "Security Issues", "type": "stat", "gridPos": {"h": 4, "w": 6, "x": 18, "y": 21}, "targets": [{"expr": "sum(ccl_security_issues{service_name=~\"$service\", severity=\"CRITICAL\"})", "legendFormat": "Critical Issues", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}, "unit": "short"}}}, {"title": "Deployment Timeline", "type": "timeseries", "gridPos": {"h": 6, "w": 24, "x": 0, "y": 25}, "targets": [{"expr": "increase(ccl_deployment_status_total{service_name=~\"$service\", environment=\"$environment\"}[1h])", "legendFormat": "{{service_name}} - {{status}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "bars", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 80, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "normal", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "unit": "short"}}}]}}