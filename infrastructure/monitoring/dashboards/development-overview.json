{"dashboard": {"id": null, "uid": "ccl-dev-overview", "title": "CCL Development Overview", "tags": ["ccl", "development", "local"], "timezone": "browser", "schemaVersion": 30, "version": 1, "refresh": "15s", "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "15s", "30s", "1m", "2m", "5m"], "time_options": ["5m", "15m", "30m", "1h", "2h", "6h", "12h", "24h"]}, "templating": {"list": [{"name": "datasource", "type": "datasource", "query": "prometheus", "current": {"text": "Prometheus", "value": "Prometheus"}}]}, "panels": [{"id": 1, "title": "Service Health Status", "type": "stat", "gridPos": {"h": 4, "w": 24, "x": 0, "y": 0}, "targets": [{"expr": "up{environment=\"development\"}", "legendFormat": "{{service_name}}", "refId": "A"}], "fieldConfig": {"defaults": {"mappings": [{"options": {"0": {"text": "DOWN", "color": "red"}, "1": {"text": "UP", "color": "green"}}, "type": "value"}], "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}}}, {"id": 2, "title": "Request Rate by Service", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}, "targets": [{"expr": "rate(http_requests_total{environment=\"development\"}[5m])", "legendFormat": "{{service_name}} - {{method}}", "refId": "A"}], "yAxes": [{"label": "Requests/sec"}, {"show": false}], "tooltip": {"shared": true, "sort": 2}}, {"id": 3, "title": "Error Rate by Endpoint", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}, "targets": [{"expr": "rate(http_requests_total{environment=\"development\", status=~\"5..\"}[5m]) / rate(http_requests_total{environment=\"development\"}[5m]) * 100", "legendFormat": "{{service_name}} - {{endpoint}}", "refId": "A"}], "yAxes": [{"label": "Error Rate %", "max": 100, "min": 0}, {"show": false}], "alert": {"conditions": [{"evaluator": {"params": [5], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "for": "1m", "frequency": "10s", "handler": 1, "name": "Development Error Rate Alert", "noDataState": "no_data", "notifications": []}}, {"id": 4, "title": "Database Query Performance", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "targets": [{"expr": "histogram_quantile(0.95, rate(database_query_duration_seconds_bucket{environment=\"development\"}[5m]))", "legendFormat": "95th percentile - {{service_name}}", "refId": "A"}, {"expr": "histogram_quantile(0.50, rate(database_query_duration_seconds_bucket{environment=\"development\"}[5m]))", "legendFormat": "50th percentile - {{service_name}}", "refId": "B"}], "yAxes": [{"label": "Duration (seconds)", "logBase": 1, "min": 0}, {"show": false}]}, {"id": 5, "title": "<PERSON><PERSON> Hit Rates", "type": "singlestat", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}, "targets": [{"expr": "cache_hits_total{environment=\"development\"} / (cache_hits_total{environment=\"development\"} + cache_misses_total{environment=\"development\"}) * 100", "legendFormat": "{{service_name}} - {{cache_type}}", "refId": "A"}], "valueName": "current", "valueFontSize": "80%", "format": "percent", "thresholds": "70,90", "colorBackground": true, "colors": ["#d44a3a", "#e6522c", "#299c46"]}, {"id": 6, "title": "Background Job Status", "type": "table", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 20}, "targets": [{"expr": "background_jobs_total{environment=\"development\"}", "format": "table", "instant": true, "refId": "A"}], "columns": [{"text": "Service", "value": "service_name"}, {"text": "Job Type", "value": "job_type"}, {"text": "Status", "value": "status"}, {"text": "Count", "value": "Value"}], "sort": {"col": 3, "desc": true}}, {"id": 7, "title": "Local Development Logs", "type": "logs", "gridPos": {"h": 12, "w": 24, "x": 0, "y": 28}, "targets": [{"expr": "{environment=\"development\"}", "refId": "A"}], "options": {"showTime": true, "showLabels": true, "showCommonLabels": false, "wrapLogMessage": true, "dedupStrategy": "none"}}]}}