{"dashboard": {"id": null, "uid": "ccl-platform-team", "title": "CCL Platform Team Dashboard", "tags": ["ccl", "platform", "team", "cicd", "infrastructure"], "timezone": "browser", "schemaVersion": 30, "version": 1, "refresh": "30s", "time": {"from": "now-7d", "to": "now"}, "templating": {"list": [{"name": "datasource", "type": "datasource", "query": "prometheus", "current": {"text": "CCL-Prometheus", "value": "CCL-Prometheus"}}, {"name": "environment", "type": "query", "datasource": "$datasource", "query": "label_values(ccl_deployment_status_total, environment)", "current": {"text": "All", "value": "$__all"}, "multi": true, "includeAll": true}]}, "panels": [{"title": "Platform Health Overview", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "collapsed": false}, {"title": "Overall Deployment Success Rate", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 0, "y": 1}, "targets": [{"expr": "sum(rate(ccl_deployment_status_total{environment=~\"$environment\", status=\"SUCCESS\"}[7d])) / sum(rate(ccl_deployment_status_total{environment=~\"$environment\"}[7d])) * 100", "legendFormat": "Success Rate", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}, "unit": "percent", "min": 0, "max": 100}}}, {"title": "Platform MTTR", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 6, "y": 1}, "targets": [{"expr": "histogram_quantile(0.50, sum(rate(ccl_mttr_seconds_bucket{environment=~\"$environment\"}[7d])) by (le)) / 60", "legendFormat": "Minutes", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 60}, {"color": "red", "value": 240}]}, "unit": "m", "decimals": 0}}}, {"title": "Pipeline Efficiency", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 12, "y": 1}, "targets": [{"expr": "avg(histogram_quantile(0.95, sum(rate(ccl_pipeline_duration_seconds_bucket{environment=~\"$environment\"}[7d])) by (le))) / 60", "legendFormat": "P95 Minutes", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 15}, {"color": "red", "value": 30}]}, "unit": "m", "decimals": 1}}}, {"title": "Security Posture", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 18, "y": 1}, "targets": [{"expr": "sum(ccl_security_issues{environment=~\"$environment\", severity=\"CRITICAL\"})", "legendFormat": "Critical Issues", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}, "unit": "short"}}}, {"title": "Resource Utilization", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "collapsed": false}, {"title": "CI/CD Runner Utilization", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "targets": [{"expr": "ccl_runner_utilization{environment=~\"$environment\"}", "legendFormat": "{{runner_type}} - {{runner_os}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent", "min": 0, "max": 100}}}, {"title": "Artifact Storage Usage", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "targets": [{"expr": "ccl_artifact_storage_usage{environment=~\"$environment\"}", "legendFormat": "{{environment}} Storage", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent", "min": 0, "max": 100}}}, {"title": "Service Performance", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "collapsed": false}, {"title": "Deployment Frequency by Service", "type": "timeseries", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 19}, "targets": [{"expr": "sum(rate(ccl_deployment_status_total{environment=~\"$environment\"}[1h])) by (service_name) * 3600", "legendFormat": "{{service_name}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}}, {"title": "Build Success Rate by Service", "type": "timeseries", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 19}, "targets": [{"expr": "ccl_build_success_rate * 100", "legendFormat": "{{service_name}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent", "min": 0, "max": 100}}}, {"title": "Lead Time Distribution", "type": "heatmap", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 19}, "targets": [{"expr": "sum(rate(ccl_lead_time_seconds_bucket{environment=~\"$environment\"}[1h])) by (le)", "legendFormat": "{{le}}", "refId": "A"}], "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}}}}, {"title": "Incident Management", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 27}, "collapsed": false}, {"title": "Rollback Events", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 28}, "targets": [{"expr": "increase(ccl_rollback_events_total{environment=~\"$environment\"}[1h])", "legendFormat": "{{service_name}} - {{rollback_reason}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}}, {"title": "Change Failure Rate", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 28}, "targets": [{"expr": "sum(rate(ccl_deployment_status_total{environment=~\"$environment\", status=\"FAILED\"}[7d])) by (service_name) / sum(rate(ccl_deployment_status_total{environment=~\"$environment\"}[7d])) by (service_name) * 100", "legendFormat": "{{service_name}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent", "min": 0}}}]}}