{"dashboard": {"id": null, "uid": "ccl-dev-team", "title": "CCL Development Team Dashboard", "tags": ["ccl", "development", "team", "cicd"], "timezone": "browser", "schemaVersion": 30, "version": 1, "refresh": "30s", "time": {"from": "now-24h", "to": "now"}, "templating": {"list": [{"name": "datasource", "type": "datasource", "query": "prometheus", "current": {"text": "CCL-Prometheus", "value": "CCL-Prometheus"}}, {"name": "service", "type": "query", "datasource": "$datasource", "query": "label_values(ccl_deployment_status_total, service_name)", "current": {"text": "All", "value": "$__all"}, "multi": true, "includeAll": true}]}, "panels": [{"title": "Development Velocity", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "collapsed": false}, {"title": "Daily Deployment Frequency", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 0, "y": 1}, "targets": [{"expr": "sum(rate(ccl_deployment_status_total{service_name=~\"$service\"}[24h])) * 86400", "legendFormat": "Deployments/Day", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 1}, {"color": "green", "value": 5}]}, "unit": "short", "decimals": 1}}}, {"title": "Lead Time (P95)", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 6, "y": 1}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(ccl_lead_time_seconds_bucket{service_name=~\"$service\"}[24h])) by (le)) / 3600", "legendFormat": "Hours", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 2}, {"color": "red", "value": 8}]}, "unit": "h", "decimals": 1}}}, {"title": "Build Success Rate", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 12, "y": 1}, "targets": [{"expr": "avg(ccl_build_success_rate{service_name=~\"$service\"}) * 100", "legendFormat": "Success Rate", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 90}, {"color": "green", "value": 95}]}, "unit": "percent", "min": 0, "max": 100}}}, {"title": "Test Coverage", "type": "stat", "gridPos": {"h": 8, "w": 6, "x": 18, "y": 1}, "targets": [{"expr": "avg(ccl_test_coverage{service_name=~\"$service\"})", "legendFormat": "Coverage %", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 85}, {"color": "green", "value": 90}]}, "unit": "percent", "min": 0, "max": 100}}}, {"title": "Build Performance", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "collapsed": false}, {"title": "Build Duration Trends", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(ccl_build_duration_seconds_bucket{service_name=~\"$service\"}[5m])) by (le, service_name))", "legendFormat": "{{service_name}} P95", "refId": "A"}, {"expr": "histogram_quantile(0.50, sum(rate(ccl_build_duration_seconds_bucket{service_name=~\"$service\"}[5m])) by (le, service_name))", "legendFormat": "{{service_name}} P50", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "s"}}}, {"title": "Pipeline Stage Performance", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(ccl_pipeline_duration_seconds_bucket{service_name=~\"$service\"}[5m])) by (le, pipeline_stage))", "legendFormat": "{{pipeline_stage}} P95", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "s"}}}, {"title": "Quality Metrics", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "collapsed": false}, {"title": "Test Coverage by Service", "type": "timeseries", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 19}, "targets": [{"expr": "ccl_test_coverage{service_name=~\"$service\"}", "legendFormat": "{{service_name}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent", "min": 0, "max": 100}}}, {"title": "Code Quality Score", "type": "timeseries", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 19}, "targets": [{"expr": "ccl_code_quality_score{service_name=~\"$service\"}", "legendFormat": "{{service_name}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short", "min": 0, "max": 10}}}, {"title": "Security Issues", "type": "timeseries", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 19}, "targets": [{"expr": "ccl_security_issues{service_name=~\"$service\"}", "legendFormat": "{{service_name}} - {{severity}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}}, {"title": "Recent Activity", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 27}, "collapsed": false}, {"title": "Recent Deployments", "type": "table", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 28}, "targets": [{"expr": "sort_desc(ccl_deployment_status_total{service_name=~\"$service\"})", "format": "table", "instant": true, "refId": "A"}], "transformations": [{"id": "organize", "options": {"excludeByName": {"__name__": true, "job": true, "instance": true}, "indexByName": {}, "renameByName": {"service_name": "Service", "environment": "Environment", "version": "Version", "status": "Status", "deployment_strategy": "Strategy"}}}], "fieldConfig": {"defaults": {"custom": {"align": "auto", "displayMode": "auto"}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Status"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}, {"id": "mappings", "value": [{"options": {"SUCCESS": {"color": "green", "index": 0}}, "type": "value"}, {"options": {"FAILED": {"color": "red", "index": 1}}, "type": "value"}, {"options": {"ROLLBACK": {"color": "orange", "index": 2}}, "type": "value"}]}]}]}}]}}