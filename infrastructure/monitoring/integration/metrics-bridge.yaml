# Metrics Bridge Configuration
# This configuration bridges CI/CD pipeline metrics with existing monitoring infrastructure

# Prometheus metric mappings for CI/CD pipeline
prometheus_metrics:
  # Map CI/CD metrics to existing Prometheus format
  metric_mappings:
    # Deployment metrics
    - source: "ccl_deployments_total"
      target: "deployment_events_total"
      labels:
        service: "service_name"
        env: "environment"
        strategy: "deployment_strategy"
      help: "Total number of deployments initiated"
      
    - source: "ccl_deployment_status"
      target: "deployment_status_total"
      labels:
        service: "service_name"
        env: "environment"
        status: "status"
        version: "version"
      help: "Deployment status outcomes"
      
    - source: "ccl_deployment_duration"
      target: "deployment_duration_seconds"
      type: "histogram"
      labels:
        service: "service_name"
        env: "environment"
        strategy: "deployment_strategy"
      help: "Time taken for deployments to complete"
      
    # Build metrics
    - source: "ccl_build_duration"
      target: "build_duration_seconds"
      type: "histogram"
      labels:
        service: "service_name"
        language: "language"
        type: "build_type"
      help: "Time taken for builds to complete"
      
    - source: "ccl_build_success_rate"
      target: "build_success_ratio"
      type: "gauge"
      labels:
        service: "service_name"
        branch: "branch"
      help: "Build success rate over time"
      
    # Test metrics
    - source: "ccl_test_coverage"
      target: "test_coverage_percent"
      type: "gauge"
      labels:
        service: "service_name"
        type: "coverage_type"
      help: "Test coverage percentage"
      
    - source: "ccl_test_duration"
      target: "test_duration_seconds"
      type: "histogram"
      labels:
        service: "service_name"
        type: "test_type"
        language: "language"
      help: "Time taken for test suites to complete"
      
    # Security metrics
    - source: "ccl_security_issues"
      target: "security_vulnerabilities_total"
      labels:
        service: "service_name"
        severity: "severity"
        type: "issue_type"
        scanner: "scanner"
      help: "Security issues found during scans"
      
    # Quality metrics
    - source: "ccl_code_quality_score"
      target: "code_quality_score"
      type: "gauge"
      labels:
        service: "service_name"
        analyzer: "analyzer"
      help: "Code quality score from static analysis"
      
    # Performance metrics
    - source: "ccl_lead_time"
      target: "lead_time_seconds"
      type: "histogram"
      labels:
        service: "service_name"
        change_type: "change_type"
      help: "Lead time from commit to production"
      
    - source: "ccl_mttr"
      target: "mean_time_to_recovery_seconds"
      type: "histogram"
      labels:
        service: "service_name"
        incident_type: "incident_type"
      help: "Mean time to recovery from incidents"

# Integration with existing service metrics
service_integration:
  # Correlate CI/CD metrics with service health metrics
  correlation_rules:
    - name: "deployment_impact_on_error_rate"
      description: "Correlate deployments with service error rate changes"
      metrics:
        deployment: "deployment_events_total"
        error_rate: "http_requests_total{status=~\"5..\"}"
      time_window: "30m"
      threshold: 0.05  # 5% error rate increase
      
    - name: "deployment_impact_on_latency"
      description: "Correlate deployments with service latency changes"
      metrics:
        deployment: "deployment_events_total"
        latency: "http_request_duration_seconds"
      time_window: "30m"
      threshold: 1.5  # 50% latency increase
      
    - name: "build_failure_correlation"
      description: "Correlate build failures with service issues"
      metrics:
        build_failure: "build_success_ratio"
        service_health: "up{job=\"ccl-services\"}"
      time_window: "1h"

# Dashboard integration
dashboard_integration:
  # Add CI/CD panels to existing service dashboards
  service_dashboard_panels:
    - dashboard_uid: "ccl-service-overview"
      panels:
        - title: "Recent Deployments"
          type: "table"
          position: {"x": 0, "y": 20, "w": 24, "h": 6}
          query: "deployment_events_total{service=\"$service\"}"
          
        - title: "Deployment Success Rate"
          type: "stat"
          position: {"x": 18, "y": 0, "w": 6, "h": 4}
          query: "rate(deployment_status_total{service=\"$service\", status=\"SUCCESS\"}[24h]) / rate(deployment_events_total{service=\"$service\"}[24h])"
          
    - dashboard_uid: "ccl-repository-analysis"
      panels:
        - title: "Analysis Engine Deployments"
          type: "timeseries"
          position: {"x": 0, "y": 16, "w": 12, "h": 6}
          query: "rate(deployment_events_total{service=\"analysis-engine\"}[5m])"
          
        - title: "Build Performance"
          type: "timeseries"
          position: {"x": 12, "y": 16, "w": 12, "h": 6}
          query: "histogram_quantile(0.95, rate(build_duration_seconds_bucket{service=\"analysis-engine\"}[5m]))"

# SLO integration
slo_integration:
  # Extend existing SLOs with CI/CD metrics
  extended_slos:
    - name: "deployment-reliability"
      description: "Deployment success rate SLO"
      sli:
        good_events: 'deployment_status_total{status="SUCCESS"}'
        total_events: 'deployment_events_total'
      objectives:
        - target: 0.99   # 99%
          window: 30d
          name: "Monthly deployment reliability"
        - target: 0.95   # 95%
          window: 7d
          name: "Weekly deployment reliability"
      error_budget_policies:
        - name: "Freeze risky deployments"
          threshold: 0.5
          duration: 12h
        - name: "Emergency response"
          threshold: 0.2
          duration: "until_resolved"
          
    - name: "pipeline-performance"
      description: "Pipeline execution time SLO"
      sli:
        threshold: 1200  # 20 minutes
        metric: 'pipeline_duration_seconds'
        aggregation: 'histogram_quantile(0.95, rate(pipeline_duration_seconds_bucket[5m]))'
      objectives:
        - target: 0.95   # 95% under 20 minutes
          window: 7d
          name: "Weekly pipeline performance"

# Alert integration
alert_integration:
  # Enhance existing alerts with CI/CD context
  alert_enrichment:
    - alert_name: "ServiceDown"
      enrichment:
        - label: "recent_deployment"
          query: "deployment_events_total{service=\"$service\"}"
          time_window: "1h"
        - annotation: "deployment_context"
          template: "Last deployment: {{ with query \"deployment_events_total{service=\\\"{{ $labels.service_name }}\\\"}[1h]\" }}{{ . | first | value }}{{ end }}"
          
    - alert_name: "HighErrorRate"
      enrichment:
        - label: "deployment_correlation"
          query: "increase(deployment_events_total{service=\"$service\"}[30m])"
        - annotation: "deployment_impact"
          template: "{{ if gt (query \"increase(deployment_events_total{service=\\\"{{ $labels.service_name }}\\\"}[30m])\") 0 }}Recent deployment detected{{ else }}No recent deployments{{ end }}"

# Notification integration
notification_integration:
  # Route CI/CD alerts through existing notification channels
  routing_rules:
    - match:
        component: "cicd"
        severity: "critical"
      route_to: "existing_critical_channel"
      
    - match:
        alert_type: "security"
      route_to: "existing_security_channel"
      
    - match:
        alert_type: "deployment"
        environment: "production"
      route_to: "existing_production_oncall"

# Grafana integration
grafana_integration:
  # Datasource configuration
  datasources:
    - name: "CCL-CI-CD-Metrics"
      type: "prometheus"
      url: "http://prometheus:9090"
      access: "proxy"
      isDefault: false
      
  # Folder organization
  folders:
    - name: "CI/CD Monitoring"
      dashboards:
        - "cicd-pipeline-dashboard.json"
        - "deployment-overview.json"
        - "security-dashboard.json"
        
  # Variable templates
  template_variables:
    - name: "cicd_service"
      type: "query"
      query: "label_values(deployment_events_total, service)"
      multi: true
      includeAll: true
      
    - name: "cicd_environment"
      type: "query"
      query: "label_values(deployment_events_total{service=~\"$cicd_service\"}, environment)"
      multi: true
      includeAll: true

# Data retention policies
retention_policies:
  # CI/CD metrics retention
  metrics:
    - metric_pattern: "deployment_.*"
      retention: "90d"
      downsampling:
        - resolution: "5m"
          retention: "7d"
        - resolution: "1h"
          retention: "30d"
        - resolution: "1d"
          retention: "90d"
          
    - metric_pattern: "build_.*"
      retention: "30d"
      downsampling:
        - resolution: "1m"
          retention: "7d"
        - resolution: "5m"
          retention: "30d"
          
    - metric_pattern: "security_.*"
      retention: "365d"  # Keep security metrics longer for compliance
      
# Export configuration
export_config:
  # Export CI/CD metrics to external systems
  exporters:
    - name: "bigquery_exporter"
      type: "bigquery"
      dataset: "ccl_cicd_metrics"
      table: "pipeline_metrics"
      batch_size: 1000
      flush_interval: "5m"
      
    - name: "datadog_exporter"
      type: "datadog"
      api_key: "${DATADOG_API_KEY}"
      tags:
        - "platform:ccl"
        - "component:cicd"
