# Performance Optimization Configuration for CI/CD Monitoring
# This file contains configurations to optimize monitoring performance and reduce resource usage

# Prometheus Configuration Optimizations
prometheus_optimizations:
  # Global settings
  global:
    scrape_interval: 30s
    evaluation_interval: 30s
    external_labels:
      cluster: 'ccl-platform'
      environment: '${ENVIRONMENT}'
  
  # Storage optimization
  storage:
    retention_time: "30d"
    retention_size: "100GB"
    
    # Compaction settings
    compaction:
      block_ranges: ["2h", "12h", "3d"]
      max_block_duration: "3d"
      
    # WAL settings
    wal:
      compression: true
      segment_duration: "2h"
  
  # Query optimization
  query:
    timeout: "2m"
    max_concurrency: 20
    max_samples: 50000000
    
    # Query log settings
    log_queries_longer_than: "10s"
    
  # Recording rules optimization
  recording_rules:
    evaluation_interval: "30s"
    
    # High-frequency rules (every 30s)
    high_frequency:
      - "ccl:deployment_success_rate"
      - "ccl:build_success_rate"
      - "ccl:error_rate_5m"
      
    # Medium-frequency rules (every 5m)
    medium_frequency:
      - "ccl:lead_time_p95"
      - "ccl:pipeline_duration_p95"
      - "ccl:security_issues_summary"
      
    # Low-frequency rules (every 1h)
    low_frequency:
      - "ccl:deployment_frequency_daily"
      - "ccl:change_failure_rate_weekly"
      - "ccl:mttr_monthly"

# Grafana Dashboard Optimizations
grafana_optimizations:
  # Query optimization
  queries:
    # Use recording rules for expensive queries
    use_recording_rules: true
    
    # Query timeout settings
    timeout: "30s"
    max_data_points: 1000
    
    # Caching settings
    cache_timeout: "5m"
    
  # Panel optimization
  panels:
    # Limit time series in panels
    max_series_per_panel: 20
    
    # Use appropriate visualization types
    visualization_guidelines:
      - "Use stat panels for single values"
      - "Use timeseries for trends"
      - "Use tables for detailed data"
      - "Avoid pie charts for many series"
      
  # Dashboard performance
  performance:
    # Auto-refresh settings
    auto_refresh:
      default: "30s"
      minimum: "10s"
      
    # Time range optimization
    default_time_range: "6h"
    max_time_range: "30d"

# Alert Manager Optimizations
alertmanager_optimizations:
  # Grouping optimization
  grouping:
    group_wait: "10s"
    group_interval: "10s"
    repeat_interval: "1h"
    
    # Group by labels to reduce noise
    group_by:
      - "alertname"
      - "service_name"
      - "environment"
      - "severity"
  
  # Notification optimization
  notifications:
    # Rate limiting
    rate_limit:
      slack: "1/10s"
      email: "1/30s"
      pagerduty: "1/5s"
      
    # Batch notifications
    batch_size: 10
    batch_timeout: "30s"
  
  # Inhibition rules to reduce alert fatigue
  inhibition_rules:
    - source_matchers:
        - alertname="ServiceDown"
      target_matchers:
        - alertname="HighErrorRate"
      equal: ["service_name", "environment"]
      
    - source_matchers:
        - alertname="DeploymentFailure"
      target_matchers:
        - alert_type="build"
      equal: ["service_name"]

# Metric Cardinality Management
cardinality_management:
  # Label guidelines
  label_guidelines:
    # High cardinality labels to avoid
    avoid_labels:
      - "user_id"
      - "request_id"
      - "timestamp"
      - "commit_sha"  # Use version instead
      
    # Recommended labels
    recommended_labels:
      - "service_name"
      - "environment"
      - "version"
      - "status"
      - "severity"
      
  # Metric retention policies
  retention_policies:
    # High-frequency metrics (short retention)
    high_frequency:
      metrics:
        - "ccl_deployment_events_total"
        - "ccl_build_status_total"
      retention: "7d"
      
    # Medium-frequency metrics (medium retention)
    medium_frequency:
      metrics:
        - "ccl_pipeline_duration_seconds"
        - "ccl_test_coverage"
      retention: "30d"
      
    # Low-frequency metrics (long retention)
    low_frequency:
      metrics:
        - "ccl_lead_time_seconds"
        - "ccl_mttr_seconds"
      retention: "90d"
      
    # Compliance metrics (extended retention)
    compliance:
      metrics:
        - "ccl_security_issues"
        - "ccl_audit_events"
      retention: "365d"

# Resource Allocation
resource_allocation:
  # Prometheus resource requirements
  prometheus:
    cpu:
      requests: "2"
      limits: "4"
    memory:
      requests: "8Gi"
      limits: "16Gi"
    storage:
      size: "100Gi"
      class: "ssd"
      
  # Grafana resource requirements
  grafana:
    cpu:
      requests: "500m"
      limits: "1"
    memory:
      requests: "1Gi"
      limits: "2Gi"
      
  # AlertManager resource requirements
  alertmanager:
    cpu:
      requests: "100m"
      limits: "500m"
    memory:
      requests: "256Mi"
      limits: "512Mi"
      
  # Exporters resource requirements
  exporters:
    github_actions:
      cpu:
        requests: "50m"
        limits: "100m"
      memory:
        requests: "64Mi"
        limits: "128Mi"
        
    deployment_webhook:
      cpu:
        requests: "100m"
        limits: "200m"
      memory:
        requests: "128Mi"
        limits: "256Mi"

# Monitoring Health Checks
health_checks:
  # Prometheus health
  prometheus:
    - name: "prometheus_up"
      query: "up{job='prometheus'}"
      threshold: 1
      
    - name: "prometheus_config_reload"
      query: "prometheus_config_last_reload_successful"
      threshold: 1
      
    - name: "prometheus_storage_usage"
      query: "prometheus_tsdb_symbol_table_size_bytes / prometheus_tsdb_head_max_time * 100"
      threshold: 80  # Alert if > 80%
      
  # Grafana health
  grafana:
    - name: "grafana_up"
      query: "up{job='grafana'}"
      threshold: 1
      
    - name: "grafana_dashboard_load_time"
      query: "grafana_dashboard_load_duration_seconds"
      threshold: 5  # Alert if > 5 seconds
      
  # AlertManager health
  alertmanager:
    - name: "alertmanager_up"
      query: "up{job='alertmanager'}"
      threshold: 1
      
    - name: "alertmanager_notification_latency"
      query: "alertmanager_notification_latency_seconds"
      threshold: 30  # Alert if > 30 seconds

# Performance Monitoring
performance_monitoring:
  # Query performance
  query_performance:
    - name: "slow_queries"
      description: "Queries taking longer than 10 seconds"
      query: "prometheus_engine_query_duration_seconds > 10"
      action: "Optimize query or add recording rule"
      
    - name: "high_cardinality_metrics"
      description: "Metrics with high cardinality"
      query: "prometheus_tsdb_symbol_table_size_bytes > 100000000"  # 100MB
      action: "Review metric labels and reduce cardinality"
      
  # Resource usage monitoring
  resource_usage:
    - name: "memory_usage"
      description: "Memory usage of monitoring components"
      query: "container_memory_usage_bytes{pod=~'prometheus|grafana|alertmanager'}"
      threshold: "80%"
      
    - name: "cpu_usage"
      description: "CPU usage of monitoring components"
      query: "rate(container_cpu_usage_seconds_total{pod=~'prometheus|grafana|alertmanager'}[5m])"
      threshold: "80%"

# Optimization Recommendations
optimization_recommendations:
  # Dashboard optimization
  dashboards:
    - "Use template variables to reduce query load"
    - "Implement proper time range controls"
    - "Use recording rules for complex calculations"
    - "Limit the number of panels per dashboard"
    - "Use appropriate refresh intervals"
    
  # Query optimization
  queries:
    - "Use recording rules for frequently used queries"
    - "Avoid high cardinality label combinations"
    - "Use rate() instead of increase() for counters"
    - "Implement proper time range filtering"
    - "Use aggregation to reduce data points"
    
  # Alert optimization
  alerts:
    - "Group related alerts to reduce noise"
    - "Use inhibition rules to prevent alert storms"
    - "Implement proper escalation policies"
    - "Use appropriate evaluation intervals"
    - "Test alert routing regularly"
