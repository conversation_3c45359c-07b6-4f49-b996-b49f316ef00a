groups:
  # CI/CD Pipeline Alerts
  - name: cicd_pipeline
    interval: 30s
    rules:
      - alert: DeploymentFailure
        expr: increase(ccl_deployment_status{status="FAILED"}[5m]) > 0
        for: 0m
        labels:
          severity: critical
          team: platform
          component: cicd
          alert_type: deployment
        annotations:
          summary: "Deployment failed for {{ $labels.service_name }}"
          description: "Deployment of {{ $labels.service_name }} version {{ $labels.version }} to {{ $labels.environment }} has failed"
          runbook: "https://docs.ccl.io/runbooks/deployment-failure"
          dashboard: "https://grafana.ccl.io/d/ccl-cicd-pipeline"
          action: "Check deployment logs and consider rollback if in production"

      - alert: HighBuildFailureRate
        expr: |
          (
            sum(rate(ccl_build_success_rate{status="FAILED"}[1h])) by (service_name)
            /
            sum(rate(ccl_build_success_rate[1h])) by (service_name)
          ) > 0.20
        for: 10m
        labels:
          severity: warning
          team: platform
          component: cicd
          alert_type: build
        annotations:
          summary: "High build failure rate for {{ $labels.service_name }}"
          description: "Build failure rate is {{ $value | humanizePercentage }} for {{ $labels.service_name }} over the last hour"
          runbook: "https://docs.ccl.io/runbooks/high-build-failure-rate"
          dashboard: "https://grafana.ccl.io/d/ccl-cicd-pipeline"

      - alert: LongLeadTime
        expr: |
          histogram_quantile(0.95, 
            sum(rate(ccl_lead_time_bucket[6h])) by (le, service_name)
          ) > 7200  # 2 hours
        for: 30m
        labels:
          severity: warning
          team: platform
          component: cicd
          alert_type: performance
        annotations:
          summary: "Long lead time for {{ $labels.service_name }}"
          description: "95th percentile lead time is {{ $value | humanizeDuration }} for {{ $labels.service_name }}"
          runbook: "https://docs.ccl.io/runbooks/long-lead-time"
          dashboard: "https://grafana.ccl.io/d/ccl-cicd-pipeline"

      - alert: CriticalSecurityIssues
        expr: ccl_security_issues{severity="CRITICAL"} > 0
        for: 0m
        labels:
          severity: critical
          team: security
          component: cicd
          alert_type: security
        annotations:
          summary: "Critical security issues found in {{ $labels.service_name }}"
          description: "{{ $value }} critical security issues detected in {{ $labels.service_name }}"
          runbook: "https://docs.ccl.io/runbooks/critical-security-issues"
          dashboard: "https://grafana.ccl.io/d/ccl-cicd-pipeline"
          action: "Block deployment and fix security issues immediately"

      - alert: TestCoverageDrop
        expr: ccl_test_coverage < 85
        for: 15m
        labels:
          severity: warning
          team: development
          component: cicd
          alert_type: quality
        annotations:
          summary: "Test coverage below threshold for {{ $labels.service_name }}"
          description: "Test coverage is {{ $value }}% for {{ $labels.service_name }}, below the 85% threshold"
          runbook: "https://docs.ccl.io/runbooks/low-test-coverage"
          dashboard: "https://grafana.ccl.io/d/ccl-cicd-pipeline"

      - alert: PipelinePerformanceDegradation
        expr: |
          histogram_quantile(0.95, 
            sum(rate(ccl_pipeline_duration_bucket[1h])) by (le, service_name)
          ) > 1200  # 20 minutes
        for: 30m
        labels:
          severity: warning
          team: platform
          component: cicd
          alert_type: performance
        annotations:
          summary: "Pipeline performance degradation for {{ $labels.service_name }}"
          description: "95th percentile pipeline duration is {{ $value | humanizeDuration }} for {{ $labels.service_name }}"
          runbook: "https://docs.ccl.io/runbooks/slow-pipeline"
          dashboard: "https://grafana.ccl.io/d/ccl-cicd-pipeline"

      - alert: RollbackExecuted
        expr: increase(ccl_rollback_events[5m]) > 0
        for: 0m
        labels:
          severity: critical
          team: platform
          component: cicd
          alert_type: rollback
        annotations:
          summary: "Rollback executed for {{ $labels.service_name }}"
          description: "Automatic rollback triggered for {{ $labels.service_name }} in {{ $labels.environment }}. Reason: {{ $labels.rollback_reason }}"
          runbook: "https://docs.ccl.io/runbooks/rollback-executed"
          dashboard: "https://grafana.ccl.io/d/ccl-cicd-pipeline"
          action: "Investigate root cause and plan fix for rolled-back deployment"

  # Quality Gates Alerts
  - name: quality_gates
    interval: 60s
    rules:
      - alert: QualityGateFailure
        expr: ccl_quality_gate_status{status="FAILED"} == 1
        for: 0m
        labels:
          severity: warning
          team: development
          component: cicd
          alert_type: quality
        annotations:
          summary: "Quality gate failed for {{ $labels.service_name }}"
          description: "Quality gate {{ $labels.gate_name }} failed for {{ $labels.service_name }}"
          runbook: "https://docs.ccl.io/runbooks/quality-gate-failure"
          dashboard: "https://grafana.ccl.io/d/ccl-cicd-pipeline"

      - alert: SecurityScanTimeout
        expr: ccl_security_scan_duration > 900  # 15 minutes
        for: 5m
        labels:
          severity: warning
          team: security
          component: cicd
          alert_type: performance
        annotations:
          summary: "Security scan timeout for {{ $labels.service_name }}"
          description: "Security scan for {{ $labels.service_name }} has been running for {{ $value | humanizeDuration }}"
          runbook: "https://docs.ccl.io/runbooks/security-scan-timeout"
          dashboard: "https://grafana.ccl.io/d/ccl-cicd-pipeline"

  # Resource Utilization Alerts
  - name: cicd_resources
    interval: 60s
    rules:
      - alert: HighRunnerUtilization
        expr: ccl_runner_utilization > 90
        for: 10m
        labels:
          severity: warning
          team: platform
          component: cicd
          alert_type: resource
        annotations:
          summary: "High CI/CD runner utilization"
          description: "Runner utilization is {{ $value }}% for {{ $labels.runner_type }}"
          runbook: "https://docs.ccl.io/runbooks/high-runner-utilization"
          dashboard: "https://grafana.ccl.io/d/ccl-cicd-pipeline"
          action: "Consider scaling up runners or optimizing pipeline performance"

      - alert: ArtifactStorageHigh
        expr: ccl_artifact_storage_usage > 80
        for: 30m
        labels:
          severity: warning
          team: platform
          component: cicd
          alert_type: resource
        annotations:
          summary: "High artifact storage usage"
          description: "Artifact storage usage is {{ $value }}%"
          runbook: "https://docs.ccl.io/runbooks/high-artifact-storage"
          dashboard: "https://grafana.ccl.io/d/ccl-cicd-pipeline"
          action: "Clean up old artifacts or increase storage capacity"

  # SLO-based Alerts for CI/CD
  - name: cicd_slo
    interval: 300s  # 5 minutes
    rules:
      - alert: DeploymentSLOBreach
        expr: |
          (
            sum(rate(ccl_deployment_status{status="SUCCESS"}[30d]))
            /
            sum(rate(ccl_deployments_total[30d]))
          ) < 0.99  # 99% success rate SLO
        for: 1h
        labels:
          severity: critical
          team: platform
          component: cicd
          alert_type: slo
        annotations:
          summary: "Deployment success rate SLO breach"
          description: "Deployment success rate is {{ $value | humanizePercentage }}, below 99% SLO"
          runbook: "https://docs.ccl.io/runbooks/deployment-slo-breach"
          dashboard: "https://grafana.ccl.io/d/ccl-cicd-pipeline"
          action: "Review recent deployment failures and improve pipeline reliability"

      - alert: LeadTimeSLOBreach
        expr: |
          histogram_quantile(0.95, 
            sum(rate(ccl_lead_time_bucket[7d])) by (le)
          ) > 14400  # 4 hours SLO
        for: 2h
        labels:
          severity: warning
          team: platform
          component: cicd
          alert_type: slo
        annotations:
          summary: "Lead time SLO breach"
          description: "95th percentile lead time is {{ $value | humanizeDuration }}, above 4 hour SLO"
          runbook: "https://docs.ccl.io/runbooks/lead-time-slo-breach"
          dashboard: "https://grafana.ccl.io/d/ccl-cicd-pipeline"
          action: "Optimize pipeline performance and reduce bottlenecks"

# Notification routing for CI/CD alerts
route:
  group_by: ['alertname', 'service_name', 'environment']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  routes:
    - match:
        severity: critical
        component: cicd
      receiver: 'cicd-critical'
      group_wait: 0s
      repeat_interval: 5m
    
    - match:
        alert_type: security
      receiver: 'security-team'
      group_wait: 0s
      repeat_interval: 15m
    
    - match:
        alert_type: deployment
        environment: production
      receiver: 'production-oncall'
      group_wait: 0s
      repeat_interval: 5m

receivers:
  - name: 'default'
    slack_configs:
      - api_url: '{{ .SlackWebhookURL }}'
        channel: '#ccl-alerts'
        title: 'CCL Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

  - name: 'cicd-critical'
    slack_configs:
      - api_url: '{{ .SlackWebhookURL }}'
        channel: '#ccl-critical-alerts'
        title: 'CRITICAL: CI/CD Pipeline Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}\n{{ .Annotations.description }}{{ end }}'
    pagerduty_configs:
      - service_key: '{{ .PagerDutyServiceKey }}'
        description: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

  - name: 'security-team'
    slack_configs:
      - api_url: '{{ .SlackWebhookURL }}'
        channel: '#ccl-security-alerts'
        title: 'Security Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}\n{{ .Annotations.description }}{{ end }}'
    email_configs:
      - to: '<EMAIL>'
        subject: 'CCL Security Alert: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        body: '{{ range .Alerts }}{{ .Annotations.description }}{{ end }}'

  - name: 'production-oncall'
    pagerduty_configs:
      - service_key: '{{ .PagerDutyProductionKey }}'
        description: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        details:
          service: '{{ range .Alerts }}{{ .Labels.service_name }}{{ end }}'
          environment: '{{ range .Alerts }}{{ .Labels.environment }}{{ end }}'
          runbook: '{{ range .Alerts }}{{ .Annotations.runbook }}{{ end }}'
