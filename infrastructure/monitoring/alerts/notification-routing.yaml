# Enhanced Notification Routing for CI/CD Integration
# This configuration extends existing notification routing with CI/CD context

# Global routing configuration
route:
  group_by: ['alertname', 'service_name', 'environment']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  routes:
    # Critical CI/CD alerts get immediate attention
    - match:
        severity: critical
        component: cicd
      receiver: 'cicd-critical'
      group_wait: 0s
      repeat_interval: 5m
      routes:
        # Production deployment failures get paged immediately
        - match:
            alert_type: deployment
            environment: production
          receiver: 'production-oncall'
          group_wait: 0s
          repeat_interval: 2m
        
        # Security issues get security team attention
        - match:
            alert_type: security
          receiver: 'security-team'
          group_wait: 0s
          repeat_interval: 15m
        
        # Rollbacks get immediate platform team attention
        - match:
            alert_type: rollback
          receiver: 'platform-oncall'
          group_wait: 0s
          repeat_interval: 5m
    
    # Enhanced service alerts with deployment context
    - match:
        alertname: ServiceDown
      receiver: 'service-down-enhanced'
      group_wait: 30s
      repeat_interval: 10m
      
    - match:
        alertname: HighErrorRate
      receiver: 'error-rate-enhanced'
      group_wait: 60s
      repeat_interval: 15m
    
    # Build and quality alerts for development team
    - match:
        alert_type: build
      receiver: 'development-team'
      group_wait: 2m
      repeat_interval: 30m
      
    - match:
        alert_type: quality
      receiver: 'development-team'
      group_wait: 5m
      repeat_interval: 1h
    
    # Performance alerts for platform team
    - match:
        alert_type: performance
        component: cicd
      receiver: 'platform-team'
      group_wait: 5m
      repeat_interval: 30m
    
    # SLO alerts with enhanced context
    - match:
        slo: availability
      receiver: 'slo-alerts-enhanced'
      group_wait: 2m
      repeat_interval: 20m

# Receiver configurations
receivers:
  - name: 'default'
    slack_configs:
      - api_url: '{{ .SlackWebhookURL }}'
        channel: '#ccl-alerts'
        title: 'CCL Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

  # Enhanced CI/CD critical alerts
  - name: 'cicd-critical'
    slack_configs:
      - api_url: '{{ .SlackWebhookURL }}'
        channel: '#ccl-critical-alerts'
        title: 'CRITICAL: CI/CD Pipeline Alert'
        text: |
          {{ range .Alerts }}
          🚨 *{{ .Annotations.summary }}*
          
          *Description:* {{ .Annotations.description }}
          
          *Service:* {{ .Labels.service_name }}
          *Environment:* {{ .Labels.environment }}
          *Component:* {{ .Labels.component }}
          
          {{ if .Annotations.action }}*Action Required:* {{ .Annotations.action }}{{ end }}
          
          📊 [Dashboard]({{ .Annotations.dashboard }}) | 📖 [Runbook]({{ .Annotations.runbook }})
          {{ end }}
        actions:
          - type: button
            text: 'View Dashboard'
            url: '{{ range .Alerts }}{{ .Annotations.dashboard }}{{ end }}'
          - type: button
            text: 'View Runbook'
            url: '{{ range .Alerts }}{{ .Annotations.runbook }}{{ end }}'
    pagerduty_configs:
      - service_key: '{{ .PagerDutyServiceKey }}'
        description: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        details:
          service: '{{ range .Alerts }}{{ .Labels.service_name }}{{ end }}'
          environment: '{{ range .Alerts }}{{ .Labels.environment }}{{ end }}'
          component: '{{ range .Alerts }}{{ .Labels.component }}{{ end }}'
          runbook: '{{ range .Alerts }}{{ .Annotations.runbook }}{{ end }}'
          dashboard: '{{ range .Alerts }}{{ .Annotations.dashboard }}{{ end }}'

  # Enhanced service down alerts with deployment context
  - name: 'service-down-enhanced'
    slack_configs:
      - api_url: '{{ .SlackWebhookURL }}'
        channel: '#ccl-incidents'
        title: '🔴 Service Down Alert'
        text: |
          {{ range .Alerts }}
          🔴 *{{ .Annotations.summary }}*
          
          *Description:* {{ .Annotations.description }}
          
          *Deployment Context:*
          {{ .Annotations.deployment_context }}
          
          📊 [Service Dashboard](https://grafana.ccl.io/d/ccl-service-overview?var-service={{ .Labels.service_name }})
          📖 [Runbook]({{ .Annotations.runbook_url }})
          {{ end }}
    pagerduty_configs:
      - service_key: '{{ .PagerDutyServiceKey }}'
        description: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

  # Enhanced error rate alerts with deployment correlation
  - name: 'error-rate-enhanced'
    slack_configs:
      - api_url: '{{ .SlackWebhookURL }}'
        channel: '#ccl-alerts'
        title: '⚠️ High Error Rate Alert'
        text: |
          {{ range .Alerts }}
          ⚠️ *{{ .Annotations.summary }}*
          
          *Description:* {{ .Annotations.description }}
          
          *Deployment Impact Analysis:*
          {{ .Annotations.deployment_impact }}
          
          📊 [Service Dashboard](https://grafana.ccl.io/d/ccl-service-overview?var-service={{ .Labels.service_name }})
          📊 [CI/CD Dashboard](https://grafana.ccl.io/d/ccl-cicd-pipeline?var-service={{ .Labels.service_name }})
          {{ end }}

  # Security team notifications
  - name: 'security-team'
    slack_configs:
      - api_url: '{{ .SlackWebhookURL }}'
        channel: '#ccl-security-alerts'
        title: '🔒 Security Alert'
        text: |
          {{ range .Alerts }}
          🔒 *{{ .Annotations.summary }}*
          
          *Description:* {{ .Annotations.description }}
          
          *Service:* {{ .Labels.service_name }}
          *Severity:* {{ .Labels.severity }}
          
          {{ if .Annotations.action }}*Immediate Action:* {{ .Annotations.action }}{{ end }}
          
          📊 [Security Dashboard]({{ .Annotations.dashboard }})
          📖 [Security Runbook]({{ .Annotations.runbook }})
          {{ end }}
    email_configs:
      - to: '<EMAIL>'
        subject: 'CCL Security Alert: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        body: |
          Security Alert Details:
          
          {{ range .Alerts }}
          Summary: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Service: {{ .Labels.service_name }}
          Severity: {{ .Labels.severity }}
          
          {{ if .Annotations.action }}Action Required: {{ .Annotations.action }}{{ end }}
          
          Dashboard: {{ .Annotations.dashboard }}
          Runbook: {{ .Annotations.runbook }}
          {{ end }}

  # Production on-call for critical deployment issues
  - name: 'production-oncall'
    pagerduty_configs:
      - service_key: '{{ .PagerDutyProductionKey }}'
        description: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        details:
          service: '{{ range .Alerts }}{{ .Labels.service_name }}{{ end }}'
          environment: '{{ range .Alerts }}{{ .Labels.environment }}{{ end }}'
          alert_type: '{{ range .Alerts }}{{ .Labels.alert_type }}{{ end }}'
          runbook: '{{ range .Alerts }}{{ .Annotations.runbook }}{{ end }}'
          dashboard: '{{ range .Alerts }}{{ .Annotations.dashboard }}{{ end }}'
        severity: critical
    slack_configs:
      - api_url: '{{ .SlackWebhookURL }}'
        channel: '#ccl-production-incidents'
        title: '🚨 PRODUCTION CRITICAL: Deployment Issue'
        text: |
          {{ range .Alerts }}
          🚨 *PRODUCTION CRITICAL*
          
          *{{ .Annotations.summary }}*
          
          *Description:* {{ .Annotations.description }}
          
          *Service:* {{ .Labels.service_name }}
          *Environment:* {{ .Labels.environment }}
          
          {{ if .Annotations.action }}*Immediate Action:* {{ .Annotations.action }}{{ end }}
          
          📞 On-call engineer has been paged
          📊 [Dashboard]({{ .Annotations.dashboard }})
          📖 [Runbook]({{ .Annotations.runbook }})
          {{ end }}

  # Platform team for infrastructure and pipeline issues
  - name: 'platform-team'
    slack_configs:
      - api_url: '{{ .SlackWebhookURL }}'
        channel: '#ccl-platform-alerts'
        title: '🔧 Platform Alert'
        text: |
          {{ range .Alerts }}
          🔧 *{{ .Annotations.summary }}*
          
          *Description:* {{ .Annotations.description }}
          
          *Component:* {{ .Labels.component }}
          *Alert Type:* {{ .Labels.alert_type }}
          
          📊 [Platform Dashboard](https://grafana.ccl.io/d/ccl-platform-team)
          📖 [Runbook]({{ .Annotations.runbook }})
          {{ end }}

  # Development team for build and quality issues
  - name: 'development-team'
    slack_configs:
      - api_url: '{{ .SlackWebhookURL }}'
        channel: '#ccl-dev-alerts'
        title: '👨‍💻 Development Alert'
        text: |
          {{ range .Alerts }}
          👨‍💻 *{{ .Annotations.summary }}*
          
          *Description:* {{ .Annotations.description }}
          
          *Service:* {{ .Labels.service_name }}
          *Alert Type:* {{ .Labels.alert_type }}
          
          📊 [Dev Dashboard](https://grafana.ccl.io/d/ccl-dev-team)
          📖 [Runbook]({{ .Annotations.runbook }})
          {{ end }}

  # Enhanced SLO alerts with CI/CD context
  - name: 'slo-alerts-enhanced'
    slack_configs:
      - api_url: '{{ .SlackWebhookURL }}'
        channel: '#ccl-slo-alerts'
        title: '📊 SLO Alert'
        text: |
          {{ range .Alerts }}
          📊 *{{ .Annotations.summary }}*
          
          *Description:* {{ .Annotations.description }}
          
          *Service:* {{ .Labels.service_name }}
          *SLO:* {{ .Labels.slo }}
          
          *Recent Activity Check:*
          Check for recent deployments that might be impacting SLO performance.
          
          📊 [SLO Dashboard]({{ .Annotations.dashboard }})
          📊 [CI/CD Impact](https://grafana.ccl.io/d/ccl-cicd-pipeline?var-service={{ .Labels.service_name }})
          📖 [Runbook]({{ .Annotations.runbook }})
          {{ end }}

# Inhibition rules to reduce alert noise
inhibit_rules:
  # Inhibit service alerts when deployment is in progress
  - source_match:
      alertname: DeploymentInProgress
    target_match:
      alertname: HighErrorRate
    equal: ['service_name', 'environment']
    
  # Inhibit build alerts when deployment failure is already firing
  - source_match:
      alertname: DeploymentFailure
    target_match:
      alert_type: build
    equal: ['service_name']
    
  # Inhibit SLO alerts when service is down
  - source_match:
      alertname: ServiceDown
    target_match:
      slo: availability
    equal: ['service_name', 'environment']
