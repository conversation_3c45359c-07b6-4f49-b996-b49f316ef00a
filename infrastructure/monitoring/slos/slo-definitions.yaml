# SLO Definitions for CCL Platform
# These SLOs define our reliability targets and are used to calculate error budgets

slos:
  # Platform-Wide SLOs
  - name: api-availability
    service: all
    description: "Overall API availability across all services"
    sli:
      type: availability
      good_events: 'http_requests_total{status!~"5.."}'
      total_events: 'http_requests_total'
    objectives:
      - target: 0.999  # 99.9%
        window: 30d
        name: "Monthly availability"
      - target: 0.995  # 99.5%
        window: 7d
        name: "Weekly availability"
      - target: 0.99   # 99%
        window: 1d
        name: "Daily availability"
    error_budget_policies:
      - name: "Freeze non-critical deployments"
        threshold: 0.5  # When 50% of error budget is consumed
        duration: 24h
      - name: "All hands on deck"
        threshold: 0.2  # When 80% of error budget is consumed
        duration: "until_resolved"
    
  - name: api-latency
    service: all
    description: "API response time for user-facing endpoints"
    sli:
      type: latency
      threshold: 100ms
      metric: 'http_request_duration_seconds'
      aggregation: 'histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le))'
    objectives:
      - target: 0.95   # 95% of requests under 100ms
        window: 30d
        name: "Monthly latency"
      - target: 0.90   # 90% of requests under 100ms
        window: 7d
        name: "Weekly latency"
    
  # Service-Specific SLOs
  - name: analysis-engine-performance
    service: analysis-engine
    description: "Repository analysis completion time"
    sli:
      type: latency
      threshold: 300s  # 5 minutes
      metric: 'analysis_completion_duration_seconds'
      aggregation: 'histogram_quantile(0.95, sum(rate(analysis_completion_duration_seconds_bucket[1h])) by (le))'
    objectives:
      - target: 0.95   # 95% of analyses complete within 5 minutes
        window: 30d
        name: "Analysis performance"
        
  - name: query-intelligence-accuracy
    service: query-intelligence
    description: "Query response accuracy based on confidence scores"
    sli:
      type: custom
      good_events: 'queries_total{confidence>="0.8"}'
      total_events: 'queries_total'
    objectives:
      - target: 0.85   # 85% of queries with high confidence
        window: 30d
        name: "Query accuracy"
      - target: 0.80   # 80% of queries with high confidence
        window: 7d
        name: "Weekly query accuracy"
    
  - name: query-intelligence-latency
    service: query-intelligence
    description: "Query response time"
    sli:
      type: latency
      threshold: 100ms
      metric: 'query_processing_duration'
      aggregation: 'histogram_quantile(0.95, sum(rate(query_processing_duration_bucket[5m])) by (le))'
    objectives:
      - target: 0.95   # 95% of queries respond within 100ms
        window: 30d
        name: "Query latency"
        
  - name: pattern-detection-accuracy
    service: pattern-mining
    description: "Pattern detection precision"
    sli:
      type: custom
      good_events: 'pattern_validation_results_total{result="correct"}'
      total_events: 'pattern_validation_results_total'
    objectives:
      - target: 0.90   # 90% pattern accuracy
        window: 30d
        name: "Pattern accuracy"
        
  - name: marketplace-transaction-success
    service: marketplace
    description: "Successful marketplace transactions"
    sli:
      type: availability
      good_events: 'purchase_attempts_total{success="true"}'
      total_events: 'purchase_attempts_total'
    objectives:
      - target: 0.999  # 99.9% transaction success
        window: 30d
        name: "Transaction success"
      - target: 0.995  # 99.5% transaction success
        window: 1d
        name: "Daily transaction success"
        
  - name: marketplace-api-availability
    service: marketplace
    description: "Marketplace API availability"
    sli:
      type: availability
      good_events: 'http_requests_total{service_name="marketplace", status!~"5.."}'
      total_events: 'http_requests_total{service_name="marketplace"}'
    objectives:
      - target: 0.9999  # 99.99% availability for marketplace
        window: 30d
        name: "Marketplace availability"
        
  # Infrastructure SLOs
  - name: database-availability
    service: infrastructure
    description: "Database connection availability"
    sli:
      type: availability
      good_events: 'database_connections_successful_total'
      total_events: 'database_connections_attempted_total'
    objectives:
      - target: 0.999  # 99.9% database availability
        window: 30d
        name: "Database availability"
        
  - name: cache-hit-rate
    service: infrastructure
    description: "Cache effectiveness"
    sli:
      type: custom
      good_events: 'cache_hits_total'
      total_events: 'sum(cache_hits_total + cache_misses_total)'
    objectives:
      - target: 0.80   # 80% cache hit rate
        window: 30d
        name: "Cache effectiveness"
        
  # AI/ML SLOs
  - name: vertex-ai-availability
    service: ai-infrastructure
    description: "Vertex AI API availability"
    sli:
      type: availability
      good_events: 'vertex_ai_requests_total{status="success"}'
      total_events: 'vertex_ai_requests_total'
    objectives:
      - target: 0.99   # 99% Vertex AI availability
        window: 30d
        name: "Vertex AI availability"
        
  - name: ml-model-performance
    service: ai-infrastructure
    description: "ML model inference latency"
    sli:
      type: latency
      threshold: 200ms
      metric: 'ml_inference_duration'
      aggregation: 'histogram_quantile(0.95, sum(rate(ml_inference_duration_bucket[5m])) by (le))'
    objectives:
      - target: 0.90   # 90% of inferences under 200ms
        window: 30d
        name: "Model performance"

# Error Budget Policies
error_budget_policies:
  - name: standard-policy
    description: "Standard error budget policy for all services"
    thresholds:
      - remaining_budget: 0.75  # 75% remaining
        actions:
          - type: notification
            target: sre-team
            message: "25% of error budget consumed"
      - remaining_budget: 0.50  # 50% remaining
        actions:
          - type: restriction
            target: deployments
            restriction: "Only critical fixes allowed"
          - type: notification
            target: engineering-leads
            message: "50% of error budget consumed - deployment restrictions in place"
      - remaining_budget: 0.25  # 25% remaining
        actions:
          - type: restriction
            target: deployments
            restriction: "No deployments except emergency fixes"
          - type: escalation
            target: engineering-director
            message: "75% of error budget consumed - urgent action required"
      - remaining_budget: 0.10  # 10% remaining
        actions:
          - type: incident
            severity: critical
            message: "90% of error budget consumed - all hands on deck"
          - type: restriction
            target: all
            restriction: "Complete deployment freeze"

# SLO Review Schedule
review_schedule:
  - frequency: weekly
    participants:
      - sre-team
      - service-owners
    agenda:
      - Current SLO status
      - Error budget consumption
      - Incidents impact
      - SLO adjustments needed
      
  - frequency: monthly
    participants:
      - sre-team
      - engineering-leads
      - product-managers
    agenda:
      - SLO achievement review
      - Customer impact analysis
      - SLO target adjustments
      - Error budget policy effectiveness
      
  - frequency: quarterly
    participants:
      - sre-team
      - engineering-director
      - cto
    agenda:
      - Overall reliability trends
      - SLO strategy review
      - Investment priorities
      - Customer satisfaction correlation

# CI/CD Pipeline SLOs
cicd_slos:
  - name: "deployment-reliability"
    description: "Deployment success rate across all services"
    service: "platform"
    sli:
      type: availability
      good_events: 'ccl_deployment_status_total{status="SUCCESS"}'
      total_events: 'ccl_deployment_status_total'
    objectives:
      - target: 0.99   # 99%
        window: 30d
        name: "Monthly deployment reliability"
      - target: 0.95   # 95%
        window: 7d
        name: "Weekly deployment reliability"
      - target: 0.90   # 90%
        window: 1d
        name: "Daily deployment reliability"
    error_budget_policies:
      - name: "Freeze risky deployments"
        threshold: 0.5
        duration: 12h
        actions:
          - "Block non-critical deployments"
          - "Require additional approvals for production"
          - "Increase testing requirements"
      - name: "Emergency response"
        threshold: 0.2
        duration: "until_resolved"
        actions:
          - "Stop all deployments"
          - "Activate incident response team"
          - "Mandatory post-mortem"

  - name: "pipeline-performance"
    description: "Pipeline execution time SLO"
    service: "platform"
    sli:
      type: latency
      threshold: 1200  # 20 minutes
      metric: 'ccl_pipeline_duration_seconds'
      aggregation: 'histogram_quantile(0.95, rate(ccl_pipeline_duration_seconds_bucket[5m]))'
    objectives:
      - target: 0.95   # 95% under 20 minutes
        window: 7d
        name: "Weekly pipeline performance"
      - target: 0.90   # 90% under 20 minutes
        window: 30d
        name: "Monthly pipeline performance"
    error_budget_policies:
      - name: "Optimize pipeline"
        threshold: 0.5
        duration: 24h
        actions:
          - "Review pipeline bottlenecks"
          - "Optimize build processes"
          - "Consider infrastructure scaling"

  - name: "security-compliance"
    description: "Security scan compliance rate"
    service: "security"
    sli:
      type: availability
      good_events: 'ccl_security_scan_status_total{status="PASSED"}'
      total_events: 'ccl_security_scan_status_total'
    objectives:
      - target: 1.0    # 100% compliance
        window: 7d
        name: "Weekly security compliance"
      - target: 0.99   # 99% compliance
        window: 30d
        name: "Monthly security compliance"
    error_budget_policies:
      - name: "Security review required"
        threshold: 0.9
        duration: "immediate"
        actions:
          - "Block deployments with security issues"
          - "Escalate to security team"
          - "Mandatory security review"

  - name: "lead-time-performance"
    description: "Lead time from commit to production"
    service: "development"
    sli:
      type: latency
      threshold: 14400  # 4 hours
      metric: 'ccl_lead_time_seconds'
      aggregation: 'histogram_quantile(0.95, rate(ccl_lead_time_seconds_bucket[24h]))'
    objectives:
      - target: 0.90   # 90% under 4 hours
        window: 7d
        name: "Weekly lead time performance"
      - target: 0.85   # 85% under 4 hours
        window: 30d
        name: "Monthly lead time performance"
    error_budget_policies:
      - name: "Process optimization"
        threshold: 0.5
        duration: 48h
        actions:
          - "Review development process"
          - "Identify bottlenecks"
          - "Streamline approval workflows"

  - name: "build-reliability"
    description: "Build success rate across all services"
    service: "development"
    sli:
      type: availability
      good_events: 'ccl_build_status_total{status="SUCCESS"}'
      total_events: 'ccl_build_status_total'
    objectives:
      - target: 0.95   # 95% success rate
        window: 7d
        name: "Weekly build reliability"
      - target: 0.90   # 90% success rate
        window: 30d
        name: "Monthly build reliability"
    error_budget_policies:
      - name: "Build stability review"
        threshold: 0.5
        duration: 24h
        actions:
          - "Review failing tests"
          - "Stabilize flaky tests"
          - "Improve build infrastructure"

# DORA Metrics SLOs
dora_metrics:
  deployment_frequency:
    target: 5  # 5 deployments per day per service
    measurement: "daily_average"

  lead_time:
    target: 4  # 4 hours
    measurement: "p95_hours"

  mttr:
    target: 1  # 1 hour
    measurement: "p50_hours"

  change_failure_rate:
    target: 0.05  # 5%
    measurement: "weekly_percentage"