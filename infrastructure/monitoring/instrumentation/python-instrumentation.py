"""
Instrumentation library for Python services in the CCL platform
Provides OpenTelemetry integration for metrics, tracing, and logging
"""

import json
import logging
import os
import time
from contextlib import contextmanager
from functools import wraps
from typing import Any, Callable, Dict, Optional, TypeVar, Union

from opentelemetry import metrics, trace
from opentelemetry._logs import set_logger_provider
from opentelemetry.exporter.otlp.proto.grpc._log_exporter import OTLPLogExporter
from opentelemetry.exporter.otlp.proto.grpc.metric_exporter import OTLPMetricExporter
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.httpx import HTTPXClientInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.propagate import inject, extract
from opentelemetry.sdk._logs import <PERSON><PERSON><PERSON><PERSON><PERSON>, LoggingHandler
from opentelemetry.sdk._logs.export import BatchLogRecordProcessor
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.sdk.trace.sampling import TraceIdRatioBased
from opentelemetry.semconv.trace import SpanAttributes
from opentelemetry.trace import Status, StatusCode
from pythonjsonlogger import jsonlogger

# Type definitions
T = TypeVar('T')
F = TypeVar('F', bound=Callable[..., Any])

# Global instances
_tracer: Optional[trace.Tracer] = None
_meter: Optional[metrics.Meter] = None
_logger: Optional[logging.Logger] = None

# Metric instruments (initialized in init_telemetry)
query_latency: Optional[metrics.Histogram] = None
query_counter: Optional[metrics.Counter] = None
pattern_detection_counter: Optional[metrics.Counter] = None
confidence_score_histogram: Optional[metrics.Histogram] = None
active_queries: Optional[metrics.UpDownCounter] = None
ml_inference_duration: Optional[metrics.Histogram] = None
cache_hit_counter: Optional[metrics.Counter] = None
error_counter: Optional[metrics.Counter] = None


class TelemetryConfig:
    """Configuration for telemetry initialization"""
    
    def __init__(
        self,
        service_name: str,
        service_version: str = "0.0.0",
        environment: str = "development",
        otlp_endpoint: str = "http://localhost:4317",
        sampling_ratio: float = 0.1,
        log_level: str = "INFO",
        insecure: bool = True,
    ):
        self.service_name = service_name
        self.service_version = service_version
        self.environment = environment
        self.otlp_endpoint = otlp_endpoint
        self.sampling_ratio = sampling_ratio
        self.log_level = log_level
        self.insecure = insecure


def init_telemetry(config: TelemetryConfig) -> None:
    """Initialize OpenTelemetry for Python services"""
    global _tracer, _meter, _logger
    global query_latency, query_counter, pattern_detection_counter
    global confidence_score_histogram, active_queries, ml_inference_duration
    global cache_hit_counter, error_counter
    
    # Create resource
    resource = Resource.create({
        "service.name": config.service_name,
        "service.version": config.service_version,
        "deployment.environment": config.environment,
        "telemetry.sdk.language": "python",
        "telemetry.sdk.name": "opentelemetry",
        "host.name": os.environ.get("HOSTNAME", "unknown"),
    })
    
    # Initialize tracing
    tracer_provider = TracerProvider(
        resource=resource,
        sampler=TraceIdRatioBased(config.sampling_ratio),
    )
    
    otlp_exporter = OTLPSpanExporter(
        endpoint=config.otlp_endpoint,
        insecure=config.insecure,
    )
    
    span_processor = BatchSpanProcessor(otlp_exporter)
    tracer_provider.add_span_processor(span_processor)
    trace.set_tracer_provider(tracer_provider)
    _tracer = trace.get_tracer(__name__)
    
    # Initialize metrics
    metric_reader = PeriodicExportingMetricReader(
        exporter=OTLPMetricExporter(
            endpoint=config.otlp_endpoint,
            insecure=config.insecure,
        ),
        export_interval_millis=10000,  # 10 seconds
    )
    
    meter_provider = MeterProvider(
        resource=resource,
        metric_readers=[metric_reader],
    )
    metrics.set_meter_provider(meter_provider)
    _meter = metrics.get_meter(__name__)
    
    # Initialize metric instruments
    query_latency = _meter.create_histogram(
        name="query_processing_duration",
        description="Query processing latency in milliseconds",
        unit="ms",
    )
    
    query_counter = _meter.create_counter(
        name="queries_total",
        description="Total number of queries processed",
        unit="1",
    )
    
    pattern_detection_counter = _meter.create_counter(
        name="patterns_detected_total",
        description="Total number of patterns detected",
        unit="1",
    )
    
    confidence_score_histogram = _meter.create_histogram(
        name="query_confidence_score",
        description="Distribution of query confidence scores",
        unit="1",
    )
    
    active_queries = _meter.create_up_down_counter(
        name="active_queries",
        description="Number of currently active queries",
        unit="1",
    )
    
    ml_inference_duration = _meter.create_histogram(
        name="ml_inference_duration",
        description="ML model inference duration in milliseconds",
        unit="ms",
    )
    
    cache_hit_counter = _meter.create_counter(
        name="cache_hits_total",
        description="Total number of cache hits",
        unit="1",
    )
    
    error_counter = _meter.create_counter(
        name="errors_total",
        description="Total number of errors",
        unit="1",
    )
    
    # Initialize logging
    logger_provider = LoggerProvider(resource=resource)
    set_logger_provider(logger_provider)
    
    log_exporter = OTLPLogExporter(
        endpoint=config.otlp_endpoint,
        insecure=config.insecure,
    )
    logger_provider.add_log_record_processor(BatchLogRecordProcessor(log_exporter))
    
    # Configure structured logging
    log_handler = logging.StreamHandler()
    formatter = jsonlogger.JsonFormatter(
        fmt="%(asctime)s %(levelname)s %(name)s %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        json_ensure_ascii=False,
    )
    log_handler.setFormatter(formatter)
    
    # Add OpenTelemetry handler
    otel_handler = LoggingHandler(
        level=getattr(logging, config.log_level),
        logger_provider=logger_provider,
    )
    
    logging.root.handlers = []
    logging.root.addHandler(log_handler)
    logging.root.addHandler(otel_handler)
    logging.root.setLevel(getattr(logging, config.log_level))
    
    _logger = logging.getLogger(__name__)
    
    # Auto-instrument libraries
    FastAPIInstrumentor.instrument()
    RequestsInstrumentor.instrument()
    HTTPXClientInstrumentor.instrument()
    RedisInstrumentor.instrument()
    SQLAlchemyInstrumentor.instrument()
    
    _logger.info(
        "Telemetry initialized successfully",
        extra={
            "service": config.service_name,
            "version": config.service_version,
            "environment": config.environment,
        }
    )


def get_tracer() -> trace.Tracer:
    """Get the global tracer instance"""
    if _tracer is None:
        raise RuntimeError("Telemetry not initialized. Call init_telemetry first.")
    return _tracer


def get_meter() -> metrics.Meter:
    """Get the global meter instance"""
    if _meter is None:
        raise RuntimeError("Telemetry not initialized. Call init_telemetry first.")
    return _meter


def get_logger() -> logging.Logger:
    """Get the global logger instance"""
    if _logger is None:
        return logging.getLogger(__name__)
    return _logger


@contextmanager
def trace_span(
    name: str,
    kind: trace.SpanKind = trace.SpanKind.INTERNAL,
    attributes: Optional[Dict[str, Any]] = None,
):
    """Context manager for creating spans"""
    tracer = get_tracer()
    with tracer.start_as_current_span(
        name,
        kind=kind,
        attributes=attributes or {},
    ) as span:
        try:
            yield span
        except Exception as e:
            span.record_exception(e)
            span.set_status(Status(StatusCode.ERROR, str(e)))
            raise


def trace_async(
    name: Optional[str] = None,
    kind: trace.SpanKind = trace.SpanKind.INTERNAL,
    attributes: Optional[Dict[str, Any]] = None,
) -> Callable[[F], F]:
    """Decorator for tracing async functions"""
    def decorator(func: F) -> F:
        span_name = name or f"{func.__module__}.{func.__name__}"
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            with trace_span(span_name, kind, attributes):
                return await func(*args, **kwargs)
        
        return wrapper  # type: ignore
    
    return decorator


def trace_sync(
    name: Optional[str] = None,
    kind: trace.SpanKind = trace.SpanKind.INTERNAL,
    attributes: Optional[Dict[str, Any]] = None,
) -> Callable[[F], F]:
    """Decorator for tracing sync functions"""
    def decorator(func: F) -> F:
        span_name = name or f"{func.__module__}.{func.__name__}"
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            with trace_span(span_name, kind, attributes):
                return func(*args, **kwargs)
        
        return wrapper  # type: ignore
    
    return decorator


# Business metric tracking functions

@trace_async("process_query")
async def track_query_processing(
    query_type: str,
    confidence: float,
    duration_ms: float,
    cache_hit: bool = False,
):
    """Track query processing metrics"""
    if not all([query_latency, query_counter, confidence_score_histogram, cache_hit_counter]):
        return
    
    attributes = {
        "query_type": query_type,
        "cache_hit": str(cache_hit),
    }
    
    query_counter.add(1, attributes)
    query_latency.record(duration_ms, attributes)
    confidence_score_histogram.record(confidence, {"query_type": query_type})
    
    if cache_hit:
        cache_hit_counter.add(1, {"cache_type": "query"})
    
    get_logger().info(
        "Query processed",
        extra={
            "query_type": query_type,
            "confidence": confidence,
            "duration_ms": duration_ms,
            "cache_hit": cache_hit,
        }
    )


def track_pattern_detection(
    pattern_type: str,
    language: str,
    count: int = 1,
):
    """Track pattern detection metrics"""
    if pattern_detection_counter:
        pattern_detection_counter.add(
            count,
            {
                "pattern_type": pattern_type,
                "language": language,
            }
        )


@trace_async("ml_inference")
async def track_ml_inference(
    model_name: str,
    model_version: str,
    input_size: int,
    duration_ms: float,
):
    """Track ML model inference metrics"""
    if ml_inference_duration:
        ml_inference_duration.record(
            duration_ms,
            {
                "model_name": model_name,
                "model_version": model_version,
                "input_size_category": _categorize_size(input_size),
            }
        )


def track_error(
    error_type: str,
    context: str,
    exception: Optional[Exception] = None,
):
    """Track error metrics"""
    if error_counter:
        error_counter.add(
            1,
            {
                "error_type": error_type,
                "context": context,
            }
        )
    
    logger = get_logger()
    if exception:
        logger.error(
            "Error occurred",
            exc_info=exception,
            extra={
                "error_type": error_type,
                "context": context,
            }
        )
    else:
        logger.error(
            "Error occurred",
            extra={
                "error_type": error_type,
                "context": context,
            }
        )


def _categorize_size(size: int) -> str:
    """Categorize input size for metrics"""
    if size <= 100:
        return "small"
    elif size <= 1000:
        return "medium"
    elif size <= 10000:
        return "large"
    else:
        return "xlarge"


# HTTP middleware for FastAPI

async def http_middleware(request, call_next):
    """FastAPI middleware for HTTP request tracking"""
    start_time = time.time()
    
    # Extract trace context from headers
    ctx = extract(request.headers)
    
    span = get_tracer().start_span(
        "http_request",
        context=ctx,
        kind=trace.SpanKind.SERVER,
        attributes={
            SpanAttributes.HTTP_METHOD: request.method,
            SpanAttributes.HTTP_URL: str(request.url),
            SpanAttributes.HTTP_TARGET: request.url.path,
            SpanAttributes.HTTP_HOST: request.url.hostname,
            SpanAttributes.HTTP_SCHEME: request.url.scheme,
            SpanAttributes.USER_AGENT_ORIGINAL: request.headers.get("user-agent", ""),
        }
    )
    
    with trace.use_span(span, end_on_exit=True):
        try:
            response = await call_next(request)
            
            span.set_attributes({
                SpanAttributes.HTTP_STATUS_CODE: response.status_code,
            })
            
            if response.status_code >= 400:
                span.set_status(Status(StatusCode.ERROR))
            
            return response
            
        except Exception as e:
            span.record_exception(e)
            span.set_status(Status(StatusCode.ERROR, str(e)))
            raise
        
        finally:
            duration = (time.time() - start_time) * 1000
            
            # Record HTTP metrics
            if query_latency:
                query_latency.record(
                    duration,
                    {
                        "method": request.method,
                        "endpoint": request.url.path,
                        "status": str(getattr(response, 'status_code', 'error')),
                    }
                )


# Context propagation helpers

def inject_context(headers: Dict[str, str]) -> Dict[str, str]:
    """Inject trace context into headers for service-to-service calls"""
    inject(headers)
    return headers


def extract_context(headers: Dict[str, str]) -> trace.Context:
    """Extract trace context from incoming headers"""
    return extract(headers)


# Shutdown function

def shutdown_telemetry():
    """Shutdown telemetry providers gracefully"""
    tp = trace.get_tracer_provider()
    if hasattr(tp, 'shutdown'):
        tp.shutdown()
    
    mp = metrics.get_meter_provider()
    if hasattr(mp, 'shutdown'):
        mp.shutdown()
    
    get_logger().info("Telemetry shutdown complete")