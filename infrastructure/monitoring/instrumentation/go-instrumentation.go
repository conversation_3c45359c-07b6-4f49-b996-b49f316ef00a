// Package instrumentation provides OpenTelemetry integration for Go services in the CCL platform
// It includes metrics, tracing, and structured logging capabilities
package instrumentation

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"runtime"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/otlp/otlpmetric/otlpmetricgrpc"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/metric"
	"go.opentelemetry.io/otel/propagation"
	sdkmetric "go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.17.0"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// Global metric instruments
var (
	// HTTP metrics
	httpRequestDuration metric.Float64Histogram
	httpRequestCount    metric.Int64Counter
	httpRequestSize     metric.Float64Histogram
	httpResponseSize    metric.Float64Histogram

	// Business metrics
	patternPurchases      metric.Int64Counter
	patternPublications   metric.Int64Counter
	revenueTotal         metric.Float64Counter
	activeTransactions   metric.Int64UpDownCounter
	paymentErrors        metric.Int64Counter
	searchLatency        metric.Float64Histogram
	apiCallDuration      metric.Float64Histogram

	// Cache metrics
	cacheHits   metric.Int64Counter
	cacheMisses metric.Int64Counter

	// Error metrics
	errorCount metric.Int64Counter

	// Prometheus metrics (for compatibility)
	promHTTPDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "http_request_duration_seconds",
			Help:    "HTTP request latencies in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"method", "endpoint", "status"},
	)
	promHTTPRequests = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "http_requests_total",
			Help: "Total number of HTTP requests",
		},
		[]string{"method", "endpoint", "status"},
	)
)

// TelemetryConfig holds configuration for telemetry initialization
type TelemetryConfig struct {
	ServiceName     string
	ServiceVersion  string
	Environment     string
	OTLPEndpoint    string
	SamplingRatio   float64
	LogLevel        string
	Insecure        bool
}

// DefaultConfig returns a default telemetry configuration
func DefaultConfig() *TelemetryConfig {
	return &TelemetryConfig{
		ServiceName:    "unknown",
		ServiceVersion: "0.0.0",
		Environment:    "development",
		OTLPEndpoint:   "localhost:4317",
		SamplingRatio:  0.1,
		LogLevel:       "info",
		Insecure:       true,
	}
}

// InitTelemetry initializes OpenTelemetry for the service
func InitTelemetry(config *TelemetryConfig) (func(), error) {
	// Initialize structured logging
	initLogging(config)

	// Create resource
	res, err := resource.Merge(
		resource.Default(),
		resource.NewWithAttributes(
			semconv.SchemaURL,
			semconv.ServiceName(config.ServiceName),
			semconv.ServiceVersion(config.ServiceVersion),
			semconv.DeploymentEnvironment(config.Environment),
			attribute.String("telemetry.sdk.language", "go"),
			attribute.String("host.name", getHostname()),
		),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create resource: %w", err)
	}

	// Initialize tracer
	tracerProvider, err := initTracer(config, res)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize tracer: %w", err)
	}

	// Initialize metrics
	meterProvider, err := initMetrics(config, res)
	if err != nil {
		tracerProvider.Shutdown(context.Background())
		return nil, fmt.Errorf("failed to initialize metrics: %w", err)
	}

	// Initialize Prometheus metrics
	prometheus.MustRegister(promHTTPDuration, promHTTPRequests)

	// Shutdown function
	shutdown := func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err := tracerProvider.Shutdown(ctx); err != nil {
			log.Error().Err(err).Msg("Failed to shutdown tracer provider")
		}

		if err := meterProvider.Shutdown(ctx); err != nil {
			log.Error().Err(err).Msg("Failed to shutdown meter provider")
		}
	}

	log.Info().
		Str("service", config.ServiceName).
		Str("version", config.ServiceVersion).
		Str("environment", config.Environment).
		Msg("Telemetry initialized successfully")

	return shutdown, nil
}

func initLogging(config *TelemetryConfig) {
	// Configure zerolog
	zerolog.TimeFieldFormat = time.RFC3339Nano
	zerolog.LevelFieldName = "severity"
	zerolog.MessageFieldName = "message"

	// Set log level
	level, err := zerolog.ParseLevel(config.LogLevel)
	if err != nil {
		level = zerolog.InfoLevel
	}
	zerolog.SetGlobalLevel(level)

	// Configure output
	if config.Environment == "development" {
		log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr})
	} else {
		log.Logger = log.With().Caller().Logger()
	}

	// Add service context
	log.Logger = log.With().
		Str("service", config.ServiceName).
		Str("version", config.ServiceVersion).
		Str("environment", config.Environment).
		Logger()
}

func initTracer(config *TelemetryConfig, res *resource.Resource) (*sdktrace.TracerProvider, error) {
	// Create OTLP exporter
	opts := []otlptracegrpc.Option{
		otlptracegrpc.WithEndpoint(config.OTLPEndpoint),
	}
	if config.Insecure {
		opts = append(opts, otlptracegrpc.WithInsecure())
	}

	exporter, err := otlptrace.New(
		context.Background(),
		otlptracegrpc.NewClient(opts...),
	)
	if err != nil {
		return nil, err
	}

	// Create tracer provider
	tp := sdktrace.NewTracerProvider(
		sdktrace.WithBatcher(exporter),
		sdktrace.WithResource(res),
		sdktrace.WithSampler(sdktrace.TraceIDRatioBased(config.SamplingRatio)),
	)

	otel.SetTracerProvider(tp)
	otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(
		propagation.TraceContext{},
		propagation.Baggage{},
	))

	return tp, nil
}

func initMetrics(config *TelemetryConfig, res *resource.Resource) (*sdkmetric.MeterProvider, error) {
	// Create OTLP metric exporter
	opts := []otlpmetricgrpc.Option{
		otlpmetricgrpc.WithEndpoint(config.OTLPEndpoint),
	}
	if config.Insecure {
		opts = append(opts, otlpmetricgrpc.WithInsecure())
	}

	exporter, err := otlpmetricgrpc.New(
		context.Background(),
		opts...,
	)
	if err != nil {
		return nil, err
	}

	// Create meter provider
	mp := sdkmetric.NewMeterProvider(
		sdkmetric.WithResource(res),
		sdkmetric.WithReader(
			sdkmetric.NewPeriodicReader(exporter, sdkmetric.WithInterval(10*time.Second)),
		),
	)

	otel.SetMeterProvider(mp)

	// Initialize metric instruments
	meter := mp.Meter("ccl")

	httpRequestDuration, _ = meter.Float64Histogram(
		"http_request_duration",
		metric.WithDescription("HTTP request duration in milliseconds"),
		metric.WithUnit("ms"),
	)

	httpRequestCount, _ = meter.Int64Counter(
		"http_requests_total",
		metric.WithDescription("Total number of HTTP requests"),
	)

	httpRequestSize, _ = meter.Float64Histogram(
		"http_request_size_bytes",
		metric.WithDescription("HTTP request size in bytes"),
		metric.WithUnit("By"),
	)

	httpResponseSize, _ = meter.Float64Histogram(
		"http_response_size_bytes",
		metric.WithDescription("HTTP response size in bytes"),
		metric.WithUnit("By"),
	)

	// Business metrics
	patternPurchases, _ = meter.Int64Counter(
		"pattern_purchases_total",
		metric.WithDescription("Total number of pattern purchases"),
	)

	patternPublications, _ = meter.Int64Counter(
		"pattern_publications_total",
		metric.WithDescription("Total number of patterns published"),
	)

	revenueTotal, _ = meter.Float64Counter(
		"revenue_total",
		metric.WithDescription("Total revenue in dollars"),
		metric.WithUnit("$"),
	)

	activeTransactions, _ = meter.Int64UpDownCounter(
		"active_transactions",
		metric.WithDescription("Number of currently active transactions"),
	)

	paymentErrors, _ = meter.Int64Counter(
		"payment_errors_total",
		metric.WithDescription("Total number of payment errors"),
	)

	searchLatency, _ = meter.Float64Histogram(
		"pattern_search_duration",
		metric.WithDescription("Pattern search latency in milliseconds"),
		metric.WithUnit("ms"),
	)

	apiCallDuration, _ = meter.Float64Histogram(
		"external_api_duration",
		metric.WithDescription("External API call duration in milliseconds"),
		metric.WithUnit("ms"),
	)

	// Cache metrics
	cacheHits, _ = meter.Int64Counter(
		"cache_hits_total",
		metric.WithDescription("Total number of cache hits"),
	)

	cacheMisses, _ = meter.Int64Counter(
		"cache_misses_total",
		metric.WithDescription("Total number of cache misses"),
	)

	// Error metrics
	errorCount, _ = meter.Int64Counter(
		"errors_total",
		metric.WithDescription("Total number of errors"),
	)

	return mp, nil
}

// GinMiddleware returns Gin middleware for HTTP instrumentation
func GinMiddleware(serviceName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Use otelgin for automatic instrumentation
		otelgin.Middleware(serviceName)(c)

		// Custom metrics tracking
		start := time.Now()
		path := c.FullPath()
		if path == "" {
			path = c.Request.URL.Path
		}

		c.Next()

		// Record metrics
		duration := float64(time.Since(start).Milliseconds())
		status := fmt.Sprintf("%d", c.Writer.Status())
		method := c.Request.Method

		attrs := []attribute.KeyValue{
			attribute.String("method", method),
			attribute.String("endpoint", path),
			attribute.String("status", status),
		}

		httpRequestDuration.Record(c.Request.Context(), duration, metric.WithAttributes(attrs...))
		httpRequestCount.Add(c.Request.Context(), 1, metric.WithAttributes(attrs...))

		// Prometheus metrics
		promHTTPDuration.WithLabelValues(method, path, status).Observe(duration / 1000.0)
		promHTTPRequests.WithLabelValues(method, path, status).Inc()

		// Log request
		log.Info().
			Str("method", method).
			Str("path", path).
			Int("status", c.Writer.Status()).
			Dur("duration", time.Since(start)).
			Str("client_ip", c.ClientIP()).
			Msg("HTTP request processed")
	}
}

// HTTPMiddleware returns standard HTTP middleware for instrumentation
func HTTPMiddleware(handler http.Handler, operation string) http.Handler {
	return otelhttp.NewHandler(handler, operation,
		otelhttp.WithMessageEvents(otelhttp.ReadEvents, otelhttp.WriteEvents),
	)
}

// GRPCServerOptions returns gRPC server options for instrumentation
func GRPCServerOptions() []grpc.ServerOption {
	return []grpc.ServerOption{
		grpc.UnaryInterceptor(otelgrpc.UnaryServerInterceptor()),
		grpc.StreamInterceptor(otelgrpc.StreamServerInterceptor()),
	}
}

// GRPCClientOptions returns gRPC client options for instrumentation
func GRPCClientOptions() []grpc.DialOption {
	return []grpc.DialOption{
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithUnaryInterceptor(otelgrpc.UnaryClientInterceptor()),
		grpc.WithStreamInterceptor(otelgrpc.StreamClientInterceptor()),
	}
}

// PrometheusHandler returns an HTTP handler for Prometheus metrics
func PrometheusHandler() http.Handler {
	return promhttp.Handler()
}

// Business metric tracking functions

// TrackPatternPurchase records a pattern purchase
func TrackPatternPurchase(ctx context.Context, patternID, userID string, price float64) {
	attrs := []attribute.KeyValue{
		attribute.String("pattern_id", patternID),
		attribute.String("user_id", userID),
	}

	patternPurchases.Add(ctx, 1, metric.WithAttributes(attrs...))
	revenueTotal.Add(ctx, price, metric.WithAttributes(attrs...))

	log.Info().
		Str("pattern_id", patternID).
		Str("user_id", userID).
		Float64("price", price).
		Msg("Pattern purchased")
}

// TrackPatternPublication records a pattern publication
func TrackPatternPublication(ctx context.Context, patternID, userID, category string) {
	attrs := []attribute.KeyValue{
		attribute.String("pattern_id", patternID),
		attribute.String("user_id", userID),
		attribute.String("category", category),
	}

	patternPublications.Add(ctx, 1, metric.WithAttributes(attrs...))

	log.Info().
		Str("pattern_id", patternID).
		Str("user_id", userID).
		Str("category", category).
		Msg("Pattern published")
}

// TrackTransactionStart records the start of a transaction
func TrackTransactionStart(ctx context.Context, transactionType string) {
	activeTransactions.Add(ctx, 1, metric.WithAttributes(
		attribute.String("type", transactionType),
	))
}

// TrackTransactionEnd records the end of a transaction
func TrackTransactionEnd(ctx context.Context, transactionType string) {
	activeTransactions.Add(ctx, -1, metric.WithAttributes(
		attribute.String("type", transactionType),
	))
}

// TrackPaymentError records a payment error
func TrackPaymentError(ctx context.Context, errorType, provider string) {
	attrs := []attribute.KeyValue{
		attribute.String("error_type", errorType),
		attribute.String("provider", provider),
	}

	paymentErrors.Add(ctx, 1, metric.WithAttributes(attrs...))
	errorCount.Add(ctx, 1, metric.WithAttributes(
		attribute.String("type", "payment_error"),
	))

	log.Error().
		Str("error_type", errorType).
		Str("provider", provider).
		Msg("Payment error occurred")
}

// TrackSearchLatency records pattern search latency
func TrackSearchLatency(ctx context.Context, query string, resultCount int, duration time.Duration) {
	attrs := []attribute.KeyValue{
		attribute.String("query_type", categorizeQuery(query)),
		attribute.Int("result_count", resultCount),
	}

	searchLatency.Record(ctx, float64(duration.Milliseconds()), metric.WithAttributes(attrs...))

	log.Info().
		Str("query", query).
		Int("result_count", resultCount).
		Dur("duration", duration).
		Msg("Pattern search completed")
}

// TrackExternalAPICall records external API call metrics
func TrackExternalAPICall(ctx context.Context, api, operation string, duration time.Duration, err error) {
	status := "success"
	if err != nil {
		status = "error"
	}

	attrs := []attribute.KeyValue{
		attribute.String("api", api),
		attribute.String("operation", operation),
		attribute.String("status", status),
	}

	apiCallDuration.Record(ctx, float64(duration.Milliseconds()), metric.WithAttributes(attrs...))

	if err != nil {
		errorCount.Add(ctx, 1, metric.WithAttributes(
			attribute.String("type", "external_api_error"),
			attribute.String("api", api),
		))
	}
}

// TrackCacheHit records a cache hit
func TrackCacheHit(ctx context.Context, cacheType, key string) {
	cacheHits.Add(ctx, 1, metric.WithAttributes(
		attribute.String("cache_type", cacheType),
		attribute.String("key_pattern", categorizeKey(key)),
	))
}

// TrackCacheMiss records a cache miss
func TrackCacheMiss(ctx context.Context, cacheType, key string) {
	cacheMisses.Add(ctx, 1, metric.WithAttributes(
		attribute.String("cache_type", cacheType),
		attribute.String("key_pattern", categorizeKey(key)),
	))
}

// TrackError records a general error
func TrackError(ctx context.Context, errorType, context string) {
	errorCount.Add(ctx, 1, metric.WithAttributes(
		attribute.String("type", errorType),
		attribute.String("context", context),
	))

	log.Error().
		Str("error_type", errorType).
		Str("context", context).
		Msg("Error occurred")
}

// Helper functions

func getHostname() string {
	hostname, err := os.Hostname()
	if err != nil {
		return "unknown"
	}
	return hostname
}

func categorizeQuery(query string) string {
	// Simple categorization logic
	if len(query) < 10 {
		return "short"
	} else if len(query) < 50 {
		return "medium"
	}
	return "long"
}

func categorizeKey(key string) string {
	// Extract pattern from cache key
	if len(key) > 20 {
		return key[:20] + "..."
	}
	return key
}

// StartSpan starts a new span with the given name
func StartSpan(ctx context.Context, name string, opts ...trace.SpanStartOption) (context.Context, trace.Span) {
	return otel.Tracer("ccl").Start(ctx, name, opts...)
}

// RecordSpanError records an error on the current span
func RecordSpanError(ctx context.Context, err error) {
	span := trace.SpanFromContext(ctx)
	if span != nil {
		span.RecordError(err)
		span.SetStatus(trace.Status{Code: trace.StatusCodeError, Description: err.Error()})
	}
}