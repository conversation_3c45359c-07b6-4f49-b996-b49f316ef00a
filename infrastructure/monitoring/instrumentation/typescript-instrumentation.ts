/**
 * Instrumentation library for TypeScript/JavaScript services in the CCL platform
 * Provides OpenTelemetry integration for browser and Node.js applications
 */

import { Context, Span, SpanKind, SpanStatusCode, Tracer, trace } from '@opentelemetry/api';
import { 
  Counter, 
  Histogram, 
  Meter, 
  UpDownCounter, 
  metrics,
  ValueType 
} from '@opentelemetry/api';
import { registerInstrumentations } from '@opentelemetry/instrumentation';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { FetchInstrumentation } from '@opentelemetry/instrumentation-fetch';
import { XMLHttpRequestInstrumentation } from '@opentelemetry/instrumentation-xml-http-request';
import { UserInteractionInstrumentation } from '@opentelemetry/instrumentation-user-interaction';
import { DocumentLoadInstrumentation } from '@opentelemetry/instrumentation-document-load';
import { OTLPMetricExporter } from '@opentelemetry/exporter-metrics-otlp-grpc';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-grpc';
import { OTLPLogExporter } from '@opentelemetry/exporter-logs-otlp-grpc';
import { Resource } from '@opentelemetry/resources';
import { 
  MeterProvider, 
  PeriodicExportingMetricReader,
  ConsoleMetricExporter,
  View,
  Aggregation
} from '@opentelemetry/sdk-metrics';
import { 
  TracerProvider,
  BatchSpanProcessor,
  ConsoleSpanExporter,
  SimpleSpanProcessor
} from '@opentelemetry/sdk-trace-base';
import { WebTracerProvider } from '@opentelemetry/sdk-trace-web';
import { NodeTracerProvider } from '@opentelemetry/sdk-trace-node';
import { 
  LoggerProvider,
  BatchLogRecordProcessor,
  ConsoleLogRecordExporter
} from '@opentelemetry/sdk-logs';
import { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';
import { ZoneContextManager } from '@opentelemetry/context-zone';
import { AsyncHooksContextManager } from '@opentelemetry/context-async-hooks';
import { CompositePropagator, W3CTraceContextPropagator, W3CBaggagePropagator } from '@opentelemetry/core';
import { diag, DiagConsoleLogger, DiagLogLevel } from '@opentelemetry/api';
import * as winston from 'winston';

// Type definitions
export interface TelemetryConfig {
  serviceName: string;
  serviceVersion?: string;
  environment?: string;
  otlpEndpoint?: string;
  samplingRatio?: number;
  logLevel?: string;
  isProduction?: boolean;
  isBrowser?: boolean;
}

// Global instances
let globalTracer: Tracer | null = null;
let globalMeter: Meter | null = null;
let globalLogger: winston.Logger | null = null;

// Metric instruments
let pageLoadDuration: Histogram | null = null;
let apiCallDuration: Histogram | null = null;
let apiCallCount: Counter | null = null;
let jsErrors: Counter | null = null;
let userInteractions: Counter | null = null;
let activeRequests: UpDownCounter | null = null;
let renderDuration: Histogram | null = null;
let cacheHits: Counter | null = null;
let cacheMisses: Counter | null = null;
let websocketMessages: Counter | null = null;
let websocketConnections: UpDownCounter | null = null;

// Business metrics for web app
let patternViews: Counter | null = null;
let searchQueries: Counter | null = null;
let purchaseAttempts: Counter | null = null;
let loginAttempts: Counter | null = null;
let sessionDuration: Histogram | null = null;

/**
 * Initialize telemetry for TypeScript services
 */
export async function initTelemetry(config: TelemetryConfig): Promise<() => void> {
  const {
    serviceName,
    serviceVersion = '0.0.0',
    environment = 'development',
    otlpEndpoint = 'http://localhost:4317',
    samplingRatio = 0.1,
    logLevel = 'info',
    isProduction = false,
    isBrowser = typeof window !== 'undefined'
  } = config;

  // Enable diagnostics in development
  if (!isProduction) {
    diag.setLogger(new DiagConsoleLogger(), DiagLogLevel.DEBUG);
  }

  // Create resource
  const resource = new Resource({
    [SemanticResourceAttributes.SERVICE_NAME]: serviceName,
    [SemanticResourceAttributes.SERVICE_VERSION]: serviceVersion,
    [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: environment,
    [SemanticResourceAttributes.TELEMETRY_SDK_LANGUAGE]: 'javascript',
    [SemanticResourceAttributes.TELEMETRY_SDK_NAME]: 'opentelemetry',
    'browser.user_agent': isBrowser ? navigator.userAgent : undefined,
    'process.runtime.name': isBrowser ? 'browser' : 'nodejs',
    'process.runtime.version': isBrowser ? undefined : process.version,
  });

  // Initialize providers based on environment
  const tracerProvider = await initTracer(config, resource, isBrowser);
  const meterProvider = await initMetrics(config, resource);
  const loggerProvider = await initLogging(config, resource);

  // Initialize global instances
  globalTracer = trace.getTracer(serviceName, serviceVersion);
  globalMeter = metrics.getMeter(serviceName, serviceVersion);
  globalLogger = createWinstonLogger(config);

  // Initialize metric instruments
  initMetricInstruments();

  // Auto-instrumentation
  if (isBrowser) {
    registerBrowserInstrumentations();
  } else {
    registerNodeInstrumentations();
  }

  // Shutdown function
  const shutdown = async (): Promise<void> => {
    await Promise.all([
      tracerProvider.shutdown(),
      meterProvider.shutdown(),
      loggerProvider?.shutdown()
    ].filter(Boolean));
  };

  globalLogger.info('Telemetry initialized successfully', {
    service: serviceName,
    version: serviceVersion,
    environment,
    isBrowser
  });

  return shutdown;
}

async function initTracer(
  config: TelemetryConfig,
  resource: Resource,
  isBrowser: boolean
): Promise<TracerProvider | WebTracerProvider> {
  const exporter = new OTLPTraceExporter({
    url: `${config.otlpEndpoint}/v1/traces`,
  });

  const spanProcessor = config.isProduction
    ? new BatchSpanProcessor(exporter)
    : new SimpleSpanProcessor(new ConsoleSpanExporter());

  const Provider = isBrowser ? WebTracerProvider : NodeTracerProvider;
  const tracerProvider = new Provider({
    resource,
    sampler: {
      shouldSample: () => ({
        decision: Math.random() < (config.samplingRatio || 0.1) ? 1 : 0,
        attributes: {},
      }),
    },
  });

  tracerProvider.addSpanProcessor(spanProcessor);
  tracerProvider.register({
    propagator: new CompositePropagator({
      propagators: [
        new W3CTraceContextPropagator(),
        new W3CBaggagePropagator(),
      ],
    }),
    contextManager: isBrowser
      ? new ZoneContextManager()
      : new AsyncHooksContextManager(),
  });

  return tracerProvider;
}

async function initMetrics(
  config: TelemetryConfig,
  resource: Resource
): Promise<MeterProvider> {
  const metricExporter = config.isProduction
    ? new OTLPMetricExporter({
        url: `${config.otlpEndpoint}/v1/metrics`,
      })
    : new ConsoleMetricExporter();

  const meterProvider = new MeterProvider({
    resource,
    readers: [
      new PeriodicExportingMetricReader({
        exporter: metricExporter,
        exportIntervalMillis: 10000, // 10 seconds
      }),
    ],
    views: [
      // Configure histogram buckets
      new View({
        instrumentName: 'http_request_duration',
        aggregation: Aggregation.Histogram([0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10]),
      }),
    ],
  });

  metrics.setGlobalMeterProvider(meterProvider);
  return meterProvider;
}

async function initLogging(
  config: TelemetryConfig,
  resource: Resource
): Promise<LoggerProvider | undefined> {
  // Only initialize OTLP logging in production
  if (!config.isProduction) {
    return undefined;
  }

  const logExporter = new OTLPLogExporter({
    url: `${config.otlpEndpoint}/v1/logs`,
  });

  const loggerProvider = new LoggerProvider({
    resource,
  });

  loggerProvider.addLogRecordProcessor(
    new BatchLogRecordProcessor(logExporter)
  );

  return loggerProvider;
}

function createWinstonLogger(config: TelemetryConfig): winston.Logger {
  return winston.createLogger({
    level: config.logLevel,
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json()
    ),
    defaultMeta: {
      service: config.serviceName,
      version: config.serviceVersion,
      environment: config.environment,
    },
    transports: [
      new winston.transports.Console({
        format: config.isProduction
          ? winston.format.json()
          : winston.format.combine(
              winston.format.colorize(),
              winston.format.simple()
            ),
      }),
    ],
  });
}

function initMetricInstruments(): void {
  const meter = getGlobalMeter();

  // Performance metrics
  pageLoadDuration = meter.createHistogram('page_load_duration', {
    description: 'Page load duration in milliseconds',
    unit: 'ms',
    valueType: ValueType.DOUBLE,
  });

  apiCallDuration = meter.createHistogram('api_call_duration', {
    description: 'API call duration in milliseconds',
    unit: 'ms',
    valueType: ValueType.DOUBLE,
  });

  apiCallCount = meter.createCounter('api_calls_total', {
    description: 'Total number of API calls',
    valueType: ValueType.INT,
  });

  jsErrors = meter.createCounter('js_errors_total', {
    description: 'Total number of JavaScript errors',
    valueType: ValueType.INT,
  });

  userInteractions = meter.createCounter('user_interactions_total', {
    description: 'Total number of user interactions',
    valueType: ValueType.INT,
  });

  activeRequests = meter.createUpDownCounter('active_requests', {
    description: 'Number of active HTTP requests',
    valueType: ValueType.INT,
  });

  renderDuration = meter.createHistogram('render_duration', {
    description: 'Component render duration in milliseconds',
    unit: 'ms',
    valueType: ValueType.DOUBLE,
  });

  // Cache metrics
  cacheHits = meter.createCounter('cache_hits_total', {
    description: 'Total number of cache hits',
    valueType: ValueType.INT,
  });

  cacheMisses = meter.createCounter('cache_misses_total', {
    description: 'Total number of cache misses',
    valueType: ValueType.INT,
  });

  // WebSocket metrics
  websocketMessages = meter.createCounter('websocket_messages_total', {
    description: 'Total number of WebSocket messages',
    valueType: ValueType.INT,
  });

  websocketConnections = meter.createUpDownCounter('websocket_connections', {
    description: 'Number of active WebSocket connections',
    valueType: ValueType.INT,
  });

  // Business metrics
  patternViews = meter.createCounter('pattern_views_total', {
    description: 'Total number of pattern views',
    valueType: ValueType.INT,
  });

  searchQueries = meter.createCounter('search_queries_total', {
    description: 'Total number of search queries',
    valueType: ValueType.INT,
  });

  purchaseAttempts = meter.createCounter('purchase_attempts_total', {
    description: 'Total number of purchase attempts',
    valueType: ValueType.INT,
  });

  loginAttempts = meter.createCounter('login_attempts_total', {
    description: 'Total number of login attempts',
    valueType: ValueType.INT,
  });

  sessionDuration = meter.createHistogram('session_duration', {
    description: 'User session duration in seconds',
    unit: 's',
    valueType: ValueType.DOUBLE,
  });
}

function registerBrowserInstrumentations(): void {
  registerInstrumentations({
    instrumentations: [
      new FetchInstrumentation({
        propagateTraceHeaderCorsUrls: /.*/,
        clearTimingResources: true,
      }),
      new XMLHttpRequestInstrumentation({
        propagateTraceHeaderCorsUrls: /.*/,
      }),
      new UserInteractionInstrumentation({
        eventNames: ['click', 'submit', 'change'],
      }),
      new DocumentLoadInstrumentation(),
    ],
  });
}

function registerNodeInstrumentations(): void {
  registerInstrumentations({
    instrumentations: [
      getNodeAutoInstrumentations({
        '@opentelemetry/instrumentation-fs': {
          enabled: false, // Too noisy
        },
      }),
    ],
  });
}

// Global accessors
export function getGlobalTracer(): Tracer {
  if (!globalTracer) {
    throw new Error('Telemetry not initialized. Call initTelemetry first.');
  }
  return globalTracer;
}

export function getGlobalMeter(): Meter {
  if (!globalMeter) {
    throw new Error('Telemetry not initialized. Call initTelemetry first.');
  }
  return globalMeter;
}

export function getLogger(): winston.Logger {
  if (!globalLogger) {
    throw new Error('Telemetry not initialized. Call initTelemetry first.');
  }
  return globalLogger;
}

// Tracing utilities
export function startSpan(
  name: string,
  options?: { kind?: SpanKind; attributes?: Record<string, any> }
): Span {
  const tracer = getGlobalTracer();
  return tracer.startSpan(name, {
    kind: options?.kind || SpanKind.INTERNAL,
    attributes: options?.attributes,
  });
}

export function withSpan<T>(
  name: string,
  fn: (span: Span) => T | Promise<T>,
  options?: { kind?: SpanKind; attributes?: Record<string, any> }
): Promise<T> {
  const span = startSpan(name, options);
  
  return trace.context.with(trace.setSpan(trace.context.active(), span), async () => {
    try {
      const result = await fn(span);
      span.setStatus({ code: SpanStatusCode.OK });
      return result;
    } catch (error) {
      span.recordException(error as Error);
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: error instanceof Error ? error.message : String(error),
      });
      throw error;
    } finally {
      span.end();
    }
  });
}

// Metric tracking functions
export function trackPageLoad(duration: number, page: string): void {
  pageLoadDuration?.record(duration, {
    page,
  });
  
  getLogger().info('Page loaded', {
    page,
    duration,
  });
}

export function trackAPICall(
  endpoint: string,
  method: string,
  duration: number,
  status: number,
  error?: Error
): void {
  const attributes = {
    endpoint,
    method,
    status: status.toString(),
    error: error ? 'true' : 'false',
  };

  apiCallDuration?.record(duration, attributes);
  apiCallCount?.add(1, attributes);

  if (error) {
    jsErrors?.add(1, { type: 'api_error', endpoint });
    getLogger().error('API call failed', {
      endpoint,
      method,
      status,
      duration,
      error: error.message,
    });
  }
}

export function trackUserInteraction(type: string, target: string): void {
  userInteractions?.add(1, { type, target });
}

export function trackRenderTime(component: string, duration: number): void {
  renderDuration?.record(duration, { component });
}

export function trackCache(hit: boolean, key: string): void {
  if (hit) {
    cacheHits?.add(1, { key_pattern: key.substring(0, 20) });
  } else {
    cacheMisses?.add(1, { key_pattern: key.substring(0, 20) });
  }
}

export function trackWebSocketMessage(type: 'sent' | 'received', messageType: string): void {
  websocketMessages?.add(1, { direction: type, message_type: messageType });
}

export function trackWebSocketConnection(connected: boolean): void {
  websocketConnections?.add(connected ? 1 : -1);
}

// Business metric tracking
export function trackPatternView(patternId: string, category: string): void {
  patternViews?.add(1, { pattern_id: patternId, category });
}

export function trackSearch(query: string, resultCount: number): void {
  searchQueries?.add(1, {
    query_length: query.length < 20 ? 'short' : query.length < 50 ? 'medium' : 'long',
    has_results: resultCount > 0 ? 'true' : 'false',
  });
}

export function trackPurchaseAttempt(success: boolean, patternId: string): void {
  purchaseAttempts?.add(1, {
    success: success ? 'true' : 'false',
    pattern_id: patternId,
  });

  if (success) {
    getLogger().info('Purchase completed', { patternId });
  } else {
    getLogger().warn('Purchase failed', { patternId });
  }
}

export function trackLogin(success: boolean, method: string): void {
  loginAttempts?.add(1, {
    success: success ? 'true' : 'false',
    method,
  });
}

export function trackSessionDuration(durationSeconds: number): void {
  sessionDuration?.record(durationSeconds);
}

// Error tracking
export function trackError(error: Error, context?: Record<string, any>): void {
  jsErrors?.add(1, {
    type: error.name || 'unknown',
    message: error.message.substring(0, 50),
  });

  const span = trace.getActiveSpan();
  if (span) {
    span.recordException(error);
    span.setStatus({
      code: SpanStatusCode.ERROR,
      message: error.message,
    });
  }

  getLogger().error('JavaScript error', {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    context,
  });
}

// React component instrumentation
export function withComponentTracing<P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
): React.ComponentType<P> {
  return (props: P) => {
    const renderStart = performance.now();

    React.useEffect(() => {
      const renderTime = performance.now() - renderStart;
      trackRenderTime(componentName, renderTime);
    }, []);

    return React.createElement(Component, props);
  };
}

// Express/Koa middleware for Node.js
export function httpMiddleware() {
  return async (req: any, res: any, next: any) => {
    const start = Date.now();
    activeRequests?.add(1);

    const span = startSpan(`${req.method} ${req.path}`, {
      kind: SpanKind.SERVER,
      attributes: {
        'http.method': req.method,
        'http.url': req.url,
        'http.target': req.path,
        'http.host': req.hostname,
        'http.scheme': req.protocol,
        'http.user_agent': req.get('user-agent'),
      },
    });

    res.on('finish', () => {
      const duration = Date.now() - start;
      
      span.setAttributes({
        'http.status_code': res.statusCode,
        'http.response_content_length': res.get('content-length'),
      });

      if (res.statusCode >= 400) {
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: `HTTP ${res.statusCode}`,
        });
      }

      span.end();
      activeRequests?.add(-1);

      trackAPICall(req.path, req.method, duration, res.statusCode);
    });

    await trace.context.with(trace.setSpan(trace.context.active(), span), () => {
      next();
    });
  };
}

// Export types
export type { Span, Context, Tracer, Meter };
export { SpanKind, SpanStatusCode };