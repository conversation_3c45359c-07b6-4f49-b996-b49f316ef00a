// Instrumentation library for Rust services in the CCL platform
// Provides OpenTelemetry integration for metrics, tracing, and logging

use anyhow::{Context, Result};
use opentelemetry::{
    global,
    metrics::{Counter, Histogram, Meter, UpDownCounter},
    propagation::TextMapPropagator,
    sdk::{
        export::metrics::aggregation,
        metrics::{controllers, processors, selectors},
        propagation::TraceContextPropagator,
        trace::{self, Sampler},
        Resource,
    },
    trace::{Span, SpanKind, StatusCode, TraceContextExt, Tracer},
    Key, KeyValue,
};
use opentelemetry_otlp::{ExportConfig, Protocol, WithExportConfig};
use prometheus::{Encoder, Registry, TextEncoder};
use std::{collections::HashMap, sync::Arc, time::Duration};
use tracing::{error, info, warn};
use tracing_opentelemetry::OpenTelemetryLayer;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt, EnvFilter, Layer};

// Re-export commonly used types
pub use opentelemetry::{
    trace::{FutureExt, TraceError},
    Context,
};

// Metric instruments
lazy_static::lazy_static! {
    // HTTP metrics
    pub static ref HTTP_REQUEST_DURATION: Histogram<f64> = global::meter("ccl")
        .f64_histogram("http_request_duration_seconds")
        .with_description("HTTP request latencies in seconds")
        .init();
    
    pub static ref HTTP_REQUEST_COUNT: Counter<u64> = global::meter("ccl")
        .u64_counter("http_requests_total")
        .with_description("Total number of HTTP requests")
        .init();
    
    pub static ref HTTP_REQUEST_SIZE: Histogram<f64> = global::meter("ccl")
        .f64_histogram("http_request_size_bytes")
        .with_description("HTTP request sizes in bytes")
        .init();
    
    pub static ref HTTP_RESPONSE_SIZE: Histogram<f64> = global::meter("ccl")
        .f64_histogram("http_response_size_bytes")
        .with_description("HTTP response sizes in bytes")
        .init();
    
    // Business metrics
    pub static ref ANALYSIS_REQUESTS: Counter<u64> = global::meter("ccl")
        .u64_counter("analysis_requests_total")
        .with_description("Total number of code analysis requests")
        .init();
    
    pub static ref PATTERNS_DETECTED: Counter<u64> = global::meter("ccl")
        .u64_counter("patterns_detected_total")
        .with_description("Total number of patterns detected")
        .init();
    
    pub static ref ACTIVE_ANALYSES: UpDownCounter<i64> = global::meter("ccl")
        .i64_up_down_counter("active_analyses")
        .with_description("Number of currently active analyses")
        .init();
    
    pub static ref FILE_PROCESSING_DURATION: Histogram<f64> = global::meter("ccl")
        .f64_histogram("file_processing_duration_seconds")
        .with_description("Time to process individual files")
        .init();
    
    // Error metrics
    pub static ref ERROR_COUNT: Counter<u64> = global::meter("ccl")
        .u64_counter("errors_total")
        .with_description("Total number of errors")
        .init();
}

pub struct TelemetryConfig {
    pub service_name: String,
    pub service_version: String,
    pub environment: String,
    pub otlp_endpoint: String,
    pub sampling_ratio: f64,
    pub log_level: String,
}

impl Default for TelemetryConfig {
    fn default() -> Self {
        Self {
            service_name: "unknown".to_string(),
            service_version: "0.0.0".to_string(),
            environment: "development".to_string(),
            otlp_endpoint: "http://localhost:4317".to_string(),
            sampling_ratio: 0.1,
            log_level: "info".to_string(),
        }
    }
}

/// Initialize telemetry for a Rust service
pub fn init_telemetry(config: TelemetryConfig) -> Result<()> {
    // Set global propagator
    global::set_text_map_propagator(TraceContextPropagator::new());
    
    // Create resource
    let resource = Resource::new(vec![
        KeyValue::new("service.name", config.service_name.clone()),
        KeyValue::new("service.version", config.service_version),
        KeyValue::new("deployment.environment", config.environment),
        KeyValue::new("telemetry.sdk.language", "rust"),
        KeyValue::new("telemetry.sdk.name", "opentelemetry"),
    ]);
    
    // Initialize tracer
    let tracer = init_tracer(&config, resource.clone())?;
    
    // Initialize metrics
    init_metrics(&config, resource)?;
    
    // Initialize logging with tracing
    init_logging(&config, tracer)?;
    
    info!(
        service = config.service_name,
        "Telemetry initialized successfully"
    );
    
    Ok(())
}

fn init_tracer(config: &TelemetryConfig, resource: Resource) -> Result<Tracer> {
    let export_config = ExportConfig {
        endpoint: config.otlp_endpoint.clone(),
        timeout: Duration::from_secs(10),
        protocol: Protocol::Grpc,
    };
    
    let tracer = opentelemetry_otlp::new_pipeline()
        .tracing()
        .with_exporter(
            opentelemetry_otlp::new_exporter()
                .tonic()
                .with_export_config(export_config),
        )
        .with_trace_config(
            trace::config()
                .with_sampler(Sampler::TraceIdRatioBased(config.sampling_ratio))
                .with_resource(resource),
        )
        .install_batch(opentelemetry::runtime::Tokio)
        .context("Failed to initialize tracer")?;
    
    Ok(tracer)
}

fn init_metrics(config: &TelemetryConfig, resource: Resource) -> Result<()> {
    let export_config = ExportConfig {
        endpoint: config.otlp_endpoint.clone(),
        timeout: Duration::from_secs(10),
        protocol: Protocol::Grpc,
    };
    
    let controller = controllers::basic(
        processors::factory(
            selectors::simple::histogram([0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]),
            aggregation::cumulative_temporality_selector(),
        )
        .with_memory(true),
    )
    .with_resource(resource)
    .with_exporter(
        opentelemetry_otlp::new_exporter()
            .tonic()
            .with_export_config(export_config)
            .build_metrics_exporter(Box::new(aggregation::cumulative_temporality_selector()))?,
    )
    .build()
    .context("Failed to build metrics controller")?;
    
    global::set_meter_provider(controller);
    
    Ok(())
}

fn init_logging(config: &TelemetryConfig, tracer: Tracer) -> Result<()> {
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new(&config.log_level));
    
    let telemetry = tracing_opentelemetry::layer().with_tracer(tracer);
    
    let fmt_layer = tracing_subscriber::fmt::layer()
        .json()
        .with_current_span(true)
        .with_span_list(true)
        .with_thread_ids(true)
        .with_thread_names(true)
        .with_target(true)
        .with_file(true)
        .with_line_number(true);
    
    tracing_subscriber::registry()
        .with(env_filter)
        .with(telemetry)
        .with(fmt_layer)
        .init();
    
    Ok(())
}

/// HTTP middleware for request tracking
pub async fn track_http_request<F, T>(
    method: &str,
    path: &str,
    handler: F,
) -> Result<T>
where
    F: std::future::Future<Output = Result<T>>,
{
    let start = std::time::Instant::now();
    let tracer = global::tracer("http");
    
    let mut span = tracer
        .span_builder("http_request")
        .with_kind(SpanKind::Server)
        .with_attributes(vec![
            KeyValue::new("http.method", method.to_string()),
            KeyValue::new("http.target", path.to_string()),
            KeyValue::new("http.scheme", "http"),
        ])
        .start(&tracer);
    
    let cx = Context::current_with_span(span);
    let _guard = cx.attach();
    
    // Execute handler
    match handler.await {
        Ok(response) => {
            let duration = start.elapsed().as_secs_f64();
            
            // Record metrics
            HTTP_REQUEST_COUNT.add(
                1,
                &[
                    KeyValue::new("method", method.to_string()),
                    KeyValue::new("endpoint", path.to_string()),
                    KeyValue::new("status", "success"),
                ],
            );
            
            HTTP_REQUEST_DURATION.record(
                duration,
                &[
                    KeyValue::new("method", method.to_string()),
                    KeyValue::new("endpoint", path.to_string()),
                    KeyValue::new("status", "success"),
                ],
            );
            
            cx.span().set_status(StatusCode::Ok, "");
            Ok(response)
        }
        Err(e) => {
            let duration = start.elapsed().as_secs_f64();
            
            // Record error metrics
            HTTP_REQUEST_COUNT.add(
                1,
                &[
                    KeyValue::new("method", method.to_string()),
                    KeyValue::new("endpoint", path.to_string()),
                    KeyValue::new("status", "error"),
                ],
            );
            
            HTTP_REQUEST_DURATION.record(
                duration,
                &[
                    KeyValue::new("method", method.to_string()),
                    KeyValue::new("endpoint", path.to_string()),
                    KeyValue::new("status", "error"),
                ],
            );
            
            ERROR_COUNT.add(
                1,
                &[
                    KeyValue::new("type", "http_error"),
                    KeyValue::new("endpoint", path.to_string()),
                ],
            );
            
            cx.span().record_error(&e);
            cx.span().set_status(StatusCode::Error, format!("{:?}", e));
            Err(e)
        }
    }
}

/// Track custom business metrics
pub fn track_analysis_request(language: &str, file_count: u64) {
    ANALYSIS_REQUESTS.add(
        1,
        &[
            KeyValue::new("language", language.to_string()),
            KeyValue::new("size_category", categorize_size(file_count)),
        ],
    );
    
    ACTIVE_ANALYSES.add(1, &[]);
}

pub fn track_analysis_complete(language: &str, patterns_found: u64, duration: Duration) {
    ACTIVE_ANALYSES.add(-1, &[]);
    
    PATTERNS_DETECTED.add(
        patterns_found,
        &[KeyValue::new("language", language.to_string())],
    );
    
    FILE_PROCESSING_DURATION.record(
        duration.as_secs_f64(),
        &[KeyValue::new("language", language.to_string())],
    );
}

/// Track errors with context
pub fn track_error(error_type: &str, context: &str) {
    ERROR_COUNT.add(
        1,
        &[
            KeyValue::new("type", error_type.to_string()),
            KeyValue::new("context", context.to_string()),
        ],
    );
    
    error!(
        error_type = error_type,
        context = context,
        "Error occurred"
    );
}

fn categorize_size(file_count: u64) -> &'static str {
    match file_count {
        0..=10 => "small",
        11..=100 => "medium",
        101..=1000 => "large",
        _ => "xlarge",
    }
}

/// Prometheus metrics endpoint handler
pub async fn metrics_handler() -> Result<String> {
    let encoder = TextEncoder::new();
    let metric_families = prometheus::gather();
    let mut buffer = Vec::new();
    encoder.encode(&metric_families, &mut buffer)?;
    String::from_utf8(buffer).context("Failed to encode metrics")
}

/// Shutdown telemetry providers gracefully
pub fn shutdown_telemetry() {
    global::shutdown_tracer_provider();
    // Note: Metrics provider shutdown is handled automatically
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_telemetry_initialization() {
        let config = TelemetryConfig {
            service_name: "test-service".to_string(),
            ..Default::default()
        };
        
        assert!(init_telemetry(config).is_ok());
        shutdown_telemetry();
    }
    
    #[tokio::test]
    async fn test_http_tracking() {
        let result = track_http_request("GET", "/test", async {
            Ok::<_, anyhow::Error>("success")
        })
        .await;
        
        assert!(result.is_ok());
    }
}