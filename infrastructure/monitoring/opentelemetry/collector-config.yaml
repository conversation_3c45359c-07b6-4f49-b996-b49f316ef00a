# OpenTelemetry Collector Configuration for CCL Platform
# This collector handles metrics, traces, and logs from all CCL services

receivers:
  # OTLP receiver for traces and metrics from applications
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
        max_recv_msg_size_mib: 100
        max_concurrent_streams: 1000
        keepalive:
          server_parameters:
            max_connection_idle: 11s
            max_connection_age: 30s
            max_connection_age_grace: 5s
            time: 30s
            timeout: 5s
      http:
        endpoint: 0.0.0.0:4318
        cors:
          allowed_origins:
            - "http://*"
            - "https://*"
  
  # Prometheus receiver for scraping metrics endpoints
  prometheus:
    config:
      scrape_configs:
        - job_name: 'ccl-services'
          scrape_interval: 30s
          kubernetes_sd_configs:
            - role: pod
              namespaces:
                names:
                  - ccl-production
                  - ccl-staging
                  - ccl-development

        # CI/CD Pipeline Metrics
        - job_name: 'ccl-github-actions'
          static_configs:
            - targets: ['github-actions-exporter:9090']
          scrape_interval: 30s
          scrape_timeout: 10s
          metrics_path: /metrics
          metric_relabel_configs:
            # Rename GitHub Actions metrics to CCL convention
            - source_labels: [__name__]
              regex: 'github_actions_workflow_run_duration_seconds'
              target_label: __name__
              replacement: 'ccl_pipeline_duration_seconds'
            - source_labels: [__name__]
              regex: 'github_actions_workflow_run_conclusion_total'
              target_label: __name__
              replacement: 'ccl_build_status_total'

        - job_name: 'ccl-deployment-events'
          static_configs:
            - targets: ['deployment-webhook:8080']
          scrape_interval: 10s
          metrics_path: /metrics
          metric_relabel_configs:
            - source_labels: [__name__]
              regex: 'deployment_(.+)'
              target_label: __name__
              replacement: 'ccl_deployment_${1}'

        - job_name: 'ccl-lead-time-calculator'
          static_configs:
            - targets: ['lead-time-calculator:8080']
          scrape_interval: 60s
          metrics_path: /metrics

        - job_name: 'ccl-security-metrics'
          static_configs:
            - targets: ['security-scanner-exporter:9091']
          scrape_interval: 60s
          metrics_path: /metrics
          metric_relabel_configs:
            - source_labels: [__name__]
              regex: 'trivy_(.+)'
              target_label: __name__
              replacement: 'ccl_security_${1}'
          relabel_configs:
            # Only scrape pods with prometheus annotations
            - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
              action: keep
              regex: true
            # Use pod annotation for metrics path
            - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
              action: replace
              target_label: __metrics_path__
              regex: (.+)
            # Use pod annotation for metrics port
            - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
              action: replace
              regex: ([^:]+)(?::\d+)?;(\d+)
              replacement: $1:$2
              target_label: __address__
            # Add kubernetes labels
            - action: labelmap
              regex: __meta_kubernetes_pod_label_(.+)
            - source_labels: [__meta_kubernetes_namespace]
              action: replace
              target_label: kubernetes_namespace
            - source_labels: [__meta_kubernetes_pod_name]
              action: replace
              target_label: kubernetes_pod_name
            - source_labels: [__meta_kubernetes_pod_label_app]
              action: replace
              target_label: service_name
  
  # Host metrics for infrastructure monitoring
  hostmetrics:
    collection_interval: 30s
    scrapers:
      cpu:
        metrics:
          - system.cpu.utilization
      memory:
        metrics:
          - system.memory.utilization
      disk:
        metrics:
          - system.disk.operations
          - system.disk.io
      network:
        metrics:
          - system.network.io
          - system.network.errors
      load:
        metrics:
          - system.cpu.load_average.1m
          - system.cpu.load_average.5m
          - system.cpu.load_average.15m

processors:
  # Batch processor to optimize network usage
  batch:
    timeout: 10s
    send_batch_size: 1024
    send_batch_max_size: 2048
  
  # Memory limiter to prevent OOM
  memory_limiter:
    check_interval: 1s
    limit_mib: 1024
    spike_limit_mib: 256
    limit_percentage: 75
    spike_limit_percentage: 25
  
  # Resource processor to add common attributes
  resource:
    attributes:
      - key: environment
        value: ${ENV}
        action: insert
      - key: cluster
        value: ${CLUSTER_NAME}
        action: insert
      - key: region
        value: ${GCP_REGION}
        action: insert
      - key: service.namespace
        value: ccl
        action: insert
      - key: telemetry.sdk.language
        from_attribute: telemetry.sdk.language
        action: insert
  
  # Span processor for trace sampling
  probabilistic_sampler:
    sampling_percentage: 10.0
  
  # Tail sampling for important traces
  tail_sampling:
    decision_wait: 10s
    num_traces: 100000
    expected_new_traces_per_sec: 1000
    policies:
      - name: errors-policy
        type: status_code
        status_code:
          status_codes: [ERROR]
      - name: slow-traces-policy
        type: latency
        latency:
          threshold_ms: 1000
      - name: high-value-policy
        type: string_attribute
        string_attribute:
          key: priority
          values: [high, critical]
      - name: probabilistic-policy
        type: probabilistic
        probabilistic:
          sampling_percentage: 10
  
  # Filter processor for removing sensitive data
  filter/errors:
    error_mode: ignore
    traces:
      span:
        - 'name == "health_check"'
        - 'attributes["http.url"] ~= ".*\\/health.*"'
    metrics:
      metric:
        - 'name == "system.network.dropped"'
  
  # Transform processor for data enrichment
  transform:
    error_mode: ignore
    trace_statements:
      - context: span
        statements:
          - set(attributes["service.version"], resource.attributes["service.version"])
          - set(attributes["deployment.environment"], resource.attributes["deployment.environment"])
    metric_statements:
      - context: datapoint
        statements:
          - set(attributes["environment"], resource.attributes["environment"])
          - set(attributes["region"], resource.attributes["region"])

exporters:
  # Google Cloud exports
  googlecloud:
    project: ccl-platform-${ENV}
    compression: gzip
    retry_on_failure:
      enabled: true
      initial_interval: 5s
      max_interval: 30s
      max_elapsed_time: 300s
    sending_queue:
      enabled: true
      num_consumers: 10
      queue_size: 5000
    timeout: 30s
    
  # Google Managed Prometheus for metrics
  googlemanagedprometheus:
    project: ccl-platform-${ENV}
    compression: gzip
    
  # BigQuery export for logs
  bigquery:
    project: ccl-platform-${ENV}
    dataset: monitoring_logs
    table: service_logs
    compression: gzip
    use_insecure_transport: false
    
  # Debug exporter for troubleshooting
  debug:
    verbosity: detailed
    sampling_initial: 5
    sampling_thereafter: 200
  
  # OTLP exporter for trace forwarding
  otlp/trace:
    endpoint: "ingest.lightstep.com:443"
    headers:
      "lightstep-access-token": "${LIGHTSTEP_ACCESS_TOKEN}"
    compression: gzip
    
  # Prometheus remote write for long-term metrics
  prometheusremotewrite:
    endpoint: "https://prometheus-prod-10-prod-us-central-0.grafana.net/api/prom/push"
    headers:
      "Authorization": "Bearer ${GRAFANA_CLOUD_API_KEY}"
    compression: snappy
    external_labels:
      cluster: ${CLUSTER_NAME}
      environment: ${ENV}

extensions:
  # Health check extension
  health_check:
    endpoint: 0.0.0.0:13133
    path: "/health"
    check_collector_pipeline:
      enabled: true
      interval: 5s
      exporter_failure_threshold: 5
  
  # Performance profiler
  pprof:
    endpoint: 0.0.0.0:1777
    block_profile_fraction: 0
    mutex_profile_fraction: 0
  
  # zPages for debugging
  zpages:
    endpoint: 0.0.0.0:55679
  
  # OAuth2 client for authenticated exports
  oauth2client:
    client_id: ${OAUTH_CLIENT_ID}
    client_secret: ${OAUTH_CLIENT_SECRET}
    token_url: https://oauth2.googleapis.com/token
    scopes: ["https://www.googleapis.com/auth/cloud-platform"]

service:
  # Enable extensions
  extensions: [health_check, pprof, zpages, oauth2client]
  
  # Configure pipelines
  pipelines:
    # Traces pipeline
    traces:
      receivers: [otlp]
      processors: [memory_limiter, batch, resource, tail_sampling, filter/errors, transform]
      exporters: [googlecloud, debug]
    
    # Metrics pipeline
    metrics:
      receivers: [otlp, prometheus, hostmetrics]
      processors: [memory_limiter, batch, resource, filter/errors, transform]
      exporters: [googlemanagedprometheus, prometheusremotewrite]
    
    # Logs pipeline
    logs:
      receivers: [otlp]
      processors: [memory_limiter, batch, resource, filter/errors, transform]
      exporters: [bigquery, debug]
  
  # Telemetry configuration for the collector itself
  telemetry:
    logs:
      level: info
      initial_fields:
        service: opentelemetry-collector
    metrics:
      level: detailed
      address: 0.0.0.0:8888
      exporters:
        - googlemanagedprometheus

# Processor execution order (for reference):
# 1. memory_limiter (prevent OOM)
# 2. filter (remove unwanted data)
# 3. resource (add common attributes)
# 4. transform (enrich data)
# 5. tail_sampling (smart sampling for traces)
# 6. batch (optimize network usage)