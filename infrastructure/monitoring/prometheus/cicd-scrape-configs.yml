# CI/CD Metrics Scrape Configuration for Prometheus
# This file extends the existing Prometheus configuration with CI/CD pipeline metrics

# GitHub Actions Exporter Configuration
- job_name: 'ccl-github-actions'
  static_configs:
    - targets: ['github-actions-exporter:9090']
  scrape_interval: 30s
  scrape_timeout: 10s
  metrics_path: /metrics
  scheme: http
  honor_labels: true
  relabel_configs:
    - source_labels: [__address__]
      target_label: __param_target
    - source_labels: [__param_target]
      target_label: instance
    - target_label: __address__
      replacement: github-actions-exporter:9090
  metric_relabel_configs:
    # Rename GitHub Actions metrics to CCL convention
    - source_labels: [__name__]
      regex: 'github_actions_workflow_run_duration_seconds'
      target_label: __name__
      replacement: 'ccl_pipeline_duration_seconds'
    - source_labels: [__name__]
      regex: 'github_actions_workflow_run_conclusion_total'
      target_label: __name__
      replacement: 'ccl_build_status_total'
    - source_labels: [__name__]
      regex: 'github_actions_job_duration_seconds'
      target_label: __name__
      replacement: 'ccl_job_duration_seconds'

# Cloud Run Deployment Events
- job_name: 'ccl-deployment-events'
  static_configs:
    - targets: ['deployment-webhook:8080']
  scrape_interval: 10s
  scrape_timeout: 5s
  metrics_path: /metrics
  scheme: http
  honor_labels: true
  metric_relabel_configs:
    # Add CCL prefix to deployment metrics
    - source_labels: [__name__]
      regex: 'deployment_(.+)'
      target_label: __name__
      replacement: 'ccl_deployment_${1}'
    - source_labels: [__name__]
      regex: 'build_(.+)'
      target_label: __name__
      replacement: 'ccl_build_${1}'

# SonarQube Quality Metrics
- job_name: 'ccl-sonarqube-metrics'
  static_configs:
    - targets: ['sonarqube-exporter:9090']
  scrape_interval: 300s  # 5 minutes - quality metrics don't change frequently
  scrape_timeout: 30s
  metrics_path: /metrics
  scheme: http
  params:
    format: ['prometheus']
  metric_relabel_configs:
    # Map SonarQube metrics to CCL convention
    - source_labels: [__name__]
      regex: 'sonarqube_(.+)'
      target_label: __name__
      replacement: 'ccl_code_quality_${1}'
    - source_labels: [project_key]
      regex: 'ccl-(.+)'
      target_label: service_name
      replacement: '${1}'

# Security Scanner Metrics (Trivy, etc.)
- job_name: 'ccl-security-metrics'
  static_configs:
    - targets: ['security-scanner-exporter:9091']
  scrape_interval: 60s
  scrape_timeout: 15s
  metrics_path: /metrics
  scheme: http
  metric_relabel_configs:
    # Standardize security metric names
    - source_labels: [__name__]
      regex: 'trivy_(.+)'
      target_label: __name__
      replacement: 'ccl_security_${1}'
    - source_labels: [__name__]
      regex: 'vulnerability_(.+)'
      target_label: __name__
      replacement: 'ccl_security_vulnerability_${1}'

# Test Coverage Metrics
- job_name: 'ccl-test-coverage'
  static_configs:
    - targets: ['coverage-exporter:9092']
  scrape_interval: 120s  # 2 minutes
  scrape_timeout: 10s
  metrics_path: /metrics
  scheme: http
  metric_relabel_configs:
    - source_labels: [__name__]
      regex: 'coverage_(.+)'
      target_label: __name__
      replacement: 'ccl_test_coverage_${1}'

# Artifact Registry Metrics
- job_name: 'ccl-artifact-metrics'
  static_configs:
    - targets: ['artifact-registry-exporter:9093']
  scrape_interval: 300s  # 5 minutes
  scrape_timeout: 30s
  metrics_path: /metrics
  scheme: http
  metric_relabel_configs:
    - source_labels: [__name__]
      regex: 'artifact_(.+)'
      target_label: __name__
      replacement: 'ccl_artifact_${1}'
    - source_labels: [repository]
      regex: 'ccl-(.+)-services'
      target_label: environment
      replacement: '${1}'

# Cloud Build Metrics (if using Cloud Build)
- job_name: 'ccl-cloud-build'
  static_configs:
    - targets: ['cloud-build-exporter:9094']
  scrape_interval: 60s
  scrape_timeout: 15s
  metrics_path: /metrics
  scheme: http
  metric_relabel_configs:
    - source_labels: [__name__]
      regex: 'cloudbuild_(.+)'
      target_label: __name__
      replacement: 'ccl_build_${1}'
    - source_labels: [project_id]
      regex: 'ccl-platform-(.+)'
      target_label: environment
      replacement: '${1}'

# Lead Time Calculator Service
- job_name: 'ccl-lead-time-calculator'
  static_configs:
    - targets: ['lead-time-calculator:8080']
  scrape_interval: 60s
  scrape_timeout: 10s
  metrics_path: /metrics
  scheme: http
  metric_relabel_configs:
    - source_labels: [__name__]
      regex: 'lead_time_(.+)'
      target_label: __name__
      replacement: 'ccl_lead_time_${1}'

# MTTR Calculator Service
- job_name: 'ccl-mttr-calculator'
  static_configs:
    - targets: ['mttr-calculator:8080']
  scrape_interval: 300s  # 5 minutes
  scrape_timeout: 30s
  metrics_path: /metrics
  scheme: http
  metric_relabel_configs:
    - source_labels: [__name__]
      regex: 'mttr_(.+)'
      target_label: __name__
      replacement: 'ccl_mttr_${1}'

# Recording Rules for CI/CD Metrics
recording_rules:
  - name: ccl_cicd_recording_rules
    interval: 30s
    rules:
      # Deployment success rate
      - record: ccl:deployment_success_rate
        expr: |
          sum(rate(ccl_deployment_status_total{status="SUCCESS"}[5m])) by (service_name, environment)
          /
          sum(rate(ccl_deployment_status_total[5m])) by (service_name, environment)
        labels:
          metric_type: "success_rate"
          
      # Build success rate
      - record: ccl:build_success_rate
        expr: |
          sum(rate(ccl_build_status_total{status="SUCCESS"}[5m])) by (service_name)
          /
          sum(rate(ccl_build_status_total[5m])) by (service_name)
        labels:
          metric_type: "success_rate"
          
      # Average lead time
      - record: ccl:lead_time_avg
        expr: |
          histogram_quantile(0.50, 
            sum(rate(ccl_lead_time_seconds_bucket[1h])) by (le, service_name)
          )
        labels:
          metric_type: "lead_time"
          percentile: "50"
          
      # 95th percentile lead time
      - record: ccl:lead_time_p95
        expr: |
          histogram_quantile(0.95, 
            sum(rate(ccl_lead_time_seconds_bucket[1h])) by (le, service_name)
          )
        labels:
          metric_type: "lead_time"
          percentile: "95"
          
      # Deployment frequency (per day)
      - record: ccl:deployment_frequency_daily
        expr: |
          sum(rate(ccl_deployment_status_total[24h])) by (service_name, environment) * 86400
        labels:
          metric_type: "frequency"
          window: "daily"
          
      # Change failure rate
      - record: ccl:change_failure_rate
        expr: |
          sum(rate(ccl_deployment_status_total{status="FAILED"}[7d])) by (service_name, environment)
          /
          sum(rate(ccl_deployment_status_total[7d])) by (service_name, environment)
        labels:
          metric_type: "failure_rate"
          window: "weekly"

# Alerting Rules for CI/CD Metrics
alerting_rules:
  - name: ccl_cicd_alerts
    rules:
      # High-level DORA metric alerts
      - alert: LowDeploymentFrequency
        expr: ccl:deployment_frequency_daily < 1
        for: 24h
        labels:
          severity: warning
          team: platform
          metric: deployment_frequency
        annotations:
          summary: "Low deployment frequency for {{ $labels.service_name }}"
          description: "Deployment frequency is {{ $value | humanize }} per day for {{ $labels.service_name }}"
          
      - alert: HighChangeFailureRate
        expr: ccl:change_failure_rate > 0.15
        for: 1h
        labels:
          severity: critical
          team: platform
          metric: change_failure_rate
        annotations:
          summary: "High change failure rate for {{ $labels.service_name }}"
          description: "Change failure rate is {{ $value | humanizePercentage }} for {{ $labels.service_name }}"
          
      - alert: LongLeadTime
        expr: ccl:lead_time_p95 > 14400  # 4 hours
        for: 2h
        labels:
          severity: warning
          team: platform
          metric: lead_time
        annotations:
          summary: "Long lead time for {{ $labels.service_name }}"
          description: "95th percentile lead time is {{ $value | humanizeDuration }} for {{ $labels.service_name }}"
