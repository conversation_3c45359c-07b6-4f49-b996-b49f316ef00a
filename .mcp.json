{"mcpServers": {"filesystem": {"command": "npx", "args": ["@modelcontextprotocol/server-filesystem"], "config": {"directories": ["/Users/<USER>/Documents/GitHub/episteme"], "allowedOperations": ["read", "write", "create", "delete", "move"]}}, "github": {"command": "npx", "args": ["@modelcontextprotocol/server-github"], "env": {"GITHUB_TOKEN": "${GITHUB_TOKEN}"}, "config": {"owner": "ccl-platform", "repo": "ccl", "branch": "main"}}, "memory": {"command": "npx", "args": ["@modelcontextprotocol/server-memory"], "config": {"dataPath": ".claude/memory/mcp-memory.json", "maxEntries": 10000, "syncInterval": 30}}, "puppeteer": {"command": "npx", "args": ["@modelcontextprotocol/server-puppeteer"], "config": {"headless": true, "defaultTimeout": 30000}}, "sequential-thinking": {"command": "npx", "args": ["@modelcontextprotocol/server-sequential-thinking"], "config": {"maxThoughts": 25, "enableBranching": true}}, "google": {"command": "npx", "args": ["@modelcontextprotocol/server-google"], "env": {"GOOGLE_API_KEY": "${GOOGLE_API_KEY}"}, "config": {"services": ["search", "maps", "places"]}}, "postgres": {"command": "npx", "args": ["@modelcontextprotocol/server-postgres"], "env": {"DATABASE_URL": "${DATABASE_URL}"}, "config": {"database": "ccl_dev", "ssl": false}}, "fetch": {"command": "npx", "args": ["@modelcontextprotocol/server-fetch"], "config": {"userAgent": "CCL-Orchestrator/1.0", "maxRedirects": 5, "timeout": 30000}}}, "globalConfig": {"logLevel": "info", "logPath": "monitoring/logs/mcp.log", "maxConcurrentServers": 8, "serverTimeout": 60000, "retryAttempts": 3, "retryDelay": 1000}, "clientConfig": {"claudeCode": {"autoConnect": true, "reconnectInterval": 5000, "shareMemory": true}, "cline": {"autoConnect": true, "approvalRequired": true, "maxAutoApprovals": 10}}}