"""
Data Quality Validation Pipeline
Ensures training data meets quality standards before use in ML models
"""
from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Optional, Any, Set
import pandas as pd
import numpy as np
from datetime import datetime
import json
import hashlib
import logging
from collections import defaultdict, Counter
from sklearn.metrics import silhouette_score
from sklearn.preprocessing import StandardScaler
import ast
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class QualityMetrics:
    """Comprehensive quality metrics for training data"""
    completeness: float
    consistency: float
    accuracy: float
    uniqueness: float
    validity: float
    class_balance: Dict[str, float]
    feature_coverage: Dict[str, float]
    label_confidence: float
    overall_score: float = field(init=False)
    
    def __post_init__(self):
        """Calculate overall quality score"""
        self.overall_score = np.mean([
            self.completeness,
            self.consistency,
            self.accuracy,
            self.uniqueness,
            self.validity,
            min(self.class_balance.values()) if self.class_balance else 0,
            self.label_confidence
        ])


@dataclass
class QualityReport:
    """Detailed quality validation report"""
    metrics: QualityMetrics
    issues: List[Dict[str, Any]]
    recommendations: List[str]
    validation_timestamp: datetime
    dataset_info: Dict[str, Any]
    passed: bool = field(init=False)
    
    def __post_init__(self):
        """Determine if dataset passes quality threshold"""
        self.passed = self.metrics.overall_score >= 0.8


class DataQualityValidator:
    """Comprehensive data quality validation system"""
    
    def __init__(self, min_quality_threshold: float = 0.8):
        self.min_quality_threshold = min_quality_threshold
        self.golden_dataset = None
        self.feature_statistics = {}
        
    def validate_dataset(self, df: pd.DataFrame) -> QualityReport:
        """Perform comprehensive data quality validation"""
        logger.info(f"Validating dataset with {len(df)} samples...")
        
        issues = []
        recommendations = []
        
        # 1. Check completeness
        completeness = self.check_completeness(df, issues)
        
        # 2. Check consistency
        consistency = self.check_label_consistency(df, issues)
        
        # 3. Check accuracy (if golden dataset available)
        accuracy = self.validate_against_golden(df, issues)
        
        # 4. Check uniqueness
        uniqueness = self.check_uniqueness(df, issues)
        
        # 5. Check validity
        validity = self.check_validity(df, issues)
        
        # 6. Check class balance
        class_balance = self.check_class_balance(df, issues, recommendations)
        
        # 7. Check feature coverage
        feature_coverage = self.check_feature_coverage(df, issues)
        
        # 8. Check label confidence
        label_confidence = self.check_label_confidence(df, issues)
        
        # Create metrics
        metrics = QualityMetrics(
            completeness=completeness,
            consistency=consistency,
            accuracy=accuracy,
            uniqueness=uniqueness,
            validity=validity,
            class_balance=class_balance,
            feature_coverage=feature_coverage,
            label_confidence=label_confidence
        )
        
        # Dataset info
        dataset_info = {
            'total_samples': len(df),
            'total_features': len(df.columns),
            'languages': df['language'].unique().tolist() if 'language' in df else [],
            'pattern_types': df['pattern_type'].unique().tolist() if 'pattern_type' in df else [],
            'date_range': {
                'start': df['created_at'].min() if 'created_at' in df else None,
                'end': df['created_at'].max() if 'created_at' in df else None
            }
        }
        
        # Generate report
        report = QualityReport(
            metrics=metrics,
            issues=issues,
            recommendations=recommendations,
            validation_timestamp=datetime.now(),
            dataset_info=dataset_info
        )
        
        self._log_validation_results(report)
        
        return report
    
    def check_completeness(self, df: pd.DataFrame, issues: List[Dict]) -> float:
        """Check if all required fields are present and populated"""
        required_fields = [
            'sample_id', 'code_snippet', 'language', 'pattern_type',
            'structural_features', 'lexical_features', 'semantic_features',
            'metric_features', 'confidence', 'labeling_method'
        ]
        
        missing_fields = [field for field in required_fields if field not in df.columns]
        if missing_fields:
            issues.append({
                'type': 'missing_fields',
                'severity': 'critical',
                'details': f"Missing required fields: {missing_fields}"
            })
            return 0.0
        
        # Check for null values
        null_counts = df[required_fields].isnull().sum()
        total_cells = len(df) * len(required_fields)
        null_ratio = null_counts.sum() / total_cells
        
        if null_ratio > 0.05:  # More than 5% nulls
            issues.append({
                'type': 'null_values',
                'severity': 'high',
                'details': f"High null ratio: {null_ratio:.2%}",
                'null_counts': null_counts.to_dict()
            })
        
        # Check for empty features
        empty_features = 0
        for idx, row in df.iterrows():
            for feature_col in ['structural_features', 'lexical_features', 'semantic_features', 'metric_features']:
                if feature_col in row and (
                    row[feature_col] is None or 
                    (isinstance(row[feature_col], list) and len(row[feature_col]) == 0)
                ):
                    empty_features += 1
        
        empty_ratio = empty_features / (len(df) * 4)
        if empty_ratio > 0.1:
            issues.append({
                'type': 'empty_features',
                'severity': 'medium',
                'details': f"Empty feature ratio: {empty_ratio:.2%}"
            })
        
        completeness = 1 - null_ratio - (empty_ratio * 0.5)
        return max(0, completeness)
    
    def check_label_consistency(self, df: pd.DataFrame, issues: List[Dict]) -> float:
        """Check if similar code samples have consistent labels"""
        if 'code_snippet' not in df or 'pattern_type' not in df:
            return 1.0
        
        # Group by code similarity
        similar_groups = self.group_by_similarity(df)
        
        inconsistent_groups = 0
        total_groups = len(similar_groups)
        
        for group in similar_groups:
            if len(group) < 2:
                continue
                
            labels = group['pattern_type'].unique()
            if len(labels) > 1:
                inconsistent_groups += 1
                
                # Log specific inconsistencies
                if inconsistent_groups <= 5:  # Log first 5 inconsistencies
                    issues.append({
                        'type': 'label_inconsistency',
                        'severity': 'medium',
                        'details': f"Samples {group['sample_id'].tolist()} have different labels: {labels.tolist()}"
                    })
        
        if total_groups == 0:
            return 1.0
            
        consistency = 1 - (inconsistent_groups / total_groups)
        
        if consistency < 0.9:
            issues.append({
                'type': 'low_consistency',
                'severity': 'high',
                'details': f"Label consistency: {consistency:.2%}"
            })
        
        return consistency
    
    def group_by_similarity(self, df: pd.DataFrame, threshold: float = 0.85) -> List[pd.DataFrame]:
        """Group samples by code similarity"""
        groups = []
        processed = set()
        
        for idx1, row1 in df.iterrows():
            if idx1 in processed:
                continue
                
            group = [row1]
            processed.add(idx1)
            
            for idx2, row2 in df.iterrows():
                if idx2 <= idx1 or idx2 in processed:
                    continue
                    
                similarity = self.calculate_code_similarity(
                    row1['code_snippet'], 
                    row2['code_snippet']
                )
                
                if similarity >= threshold:
                    group.append(row2)
                    processed.add(idx2)
            
            if len(group) > 1:
                groups.append(pd.DataFrame(group))
        
        return groups
    
    def calculate_code_similarity(self, code1: str, code2: str) -> float:
        """Calculate similarity between two code snippets"""
        # Normalize code (remove whitespace, comments)
        norm1 = self.normalize_code(code1)
        norm2 = self.normalize_code(code2)
        
        # Use Jaccard similarity on tokens
        tokens1 = set(norm1.split())
        tokens2 = set(norm2.split())
        
        if not tokens1 or not tokens2:
            return 0.0
            
        intersection = tokens1 & tokens2
        union = tokens1 | tokens2
        
        return len(intersection) / len(union)
    
    def normalize_code(self, code: str) -> str:
        """Normalize code for comparison"""
        # Remove comments
        code = re.sub(r'#.*', '', code)  # Python comments
        code = re.sub(r'//.*', '', code)  # C-style comments
        code = re.sub(r'/\*.*?\*/', '', code, flags=re.DOTALL)  # Multi-line comments
        
        # Remove extra whitespace
        code = ' '.join(code.split())
        
        # Convert to lowercase
        code = code.lower()
        
        return code
    
    def validate_against_golden(self, df: pd.DataFrame, issues: List[Dict]) -> float:
        """Validate against golden dataset if available"""
        if self.golden_dataset is None:
            return 1.0  # No golden dataset, assume accurate
        
        # Find matching samples
        matches = 0
        correct = 0
        
        for _, row in df.iterrows():
            golden_match = self.find_golden_match(row)
            if golden_match is not None:
                matches += 1
                if row['pattern_type'] == golden_match['pattern_type']:
                    correct += 1
        
        if matches == 0:
            return 1.0
            
        accuracy = correct / matches
        
        if accuracy < 0.9:
            issues.append({
                'type': 'low_accuracy',
                'severity': 'high',
                'details': f"Accuracy against golden dataset: {accuracy:.2%}"
            })
        
        return accuracy
    
    def find_golden_match(self, sample: pd.Series) -> Optional[pd.Series]:
        """Find matching sample in golden dataset"""
        if self.golden_dataset is None:
            return None
        
        # Try exact match on sample_id
        if 'sample_id' in sample:
            matches = self.golden_dataset[
                self.golden_dataset['sample_id'] == sample['sample_id']
            ]
            if not matches.empty:
                return matches.iloc[0]
        
        # Try similarity match on code
        if 'code_snippet' in sample:
            for _, golden_row in self.golden_dataset.iterrows():
                similarity = self.calculate_code_similarity(
                    sample['code_snippet'],
                    golden_row['code_snippet']
                )
                if similarity > 0.95:
                    return golden_row
        
        return None
    
    def check_uniqueness(self, df: pd.DataFrame, issues: List[Dict]) -> float:
        """Check for duplicate samples"""
        # Check exact duplicates
        duplicates = df.duplicated(subset=['code_snippet'], keep=False)
        duplicate_count = duplicates.sum()
        
        if duplicate_count > 0:
            duplicate_ratio = duplicate_count / len(df)
            issues.append({
                'type': 'duplicates',
                'severity': 'medium',
                'details': f"Found {duplicate_count} duplicate samples ({duplicate_ratio:.2%})"
            })
            
            # Log some duplicate examples
            duplicate_samples = df[duplicates].head(10)
            issues.append({
                'type': 'duplicate_examples',
                'severity': 'low',
                'details': f"Example duplicate IDs: {duplicate_samples['sample_id'].tolist()}"
            })
        
        # Check near-duplicates
        near_duplicates = self.find_near_duplicates(df)
        near_duplicate_ratio = len(near_duplicates) / len(df)
        
        if near_duplicate_ratio > 0.1:
            issues.append({
                'type': 'near_duplicates',
                'severity': 'medium',
                'details': f"High near-duplicate ratio: {near_duplicate_ratio:.2%}"
            })
        
        uniqueness = 1 - duplicate_count / len(df) - (near_duplicate_ratio * 0.5)
        return max(0, uniqueness)
    
    def find_near_duplicates(self, df: pd.DataFrame, threshold: float = 0.95) -> List[Tuple[int, int]]:
        """Find near-duplicate samples"""
        near_duplicates = []
        
        # Sample for efficiency on large datasets
        sample_size = min(1000, len(df))
        sample_df = df.sample(n=sample_size) if len(df) > sample_size else df
        
        for idx1, row1 in sample_df.iterrows():
            for idx2, row2 in sample_df.iterrows():
                if idx2 <= idx1:
                    continue
                    
                similarity = self.calculate_code_similarity(
                    row1['code_snippet'],
                    row2['code_snippet']
                )
                
                if similarity >= threshold:
                    near_duplicates.append((idx1, idx2))
        
        return near_duplicates
    
    def check_validity(self, df: pd.DataFrame, issues: List[Dict]) -> float:
        """Check data validity (format, ranges, etc.)"""
        validity_score = 1.0
        
        # Check feature dimensions
        feature_cols = ['structural_features', 'lexical_features', 'semantic_features', 'metric_features']
        expected_dims = {'structural_features': 50, 'lexical_features': 30, 'semantic_features': 20, 'metric_features': 20}
        
        for col in feature_cols:
            if col not in df:
                continue
                
            # Check if all features have correct dimensions
            wrong_dims = 0
            for features in df[col]:
                if isinstance(features, list) and len(features) != expected_dims[col]:
                    wrong_dims += 1
            
            if wrong_dims > 0:
                dim_ratio = wrong_dims / len(df)
                validity_score -= dim_ratio * 0.25
                issues.append({
                    'type': 'wrong_dimensions',
                    'severity': 'medium',
                    'details': f"{col} has {wrong_dims} samples with wrong dimensions"
                })
        
        # Check confidence scores
        if 'confidence' in df:
            invalid_confidence = df[(df['confidence'] < 0) | (df['confidence'] > 1)]
            if not invalid_confidence.empty:
                validity_score -= 0.1
                issues.append({
                    'type': 'invalid_confidence',
                    'severity': 'high',
                    'details': f"{len(invalid_confidence)} samples with invalid confidence scores"
                })
        
        # Check pattern types
        if 'pattern_type' in df:
            valid_patterns = {
                'singleton', 'factory', 'observer', 'strategy', 'decorator',
                'adapter', 'template', 'builder', 'prototype', 'facade',
                'long_method', 'god_class', 'duplicate_code', 'feature_envy',
                'sql_injection', 'xss', 'hardcoded_secret', 'n_plus_one',
                'memory_leak', 'inefficient_loop', 'unknown'
            }
            
            invalid_patterns = df[~df['pattern_type'].isin(valid_patterns)]
            if not invalid_patterns.empty:
                validity_score -= 0.2
                issues.append({
                    'type': 'invalid_patterns',
                    'severity': 'critical',
                    'details': f"Invalid pattern types: {invalid_patterns['pattern_type'].unique().tolist()}"
                })
        
        # Check language validity
        if 'language' in df:
            valid_languages = {'python', 'java', 'javascript', 'typescript', 'go', 'rust', 'cpp', 'c', 'csharp'}
            invalid_languages = df[~df['language'].isin(valid_languages)]
            if not invalid_languages.empty:
                validity_score -= 0.1
                issues.append({
                    'type': 'invalid_languages',
                    'severity': 'medium',
                    'details': f"Invalid languages: {invalid_languages['language'].unique().tolist()}"
                })
        
        return max(0, validity_score)
    
    def check_class_balance(self, df: pd.DataFrame, issues: List[Dict], recommendations: List[str]) -> Dict[str, float]:
        """Check class distribution balance"""
        if 'pattern_type' not in df:
            return {}
        
        # Calculate class distribution
        class_counts = df['pattern_type'].value_counts()
        total_samples = len(df)
        class_ratios = (class_counts / total_samples).to_dict()
        
        # Check for imbalanced classes
        min_ratio = min(class_ratios.values())
        max_ratio = max(class_ratios.values())
        
        if max_ratio / min_ratio > 10:  # More than 10x difference
            issues.append({
                'type': 'class_imbalance',
                'severity': 'high',
                'details': f"Severe class imbalance - ratio: {max_ratio/min_ratio:.1f}x",
                'distribution': class_ratios
            })
            
            # Add recommendations
            underrepresented = [cls for cls, ratio in class_ratios.items() if ratio < 0.05]
            if underrepresented:
                recommendations.append(
                    f"Consider augmenting underrepresented classes: {underrepresented}"
                )
            
            overrepresented = [cls for cls, ratio in class_ratios.items() if ratio > 0.3]
            if overrepresented:
                recommendations.append(
                    f"Consider downsampling overrepresented classes: {overrepresented}"
                )
        
        return class_ratios
    
    def check_feature_coverage(self, df: pd.DataFrame, issues: List[Dict]) -> Dict[str, float]:
        """Check feature value coverage and distribution"""
        coverage = {}
        
        feature_cols = ['structural_features', 'lexical_features', 'semantic_features', 'metric_features']
        
        for col in feature_cols:
            if col not in df:
                coverage[col] = 0.0
                continue
            
            # Calculate feature statistics
            all_features = []
            for features in df[col]:
                if isinstance(features, list):
                    all_features.extend(features)
            
            if not all_features:
                coverage[col] = 0.0
                continue
            
            # Check for zero variance features
            feature_array = np.array(all_features).reshape(-1, len(df[col].iloc[0]))
            zero_variance = np.var(feature_array, axis=0) == 0
            zero_variance_ratio = zero_variance.sum() / len(zero_variance)
            
            if zero_variance_ratio > 0.1:
                issues.append({
                    'type': 'zero_variance_features',
                    'severity': 'medium',
                    'details': f"{col} has {zero_variance_ratio:.2%} zero-variance features"
                })
            
            # Check for feature range
            feature_range = np.ptp(feature_array, axis=0)
            narrow_range = (feature_range < 0.01).sum() / len(feature_range)
            
            if narrow_range > 0.2:
                issues.append({
                    'type': 'narrow_feature_range',
                    'severity': 'low',
                    'details': f"{col} has {narrow_range:.2%} features with narrow range"
                })
            
            coverage[col] = 1 - zero_variance_ratio - (narrow_range * 0.5)
        
        return coverage
    
    def check_label_confidence(self, df: pd.DataFrame, issues: List[Dict]) -> float:
        """Check label confidence distribution"""
        if 'confidence' not in df:
            return 1.0
        
        # Calculate confidence statistics
        mean_confidence = df['confidence'].mean()
        low_confidence = (df['confidence'] < 0.5).sum()
        low_confidence_ratio = low_confidence / len(df)
        
        if mean_confidence < 0.7:
            issues.append({
                'type': 'low_confidence',
                'severity': 'high',
                'details': f"Low average confidence: {mean_confidence:.3f}"
            })
        
        if low_confidence_ratio > 0.2:
            issues.append({
                'type': 'many_low_confidence',
                'severity': 'medium',
                'details': f"{low_confidence_ratio:.2%} samples have confidence < 0.5"
            })
        
        # Check confidence by pattern type
        if 'pattern_type' in df:
            confidence_by_pattern = df.groupby('pattern_type')['confidence'].agg(['mean', 'count'])
            
            low_confidence_patterns = confidence_by_pattern[confidence_by_pattern['mean'] < 0.6]
            if not low_confidence_patterns.empty:
                issues.append({
                    'type': 'pattern_low_confidence',
                    'severity': 'medium',
                    'details': f"Patterns with low confidence: {low_confidence_patterns.to_dict()}"
                })
        
        return mean_confidence
    
    def _log_validation_results(self, report: QualityReport):
        """Log validation results"""
        logger.info(f"\nData Quality Validation Report")
        logger.info(f"{'='*50}")
        logger.info(f"Overall Quality Score: {report.metrics.overall_score:.3f}")
        logger.info(f"Status: {'PASSED' if report.passed else 'FAILED'}")
        
        logger.info(f"\nDetailed Metrics:")
        logger.info(f"  Completeness: {report.metrics.completeness:.3f}")
        logger.info(f"  Consistency: {report.metrics.consistency:.3f}")
        logger.info(f"  Accuracy: {report.metrics.accuracy:.3f}")
        logger.info(f"  Uniqueness: {report.metrics.uniqueness:.3f}")
        logger.info(f"  Validity: {report.metrics.validity:.3f}")
        logger.info(f"  Label Confidence: {report.metrics.label_confidence:.3f}")
        
        if report.issues:
            logger.info(f"\nIssues Found: {len(report.issues)}")
            for issue in report.issues[:5]:  # Show first 5 issues
                logger.info(f"  - [{issue['severity']}] {issue['type']}: {issue['details']}")
        
        if report.recommendations:
            logger.info(f"\nRecommendations:")
            for rec in report.recommendations:
                logger.info(f"  - {rec}")
    
    def set_golden_dataset(self, golden_df: pd.DataFrame):
        """Set golden dataset for accuracy validation"""
        self.golden_dataset = golden_df
        logger.info(f"Golden dataset set with {len(golden_df)} samples")
    
    def export_report(self, report: QualityReport, output_path: str):
        """Export validation report to file"""
        report_dict = {
            'validation_timestamp': report.validation_timestamp.isoformat(),
            'dataset_info': report.dataset_info,
            'passed': report.passed,
            'metrics': {
                'overall_score': report.metrics.overall_score,
                'completeness': report.metrics.completeness,
                'consistency': report.metrics.consistency,
                'accuracy': report.metrics.accuracy,
                'uniqueness': report.metrics.uniqueness,
                'validity': report.metrics.validity,
                'class_balance': report.metrics.class_balance,
                'feature_coverage': report.metrics.feature_coverage,
                'label_confidence': report.metrics.label_confidence
            },
            'issues': report.issues,
            'recommendations': report.recommendations
        }
        
        with open(output_path, 'w') as f:
            json.dump(report_dict, f, indent=2)
        
        logger.info(f"Report exported to {output_path}")


def main():
    """Example usage of data quality validator"""
    # Create sample dataset
    sample_data = {
        'sample_id': ['s1', 's2', 's3', 's4', 's5'],
        'code_snippet': [
            'class Singleton: pass',
            'def factory(): return Product()',
            'for i in range(10): query(i)',
            'class Singleton: pass',  # Duplicate
            'SELECT * FROM users WHERE id = ' + str(id)  # SQL injection
        ],
        'language': ['python', 'python', 'python', 'python', 'python'],
        'pattern_type': ['singleton', 'factory', 'n_plus_one', 'singleton', 'sql_injection'],
        'structural_features': [[0.1] * 50] * 5,
        'lexical_features': [[0.2] * 30] * 5,
        'semantic_features': [[0.3] * 20] * 5,
        'metric_features': [[0.4] * 20] * 5,
        'confidence': [0.9, 0.8, 0.7, 0.9, 0.95],
        'labeling_method': ['heuristic'] * 5,
        'created_at': [datetime.now()] * 5
    }
    
    df = pd.DataFrame(sample_data)
    
    # Initialize validator
    validator = DataQualityValidator(min_quality_threshold=0.8)
    
    # Validate dataset
    report = validator.validate_dataset(df)
    
    # Export report
    validator.export_report(report, 'quality_report.json')


if __name__ == '__main__':
    main()