-- BigQuery Schema for CCL ML Training Data
-- This schema stores training data for pattern detection ML models
-- Optimized for both training efficiency and cost management

-- ============================================================================
-- MAIN TRAINING DATA TABLE
-- ============================================================================

-- Primary table for storing all code pattern training data
CREATE OR REPLACE TABLE `ccl-ml.training_data.code_patterns` (
  -- Identifiers
  sample_id STRING NOT NULL OPTIONS(description="Unique identifier for the code sample"),
  source_repo STRING OPTIONS(description="Repository name or synthetic source identifier"),
  file_path STRING OPTIONS(description="Path to the file within the repository"),
  
  -- Code content
  code_snippet STRING OPTIONS(description="Actual code content (truncated to 10KB for efficiency)"),
  ast_json JSON OPTIONS(description="Abstract Syntax Tree representation as JSON"),
  language STRING NOT NULL OPTIONS(description="Programming language (python, java, javascript, etc.)"),
  
  -- Feature vectors (extracted by ML pipeline)
  structural_features ARRAY<FLOAT64> OPTIONS(description="AST-based structural features (50 dimensions)"),
  lexical_features ARRAY<FLOAT64> OPTIONS(description="Token and naming features (30 dimensions)"),
  semantic_features ARRAY<FLOAT64> OPTIONS(description="Data/control flow features (20 dimensions)"),
  metric_features ARRAY<FLOAT64> OPTIONS(description="Code quality metrics (20 dimensions)"),
  
  -- Labels and annotations
  pattern_category STRING OPTIONS(description="High-level category: design_pattern, anti_pattern, security, performance, code_smell"),
  pattern_type STRING OPTIONS(description="Specific pattern: singleton, factory, sql_injection, n_plus_one, etc."),
  confidence FLOAT64 OPTIONS(description="Labeling confidence score (0.0-1.0)"),
  labeling_method STRING OPTIONS(description="How label was assigned: heuristic, weak_supervision, manual, model"),
  
  -- Metadata
  source_type STRING OPTIONS(description="Data source: github, synthetic, community, enterprise"),
  license STRING OPTIONS(description="Code license type"),
  stars INT64 OPTIONS(description="Repository stars (quality indicator)"),
  
  -- Quality and validation
  quality_score FLOAT64 OPTIONS(description="Overall data quality score (0.0-1.0)"),
  validation_status STRING OPTIONS(description="Quality validation status: pending, validated, low_quality, rejected"),
  validation_issues ARRAY<STRING> OPTIONS(description="List of quality issues if any"),
  
  -- Timestamps
  created_at TIMESTAMP NOT NULL OPTIONS(description="When the sample was added to training data"),
  updated_at TIMESTAMP NOT NULL OPTIONS(description="Last modification timestamp"),
  validated_at TIMESTAMP OPTIONS(description="When quality validation was performed"),
  
  -- Version tracking
  version STRING OPTIONS(description="Data schema version for compatibility"),
  pipeline_version STRING OPTIONS(description="Version of the processing pipeline used")
)
PARTITION BY DATE(created_at)
CLUSTER BY pattern_type, language, validation_status
OPTIONS(
  description="Main training data table for pattern detection ML models",
  partition_expiration_days=730,  -- 2 years retention
  require_partition_filter=false,  -- Allow full table scans for training
  labels=[("team", "ml"), ("dataset", "training"), ("cost-center", "engineering")]
);

-- ============================================================================
-- PATTERN FEATURES AGGREGATION TABLE
-- ============================================================================

-- Aggregated feature statistics for pattern analysis
CREATE OR REPLACE TABLE `ccl-ml.training_data.pattern_features` (
  -- Pattern identification
  pattern_type STRING NOT NULL,
  language STRING NOT NULL,
  
  -- Feature statistics
  feature_means ARRAY<FLOAT64> OPTIONS(description="Mean values for each feature dimension"),
  feature_stds ARRAY<FLOAT64> OPTIONS(description="Standard deviations for each feature dimension"),
  feature_mins ARRAY<FLOAT64> OPTIONS(description="Minimum values for each feature dimension"),
  feature_maxs ARRAY<FLOAT64> OPTIONS(description="Maximum values for each feature dimension"),
  
  -- Pattern statistics
  sample_count INT64 OPTIONS(description="Number of samples for this pattern/language combination"),
  avg_confidence FLOAT64 OPTIONS(description="Average labeling confidence"),
  quality_distribution STRUCT<
    high FLOAT64,
    medium FLOAT64,
    low FLOAT64
  > OPTIONS(description="Distribution of quality scores"),
  
  -- Computed metrics
  pattern_prototype ARRAY<FLOAT64> OPTIONS(description="Centroid/prototype vector for the pattern"),
  intra_pattern_variance FLOAT64 OPTIONS(description="Variance within pattern samples"),
  inter_pattern_distance FLOAT64 OPTIONS(description="Average distance to other patterns"),
  
  -- Metadata
  last_updated TIMESTAMP NOT NULL,
  computation_version STRING
)
PARTITION BY DATE(last_updated)
CLUSTER BY pattern_type, language
OPTIONS(
  description="Aggregated feature statistics for pattern analysis and model optimization",
  partition_expiration_days=365  -- 1 year retention
);

-- ============================================================================
-- LABELING HISTORY TABLE
-- ============================================================================

-- Track labeling changes and confidence evolution
CREATE OR REPLACE TABLE `ccl-ml.training_data.labeling_history` (
  -- Reference to main sample
  sample_id STRING NOT NULL,
  
  -- Label change information
  change_id STRING NOT NULL,
  previous_label STRING,
  new_label STRING,
  previous_confidence FLOAT64,
  new_confidence FLOAT64,
  
  -- Change metadata
  change_type STRING OPTIONS(description="correction, validation, model_update, manual_review"),
  change_reason STRING,
  changed_by STRING OPTIONS(description="user_id, model_id, or system"),
  changed_at TIMESTAMP NOT NULL,
  
  -- Model information if applicable
  model_version STRING,
  model_type STRING,
  model_confidence_delta FLOAT64
)
PARTITION BY DATE(changed_at)
CLUSTER BY sample_id, change_type
OPTIONS(
  description="Audit trail of label changes for training data quality tracking",
  partition_expiration_days=180  -- 6 months retention
);

-- ============================================================================
-- MODEL TRAINING DATASETS TABLE
-- ============================================================================

-- Versioned datasets for reproducible model training
CREATE OR REPLACE TABLE `ccl-ml.training_data.training_datasets` (
  -- Dataset identification
  dataset_id STRING NOT NULL,
  dataset_name STRING NOT NULL,
  dataset_version STRING NOT NULL,
  
  -- Dataset composition
  total_samples INT64,
  pattern_distribution JSON OPTIONS(description="Count of samples per pattern type"),
  language_distribution JSON OPTIONS(description="Count of samples per language"),
  quality_threshold FLOAT64 OPTIONS(description="Minimum quality score for inclusion"),
  
  -- Split information
  train_samples INT64,
  validation_samples INT64,
  test_samples INT64,
  split_method STRING OPTIONS(description="random, stratified, temporal"),
  split_seed INT64,
  
  -- Sample references
  sample_ids ARRAY<STRING> OPTIONS(description="All sample IDs in this dataset"),
  train_ids ARRAY<STRING>,
  validation_ids ARRAY<STRING>,
  test_ids ARRAY<STRING>,
  
  -- Dataset quality metrics
  avg_quality_score FLOAT64,
  avg_confidence_score FLOAT64,
  class_balance_score FLOAT64,
  
  -- Metadata
  created_at TIMESTAMP NOT NULL,
  created_by STRING,
  description STRING,
  tags ARRAY<STRING>,
  is_active BOOL OPTIONS(description="Whether this dataset is currently used for training")
)
PARTITION BY DATE(created_at)
CLUSTER BY dataset_name, dataset_version
OPTIONS(
  description="Versioned training datasets for model reproducibility",
  partition_expiration_days=1095  -- 3 years retention
);

-- ============================================================================
-- SYNTHETIC DATA GENERATION TABLE
-- ============================================================================

-- Track synthetic data generation for augmentation
CREATE OR REPLACE TABLE `ccl-ml.training_data.synthetic_generation` (
  -- Generation run information
  generation_id STRING NOT NULL,
  generation_timestamp TIMESTAMP NOT NULL,
  
  -- Generation parameters
  base_pattern_type STRING NOT NULL,
  target_language STRING NOT NULL,
  generation_method STRING OPTIONS(description="template_variation, ast_mutation, style_transfer"),
  variation_parameters JSON,
  
  -- Generation results
  samples_generated INT64,
  samples_validated INT64,
  samples_accepted INT64,
  quality_scores ARRAY<FLOAT64>,
  
  -- Source information
  source_samples ARRAY<STRING> OPTIONS(description="Sample IDs used as base for generation"),
  generator_version STRING,
  random_seed INT64
)
PARTITION BY DATE(generation_timestamp)
CLUSTER BY base_pattern_type, target_language
OPTIONS(
  description="Synthetic data generation tracking for augmentation",
  partition_expiration_days=365
);

-- ============================================================================
-- DATA QUALITY METRICS TABLE
-- ============================================================================

-- Daily data quality metrics for monitoring
CREATE OR REPLACE TABLE `ccl-ml.training_data.quality_metrics` (
  -- Time dimension
  metric_date DATE NOT NULL,
  
  -- Volume metrics
  total_samples INT64,
  new_samples_added INT64,
  samples_removed INT64,
  samples_updated INT64,
  
  -- Quality metrics
  avg_quality_score FLOAT64,
  high_quality_ratio FLOAT64,
  validation_pass_rate FLOAT64,
  
  -- Coverage metrics
  patterns_covered INT64,
  languages_covered INT64,
  avg_samples_per_pattern FLOAT64,
  min_samples_per_pattern INT64,
  underrepresented_patterns ARRAY<STRING>,
  
  -- Labeling metrics
  avg_labeling_confidence FLOAT64,
  manual_labels_ratio FLOAT64,
  weak_supervision_ratio FLOAT64,
  label_agreement_score FLOAT64,
  
  -- Data diversity metrics
  code_duplication_ratio FLOAT64,
  feature_diversity_score FLOAT64,
  repository_diversity INT64,
  
  -- Processing metrics
  avg_processing_time_ms FLOAT64,
  failed_processing_count INT64,
  
  -- Metadata
  computed_at TIMESTAMP NOT NULL
)
PARTITION BY metric_date
OPTIONS(
  description="Daily data quality metrics for monitoring and alerting",
  partition_expiration_days=90
);

-- ============================================================================
-- VIEWS FOR TRAINING
-- ============================================================================

-- High-quality patterns view for model training
CREATE OR REPLACE VIEW `ccl-ml.training_data.high_quality_patterns` AS
SELECT 
  sample_id,
  source_repo,
  file_path,
  code_snippet,
  language,
  structural_features,
  lexical_features,
  semantic_features,
  metric_features,
  pattern_category,
  pattern_type,
  confidence,
  labeling_method,
  quality_score,
  created_at
FROM `ccl-ml.training_data.code_patterns`
WHERE quality_score >= 0.8
  AND validation_status = 'validated'
  AND confidence >= 0.7;

-- Language-specific views for targeted training
CREATE OR REPLACE VIEW `ccl-ml.training_data.python_patterns` AS
SELECT * FROM `ccl-ml.training_data.code_patterns`
WHERE language = 'python' 
  AND validation_status = 'validated';

CREATE OR REPLACE VIEW `ccl-ml.training_data.java_patterns` AS
SELECT * FROM `ccl-ml.training_data.code_patterns`
WHERE language = 'java' 
  AND validation_status = 'validated';

CREATE OR REPLACE VIEW `ccl-ml.training_data.javascript_patterns` AS
SELECT * FROM `ccl-ml.training_data.code_patterns`
WHERE language = 'javascript' 
  AND validation_status = 'validated';

-- Pattern type views for specialized models
CREATE OR REPLACE VIEW `ccl-ml.training_data.design_patterns` AS
SELECT * FROM `ccl-ml.training_data.code_patterns`
WHERE pattern_category = 'design_pattern'
  AND validation_status = 'validated';

CREATE OR REPLACE VIEW `ccl-ml.training_data.security_patterns` AS
SELECT * FROM `ccl-ml.training_data.code_patterns`
WHERE pattern_category = 'security'
  AND validation_status = 'validated';

CREATE OR REPLACE VIEW `ccl-ml.training_data.performance_patterns` AS
SELECT * FROM `ccl-ml.training_data.code_patterns`
WHERE pattern_category = 'performance'
  AND validation_status = 'validated';

-- Balanced dataset view for training
CREATE OR REPLACE VIEW `ccl-ml.training_data.balanced_training_set` AS
WITH pattern_counts AS (
  SELECT 
    pattern_type,
    COUNT(*) as sample_count
  FROM `ccl-ml.training_data.high_quality_patterns`
  GROUP BY pattern_type
),
min_count AS (
  SELECT MIN(sample_count) as min_samples
  FROM pattern_counts
),
balanced_samples AS (
  SELECT 
    *,
    ROW_NUMBER() OVER (PARTITION BY pattern_type ORDER BY RAND()) as rn
  FROM `ccl-ml.training_data.high_quality_patterns`
)
SELECT * EXCEPT(rn)
FROM balanced_samples
WHERE rn <= (SELECT min_samples FROM min_count);

-- Recent high-confidence patterns for active learning
CREATE OR REPLACE VIEW `ccl-ml.training_data.active_learning_candidates` AS
SELECT *
FROM `ccl-ml.training_data.code_patterns`
WHERE DATE(created_at) >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
  AND confidence BETWEEN 0.5 AND 0.8  -- Uncertain predictions
  AND validation_status = 'pending'
ORDER BY confidence ASC
LIMIT 1000;

-- ============================================================================
-- MATERIALIZED VIEWS FOR PERFORMANCE
-- ============================================================================

-- Pattern statistics materialized view
CREATE MATERIALIZED VIEW `ccl-ml.training_data.pattern_statistics` AS
SELECT
  pattern_type,
  language,
  COUNT(*) as sample_count,
  AVG(confidence) as avg_confidence,
  AVG(quality_score) as avg_quality,
  COUNTIF(labeling_method = 'manual') as manual_labels,
  COUNTIF(labeling_method = 'weak_supervision') as weak_labels,
  COUNTIF(labeling_method = 'model') as model_labels,
  MIN(created_at) as first_sample_date,
  MAX(created_at) as last_sample_date
FROM `ccl-ml.training_data.code_patterns`
WHERE validation_status = 'validated'
GROUP BY pattern_type, language
OPTIONS(
  enable_refresh = true,
  refresh_interval_minutes = 360,  -- Refresh every 6 hours
  description = "Aggregated pattern statistics for quick analysis"
);

-- ============================================================================
-- STORED PROCEDURES FOR DATA MANAGEMENT
-- ============================================================================

-- Procedure to create a new training dataset version
CREATE OR REPLACE PROCEDURE `ccl-ml.training_data.create_training_dataset`(
  dataset_name STRING,
  quality_threshold FLOAT64,
  split_ratios STRUCT<train FLOAT64, validation FLOAT64, test FLOAT64>,
  OUT dataset_id STRING
)
BEGIN
  DECLARE total_samples INT64;
  DECLARE dataset_version STRING;
  
  -- Generate version based on timestamp
  SET dataset_version = FORMAT_TIMESTAMP('%Y%m%d_%H%M%S', CURRENT_TIMESTAMP());
  SET dataset_id = CONCAT(dataset_name, '_', dataset_version);
  
  -- Create temporary table with filtered samples
  CREATE TEMP TABLE filtered_samples AS
  SELECT sample_id, pattern_type, language
  FROM `ccl-ml.training_data.code_patterns`
  WHERE quality_score >= quality_threshold
    AND validation_status = 'validated';
  
  SET total_samples = (SELECT COUNT(*) FROM filtered_samples);
  
  -- Perform stratified split
  CREATE TEMP TABLE split_samples AS
  SELECT 
    sample_id,
    pattern_type,
    language,
    CASE 
      WHEN rand_val < split_ratios.train THEN 'train'
      WHEN rand_val < split_ratios.train + split_ratios.validation THEN 'validation'
      ELSE 'test'
    END as split
  FROM (
    SELECT *, RAND() as rand_val
    FROM filtered_samples
  );
  
  -- Insert into training datasets table
  INSERT INTO `ccl-ml.training_data.training_datasets`
  SELECT
    dataset_id,
    dataset_name,
    dataset_version,
    total_samples,
    TO_JSON(
      (SELECT AS STRUCT pattern_type, COUNT(*) as count 
       FROM filtered_samples 
       GROUP BY pattern_type)
    ),
    TO_JSON(
      (SELECT AS STRUCT language, COUNT(*) as count 
       FROM filtered_samples 
       GROUP BY language)
    ),
    quality_threshold,
    (SELECT COUNT(*) FROM split_samples WHERE split = 'train'),
    (SELECT COUNT(*) FROM split_samples WHERE split = 'validation'),
    (SELECT COUNT(*) FROM split_samples WHERE split = 'test'),
    'stratified',
    CAST(RAND() * 1000000 AS INT64),
    ARRAY(SELECT sample_id FROM filtered_samples),
    ARRAY(SELECT sample_id FROM split_samples WHERE split = 'train'),
    ARRAY(SELECT sample_id FROM split_samples WHERE split = 'validation'),
    ARRAY(SELECT sample_id FROM split_samples WHERE split = 'test'),
    (SELECT AVG(quality_score) FROM `ccl-ml.training_data.code_patterns` 
     WHERE sample_id IN (SELECT sample_id FROM filtered_samples)),
    (SELECT AVG(confidence) FROM `ccl-ml.training_data.code_patterns` 
     WHERE sample_id IN (SELECT sample_id FROM filtered_samples)),
    0.0,  -- Class balance score to be calculated separately
    CURRENT_TIMESTAMP(),
    SESSION_USER(),
    CONCAT('Training dataset created with quality threshold ', CAST(quality_threshold AS STRING)),
    ['ml_training', 'pattern_detection'],
    TRUE;
    
  -- Clean up temp tables
  DROP TABLE filtered_samples;
  DROP TABLE split_samples;
END;

-- ============================================================================
-- INDEXES FOR QUERY OPTIMIZATION
-- ============================================================================

-- Note: BigQuery doesn't support traditional indexes, but clustering provides similar benefits
-- The clustering keys defined above optimize for common query patterns:
-- 1. pattern_type, language, validation_status - for filtered training data retrieval
-- 2. sample_id, change_type - for tracking label evolution
-- 3. dataset_name, dataset_version - for dataset management

-- ============================================================================
-- DATA LIFECYCLE POLICIES
-- ============================================================================

-- Set up scheduled queries for data maintenance
-- 1. Daily quality metrics computation
-- 2. Weekly pattern statistics refresh
-- 3. Monthly data cleanup for low-quality samples
-- 4. Quarterly archival of old training datasets

-- Example scheduled query for daily metrics
CREATE OR REPLACE SCHEDULED QUERY `ccl-ml.training_data.daily_quality_metrics`
OPTIONS (
  query="""
    INSERT INTO `ccl-ml.training_data.quality_metrics`
    SELECT
      CURRENT_DATE() as metric_date,
      COUNT(*) as total_samples,
      COUNTIF(DATE(created_at) = CURRENT_DATE()) as new_samples_added,
      0 as samples_removed,  -- To be implemented with deletion tracking
      COUNTIF(DATE(updated_at) = CURRENT_DATE() AND created_at < updated_at) as samples_updated,
      AVG(quality_score) as avg_quality_score,
      COUNTIF(quality_score >= 0.8) / COUNT(*) as high_quality_ratio,
      COUNTIF(validation_status = 'validated') / COUNT(*) as validation_pass_rate,
      COUNT(DISTINCT pattern_type) as patterns_covered,
      COUNT(DISTINCT language) as languages_covered,
      COUNT(*) / COUNT(DISTINCT pattern_type) as avg_samples_per_pattern,
      MIN(pattern_count) as min_samples_per_pattern,
      ARRAY(
        SELECT pattern_type 
        FROM (
          SELECT pattern_type, COUNT(*) as pattern_count
          FROM `ccl-ml.training_data.code_patterns`
          WHERE validation_status = 'validated'
          GROUP BY pattern_type
          HAVING pattern_count < 100
          ORDER BY pattern_count
          LIMIT 10
        )
      ) as underrepresented_patterns,
      AVG(confidence) as avg_labeling_confidence,
      COUNTIF(labeling_method = 'manual') / COUNT(*) as manual_labels_ratio,
      COUNTIF(labeling_method = 'weak_supervision') / COUNT(*) as weak_supervision_ratio,
      0.0 as label_agreement_score,  -- To be calculated from labeling history
      0.0 as code_duplication_ratio,  -- To be calculated with similarity hashing
      0.0 as feature_diversity_score,  -- To be calculated from feature distributions
      COUNT(DISTINCT source_repo) as repository_diversity,
      0.0 as avg_processing_time_ms,  -- To be tracked in pipeline
      0 as failed_processing_count,  -- To be tracked in pipeline
      CURRENT_TIMESTAMP() as computed_at
    FROM `ccl-ml.training_data.code_patterns`
    WHERE DATE(created_at) <= CURRENT_DATE()
  """,
  schedule="every day 02:00",
  time_zone="UTC"
);