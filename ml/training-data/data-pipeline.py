"""
Training Data Pipeline for Pattern Detection
Processes raw code into ML-ready features using Apache Beam
"""
import apache_beam as beam
from apache_beam.options.pipeline_options import PipelineOptions
from apache_beam.io.gcp.internal.clients import bigquery
import tensorflow as tf
from typing import Dict, List, Tuple, Optional, Any
import json
import hashlib
import ast
import re
from datetime import datetime
from dataclasses import dataclass, asdict
import numpy as np
from sklearn.preprocessing import StandardScaler
import logging
import subprocess
import os
import tempfile
try:
    import tree_sitter
    from tree_sitter import Language, Parser
    TREE_SITTER_AVAILABLE = True
except ImportError:
    TREE_SITTER_AVAILABLE = False
    logger.warning("tree-sitter not available, falling back to simple parsing")

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class CodeSample:
    """Raw code sample with metadata"""
    sample_id: str
    repository_id: str
    file_path: str
    code_content: str
    language: str
    source_type: str  # 'github', 'synthetic', 'community'
    license: str
    stars: int
    last_updated: datetime
    metadata: Dict[str, Any]


@dataclass
class ProcessedSample:
    """Processed sample ready for ML training"""
    sample_id: str
    source_repo: str
    file_path: str
    code_snippet: str
    ast_json: str
    language: str
    structural_features: List[float]
    lexical_features: List[float]
    semantic_features: List[float]
    metric_features: List[float]
    pattern_category: Optional[str] = None
    pattern_type: Optional[str] = None
    confidence: Optional[float] = None
    labeling_method: Optional[str] = None
    quality_score: float = 0.0
    validation_status: str = 'pending'
    created_at: datetime = None
    updated_at: datetime = None


class CodePatternPipeline:
    """Main pipeline for processing code into training data"""
    
    def __init__(self, project_id: str, dataset_id: str):
        self.project_id = project_id
        self.dataset_id = dataset_id
        self.table_id = f'{project_id}:{dataset_id}.training_data'
        
    def create_pipeline(self) -> beam.Pipeline:
        """Create the Apache Beam pipeline"""
        options = PipelineOptions([
            f'--project={self.project_id}',
            '--runner=DataflowRunner',
            '--region=us-central1',
            '--temp_location=gs://ccl-ml-temp/dataflow',
            '--staging_location=gs://ccl-ml-staging/dataflow',
            '--max_num_workers=50',
            '--autoscaling_algorithm=THROUGHPUT_BASED',
            '--save_main_session=True',
            '--setup_file=./setup.py'
        ])
        
        return beam.Pipeline(options=options)
    
    def run(self, source_repos: List[str]):
        """Execute the data processing pipeline"""
        with self.create_pipeline() as p:
            # Step 1: Read repository list
            repos = p | 'CreateRepos' >> beam.Create(source_repos)
            
            # Step 2: Clone and extract code files
            code_files = (
                repos 
                | 'CloneRepos' >> beam.ParDo(CloneRepository())
                | 'ExtractFiles' >> beam.FlatMap(ExtractCodeFiles())
                | 'FilterBySize' >> beam.Filter(lambda x: 50 < len(x.code_content.split('\n')) < 5000)
            )
            
            # Step 3: Parse AST
            ast_data = (
                code_files
                | 'ParseAST' >> beam.ParDo(ParseToAST())
                | 'ValidateAST' >> beam.Filter(lambda x: x.is_valid)
            )
            
            # Step 4: Extract features
            features = (
                ast_data
                | 'ExtractFeatures' >> beam.ParDo(FeatureExtractor())
                | 'NormalizeFeatures' >> beam.ParDo(FeatureNormalizer())
            )
            
            # Step 5: Apply weak labels
            weak_labels = (
                features
                | 'ApplyHeuristics' >> beam.ParDo(HeuristicLabeler())
                | 'AggregateLabels' >> beam.CombinePerKey(LabelAggregator())
            )
            
            # Step 6: Quality validation
            validated = (
                weak_labels
                | 'ValidateQuality' >> beam.ParDo(QualityValidator())
                | 'FilterLowQuality' >> beam.Filter(lambda x: x.quality_score > 0.7)
            )
            
            # Step 7: Store in BigQuery
            validated | 'WriteToBQ' >> beam.io.WriteToBigQuery(
                table=self.table_id,
                schema=self.get_schema(),
                write_disposition=beam.io.BigQueryDisposition.WRITE_APPEND,
                create_disposition=beam.io.BigQueryDisposition.CREATE_IF_NEEDED
            )
            
            # Step 8: Export to GCS for model training
            validated | 'ExportToGCS' >> beam.io.WriteToText(
                'gs://ccl-ml-training-data/processed/batch',
                file_name_suffix='.jsonl',
                shard_name_template='-SSSSS-of-NNNNN'
            )


class CloneRepository(beam.DoFn):
    """Clone repository and extract metadata"""
    
    def setup(self):
        import git
        import tempfile
        self.temp_dir = tempfile.mkdtemp()
    
    def process(self, repo_url: str) -> List[Dict[str, Any]]:
        """Clone repository and yield file information"""
        import git
        import os
        from pathlib import Path
        
        try:
            # Extract repo name from URL
            repo_name = repo_url.split('/')[-1].replace('.git', '')
            repo_path = os.path.join(self.temp_dir, repo_name)
            
            # Clone the repository
            logger.info(f"Cloning repository: {repo_url}")
            repo = git.Repo.clone_from(repo_url, repo_path, depth=1)
            
            # Get repository metadata
            repo_metadata = {
                'url': repo_url,
                'name': repo_name,
                'default_branch': repo.active_branch.name,
                'commit_hash': repo.head.commit.hexsha
            }
            
            # Find all code files
            code_extensions = {'.py', '.java', '.js', '.ts', '.go', '.rs', '.cpp', '.c', '.cs'}
            for root, dirs, files in os.walk(repo_path):
                # Skip hidden directories
                dirs[:] = [d for d in dirs if not d.startswith('.')]
                
                for file in files:
                    if any(file.endswith(ext) for ext in code_extensions):
                        file_path = os.path.join(root, file)
                        relative_path = os.path.relpath(file_path, repo_path)
                        
                        yield {
                            'repo_metadata': repo_metadata,
                            'file_path': relative_path,
                            'full_path': file_path
                        }
                        
        except Exception as e:
            logger.error(f"Error cloning {repo_url}: {str(e)}")
            
    def teardown(self):
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)


class ExtractCodeFiles(beam.DoFn):
    """Extract code content from files"""
    
    def process(self, file_info: Dict[str, Any]) -> List[CodeSample]:
        """Read file content and create CodeSample"""
        try:
            with open(file_info['full_path'], 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Detect language from extension
            ext = file_info['file_path'].split('.')[-1]
            language_map = {
                'py': 'python',
                'java': 'java',
                'js': 'javascript',
                'ts': 'typescript',
                'go': 'go',
                'rs': 'rust',
                'cpp': 'cpp',
                'c': 'c',
                'cs': 'csharp'
            }
            language = language_map.get(ext, 'unknown')
            
            # Generate unique sample ID
            sample_id = hashlib.sha256(
                f"{file_info['repo_metadata']['url']}:{file_info['file_path']}".encode()
            ).hexdigest()[:16]
            
            yield CodeSample(
                sample_id=sample_id,
                repository_id=file_info['repo_metadata']['name'],
                file_path=file_info['file_path'],
                code_content=content,
                language=language,
                source_type='github',
                license='MIT',  # This should be extracted from repo
                stars=1000,  # This should be from GitHub API
                last_updated=datetime.now(),
                metadata=file_info['repo_metadata']
            )
            
        except Exception as e:
            logger.error(f"Error reading file {file_info['file_path']}: {str(e)}")


class ParseToAST(beam.DoFn):
    """Parse code to Abstract Syntax Tree"""
    
    class ASTNode:
        def __init__(self, ast_data: Dict[str, Any], is_valid: bool):
            self.ast_data = ast_data
            self.is_valid = is_valid
    
    def __init__(self):
        self.parsers = {}
        self.languages = {}
        
    def setup(self):
        """Initialize tree-sitter parsers for supported languages"""
        if TREE_SITTER_AVAILABLE:
            self._setup_tree_sitter_parsers()
    
    def _setup_tree_sitter_parsers(self):
        """Setup tree-sitter parsers for multiple languages"""
        try:
            # Language library paths (these would be installed in the container)
            language_paths = {
                'python': '/opt/tree-sitter/python.so',
                'javascript': '/opt/tree-sitter/javascript.so',
                'typescript': '/opt/tree-sitter/typescript.so',
                'go': '/opt/tree-sitter/go.so',
                'rust': '/opt/tree-sitter/rust.so',
                'java': '/opt/tree-sitter/java.so'
            }
            
            for lang, path in language_paths.items():
                if os.path.exists(path):
                    self.languages[lang] = Language(path, lang)
                    parser = Parser()
                    parser.set_language(self.languages[lang])
                    self.parsers[lang] = parser
                else:
                    logger.warning(f"Tree-sitter library not found for {lang}: {path}")
                    
        except Exception as e:
            logger.error(f"Error setting up tree-sitter parsers: {e}")
            # Fall back to simple parsing
    
    def process(self, sample: CodeSample) -> List[ASTNode]:
        """Parse code to AST based on language"""
        try:
            # Route to appropriate parser based on language
            if sample.language == 'python':
                ast_data = self._parse_python(sample.code_content)
            elif sample.language == 'javascript':
                ast_data = self._parse_javascript(sample.code_content)
            elif sample.language == 'typescript':
                ast_data = self._parse_typescript(sample.code_content)
            elif sample.language == 'go':
                ast_data = self._parse_go(sample.code_content)
            elif sample.language == 'rust':
                ast_data = self._parse_rust(sample.code_content)
            elif sample.language == 'java':
                ast_data = self._parse_java(sample.code_content)
            else:
                # For unsupported languages, use basic parsing
                ast_data = self._parse_generic(sample.code_content)
            
            # Add sample metadata to AST
            ast_data['sample'] = sample
            
            # Validate AST structure
            is_valid = self._validate_ast(ast_data)
            
            yield self.ASTNode(ast_data, is_valid)
            
        except Exception as e:
            logger.error(f"AST parsing error for {sample.sample_id}: {str(e)}")
            yield self.ASTNode({'sample': sample, 'error': str(e)}, False)
    
    def _validate_ast(self, ast_data: Dict[str, Any]) -> bool:
        """Validate that AST data is properly structured"""
        try:
            # Check for required fields
            if 'type' not in ast_data or 'language' not in ast_data:
                return False
            
            # Check for parse errors
            if 'error' in ast_data:
                return False
            
            # Language-specific validation
            language = ast_data.get('language')
            
            if language == 'python':
                # Python AST should have specific structure
                return isinstance(ast_data.get('type'), str) and ast_data['type'] != 'ParseError'
            
            elif language in ['javascript', 'typescript']:
                # JS/TS should have functions, classes, or other constructs
                return ('functions' in ast_data or 'classes' in ast_data or 
                       ast_data.get('type') == 'TreeSitterAST')
            
            elif language == 'go':
                # Go should have package info or functions
                return ('packages' in ast_data or 'functions' in ast_data or 
                       ast_data.get('type') == 'TreeSitterAST')
            
            elif language == 'rust':
                # Rust should have modules, functions, or structs
                return ('functions' in ast_data or 'structs' in ast_data or 
                       ast_data.get('type') == 'TreeSitterAST')
            
            elif language == 'java':
                # Java should have classes or interfaces
                return ('classes' in ast_data or 'interfaces' in ast_data or 
                       ast_data.get('type') == 'TreeSitterAST')
            
            return True
            
        except Exception as e:
            logger.error(f"AST validation error: {e}")
            return False
    
    def _parse_python(self, code: str) -> Dict[str, Any]:
        """Parse Python code to AST"""
        tree = ast.parse(code)
        return self._ast_to_dict(tree)
    
    def _ast_to_dict(self, node) -> Dict[str, Any]:
        """Convert Python AST to dictionary"""
        if isinstance(node, ast.AST):
            result = {'type': node.__class__.__name__}
            for field, value in ast.iter_fields(node):
                if isinstance(value, list):
                    result[field] = [self._ast_to_dict(item) for item in value]
                else:
                    result[field] = self._ast_to_dict(value)
            return result
        else:
            return node
    
    def _parse_javascript(self, code: str) -> Dict[str, Any]:
        """Parse JavaScript code using tree-sitter"""
        if 'javascript' in self.parsers:
            return self._parse_with_tree_sitter(code, 'javascript')
        else:
            # Fallback to basic tokenization
            return self._parse_javascript_fallback(code)
    
    def _parse_typescript(self, code: str) -> Dict[str, Any]:
        """Parse TypeScript code using tree-sitter"""
        if 'typescript' in self.parsers:
            return self._parse_with_tree_sitter(code, 'typescript')
        else:
            # Fallback to JavaScript parsing
            return self._parse_javascript_fallback(code)
    
    def _parse_go(self, code: str) -> Dict[str, Any]:
        """Parse Go code using tree-sitter"""
        if 'go' in self.parsers:
            return self._parse_with_tree_sitter(code, 'go')
        else:
            return self._parse_go_fallback(code)
    
    def _parse_rust(self, code: str) -> Dict[str, Any]:
        """Parse Rust code using tree-sitter"""
        if 'rust' in self.parsers:
            return self._parse_with_tree_sitter(code, 'rust')
        else:
            return self._parse_rust_fallback(code)
    
    def _parse_with_tree_sitter(self, code: str, language: str) -> Dict[str, Any]:
        """Parse code using tree-sitter"""
        try:
            parser = self.parsers[language]
            tree = parser.parse(bytes(code, 'utf8'))
            return {
                'type': 'TreeSitterAST',
                'language': language,
                'root': self._tree_sitter_node_to_dict(tree.root_node, code),
                'syntax_errors': []
            }
        except Exception as e:
            logger.error(f"Tree-sitter parsing error for {language}: {e}")
            return {'type': 'ParseError', 'language': language, 'error': str(e)}
    
    def _tree_sitter_node_to_dict(self, node, code: str) -> Dict[str, Any]:
        """Convert tree-sitter node to dictionary"""
        result = {
            'type': node.type,
            'start_point': node.start_point,
            'end_point': node.end_point,
            'text': code[node.start_byte:node.end_byte] if node.start_byte < len(code) else ""
        }
        
        if node.children:
            result['children'] = [
                self._tree_sitter_node_to_dict(child, code) 
                for child in node.children
            ]
        
        return result
    
    def _parse_javascript_fallback(self, code: str) -> Dict[str, Any]:
        """Fallback JavaScript parser using regex patterns"""
        features = {
            'type': 'JavaScriptAST',
            'language': 'javascript',
            'functions': [],
            'classes': [],
            'variables': [],
            'imports': [],
            'exports': []
        }
        
        lines = code.split('\n')
        
        for line_num, line in enumerate(lines):
            line = line.strip()
            
            # Function declarations
            func_match = re.search(r'(?:function\s+(\w+)|const\s+(\w+)\s*=\s*(?:\([^)]*\)\s*=>|function))', line)
            if func_match:
                func_name = func_match.group(1) or func_match.group(2)
                features['functions'].append({
                    'name': func_name,
                    'line': line_num + 1,
                    'type': 'function_declaration'
                })
            
            # Class declarations
            class_match = re.search(r'class\s+(\w+)', line)
            if class_match:
                features['classes'].append({
                    'name': class_match.group(1),
                    'line': line_num + 1
                })
            
            # Variable declarations
            var_match = re.search(r'(?:let|const|var)\s+(\w+)', line)
            if var_match:
                features['variables'].append({
                    'name': var_match.group(1),
                    'line': line_num + 1
                })
            
            # Import statements
            import_match = re.search(r'import\s+.*?from\s+[\'"](.+?)[\'"]', line)
            if import_match:
                features['imports'].append({
                    'module': import_match.group(1),
                    'line': line_num + 1
                })
            
            # Export statements
            if re.search(r'export\s+', line):
                features['exports'].append({
                    'line': line_num + 1,
                    'content': line
                })
        
        return features
    
    def _parse_go_fallback(self, code: str) -> Dict[str, Any]:
        """Fallback Go parser using regex patterns"""
        features = {
            'type': 'GoAST',
            'language': 'go',
            'functions': [],
            'structs': [],
            'interfaces': [],
            'imports': [],
            'packages': []
        }
        
        lines = code.split('\n')
        
        for line_num, line in enumerate(lines):
            line = line.strip()
            
            # Package declaration
            pkg_match = re.search(r'package\s+(\w+)', line)
            if pkg_match:
                features['packages'].append({
                    'name': pkg_match.group(1),
                    'line': line_num + 1
                })
            
            # Function declarations
            func_match = re.search(r'func(?:\s+\(\w+\s+\*?\w+\))?\s+(\w+)\s*\(', line)
            if func_match:
                features['functions'].append({
                    'name': func_match.group(1),
                    'line': line_num + 1,
                    'type': 'function'
                })
            
            # Struct declarations
            struct_match = re.search(r'type\s+(\w+)\s+struct', line)
            if struct_match:
                features['structs'].append({
                    'name': struct_match.group(1),
                    'line': line_num + 1
                })
            
            # Interface declarations
            interface_match = re.search(r'type\s+(\w+)\s+interface', line)
            if interface_match:
                features['interfaces'].append({
                    'name': interface_match.group(1),
                    'line': line_num + 1
                })
            
            # Import statements
            import_match = re.search(r'import\s+[\"](.*?)[\"]', line)
            if import_match:
                features['imports'].append({
                    'package': import_match.group(1),
                    'line': line_num + 1
                })
        
        return features
    
    def _parse_rust_fallback(self, code: str) -> Dict[str, Any]:
        """Fallback Rust parser using regex patterns"""
        features = {
            'type': 'RustAST',
            'language': 'rust',
            'functions': [],
            'structs': [],
            'enums': [],
            'traits': [],
            'impls': [],
            'modules': [],
            'uses': []
        }
        
        lines = code.split('\n')
        
        for line_num, line in enumerate(lines):
            line = line.strip()
            
            # Function declarations
            func_match = re.search(r'(?:pub\s+)?fn\s+(\w+)\s*\(', line)
            if func_match:
                features['functions'].append({
                    'name': func_match.group(1),
                    'line': line_num + 1,
                    'visibility': 'pub' if 'pub' in line else 'private'
                })
            
            # Struct declarations
            struct_match = re.search(r'(?:pub\s+)?struct\s+(\w+)', line)
            if struct_match:
                features['structs'].append({
                    'name': struct_match.group(1),
                    'line': line_num + 1,
                    'visibility': 'pub' if 'pub' in line else 'private'
                })
            
            # Enum declarations
            enum_match = re.search(r'(?:pub\s+)?enum\s+(\w+)', line)
            if enum_match:
                features['enums'].append({
                    'name': enum_match.group(1),
                    'line': line_num + 1,
                    'visibility': 'pub' if 'pub' in line else 'private'
                })
            
            # Trait declarations
            trait_match = re.search(r'(?:pub\s+)?trait\s+(\w+)', line)
            if trait_match:
                features['traits'].append({
                    'name': trait_match.group(1),
                    'line': line_num + 1,
                    'visibility': 'pub' if 'pub' in line else 'private'
                })
            
            # Impl blocks
            impl_match = re.search(r'impl(?:\s+<[^>]+>)?\s+(?:(\w+)\s+for\s+)?(\w+)', line)
            if impl_match:
                features['impls'].append({
                    'trait': impl_match.group(1),
                    'type': impl_match.group(2),
                    'line': line_num + 1
                })
            
            # Module declarations
            mod_match = re.search(r'(?:pub\s+)?mod\s+(\w+)', line)
            if mod_match:
                features['modules'].append({
                    'name': mod_match.group(1),
                    'line': line_num + 1,
                    'visibility': 'pub' if 'pub' in line else 'private'
                })
            
            # Use statements
            use_match = re.search(r'use\s+([^;]+);', line)
            if use_match:
                features['uses'].append({
                    'path': use_match.group(1),
                    'line': line_num + 1
                })
        
        return features
    
    def _parse_java(self, code: str) -> Dict[str, Any]:
        """Parse Java code using tree-sitter or fallback"""
        if 'java' in self.parsers:
            return self._parse_with_tree_sitter(code, 'java')
        else:
            return self._parse_java_fallback(code)
    
    def _parse_java_fallback(self, code: str) -> Dict[str, Any]:
        """Fallback Java parser using regex patterns"""
        features = {
            'type': 'JavaAST',
            'language': 'java',
            'classes': [],
            'interfaces': [],
            'methods': [],
            'fields': [],
            'imports': [],
            'package': None
        }
        
        lines = code.split('\n')
        
        for line_num, line in enumerate(lines):
            line = line.strip()
            
            # Package declaration
            pkg_match = re.search(r'package\s+([\w.]+);', line)
            if pkg_match:
                features['package'] = {
                    'name': pkg_match.group(1),
                    'line': line_num + 1
                }
            
            # Import statements
            import_match = re.search(r'import\s+(?:static\s+)?([\w.*]+);', line)
            if import_match:
                features['imports'].append({
                    'class': import_match.group(1),
                    'line': line_num + 1,
                    'static': 'static' in line
                })
            
            # Class declarations
            class_match = re.search(r'(?:public\s+|private\s+|protected\s+)?(?:abstract\s+|final\s+)?class\s+(\w+)', line)
            if class_match:
                features['classes'].append({
                    'name': class_match.group(1),
                    'line': line_num + 1,
                    'modifiers': self._extract_java_modifiers(line)
                })
            
            # Interface declarations
            interface_match = re.search(r'(?:public\s+)?interface\s+(\w+)', line)
            if interface_match:
                features['interfaces'].append({
                    'name': interface_match.group(1),
                    'line': line_num + 1,
                    'modifiers': self._extract_java_modifiers(line)
                })
            
            # Method declarations
            method_match = re.search(r'(?:public\s+|private\s+|protected\s+)?(?:static\s+)?(?:final\s+)?\w+\s+(\w+)\s*\(', line)
            if method_match and not re.search(r'\b(?:class|interface)\b', line):
                features['methods'].append({
                    'name': method_match.group(1),
                    'line': line_num + 1,
                    'modifiers': self._extract_java_modifiers(line)
                })
            
            # Field declarations
            field_match = re.search(r'(?:public\s+|private\s+|protected\s+)?(?:static\s+)?(?:final\s+)?\w+\s+(\w+)\s*[=;]', line)
            if field_match and not re.search(r'\b(?:class|interface|if|for|while)\b', line):
                features['fields'].append({
                    'name': field_match.group(1),
                    'line': line_num + 1,
                    'modifiers': self._extract_java_modifiers(line)
                })
        
        return features
    
    def _extract_java_modifiers(self, line: str) -> List[str]:
        """Extract Java modifiers from a line"""
        modifiers = []
        java_modifiers = ['public', 'private', 'protected', 'static', 'final', 'abstract', 'synchronized', 'volatile']
        
        for modifier in java_modifiers:
            if re.search(r'\b' + modifier + r'\b', line):
                modifiers.append(modifier)
        
        return modifiers
    
    def _parse_generic(self, code: str) -> Dict[str, Any]:
        """Generic parsing for unsupported languages"""
        lines = code.split('\n')
        return {
            'type': 'GenericAST',
            'line_count': len(lines),
            'tokens': code.split(),
            'language': 'generic'
        }


class FeatureExtractor(beam.DoFn):
    """Extract ML features from AST and code"""
    
    def process(self, ast_node) -> List[ProcessedSample]:
        """Extract comprehensive features for pattern detection"""
        sample = ast_node.ast_data['sample']
        
        # Extract different feature types
        structural = self._extract_structural_features(ast_node.ast_data)
        lexical = self._extract_lexical_features(sample.code_content)
        semantic = self._extract_semantic_features(ast_node.ast_data)
        metrics = self._extract_code_metrics(sample.code_content)
        
        yield ProcessedSample(
            sample_id=sample.sample_id,
            source_repo=sample.repository_id,
            file_path=sample.file_path,
            code_snippet=sample.code_content[:1000],  # First 1000 chars
            ast_json=json.dumps(ast_node.ast_data),
            language=sample.language,
            structural_features=structural,
            lexical_features=lexical,
            semantic_features=semantic,
            metric_features=metrics,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    def _extract_structural_features(self, ast_data: Dict[str, Any]) -> List[float]:
        """Extract AST-based structural features"""
        features = []
        
        # AST depth
        depth = self._calculate_ast_depth(ast_data)
        features.append(float(depth))
        
        # Node type counts
        node_counts = self._count_node_types(ast_data)
        common_types = ['ClassDef', 'FunctionDef', 'If', 'For', 'While', 'Try']
        for node_type in common_types:
            features.append(float(node_counts.get(node_type, 0)))
        
        # Nesting level
        max_nesting = self._calculate_max_nesting(ast_data)
        features.append(float(max_nesting))
        
        # Cyclomatic complexity
        complexity = self._calculate_cyclomatic_complexity(ast_data)
        features.append(float(complexity))
        
        # Pad or truncate to fixed size
        target_size = 50
        if len(features) < target_size:
            features.extend([0.0] * (target_size - len(features)))
        else:
            features = features[:target_size]
        
        return features
    
    def _extract_lexical_features(self, code: str) -> List[float]:
        """Extract token and naming features"""
        features = []
        
        # Line and token counts
        lines = code.split('\n')
        tokens = re.findall(r'\b\w+\b', code)
        features.append(float(len(lines)))
        features.append(float(len(tokens)))
        
        # Naming patterns
        camel_case = len([t for t in tokens if re.match(r'^[a-z]+[A-Z]', t)])
        snake_case = len([t for t in tokens if '_' in t])
        features.append(float(camel_case))
        features.append(float(snake_case))
        
        # Comment density
        comment_lines = len([l for l in lines if l.strip().startswith(('#', '//', '/*'))])
        comment_density = comment_lines / max(len(lines), 1)
        features.append(comment_density)
        
        # Vocabulary richness
        unique_tokens = len(set(tokens))
        vocabulary_richness = unique_tokens / max(len(tokens), 1)
        features.append(vocabulary_richness)
        
        # Average line length
        avg_line_length = sum(len(l) for l in lines) / max(len(lines), 1)
        features.append(avg_line_length)
        
        # Pad to fixed size
        target_size = 30
        if len(features) < target_size:
            features.extend([0.0] * (target_size - len(features)))
        else:
            features = features[:target_size]
        
        return features
    
    def _extract_semantic_features(self, ast_data: Dict[str, Any]) -> List[float]:
        """Extract semantic and flow features"""
        features = []
        
        # Control flow patterns
        if_count = self._count_pattern(ast_data, 'If')
        loop_count = self._count_pattern(ast_data, ['For', 'While'])
        try_count = self._count_pattern(ast_data, 'Try')
        features.extend([float(if_count), float(loop_count), float(try_count)])
        
        # Function/method patterns
        function_count = self._count_pattern(ast_data, 'FunctionDef')
        class_count = self._count_pattern(ast_data, 'ClassDef')
        features.extend([float(function_count), float(class_count)])
        
        # Pad to fixed size
        target_size = 20
        if len(features) < target_size:
            features.extend([0.0] * (target_size - len(features)))
        else:
            features = features[:target_size]
        
        return features
    
    def _extract_code_metrics(self, code: str) -> List[float]:
        """Extract code quality metrics"""
        features = []
        
        lines = code.split('\n')
        
        # Lines of code
        loc = len([l for l in lines if l.strip() and not l.strip().startswith('#')])
        features.append(float(loc))
        
        # Maximum line length
        max_line_length = max(len(l) for l in lines) if lines else 0
        features.append(float(max_line_length))
        
        # Indentation levels
        indentation_levels = []
        for line in lines:
            if line.strip():
                indent = len(line) - len(line.lstrip())
                indentation_levels.append(indent)
        
        max_indent = max(indentation_levels) if indentation_levels else 0
        avg_indent = sum(indentation_levels) / len(indentation_levels) if indentation_levels else 0
        features.extend([float(max_indent), float(avg_indent)])
        
        # Code duplication estimate (simplified)
        line_hashes = [hash(l.strip()) for l in lines if l.strip()]
        unique_lines = len(set(line_hashes))
        duplication_ratio = 1 - (unique_lines / max(len(line_hashes), 1))
        features.append(duplication_ratio)
        
        # Pad to fixed size
        target_size = 20
        if len(features) < target_size:
            features.extend([0.0] * (target_size - len(features)))
        else:
            features = features[:target_size]
        
        return features
    
    def _calculate_ast_depth(self, node: Dict, depth: int = 0) -> int:
        """Calculate maximum depth of AST"""
        if not isinstance(node, dict):
            return depth
        
        max_child_depth = depth
        for key, value in node.items():
            if isinstance(value, dict):
                child_depth = self._calculate_ast_depth(value, depth + 1)
                max_child_depth = max(max_child_depth, child_depth)
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, dict):
                        child_depth = self._calculate_ast_depth(item, depth + 1)
                        max_child_depth = max(max_child_depth, child_depth)
        
        return max_child_depth
    
    def _count_node_types(self, node: Dict, counts: Optional[Dict[str, int]] = None) -> Dict[str, int]:
        """Count occurrences of each node type"""
        if counts is None:
            counts = {}
        
        if isinstance(node, dict) and 'type' in node:
            node_type = node['type']
            counts[node_type] = counts.get(node_type, 0) + 1
            
            for key, value in node.items():
                if isinstance(value, dict):
                    self._count_node_types(value, counts)
                elif isinstance(value, list):
                    for item in value:
                        if isinstance(item, dict):
                            self._count_node_types(item, counts)
        
        return counts
    
    def _calculate_max_nesting(self, node: Dict, current_nesting: int = 0) -> int:
        """Calculate maximum nesting level"""
        if not isinstance(node, dict):
            return current_nesting
        
        node_type = node.get('type', '')
        if node_type in ['If', 'For', 'While', 'Try', 'FunctionDef', 'ClassDef']:
            current_nesting += 1
        
        max_nesting = current_nesting
        for key, value in node.items():
            if isinstance(value, dict):
                child_nesting = self._calculate_max_nesting(value, current_nesting)
                max_nesting = max(max_nesting, child_nesting)
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, dict):
                        child_nesting = self._calculate_max_nesting(item, current_nesting)
                        max_nesting = max(max_nesting, child_nesting)
        
        return max_nesting
    
    def _calculate_cyclomatic_complexity(self, node: Dict, complexity: int = 1) -> int:
        """Calculate cyclomatic complexity"""
        if not isinstance(node, dict):
            return complexity
        
        node_type = node.get('type', '')
        if node_type in ['If', 'While', 'For', 'ExceptHandler']:
            complexity += 1
        elif node_type == 'BoolOp':
            # Each and/or adds a path
            complexity += 1
        
        for key, value in node.items():
            if isinstance(value, dict):
                complexity = self._calculate_cyclomatic_complexity(value, complexity)
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, dict):
                        complexity = self._calculate_cyclomatic_complexity(item, complexity)
        
        return complexity
    
    def _count_pattern(self, node: Dict, pattern_types: Any) -> int:
        """Count occurrences of specific patterns"""
        if isinstance(pattern_types, str):
            pattern_types = [pattern_types]
        
        count = 0
        if isinstance(node, dict):
            if node.get('type') in pattern_types:
                count += 1
            
            for value in node.values():
                if isinstance(value, dict):
                    count += self._count_pattern(value, pattern_types)
                elif isinstance(value, list):
                    for item in value:
                        if isinstance(item, dict):
                            count += self._count_pattern(item, pattern_types)
        
        return count


class FeatureNormalizer(beam.DoFn):
    """Normalize features for ML training"""
    
    def setup(self):
        self.scaler = StandardScaler()
    
    def process(self, sample: ProcessedSample) -> List[ProcessedSample]:
        """Normalize feature values"""
        # In production, this would use a pre-fitted scaler
        # For now, we'll do simple normalization
        
        # Normalize structural features
        sample.structural_features = self._normalize_features(sample.structural_features)
        sample.lexical_features = self._normalize_features(sample.lexical_features)
        sample.semantic_features = self._normalize_features(sample.semantic_features)
        sample.metric_features = self._normalize_features(sample.metric_features)
        
        yield sample
    
    def _normalize_features(self, features: List[float]) -> List[float]:
        """Simple min-max normalization"""
        if not features:
            return features
        
        min_val = min(features)
        max_val = max(features)
        
        if max_val - min_val == 0:
            return features
        
        return [(f - min_val) / (max_val - min_val) for f in features]


class HeuristicLabeler(beam.DoFn):
    """Apply heuristic rules for initial labeling"""
    
    def process(self, sample: ProcessedSample) -> List[Tuple[str, ProcessedSample]]:
        """Apply heuristic labeling rules"""
        labels = []
        
        # Singleton pattern detection
        if self._detect_singleton_pattern(sample):
            labels.append(('singleton', 0.8))
        
        # Factory pattern detection
        if self._detect_factory_pattern(sample):
            labels.append(('factory', 0.7))
        
        # Long method anti-pattern
        if self._detect_long_method(sample):
            labels.append(('long_method', 0.9))
        
        # SQL injection vulnerability
        if self._detect_sql_injection(sample):
            labels.append(('sql_injection', 0.85))
        
        # If no patterns detected, mark as unknown
        if not labels:
            labels.append(('unknown', 0.5))
        
        # Emit sample with each detected label
        for label, confidence in labels:
            labeled_sample = ProcessedSample(**asdict(sample))
            labeled_sample.pattern_type = label
            labeled_sample.confidence = confidence
            labeled_sample.labeling_method = 'heuristic'
            
            yield (sample.sample_id, labeled_sample)
    
    def _detect_singleton_pattern(self, sample: ProcessedSample) -> bool:
        """Detect singleton pattern using heuristics"""
        code = sample.code_snippet.lower()
        
        # Look for singleton indicators
        indicators = [
            'singleton' in code,
            '_instance' in code,
            'getinstance' in code or 'get_instance' in code,
            re.search(r'private\s+static\s+\w+\s+instance', code),
            re.search(r'private\s+__init__', code)
        ]
        
        return sum(indicators) >= 2
    
    def _detect_factory_pattern(self, sample: ProcessedSample) -> bool:
        """Detect factory pattern"""
        code = sample.code_snippet.lower()
        
        indicators = [
            'factory' in code,
            'create' in code and 'return' in code,
            re.search(r'def\s+create\w+', code),
            re.search(r'class\s+\w*factory', code)
        ]
        
        return sum(indicators) >= 2
    
    def _detect_long_method(self, sample: ProcessedSample) -> bool:
        """Detect long method anti-pattern"""
        # Check method length from features
        if sample.metric_features[0] > 100:  # LOC > 100
            return True
        
        # Check cyclomatic complexity
        if sample.structural_features[8] > 10:  # High complexity
            return True
        
        return False
    
    def _detect_sql_injection(self, sample: ProcessedSample) -> bool:
        """Detect potential SQL injection vulnerabilities"""
        code = sample.code_snippet
        
        # Look for dangerous SQL patterns
        dangerous_patterns = [
            r'query\s*=\s*["\'].*\+.*["\']',  # String concatenation in query
            r'execute\s*\(\s*["\'].*%s.*["\'].*%',  # String formatting
            r'f["\'].*SELECT.*{.*}',  # f-string in SQL
            r'sql\s*=.*format\s*\(',  # .format() in SQL
        ]
        
        return any(re.search(pattern, code, re.IGNORECASE) for pattern in dangerous_patterns)


class LabelAggregator(beam.CombineFn):
    """Aggregate multiple labels for the same sample"""
    
    def create_accumulator(self):
        return []
    
    def add_input(self, accumulator, input_sample):
        accumulator.append(input_sample)
        return accumulator
    
    def merge_accumulators(self, accumulators):
        merged = []
        for acc in accumulators:
            merged.extend(acc)
        return merged
    
    def extract_output(self, accumulator):
        """Select best label based on confidence"""
        if not accumulator:
            return None
        
        # Sort by confidence and take the highest
        best_sample = max(accumulator, key=lambda x: x.confidence)
        
        # If multiple high-confidence labels, note them in metadata
        high_confidence_labels = [
            s.pattern_type for s in accumulator 
            if s.confidence > 0.7 and s.pattern_type != 'unknown'
        ]
        
        if len(high_confidence_labels) > 1:
            best_sample.metadata = best_sample.metadata or {}
            best_sample.metadata['alternative_labels'] = high_confidence_labels
        
        return best_sample


class QualityValidator(beam.DoFn):
    """Validate data quality before storage"""
    
    def process(self, sample: ProcessedSample) -> List[ProcessedSample]:
        """Validate sample quality"""
        quality_score = 1.0
        
        # Check feature completeness
        if any(len(f) == 0 for f in [
            sample.structural_features,
            sample.lexical_features,
            sample.semantic_features,
            sample.metric_features
        ]):
            quality_score -= 0.3
        
        # Check code snippet quality
        if len(sample.code_snippet) < 50:
            quality_score -= 0.2
        
        # Check AST validity
        try:
            ast_data = json.loads(sample.ast_json)
            if 'error' in ast_data:
                quality_score -= 0.4
        except:
            quality_score -= 0.5
        
        # Check label confidence
        if sample.confidence and sample.confidence < 0.5:
            quality_score -= 0.2
        
        # Update sample with quality score
        sample.quality_score = max(0.0, quality_score)
        sample.validation_status = 'validated' if quality_score > 0.7 else 'low_quality'
        
        yield sample


def main():
    """Main execution function"""
    import argparse
    
    parser = argparse.ArgumentParser()
    parser.add_argument('--project_id', required=True, help='GCP Project ID')
    parser.add_argument('--dataset_id', default='ccl_ml', help='BigQuery dataset ID')
    parser.add_argument('--repo_list', required=True, help='Path to repository list file')
    
    args = parser.parse_args()
    
    # Read repository list
    with open(args.repo_list, 'r') as f:
        repos = [line.strip() for line in f if line.strip()]
    
    # Create and run pipeline
    pipeline = CodePatternPipeline(args.project_id, args.dataset_id)
    pipeline.run(repos)


if __name__ == '__main__':
    main()