<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CCL Pattern Labeling Tool</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .session-setup {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .labeling-interface {
            display: none;
            gap: 20px;
        }

        .code-panel {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            flex: 2;
        }

        .labeling-panel {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            flex: 1;
            padding: 20px;
        }

        .code-header {
            background: #2d3748;
            color: white;
            padding: 15px 20px;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .code-content {
            padding: 20px;
        }

        .code-snippet {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2d3748;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .confidence-slider {
            margin: 10px 0;
        }

        .confidence-value {
            text-align: center;
            font-weight: bold;
            color: #667eea;
            margin-top: 5px;
        }

        .button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .button:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .button.secondary {
            background: #718096;
        }

        .button.secondary:hover {
            background: #4a5568;
        }

        .button.danger {
            background: #e53e3e;
        }

        .button.danger:hover {
            background: #c53030;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .stats-panel {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .alert {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .alert.success {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .alert.error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #fc8181;
        }

        .file-info {
            background: #edf2f7;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .file-info strong {
            color: #2d3748;
        }

        .pattern-types {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 10px;
        }

        .pattern-types label {
            display: block;
            padding: 5px;
            cursor: pointer;
            border-radius: 3px;
            transition: background-color 0.2s;
        }

        .pattern-types label:hover {
            background-color: #edf2f7;
        }

        .pattern-types input[type="radio"] {
            margin-right: 8px;
        }

        @media (max-width: 768px) {
            .labeling-interface {
                flex-direction: column;
            }
            
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 CCL Pattern Labeling Tool</h1>
            <p>Help train our AI by labeling code patterns</p>
        </div>

        <!-- Session Setup -->
        <div id="sessionSetup" class="session-setup">
            <h2>Start Labeling Session</h2>
            <div class="form-group">
                <label for="labelerName">Your Name:</label>
                <input type="text" id="labelerName" placeholder="Enter your name" required>
            </div>
            <button class="button" onclick="startSession()">Start Session</button>
        </div>

        <!-- Session Statistics -->
        <div id="statsPanel" class="stats-panel" style="display: none;">
            <h3>Session Progress</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <span id="samplesLabeled" class="stat-value">0</span>
                    <span class="stat-label">Samples Labeled</span>
                </div>
                <div class="stat-card">
                    <span id="labelsCreated" class="stat-value">0</span>
                    <span class="stat-label">Labels Created</span>
                </div>
                <div class="stat-card">
                    <span id="sessionTime" class="stat-value">0m</span>
                    <span class="stat-label">Session Time</span>
                </div>
            </div>
        </div>

        <!-- Labeling Interface -->
        <div id="labelingInterface" class="labeling-interface">
            <!-- Code Panel -->
            <div class="code-panel">
                <div class="code-header">
                    <div>
                        <h3>Code Sample</h3>
                        <span id="languageBadge" class="language-badge"></span>
                    </div>
                </div>
                <div class="code-content">
                    <div id="fileInfo" class="file-info">
                        <strong>Repository:</strong> <span id="sourceRepo">-</span><br>
                        <strong>File:</strong> <span id="filePath">-</span><br>
                        <strong>Language:</strong> <span id="language">-</span>
                    </div>
                    <pre id="codeSnippet" class="code-snippet">Loading...</pre>
                </div>
            </div>

            <!-- Labeling Panel -->
            <div class="labeling-panel">
                <h3>Label This Pattern</h3>
                
                <div class="form-group">
                    <label for="patternCategory">Pattern Category:</label>
                    <select id="patternCategory" onchange="updatePatternTypes()">
                        <option value="">Select a category...</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>Pattern Type:</label>
                    <div id="patternTypes" class="pattern-types">
                        <p>Select a category first</p>
                    </div>
                </div>

                <div class="form-group">
                    <label for="confidence">Confidence Level:</label>
                    <input type="range" id="confidence" class="confidence-slider" 
                           min="0" max="1" step="0.1" value="0.8" 
                           oninput="updateConfidenceDisplay()">
                    <div id="confidenceValue" class="confidence-value">80%</div>
                </div>

                <div class="form-group">
                    <label for="notes">Notes (optional):</label>
                    <textarea id="notes" rows="3" 
                             placeholder="Add any additional notes about this pattern..."></textarea>
                </div>

                <div class="button-group">
                    <button class="button" onclick="submitLabel()">
                        ✅ Submit Label
                    </button>
                    <button class="button secondary" onclick="skipSample()">
                        ⏭️ Skip Sample
                    </button>
                    <button class="button danger" onclick="endSession()">
                        🛑 End Session
                    </button>
                </div>
            </div>
        </div>

        <!-- Alerts -->
        <div id="alertContainer"></div>
    </div>

    <script>
        let currentSample = null;
        let sessionStartTime = null;
        let patternCategories = {};

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadPatternCategories();
        });

        // Load pattern categories from server
        async function loadPatternCategories() {
            try {
                const response = await fetch('/pattern_categories');
                patternCategories = await response.json();
                populateCategorySelect();
            } catch (error) {
                showAlert('Error loading pattern categories', 'error');
            }
        }

        // Populate the category select element
        function populateCategorySelect() {
            const select = document.getElementById('patternCategory');
            select.innerHTML = '<option value="">Select a category...</option>';
            
            for (const [category, types] of Object.entries(patternCategories)) {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category.replace('_', ' ').toUpperCase();
                select.appendChild(option);
            }
        }

        // Update pattern types based on selected category
        function updatePatternTypes() {
            const category = document.getElementById('patternCategory').value;
            const typesContainer = document.getElementById('patternTypes');
            
            if (!category || !patternCategories[category]) {
                typesContainer.innerHTML = '<p>Select a category first</p>';
                return;
            }
            
            typesContainer.innerHTML = '';
            
            patternCategories[category].forEach(type => {
                const label = document.createElement('label');
                const radio = document.createElement('input');
                radio.type = 'radio';
                radio.name = 'patternType';
                radio.value = type;
                
                label.appendChild(radio);
                label.appendChild(document.createTextNode(type.replace('_', ' ')));
                typesContainer.appendChild(label);
            });
        }

        // Update confidence display
        function updateConfidenceDisplay() {
            const confidence = document.getElementById('confidence').value;
            const display = document.getElementById('confidenceValue');
            display.textContent = Math.round(confidence * 100) + '%';
        }

        // Start a new labeling session
        async function startSession() {
            const labelerName = document.getElementById('labelerName').value.trim();
            
            if (!labelerName) {
                showAlert('Please enter your name', 'error');
                return;
            }
            
            try {
                const response = await fetch('/start_session', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({labeler_name: labelerName})
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    sessionStartTime = new Date();
                    document.getElementById('sessionSetup').style.display = 'none';
                    document.getElementById('statsPanel').style.display = 'block';
                    document.getElementById('labelingInterface').style.display = 'flex';
                    
                    startSessionTimer();
                    loadNextSample();
                    showAlert(`Session started for ${labelerName}`, 'success');
                } else {
                    showAlert(data.error || 'Error starting session', 'error');
                }
            } catch (error) {
                showAlert('Network error starting session', 'error');
            }
        }

        // Start session timer
        function startSessionTimer() {
            setInterval(() => {
                if (sessionStartTime) {
                    const elapsed = Math.floor((new Date() - sessionStartTime) / 60000);
                    document.getElementById('sessionTime').textContent = elapsed + 'm';
                }
            }, 60000); // Update every minute
        }

        // Load the next sample for labeling
        async function loadNextSample() {
            try {
                const response = await fetch('/get_sample');
                
                if (response.ok) {
                    currentSample = await response.json();
                    displaySample(currentSample);
                } else if (response.status === 404) {
                    showAlert('No more samples to label!', 'success');
                    endSession();
                } else {
                    const error = await response.json();
                    showAlert(error.error || 'Error loading sample', 'error');
                }
            } catch (error) {
                showAlert('Network error loading sample', 'error');
            }
        }

        // Display a code sample
        function displaySample(sample) {
            document.getElementById('sourceRepo').textContent = sample.source_repo || 'Unknown';
            document.getElementById('filePath').textContent = sample.file_path || 'Unknown';
            document.getElementById('language').textContent = sample.language || 'Unknown';
            document.getElementById('codeSnippet').textContent = sample.code_snippet || 'No code available';
            
            // Reset form
            document.getElementById('patternCategory').value = '';
            document.getElementById('patternTypes').innerHTML = '<p>Select a category first</p>';
            document.getElementById('confidence').value = 0.8;
            document.getElementById('notes').value = '';
            updateConfidenceDisplay();
        }

        // Submit a label for the current sample
        async function submitLabel() {
            if (!currentSample) {
                showAlert('No sample loaded', 'error');
                return;
            }
            
            const category = document.getElementById('patternCategory').value;
            const typeRadio = document.querySelector('input[name="patternType"]:checked');
            const confidence = document.getElementById('confidence').value;
            const notes = document.getElementById('notes').value;
            
            if (!category) {
                showAlert('Please select a pattern category', 'error');
                return;
            }
            
            if (!typeRadio) {
                showAlert('Please select a pattern type', 'error');
                return;
            }
            
            try {
                const response = await fetch('/label_sample', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        sample_id: currentSample.id,
                        pattern_category: category,
                        pattern_type: typeRadio.value,
                        confidence: parseFloat(confidence),
                        notes: notes
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showAlert('Label saved successfully!', 'success');
                    updateSessionStats();
                    loadNextSample();
                } else {
                    showAlert(data.error || 'Error saving label', 'error');
                }
            } catch (error) {
                showAlert('Network error saving label', 'error');
            }
        }

        // Skip the current sample
        async function skipSample() {
            if (!currentSample) {
                showAlert('No sample loaded', 'error');
                return;
            }
            
            const reason = prompt('Reason for skipping (optional):') || 'No reason provided';
            
            try {
                const response = await fetch('/skip_sample', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        sample_id: currentSample.id,
                        reason: reason
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showAlert('Sample skipped', 'success');
                    updateSessionStats();
                    loadNextSample();
                } else {
                    showAlert(data.error || 'Error skipping sample', 'error');
                }
            } catch (error) {
                showAlert('Network error skipping sample', 'error');
            }
        }

        // Update session statistics
        async function updateSessionStats() {
            try {
                const response = await fetch('/session_stats');
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('samplesLabeled').textContent = data.samples_labeled;
                    document.getElementById('labelsCreated').textContent = data.labels_created;
                }
            } catch (error) {
                console.error('Error updating stats:', error);
            }
        }

        // End the current session
        function endSession() {
            if (confirm('Are you sure you want to end this session?')) {
                document.getElementById('sessionSetup').style.display = 'block';
                document.getElementById('statsPanel').style.display = 'none';
                document.getElementById('labelingInterface').style.display = 'none';
                
                document.getElementById('labelerName').value = '';
                sessionStartTime = null;
                
                showAlert('Session ended. Thank you for your contributions!', 'success');
            }
        }

        // Show alert message
        function showAlert(message, type = 'success') {
            const container = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert ${type}`;
            alert.textContent = message;
            
            container.appendChild(alert);
            
            setTimeout(() => {
                container.removeChild(alert);
            }, 5000);
        }

        // Handle keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    submitLabel();
                } else if (e.key === 's') {
                    e.preventDefault();
                    skipSample();
                }
            }
        });
    </script>
</body>
</html>