# Manual Pattern Labeling UI

A Flask-based web interface for manually labeling code patterns to train machine learning models.

## Features

- **Interactive Code Display**: View code samples with syntax highlighting
- **Pattern Categories**: Label patterns across multiple categories:
  - Design Patterns (singleton, factory, observer, etc.)
  - Anti-patterns (long method, god object, etc.)
  - Security Patterns (SQL injection, XSS, etc.)
  - Performance Patterns (N+1 queries, memory leaks, etc.)
  - Architecture Patterns (MVC, repository, etc.)

- **Confidence Scoring**: Rate your confidence in each label
- **Session Tracking**: Track labeling progress and statistics
- **Skip Functionality**: Skip ambiguous or difficult samples
- **Export Labels**: Export labeled data for ML training

## Quick Start

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the Application**:
   ```bash
   python app.py
   ```

3. **Open Browser**:
   Navigate to `http://localhost:5000`

4. **Start Labeling**:
   - Enter your name to start a session
   - Review code samples and select appropriate pattern labels
   - Use confidence slider to indicate certainty
   - Add notes for complex cases

## Usage Guide

### Starting a Session

1. Enter your name in the session setup
2. Click "Start Session"
3. The interface will load the first unlabeled sample

### Labeling Process

1. **Review the Code**: Examine the displayed code snippet
2. **Select Category**: Choose the appropriate pattern category
3. **Select Type**: Pick the specific pattern type
4. **Set Confidence**: Use the slider to indicate confidence (0-100%)
5. **Add Notes**: Optional notes for complex cases
6. **Submit**: Click "Submit Label" or use Ctrl+Enter

### Keyboard Shortcuts

- `Ctrl+Enter` / `Cmd+Enter`: Submit current label
- `Ctrl+S` / `Cmd+S`: Skip current sample

### Session Statistics

The interface tracks:
- Total samples labeled
- Labels created
- Session duration

## Data Storage

The application uses SQLite to store:
- **Labeling Sessions**: Track user sessions and statistics
- **Code Samples**: Store code snippets and metadata
- **Pattern Labels**: Store user-assigned labels
- **Feedback**: Track skipped samples and user feedback

## Database Schema

### Tables

1. **labeling_sessions**
   - `id`: Unique session identifier
   - `labeler_name`: Name of the person labeling
   - `start_time`, `end_time`: Session timing
   - `samples_labeled`, `labels_created`: Statistics

2. **code_samples**
   - `id`: Unique sample identifier
   - `source_repo`: Repository source
   - `file_path`: File location
   - `code_snippet`: Code content
   - `language`: Programming language
   - `ast_features`: JSON of extracted features

3. **pattern_labels**
   - `id`: Unique label identifier
   - `sample_id`: Reference to code sample
   - `session_id`: Reference to labeling session
   - `pattern_category`, `pattern_type`: Label classification
   - `confidence`: Labeler confidence (0.0-1.0)
   - `notes`: Optional notes

4. **labeler_feedback**
   - Tracks skipped samples and feedback

## API Endpoints

### Core Endpoints

- `GET /`: Main labeling interface
- `POST /start_session`: Start new labeling session
- `GET /get_sample`: Get next unlabeled sample
- `POST /label_sample`: Submit pattern label
- `POST /skip_sample`: Skip current sample
- `GET /session_stats`: Get current session statistics
- `GET /export_labels`: Export all labels as JSON
- `GET /pattern_categories`: Get available pattern types

### Sample Request/Response

**Start Session**:
```json
POST /start_session
{
  "labeler_name": "Alice Smith"
}

Response:
{
  "session_id": "uuid-here",
  "labeler_name": "Alice Smith",
  "status": "started"
}
```

**Submit Label**:
```json
POST /label_sample
{
  "sample_id": "sample-uuid",
  "pattern_category": "design_patterns",
  "pattern_type": "singleton",
  "confidence": 0.9,
  "notes": "Clear singleton implementation"
}
```

## Configuration

### Pattern Categories

The application supports these pattern categories by default:

- **Design Patterns**: Common software design patterns
- **Anti-patterns**: Code smells and poor practices
- **Security Patterns**: Security vulnerabilities and issues
- **Performance Patterns**: Performance problems and bottlenecks
- **Architecture Patterns**: Architectural design patterns

### Adding New Patterns

To add new pattern types, modify the `PATTERN_CATEGORIES` dictionary in `app.py`:

```python
PATTERN_CATEGORIES = {
    'new_category': [
        'pattern_type_1',
        'pattern_type_2',
        # ...
    ]
}
```

## Data Export

### Export Format

Labels can be exported via `GET /export_labels` in this format:

```json
{
  "labels": [
    {
      "sample_id": "uuid",
      "source_repo": "example/repo",
      "file_path": "src/pattern.py",
      "code_snippet": "class Singleton: ...",
      "language": "python",
      "pattern_category": "design_patterns",
      "pattern_type": "singleton",
      "confidence": 0.9,
      "notes": "Clear implementation",
      "labeled_at": "2024-01-01T12:00:00",
      "labeler_name": "Alice Smith"
    }
  ],
  "total_count": 150,
  "export_time": "2024-01-01T12:00:00"
}
```

### Integration with ML Pipeline

Export data can be directly used in the ML training pipeline:

```python
import requests
import json

# Export labels
response = requests.get('http://localhost:5000/export_labels')
labels = response.json()

# Use in training
for label in labels['labels']:
    # Process for ML training
    pass
```

## Development

### Adding New Features

1. **New Pattern Categories**: Modify `PATTERN_CATEGORIES`
2. **UI Enhancements**: Edit `templates/index.html`
3. **API Extensions**: Add routes to `app.py`
4. **Database Changes**: Update schema in `init_database()`

### Testing

To test with sample data:

1. Run the application
2. Sample data is automatically created in `unlabeled_samples.json`
3. Start a session and begin labeling

### Production Deployment

For production use:

1. **Security**: Change the Flask secret key
2. **Database**: Consider PostgreSQL for production
3. **Authentication**: Add user authentication
4. **Scaling**: Use WSGI server like Gunicorn
5. **Monitoring**: Add logging and metrics

### Docker Deployment

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["python", "app.py"]
```

## Troubleshooting

### Common Issues

1. **Database Locked**: Ensure only one instance is running
2. **No Samples**: Check `unlabeled_samples.json` exists
3. **Session Lost**: Check browser cookies/storage
4. **Performance**: For large datasets, consider pagination

### Logs

The application logs to console. Check for:
- Database connection issues
- File loading errors
- Session management problems

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

This tool is part of the CCL platform and follows the same licensing terms.