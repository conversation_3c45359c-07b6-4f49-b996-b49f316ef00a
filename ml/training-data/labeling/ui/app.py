#!/usr/bin/env python3
"""
Manual Pattern Labeling UI
A Flask-based web interface for manually labeling code patterns
"""

from flask import Flask, render_template, request, jsonify, session
import json
import sqlite3
import hashlib
from datetime import datetime
from pathlib import Path
import uuid
from typing import Dict, List, Optional, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'ccl-labeling-ui-secret-key'  # In production, use environment variable

# Configuration
DATABASE_PATH = Path(__file__).parent / 'labeling.db'
UNLABELED_SAMPLES_PATH = Path(__file__).parent / 'unlabeled_samples.json'

# Pattern categories and types
PATTERN_CATEGORIES = {
    'design_patterns': [
        'singleton', 'factory', 'observer', 'decorator', 'adapter', 
        'strategy', 'command', 'builder', 'proxy', 'facade'
    ],
    'anti_patterns': [
        'long_method', 'large_class', 'god_object', 'feature_envy',
        'data_clumps', 'shotgun_surgery', 'divergent_change'
    ],
    'security_patterns': [
        'sql_injection', 'xss_vulnerability', 'authentication_bypass',
        'insecure_crypto', 'path_traversal', 'command_injection'
    ],
    'performance_patterns': [
        'n_plus_one_query', 'memory_leak', 'inefficient_loop',
        'blocking_io', 'resource_leak', 'cache_miss'
    ],
    'architecture_patterns': [
        'mvc', 'mvp', 'mvvm', 'repository', 'service_layer',
        'dependency_injection', 'event_sourcing'
    ]
}

def init_database():
    """Initialize the SQLite database for labeling data"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()
    
    # Create tables
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS labeling_sessions (
            id TEXT PRIMARY KEY,
            labeler_name TEXT NOT NULL,
            start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            end_time TIMESTAMP NULL,
            samples_labeled INTEGER DEFAULT 0,
            labels_created INTEGER DEFAULT 0
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS code_samples (
            id TEXT PRIMARY KEY,
            source_repo TEXT,
            file_path TEXT,
            code_snippet TEXT NOT NULL,
            language TEXT,
            ast_features TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS pattern_labels (
            id TEXT PRIMARY KEY,
            sample_id TEXT NOT NULL,
            session_id TEXT NOT NULL,
            pattern_category TEXT NOT NULL,
            pattern_type TEXT NOT NULL,
            confidence REAL NOT NULL,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (sample_id) REFERENCES code_samples (id),
            FOREIGN KEY (session_id) REFERENCES labeling_sessions (id)
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS labeler_feedback (
            id TEXT PRIMARY KEY,
            sample_id TEXT NOT NULL,
            session_id TEXT NOT NULL,
            feedback_type TEXT NOT NULL,
            feedback_text TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (sample_id) REFERENCES code_samples (id),
            FOREIGN KEY (session_id) REFERENCES labeling_sessions (id)
        )
    ''')
    
    conn.commit()
    conn.close()

def load_unlabeled_samples() -> List[Dict[str, Any]]:
    """Load unlabeled code samples from JSON file"""
    if not UNLABELED_SAMPLES_PATH.exists():
        return []
    
    try:
        with open(UNLABELED_SAMPLES_PATH, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error loading unlabeled samples: {e}")
        return []

def save_sample_to_db(sample: Dict[str, Any]) -> str:
    """Save a code sample to the database"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()
    
    sample_id = str(uuid.uuid4())
    
    cursor.execute('''
        INSERT OR REPLACE INTO code_samples 
        (id, source_repo, file_path, code_snippet, language, ast_features)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', (
        sample_id,
        sample.get('source_repo', ''),
        sample.get('file_path', ''),
        sample.get('code_snippet', ''),
        sample.get('language', ''),
        json.dumps(sample.get('ast_features', {}))
    ))
    
    conn.commit()
    conn.close()
    
    return sample_id

@app.route('/')
def index():
    """Main labeling interface"""
    return render_template('index.html', pattern_categories=PATTERN_CATEGORIES)

@app.route('/start_session', methods=['POST'])
def start_session():
    """Start a new labeling session"""
    labeler_name = request.json.get('labeler_name', 'Anonymous')
    
    session_id = str(uuid.uuid4())
    session['session_id'] = session_id
    session['labeler_name'] = labeler_name
    
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT INTO labeling_sessions (id, labeler_name)
        VALUES (?, ?)
    ''', (session_id, labeler_name))
    
    conn.commit()
    conn.close()
    
    return jsonify({
        'session_id': session_id,
        'labeler_name': labeler_name,
        'status': 'started'
    })

@app.route('/get_sample')
def get_sample():
    """Get the next unlabeled sample for labeling"""
    if 'session_id' not in session:
        return jsonify({'error': 'No active session'}), 400
    
    # First, try to get samples from database that haven't been labeled
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT cs.id, cs.source_repo, cs.file_path, cs.code_snippet, cs.language, cs.ast_features
        FROM code_samples cs
        LEFT JOIN pattern_labels pl ON cs.id = pl.sample_id
        WHERE pl.sample_id IS NULL
        ORDER BY cs.created_at
        LIMIT 1
    ''')
    
    row = cursor.fetchone()
    
    if row:
        sample = {
            'id': row[0],
            'source_repo': row[1],
            'file_path': row[2],
            'code_snippet': row[3],
            'language': row[4],
            'ast_features': json.loads(row[5]) if row[5] else {}
        }
        conn.close()
        return jsonify(sample)
    
    conn.close()
    
    # If no samples in database, load from unlabeled samples file
    unlabeled_samples = load_unlabeled_samples()
    
    if not unlabeled_samples:
        return jsonify({'error': 'No more samples to label'}), 404
    
    # Take the first sample and save it to database
    sample = unlabeled_samples[0]
    sample_id = save_sample_to_db(sample)
    sample['id'] = sample_id
    
    # Remove the sample from the unlabeled list
    unlabeled_samples = unlabeled_samples[1:]
    
    try:
        with open(UNLABELED_SAMPLES_PATH, 'w') as f:
            json.dump(unlabeled_samples, f, indent=2)
    except Exception as e:
        logger.error(f"Error updating unlabeled samples file: {e}")
    
    return jsonify(sample)

@app.route('/label_sample', methods=['POST'])
def label_sample():
    """Save a label for a code sample"""
    if 'session_id' not in session:
        return jsonify({'error': 'No active session'}), 400
    
    data = request.json
    sample_id = data.get('sample_id')
    pattern_category = data.get('pattern_category')
    pattern_type = data.get('pattern_type')
    confidence = data.get('confidence', 0.5)
    notes = data.get('notes', '')
    
    if not all([sample_id, pattern_category, pattern_type]):
        return jsonify({'error': 'Missing required fields'}), 400
    
    if pattern_category not in PATTERN_CATEGORIES:
        return jsonify({'error': 'Invalid pattern category'}), 400
    
    if pattern_type not in PATTERN_CATEGORIES[pattern_category]:
        return jsonify({'error': 'Invalid pattern type for category'}), 400
    
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()
    
    label_id = str(uuid.uuid4())
    
    cursor.execute('''
        INSERT INTO pattern_labels 
        (id, sample_id, session_id, pattern_category, pattern_type, confidence, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', (
        label_id,
        sample_id,
        session['session_id'],
        pattern_category,
        pattern_type,
        float(confidence),
        notes
    ))
    
    # Update session statistics
    cursor.execute('''
        UPDATE labeling_sessions 
        SET samples_labeled = samples_labeled + 1,
            labels_created = labels_created + 1
        WHERE id = ?
    ''', (session['session_id'],))
    
    conn.commit()
    conn.close()
    
    return jsonify({
        'label_id': label_id,
        'status': 'saved',
        'message': 'Label saved successfully'
    })

@app.route('/skip_sample', methods=['POST'])
def skip_sample():
    """Skip a sample (mark as too difficult or ambiguous)"""
    if 'session_id' not in session:
        return jsonify({'error': 'No active session'}), 400
    
    data = request.json
    sample_id = data.get('sample_id')
    reason = data.get('reason', 'skipped')
    
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()
    
    feedback_id = str(uuid.uuid4())
    
    cursor.execute('''
        INSERT INTO labeler_feedback 
        (id, sample_id, session_id, feedback_type, feedback_text)
        VALUES (?, ?, ?, ?, ?)
    ''', (
        feedback_id,
        sample_id,
        session['session_id'],
        'skip',
        reason
    ))
    
    # Update session statistics
    cursor.execute('''
        UPDATE labeling_sessions 
        SET samples_labeled = samples_labeled + 1
        WHERE id = ?
    ''', (session['session_id'],))
    
    conn.commit()
    conn.close()
    
    return jsonify({
        'status': 'skipped',
        'message': 'Sample skipped'
    })

@app.route('/session_stats')
def session_stats():
    """Get current session statistics"""
    if 'session_id' not in session:
        return jsonify({'error': 'No active session'}), 400
    
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT samples_labeled, labels_created, start_time
        FROM labeling_sessions
        WHERE id = ?
    ''', (session['session_id'],))
    
    row = cursor.fetchone()
    conn.close()
    
    if not row:
        return jsonify({'error': 'Session not found'}), 404
    
    return jsonify({
        'session_id': session['session_id'],
        'labeler_name': session.get('labeler_name', 'Anonymous'),
        'samples_labeled': row[0],
        'labels_created': row[1],
        'start_time': row[2]
    })

@app.route('/export_labels')
def export_labels():
    """Export all labels as JSON for training"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT 
            cs.id, cs.source_repo, cs.file_path, cs.code_snippet, 
            cs.language, cs.ast_features,
            pl.pattern_category, pl.pattern_type, pl.confidence, 
            pl.notes, pl.created_at,
            ls.labeler_name
        FROM code_samples cs
        JOIN pattern_labels pl ON cs.id = pl.sample_id
        JOIN labeling_sessions ls ON pl.session_id = ls.id
        ORDER BY pl.created_at
    ''')
    
    rows = cursor.fetchall()
    conn.close()
    
    labels = []
    for row in rows:
        labels.append({
            'sample_id': row[0],
            'source_repo': row[1],
            'file_path': row[2],
            'code_snippet': row[3],
            'language': row[4],
            'ast_features': json.loads(row[5]) if row[5] else {},
            'pattern_category': row[6],
            'pattern_type': row[7],
            'confidence': row[8],
            'notes': row[9],
            'labeled_at': row[10],
            'labeler_name': row[11]
        })
    
    return jsonify({
        'labels': labels,
        'total_count': len(labels),
        'export_time': datetime.now().isoformat()
    })

@app.route('/pattern_categories')
def pattern_categories():
    """Get available pattern categories and types"""
    return jsonify(PATTERN_CATEGORIES)

if __name__ == '__main__':
    init_database()
    
    # Create sample unlabeled data if file doesn't exist
    if not UNLABELED_SAMPLES_PATH.exists():
        sample_data = [
            {
                'source_repo': 'example/repo',
                'file_path': 'src/patterns/singleton.py',
                'code_snippet': '''class Singleton:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def get_instance(self):
        return self._instance''',
                'language': 'python',
                'ast_features': {'class_count': 1, 'method_count': 2}
            },
            {
                'source_repo': 'example/repo',
                'file_path': 'src/sql/user.py',
                'code_snippet': '''def get_user(user_id):
    query = f"SELECT * FROM users WHERE id = {user_id}"
    return execute_query(query)''',
                'language': 'python',
                'ast_features': {'function_count': 1, 'string_format': True}
            }
        ]
        
        with open(UNLABELED_SAMPLES_PATH, 'w') as f:
            json.dump(sample_data, f, indent=2)
    
    app.run(debug=True, host='0.0.0.0', port=5000)