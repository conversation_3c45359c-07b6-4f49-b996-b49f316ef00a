"""
Weak Supervision Implementation using Snorkel
Combines multiple weak labeling functions to create high-quality labels
"""
import numpy as np
import pandas as pd
from typing import List, Dict, Optional, Callable, Any
from dataclasses import dataclass
import re
import ast
from snorkel.labeling import (
    LabelingFunction, 
    labeling_function,
    PandasLFApplier,
    LabelModel,
    MajorityLabelVoter,
    filter_unlabeled_dataframe
)
from snorkel.labeling.model import MajorityLabelVoter
from snorkel.analysis import get_label_buckets
import logging

logger = logging.getLogger(__name__)

# Define pattern label mappings
ABSTAIN = -1
PATTERN_LABELS = {
    'SINGLETON': 0,
    'FACTORY': 1,
    'OBSERVER': 2,
    'STRATEGY': 3,
    'DECORATOR': 4,
    'ADAPTER': 5,
    'TEMPLATE': 6,
    'BUILDER': 7,
    'PROTOTYPE': 8,
    'FACADE': 9,
    'LONG_METHOD': 10,
    'GOD_CLASS': 11,
    'DUPLICATE_CODE': 12,
    'FEATURE_ENVY': 13,
    'SQL_INJECTION': 14,
    'XSS': 15,
    'HARDCODED_SECRET': 16,
    'N_PLUS_ONE': 17,
    'MEMORY_LEAK': 18,
    'INEFFICIENT_LOOP': 19,
    'UNKNOWN': 20
}

# Reverse mapping for label to pattern name
LABEL_TO_PATTERN = {v: k for k, v in PATTERN_LABELS.items()}


@dataclass
class CodeFeatures:
    """Features extracted from code for labeling"""
    code: str
    ast_tree: Optional[ast.AST]
    class_names: List[str]
    method_names: List[str]
    variable_names: List[str]
    imports: List[str]
    comments: List[str]
    line_count: int
    complexity: int
    has_static_fields: bool
    has_private_constructor: bool
    method_lengths: Dict[str, int]
    class_method_counts: Dict[str, int]
    sql_patterns: List[str]
    loop_patterns: List[str]


class WeakSupervisionLabeler:
    """Main class for weak supervision labeling"""
    
    def __init__(self):
        self.label_functions = self._create_label_functions()
        self.applier = PandasLFApplier(lfs=self.label_functions)
        self.label_model = LabelModel(cardinality=len(PATTERN_LABELS), verbose=True)
        self.majority_model = MajorityLabelVoter()
        
    def _create_label_functions(self) -> List[LabelingFunction]:
        """Create all labeling functions"""
        return [
            # Singleton pattern LFs
            lf_singleton_name,
            lf_singleton_instance_field,
            lf_singleton_private_constructor,
            lf_singleton_get_instance,
            
            # Factory pattern LFs
            lf_factory_name,
            lf_factory_create_method,
            lf_factory_product_hierarchy,
            
            # Observer pattern LFs
            lf_observer_name,
            lf_observer_subscribe_method,
            lf_observer_notify_method,
            lf_observer_listener_list,
            
            # Long method anti-pattern LFs
            lf_long_method_lines,
            lf_long_method_complexity,
            lf_long_method_parameters,
            
            # God class anti-pattern LFs
            lf_god_class_methods,
            lf_god_class_attributes,
            lf_god_class_dependencies,
            
            # SQL injection LFs
            lf_sql_injection_concatenation,
            lf_sql_injection_format,
            lf_sql_injection_fstring,
            
            # N+1 query problem LFs
            lf_n_plus_one_loop_query,
            lf_n_plus_one_orm_pattern,
            
            # Generic pattern LFs
            lf_pattern_comments,
            lf_pattern_naming_convention
        ]
    
    def extract_features(self, code: str, language: str = 'python') -> CodeFeatures:
        """Extract features from code for labeling functions"""
        features = CodeFeatures(
            code=code,
            ast_tree=None,
            class_names=[],
            method_names=[],
            variable_names=[],
            imports=[],
            comments=[],
            line_count=len(code.split('\n')),
            complexity=0,
            has_static_fields=False,
            has_private_constructor=False,
            method_lengths={},
            class_method_counts={},
            sql_patterns=[],
            loop_patterns=[]
        )
        
        # Parse AST for Python code
        if language == 'python':
            try:
                features.ast_tree = ast.parse(code)
                self._extract_python_features(features)
            except:
                logger.warning("Failed to parse Python AST")
        
        # Extract text-based features
        self._extract_text_features(features)
        
        return features
    
    def _extract_python_features(self, features: CodeFeatures):
        """Extract features from Python AST"""
        for node in ast.walk(features.ast_tree):
            if isinstance(node, ast.ClassDef):
                features.class_names.append(node.name)
                # Count methods in class
                method_count = sum(1 for n in node.body if isinstance(n, ast.FunctionDef))
                features.class_method_counts[node.name] = method_count
                
                # Check for private constructor
                for item in node.body:
                    if isinstance(item, ast.FunctionDef) and item.name == '__init__':
                        # Check if constructor has underscore prefix (private convention)
                        if any(d.id == 'private' for d in item.decorator_list if hasattr(d, 'id')):
                            features.has_private_constructor = True
            
            elif isinstance(node, ast.FunctionDef):
                features.method_names.append(node.name)
                # Calculate method length
                if hasattr(node, 'lineno') and hasattr(node, 'end_lineno'):
                    features.method_lengths[node.name] = node.end_lineno - node.lineno
                
            elif isinstance(node, ast.Name) and isinstance(node.ctx, ast.Store):
                features.variable_names.append(node.id)
                
            elif isinstance(node, ast.Import) or isinstance(node, ast.ImportFrom):
                module = node.module if hasattr(node, 'module') and node.module else ''
                features.imports.append(module)
        
        # Calculate cyclomatic complexity (simplified)
        features.complexity = self._calculate_complexity(features.ast_tree)
        
        # Check for static fields
        features.has_static_fields = self._has_static_fields(features.ast_tree)
    
    def _extract_text_features(self, features: CodeFeatures):
        """Extract text-based features"""
        lines = features.code.split('\n')
        
        # Extract comments
        for line in lines:
            if '#' in line:
                comment = line[line.index('#'):].strip()
                features.comments.append(comment)
        
        # Extract SQL patterns
        sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'FROM', 'WHERE']
        for line in lines:
            if any(keyword in line.upper() for keyword in sql_keywords):
                features.sql_patterns.append(line.strip())
        
        # Extract loop patterns
        loop_keywords = ['for ', 'while ', 'forEach', 'map(', 'filter(']
        for line in lines:
            if any(keyword in line for keyword in loop_keywords):
                features.loop_patterns.append(line.strip())
    
    def _calculate_complexity(self, tree: ast.AST) -> int:
        """Calculate cyclomatic complexity"""
        complexity = 1
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.ExceptHandler)):
                complexity += 1
            elif isinstance(node, ast.BoolOp):
                complexity += len(node.values) - 1
        return complexity
    
    def _has_static_fields(self, tree: ast.AST) -> bool:
        """Check if class has static fields"""
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                for item in node.body:
                    if isinstance(item, ast.AnnAssign) or isinstance(item, ast.Assign):
                        # Simple heuristic: class-level assignments might be static
                        return True
        return False
    
    def label_dataset(self, df: pd.DataFrame) -> pd.DataFrame:
        """Apply weak supervision to label dataset"""
        logger.info(f"Applying {len(self.label_functions)} labeling functions...")
        
        # Apply labeling functions
        L_train = self.applier.apply(df)
        
        # Train label model
        logger.info("Training label model...")
        self.label_model.fit(L_train=L_train, n_epochs=500, log_freq=100)
        
        # Get label model predictions
        label_model_preds = self.label_model.predict(L=L_train)
        label_model_probs = self.label_model.predict_proba(L=L_train)
        
        # Get majority vote predictions for comparison
        majority_preds = self.majority_model.predict(L=L_train)
        
        # Add predictions to dataframe
        df['label_model_prediction'] = label_model_preds
        df['label_model_confidence'] = label_model_probs.max(axis=1)
        df['majority_vote_prediction'] = majority_preds
        
        # Convert numeric labels to pattern names
        df['pattern_type'] = df['label_model_prediction'].map(LABEL_TO_PATTERN)
        
        # Analyze labeling function performance
        self._analyze_label_functions(L_train, df)
        
        return df
    
    def _analyze_label_functions(self, L: np.ndarray, df: pd.DataFrame):
        """Analyze labeling function performance"""
        logger.info("\nLabeling Function Analysis:")
        
        # Coverage: fraction of dataset each LF labels
        coverage = (L != ABSTAIN).mean(axis=0)
        
        # Conflicts: how often LFs disagree
        conflicts = []
        for i in range(len(self.label_functions)):
            for j in range(i+1, len(self.label_functions)):
                mask = (L[:, i] != ABSTAIN) & (L[:, j] != ABSTAIN)
                if mask.sum() > 0:
                    conflict_rate = (L[mask, i] != L[mask, j]).mean()
                    conflicts.append((i, j, conflict_rate))
        
        # Print analysis
        for i, lf in enumerate(self.label_functions):
            logger.info(f"  {lf.name}: Coverage = {coverage[i]:.3f}")
        
        # Sort conflicts by rate
        conflicts.sort(key=lambda x: x[2], reverse=True)
        logger.info("\nTop Conflicts:")
        for i, j, rate in conflicts[:5]:
            logger.info(f"  {self.label_functions[i].name} vs {self.label_functions[j].name}: {rate:.3f}")


# Singleton Pattern Labeling Functions
@labeling_function()
def lf_singleton_name(x):
    """Check if class name contains 'singleton'"""
    for class_name in x.class_names:
        if 'singleton' in class_name.lower():
            return PATTERN_LABELS['SINGLETON']
    return ABSTAIN

@labeling_function()
def lf_singleton_instance_field(x):
    """Check for static instance field"""
    if x.has_static_fields:
        for var in x.variable_names:
            if any(keyword in var.lower() for keyword in ['instance', '_instance', 'inst']):
                return PATTERN_LABELS['SINGLETON']
    return ABSTAIN

@labeling_function()
def lf_singleton_private_constructor(x):
    """Check for private constructor pattern"""
    if x.has_private_constructor and x.has_static_fields:
        return PATTERN_LABELS['SINGLETON']
    return ABSTAIN

@labeling_function()
def lf_singleton_get_instance(x):
    """Check for getInstance method"""
    for method in x.method_names:
        if any(pattern in method.lower() for pattern in ['getinstance', 'get_instance', 'instance']):
            if x.has_static_fields:
                return PATTERN_LABELS['SINGLETON']
    return ABSTAIN


# Factory Pattern Labeling Functions
@labeling_function()
def lf_factory_name(x):
    """Check if class/method name contains 'factory'"""
    all_names = x.class_names + x.method_names
    for name in all_names:
        if 'factory' in name.lower():
            return PATTERN_LABELS['FACTORY']
    return ABSTAIN

@labeling_function()
def lf_factory_create_method(x):
    """Check for create/make methods"""
    create_keywords = ['create', 'make', 'build', 'construct', 'produce']
    for method in x.method_names:
        if any(keyword in method.lower() for keyword in create_keywords):
            # Check if method likely returns objects
            if 'return' in x.code:
                return PATTERN_LABELS['FACTORY']
    return ABSTAIN

@labeling_function()
def lf_factory_product_hierarchy(x):
    """Check for product class hierarchy indicators"""
    # Look for abstract product classes
    if any('abstract' in imp.lower() for imp in x.imports):
        if any('product' in name.lower() for name in x.class_names):
            return PATTERN_LABELS['FACTORY']
    return ABSTAIN


# Observer Pattern Labeling Functions
@labeling_function()
def lf_observer_name(x):
    """Check for observer-related naming"""
    observer_keywords = ['observer', 'subscriber', 'listener', 'watcher']
    all_names = x.class_names + x.method_names + x.variable_names
    for name in all_names:
        if any(keyword in name.lower() for keyword in observer_keywords):
            return PATTERN_LABELS['OBSERVER']
    return ABSTAIN

@labeling_function()
def lf_observer_subscribe_method(x):
    """Check for subscribe/attach methods"""
    subscribe_keywords = ['subscribe', 'attach', 'register', 'add_listener', 'add_observer']
    for method in x.method_names:
        if any(keyword in method.lower() for keyword in subscribe_keywords):
            return PATTERN_LABELS['OBSERVER']
    return ABSTAIN

@labeling_function()
def lf_observer_notify_method(x):
    """Check for notify/update methods"""
    notify_keywords = ['notify', 'update', 'publish', 'broadcast', 'emit']
    for method in x.method_names:
        if any(keyword in method.lower() for keyword in notify_keywords):
            return PATTERN_LABELS['OBSERVER']
    return ABSTAIN

@labeling_function()
def lf_observer_listener_list(x):
    """Check for observer/listener collection"""
    collection_keywords = ['observers', 'listeners', 'subscribers', 'watchers']
    for var in x.variable_names:
        if any(keyword in var.lower() for keyword in collection_keywords):
            return PATTERN_LABELS['OBSERVER']
    return ABSTAIN


# Long Method Anti-Pattern Labeling Functions
@labeling_function()
def lf_long_method_lines(x):
    """Check if any method is too long"""
    for method, length in x.method_lengths.items():
        if length > 50:  # More than 50 lines
            return PATTERN_LABELS['LONG_METHOD']
    return ABSTAIN

@labeling_function()
def lf_long_method_complexity(x):
    """Check for high cyclomatic complexity"""
    if x.complexity > 10:
        return PATTERN_LABELS['LONG_METHOD']
    return ABSTAIN

@labeling_function()
def lf_long_method_parameters(x):
    """Check for methods with too many parameters"""
    # Simple heuristic: look for function definitions with many commas
    import re
    func_pattern = r'def\s+\w+\s*\([^)]+\)'
    for match in re.finditer(func_pattern, x.code):
        params = match.group()
        comma_count = params.count(',')
        if comma_count > 5:  # More than 5 parameters
            return PATTERN_LABELS['LONG_METHOD']
    return ABSTAIN


# God Class Anti-Pattern Labeling Functions
@labeling_function()
def lf_god_class_methods(x):
    """Check for classes with too many methods"""
    for class_name, method_count in x.class_method_counts.items():
        if method_count > 20:  # More than 20 methods
            return PATTERN_LABELS['GOD_CLASS']
    return ABSTAIN

@labeling_function()
def lf_god_class_attributes(x):
    """Check for classes with too many attributes"""
    # Heuristic: count variable assignments in class
    if len(x.class_names) > 0:
        vars_per_class = len(x.variable_names) / len(x.class_names)
        if vars_per_class > 15:  # More than 15 attributes per class
            return PATTERN_LABELS['GOD_CLASS']
    return ABSTAIN

@labeling_function()
def lf_god_class_dependencies(x):
    """Check for classes with too many imports/dependencies"""
    if len(x.imports) > 10:  # More than 10 imports
        if len(x.class_names) == 1:  # Single class file
            return PATTERN_LABELS['GOD_CLASS']
    return ABSTAIN


# SQL Injection Labeling Functions
@labeling_function()
def lf_sql_injection_concatenation(x):
    """Check for SQL string concatenation"""
    for sql_line in x.sql_patterns:
        if '+' in sql_line or '.format(' in sql_line:
            return PATTERN_LABELS['SQL_INJECTION']
    return ABSTAIN

@labeling_function()
def lf_sql_injection_format(x):
    """Check for SQL with % formatting"""
    sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE']
    for line in x.code.split('\n'):
        if any(keyword in line.upper() for keyword in sql_keywords):
            if '%s' in line or '%d' in line:
                return PATTERN_LABELS['SQL_INJECTION']
    return ABSTAIN

@labeling_function()
def lf_sql_injection_fstring(x):
    """Check for f-strings in SQL"""
    import re
    fstring_sql_pattern = r'f["\'].*(?:SELECT|INSERT|UPDATE|DELETE).*{.*}.*["\']'
    if re.search(fstring_sql_pattern, x.code, re.IGNORECASE):
        return PATTERN_LABELS['SQL_INJECTION']
    return ABSTAIN


# N+1 Query Problem Labeling Functions
@labeling_function()
def lf_n_plus_one_loop_query(x):
    """Check for queries inside loops"""
    in_loop = False
    for line in x.code.split('\n'):
        # Simple loop detection
        if any(keyword in line for keyword in ['for ', 'while ', 'forEach']):
            in_loop = True
        elif in_loop and any(keyword in line for keyword in ['.query(', '.find(', '.get(', 'SELECT']):
            return PATTERN_LABELS['N_PLUS_ONE']
        elif line.strip() == '' or (not line.startswith(' ') and not line.startswith('\t')):
            in_loop = False
    return ABSTAIN

@labeling_function()
def lf_n_plus_one_orm_pattern(x):
    """Check for ORM lazy loading patterns"""
    orm_patterns = [
        r'\.all\(\).*\n.*\.',  # .all() followed by attribute access
        r'for.*in.*\.objects\..*\n.*\.',  # Django ORM pattern
        r'session\.query.*\n.*for.*in',  # SQLAlchemy pattern
    ]
    for pattern in orm_patterns:
        if re.search(pattern, x.code, re.MULTILINE):
            return PATTERN_LABELS['N_PLUS_ONE']
    return ABSTAIN


# Generic Pattern Labeling Functions
@labeling_function()
def lf_pattern_comments(x):
    """Check comments for pattern mentions"""
    pattern_keywords = {
        'singleton': PATTERN_LABELS['SINGLETON'],
        'factory': PATTERN_LABELS['FACTORY'],
        'observer': PATTERN_LABELS['OBSERVER'],
        'strategy': PATTERN_LABELS['STRATEGY'],
        'decorator': PATTERN_LABELS['DECORATOR'],
        'anti-pattern': PATTERN_LABELS['GOD_CLASS'],
        'code smell': PATTERN_LABELS['LONG_METHOD']
    }
    
    for comment in x.comments:
        comment_lower = comment.lower()
        for keyword, label in pattern_keywords.items():
            if keyword in comment_lower:
                return label
    return ABSTAIN

@labeling_function()
def lf_pattern_naming_convention(x):
    """Check for common pattern naming conventions"""
    # Check class names for pattern suffixes
    pattern_suffixes = {
        'Factory': PATTERN_LABELS['FACTORY'],
        'Builder': PATTERN_LABELS['BUILDER'],
        'Adapter': PATTERN_LABELS['ADAPTER'],
        'Decorator': PATTERN_LABELS['DECORATOR'],
        'Facade': PATTERN_LABELS['FACADE'],
        'Proxy': PATTERN_LABELS['PROTOTYPE'],
        'Strategy': PATTERN_LABELS['STRATEGY'],
        'Observer': PATTERN_LABELS['OBSERVER']
    }
    
    for class_name in x.class_names:
        for suffix, label in pattern_suffixes.items():
            if class_name.endswith(suffix):
                return label
    return ABSTAIN


def main():
    """Example usage of weak supervision labeler"""
    # Create sample dataset
    sample_code = """
    class DatabaseConnection:
        _instance = None
        
        def __new__(cls):
            if cls._instance is None:
                cls._instance = super().__new__(cls)
            return cls._instance
        
        def get_instance(cls):
            return cls._instance
    """
    
    # Initialize labeler
    labeler = WeakSupervisionLabeler()
    
    # Extract features
    features = labeler.extract_features(sample_code)
    
    # Create dataframe
    df = pd.DataFrame([features.__dict__])
    
    # Label dataset
    labeled_df = labeler.label_dataset(df)
    
    print("Labeling complete!")
    print(f"Predicted pattern: {labeled_df['pattern_type'].iloc[0]}")
    print(f"Confidence: {labeled_df['label_model_confidence'].iloc[0]:.3f}")


if __name__ == '__main__':
    main()