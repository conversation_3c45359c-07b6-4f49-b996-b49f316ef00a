# Data Privacy & Compliance Framework

## Overview
Comprehensive privacy and compliance framework for CCL's ML training data collection, ensuring adherence to legal requirements, ethical standards, and user privacy protection.

## Privacy Requirements

### 1. License Compliance
Ensuring all code used for training respects intellectual property rights.

#### Permitted Licenses
- **MIT License**: Full usage permitted
- **Apache 2.0**: Full usage with attribution
- **BSD (2/3-Clause)**: Full usage with attribution
- **ISC License**: Full usage permitted
- **Unlicense/CC0**: Public domain, full usage
- **MPL 2.0**: Usage permitted with file-level copyleft

#### Restricted Licenses
- **GPL/AGPL**: Avoid due to copyleft requirements
- **Proprietary**: No usage without explicit permission
- **No License**: Treat as proprietary, do not use

#### License Validation Process
```python
def validate_license(repository: Repository) -> bool:
    """
    Validate repository license for ML training usage
    """
    # Check for license file
    license_file = find_license_file(repository)
    if not license_file:
        return False  # No license = proprietary
    
    # Parse license type
    license_type = detect_license_type(license_file.content)
    
    # Check against permitted list
    return license_type in PERMITTED_LICENSES
```

### 2. PII Removal
Protecting personally identifiable information in code samples.

#### PII Detection and Removal
```python
def remove_pii(code: str) -> str:
    """Remove personally identifiable information"""
    # Remove email addresses
    code = re.sub(r'[\w\.-]+@[\w\.-]+\.\w+', '<EMAIL>', code)
    
    # Remove API keys
    code = re.sub(r'api[_-]?key\s*=\s*["\'][\w\-]+["\']', 'api_key="REDACTED"', code)
    code = re.sub(r'[a-zA-Z0-9]{32,}', 'REDACTED_KEY', code)  # Long keys
    
    # Remove credentials
    code = re.sub(r'password\s*=\s*["\'][^"\']+["\']', 'password="REDACTED"', code)
    code = re.sub(r'token\s*=\s*["\'][^"\']+["\']', 'token="REDACTED"', code)
    
    # Remove URLs with credentials
    code = re.sub(r'https?://[^:]+:[^@]+@[^\s]+', 'https://user:<EMAIL>', code)
    
    # Remove IP addresses
    code = re.sub(r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b', '0.0.0.0', code)
    
    # Remove phone numbers
    code = re.sub(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b', '************', code)
    code = re.sub(r'\+\d{1,3}\s?\d{1,14}', '+0 0000000000', code)
    
    # Remove SSN patterns
    code = re.sub(r'\b\d{3}-\d{2}-\d{4}\b', '***********', code)
    
    # Remove names from comments (heuristic)
    code = remove_names_from_comments(code)
    
    # Remove file paths with usernames
    code = re.sub(r'/home/<USER>/]+/', '/home/<USER>/', code)
    code = re.sub(r'/Users/<USER>/]+/', '/Users/<USER>/', code)
    code = re.sub(r'C:\\Users\\<USER>\\]+\\', 'C:\\Users\\<USER>\\', code)
    
    return code
```

#### PII Categories
1. **Email Addresses**: Replace with generic
2. **API Keys/Tokens**: Redact completely
3. **Passwords/Secrets**: Redact completely
4. **IP Addresses**: Replace with 0.0.0.0
5. **Phone Numbers**: Replace with generic
6. **SSNs**: Replace with generic
7. **Names**: Remove from comments/strings
8. **File Paths**: Anonymize usernames
9. **URLs**: Remove credentials
10. **Credit Card Numbers**: Redact completely

### 3. Data Retention
Clear policies for data lifecycle management.

#### Retention Periods
- **Training Data**: 2 years from collection
- **Model Artifacts**: Indefinite (anonymized)
- **User Submissions**: 90 days unless consent given
- **Audit Logs**: 7 years (compliance requirement)
- **Rejected Data**: 30 days then permanent deletion

#### Retention Policy Implementation
```python
class DataRetentionManager:
    def __init__(self):
        self.retention_policies = {
            'training_data': timedelta(days=730),  # 2 years
            'user_submissions': timedelta(days=90),
            'audit_logs': timedelta(days=2555),    # 7 years
            'rejected_data': timedelta(days=30)
        }
    
    def apply_retention_policies(self):
        """Apply data retention policies"""
        for data_type, retention_period in self.retention_policies.items():
            cutoff_date = datetime.now() - retention_period
            
            # Mark data for deletion
            expired_data = self.find_expired_data(data_type, cutoff_date)
            
            # Anonymize before deletion if required
            if data_type in ['training_data', 'user_submissions']:
                self.anonymize_data(expired_data)
            
            # Delete expired data
            self.delete_data(expired_data)
            
            # Log deletion for audit
            self.log_deletion(data_type, len(expired_data))
```

### 4. Right to Deletion
Supporting user rights to have their data removed.

#### Deletion Request Process
1. **Request Verification**: Authenticate requester
2. **Data Discovery**: Find all instances of user data
3. **Impact Assessment**: Determine deletion impact
4. **Execution**: Remove or anonymize data
5. **Confirmation**: Notify user of completion

#### Implementation
```python
class DeletionRequestHandler:
    def process_deletion_request(self, user_id: str, verification_token: str):
        """Process user deletion request"""
        # Verify request
        if not self.verify_request(user_id, verification_token):
            raise UnauthorizedError("Invalid deletion request")
        
        # Find user data
        user_data = self.find_user_data(user_id)
        
        # Check for legal holds
        if self.has_legal_hold(user_id):
            return DeletionResult(
                status='partially_completed',
                reason='Legal hold prevents full deletion'
            )
        
        # Anonymize training data (can't delete from models)
        anonymized_count = self.anonymize_training_data(user_data['training'])
        
        # Delete raw submissions
        deleted_count = self.delete_raw_data(user_data['raw'])
        
        # Update audit log
        self.log_deletion_request(user_id, anonymized_count, deleted_count)
        
        return DeletionResult(
            status='completed',
            anonymized=anonymized_count,
            deleted=deleted_count
        )
```

## Compliance Framework

### GDPR Compliance
Meeting General Data Protection Regulation requirements.

#### Lawful Basis
- **Legitimate Interest**: Improving code quality tools
- **Consent**: For user-submitted patterns
- **Public Interest**: Open source code analysis

#### Data Subject Rights
1. **Right to Access**: Export user's submitted data
2. **Right to Rectification**: Correct mislabeled patterns
3. **Right to Erasure**: Delete/anonymize on request
4. **Right to Portability**: Export in standard format
5. **Right to Object**: Opt-out of data collection

#### Privacy by Design
```python
class PrivacyByDesign:
    """Implement privacy by design principles"""
    
    def __init__(self):
        self.privacy_settings = {
            'data_minimization': True,
            'purpose_limitation': True,
            'default_privacy': True,
            'transparency': True
        }
    
    def collect_data(self, source: DataSource) -> ProcessedData:
        """Collect only necessary data"""
        # Minimize data collection
        if self.privacy_settings['data_minimization']:
            data = source.get_minimal_data()
        else:
            data = source.get_all_data()
        
        # Apply purpose limitation
        if self.privacy_settings['purpose_limitation']:
            data = self.filter_by_purpose(data, purpose='pattern_detection')
        
        # Remove PII by default
        if self.privacy_settings['default_privacy']:
            data = self.remove_all_pii(data)
        
        return data
```

### CCPA Compliance
Meeting California Consumer Privacy Act requirements.

#### Consumer Rights
1. **Right to Know**: What data is collected
2. **Right to Delete**: Request deletion
3. **Right to Opt-Out**: Decline data sale/sharing
4. **Right to Non-Discrimination**: Equal service

#### Implementation
```python
class CCPACompliance:
    def handle_consumer_request(self, request_type: str, consumer_id: str):
        """Handle CCPA consumer requests"""
        
        if request_type == 'access':
            # Provide data inventory
            return self.generate_data_inventory(consumer_id)
            
        elif request_type == 'delete':
            # Process deletion
            return self.process_deletion(consumer_id)
            
        elif request_type == 'opt_out':
            # Record opt-out preference
            return self.record_opt_out(consumer_id)
```

### HIPAA Considerations
For healthcare-related code patterns.

#### Safeguards
- **No PHI Collection**: Never collect patient data
- **Code Sanitization**: Remove any health identifiers
- **Access Controls**: Restrict healthcare pattern access
- **Audit Trails**: Track all healthcare code access

### Data Governance

#### Data Classification
```yaml
Data Categories:
  public:
    - description: Open source code with permissive licenses
    - retention: 2 years
    - pii_removal: required
    
  internal:
    - description: Generated synthetic patterns
    - retention: indefinite
    - pii_removal: not_applicable
    
  confidential:
    - description: User-submitted patterns
    - retention: 90 days
    - pii_removal: required
    - consent: required
    
  restricted:
    - description: Enterprise partner data
    - retention: per_agreement
    - pii_removal: required
    - encryption: required
```

#### Access Controls
```python
class DataAccessControl:
    def __init__(self):
        self.access_levels = {
            'public': ['researcher', 'engineer', 'analyst'],
            'internal': ['engineer', 'analyst'],
            'confidential': ['senior_engineer', 'privacy_officer'],
            'restricted': ['privacy_officer', 'cto']
        }
    
    def check_access(self, user_role: str, data_category: str) -> bool:
        """Check if user role has access to data category"""
        allowed_roles = self.access_levels.get(data_category, [])
        return user_role in allowed_roles
```

## Security Measures

### Encryption
- **At Rest**: AES-256 encryption for stored data
- **In Transit**: TLS 1.3 for all transfers
- **Key Management**: Google Cloud KMS

### Anonymization Techniques
```python
class DataAnonymizer:
    def anonymize_code(self, code: str, level: str = 'standard') -> str:
        """Anonymize code based on privacy level"""
        
        if level == 'standard':
            # Remove PII
            code = remove_pii(code)
            # Normalize variable names
            code = self.normalize_identifiers(code)
            
        elif level == 'strict':
            # Remove all identifiers
            code = self.remove_all_identifiers(code)
            # Remove comments
            code = self.remove_comments(code)
            # Generalize structure
            code = self.generalize_structure(code)
            
        return code
    
    def k_anonymity(self, dataset: pd.DataFrame, k: int = 5) -> pd.DataFrame:
        """Ensure k-anonymity in dataset"""
        # Group by quasi-identifiers
        groups = dataset.groupby(['language', 'pattern_type'])
        
        # Remove groups with less than k members
        anonymous_groups = []
        for name, group in groups:
            if len(group) >= k:
                anonymous_groups.append(group)
        
        return pd.concat(anonymous_groups)
```

### Audit Logging
```python
class PrivacyAuditLogger:
    def log_data_access(self, user: str, data_id: str, purpose: str):
        """Log all data access for privacy audits"""
        audit_entry = {
            'timestamp': datetime.now(),
            'user': user,
            'data_id': data_id,
            'purpose': purpose,
            'action': 'access',
            'ip_address': get_client_ip(),
            'classification': get_data_classification(data_id)
        }
        
        # Store in append-only audit log
        self.append_to_audit_log(audit_entry)
        
        # Alert on suspicious access
        if self.is_suspicious_access(audit_entry):
            self.send_security_alert(audit_entry)
```

## Compliance Monitoring

### Automated Checks
```python
class ComplianceMonitor:
    def run_compliance_checks(self):
        """Run automated compliance checks"""
        
        checks = {
            'license_compliance': self.check_license_compliance(),
            'pii_removal': self.check_pii_removal(),
            'retention_compliance': self.check_retention_compliance(),
            'access_control': self.check_access_controls(),
            'encryption': self.check_encryption_status()
        }
        
        # Generate compliance report
        report = ComplianceReport(
            timestamp=datetime.now(),
            checks=checks,
            passed=all(check.passed for check in checks.values())
        )
        
        # Alert on failures
        if not report.passed:
            self.send_compliance_alert(report)
        
        return report
```

### Privacy Impact Assessment
Regular assessments to identify and mitigate privacy risks.

```python
class PrivacyImpactAssessment:
    def assess_data_collection(self, collection_method: str) -> PIAReport:
        """Assess privacy impact of data collection method"""
        
        risks = []
        mitigations = []
        
        # Assess PII risk
        pii_risk = self.assess_pii_risk(collection_method)
        if pii_risk.level > 'low':
            risks.append(pii_risk)
            mitigations.append('Implement PII scanning and removal')
        
        # Assess consent requirements
        consent_required = self.assess_consent_requirements(collection_method)
        if consent_required:
            mitigations.append('Implement consent management')
        
        # Assess cross-border transfer
        transfer_risk = self.assess_transfer_risk(collection_method)
        if transfer_risk.level > 'low':
            risks.append(transfer_risk)
            mitigations.append('Implement data localization')
        
        return PIAReport(
            method=collection_method,
            risks=risks,
            mitigations=mitigations,
            overall_risk=self.calculate_overall_risk(risks)
        )
```

## Best Practices

### Data Minimization
1. Collect only necessary code features
2. Avoid collecting entire repositories
3. Focus on specific pattern-relevant code
4. Discard intermediate processing data

### Transparency
1. Clear documentation of data usage
2. Public privacy policy
3. Data collection notifications
4. Regular transparency reports

### User Control
1. Easy opt-out mechanisms
2. Data export capabilities
3. Deletion request portal
4. Consent management

### Security First
1. Encryption by default
2. Access logging
3. Regular security audits
4. Incident response plan

## Compliance Checklist

- [ ] All source code has valid licenses
- [ ] PII removal process implemented
- [ ] Data retention policies automated
- [ ] Deletion request system functional
- [ ] GDPR compliance verified
- [ ] CCPA requirements met
- [ ] Encryption implemented
- [ ] Access controls configured
- [ ] Audit logging active
- [ ] Privacy policy published
- [ ] Consent system operational
- [ ] Regular compliance audits scheduled