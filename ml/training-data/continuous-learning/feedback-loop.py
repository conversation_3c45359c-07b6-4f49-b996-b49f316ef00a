"""
Continuous Learning from Production Feedback
Collects user corrections and high-confidence predictions to improve models
"""
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from collections import defaultdict
import json
import logging
from enum import Enum
import asyncio
from abc import ABC, abstractmethod

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FeedbackType(Enum):
    """Types of feedback from production"""
    USER_CORRECTION = "user_correction"
    USER_VALIDATION = "user_validation"
    HIGH_CONFIDENCE = "high_confidence"
    EDGE_CASE = "edge_case"
    MODEL_DISAGREEMENT = "model_disagreement"
    NEW_PATTERN = "new_pattern"


@dataclass
class FeedbackItem:
    """Individual feedback item from production"""
    feedback_id: str
    sample_id: str
    feedback_type: FeedbackType
    original_prediction: Dict[str, Any]
    corrected_label: Optional[str]
    confidence_delta: float
    user_id: Optional[str]
    timestamp: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class LearningBatch:
    """Batch of feedback ready for model update"""
    batch_id: str
    feedback_items: List[FeedbackItem]
    created_at: datetime
    processed: bool = False
    model_version: Optional[str] = None
    performance_delta: Optional[float] = None


class ContinuousLearningPipeline:
    """Main pipeline for continuous learning from production feedback"""
    
    def __init__(self, model_registry: str, data_store: str):
        self.model_registry = model_registry
        self.data_store = data_store
        self.feedback_buffer = []
        self.learning_threshold = 100  # Min feedback items before retraining
        self.confidence_threshold = 0.85
        self.edge_case_detector = EdgeCaseDetector()
        self.model_evaluator = ModelEvaluator()
        self.active_learning = ActiveLearningSelector()
        
    async def collect_production_feedback(self):
        """Collect feedback from various production sources"""
        feedback_items = []
        
        # 1. User corrections
        corrections = await self.get_user_corrections()
        feedback_items.extend(corrections)
        
        # 2. High-confidence predictions
        confident_predictions = await self.get_confident_predictions()
        feedback_items.extend(confident_predictions)
        
        # 3. Edge cases
        edge_cases = await self.get_edge_cases()
        feedback_items.extend(edge_cases)
        
        # 4. Model disagreements
        disagreements = await self.get_model_disagreements()
        feedback_items.extend(disagreements)
        
        # Add to buffer
        self.feedback_buffer.extend(feedback_items)
        
        # Trigger learning if threshold met
        if len(self.feedback_buffer) >= self.learning_threshold:
            await self.trigger_model_update()
        
        return feedback_items
    
    async def get_user_corrections(self) -> List[FeedbackItem]:
        """Get user-provided corrections"""
        corrections = []
        
        # Query correction database
        user_corrections = await self.query_corrections_db()
        
        for correction in user_corrections:
            feedback = FeedbackItem(
                feedback_id=f"correction_{correction['id']}",
                sample_id=correction['sample_id'],
                feedback_type=FeedbackType.USER_CORRECTION,
                original_prediction={
                    'pattern': correction['original_pattern'],
                    'confidence': correction['original_confidence']
                },
                corrected_label=correction['corrected_pattern'],
                confidence_delta=1.0 - correction['original_confidence'],
                user_id=correction['user_id'],
                timestamp=correction['timestamp'],
                metadata={'correction_reason': correction.get('reason')}
            )
            corrections.append(feedback)
        
        logger.info(f"Collected {len(corrections)} user corrections")
        return corrections
    
    async def get_confident_predictions(self) -> List[FeedbackItem]:
        """Get high-confidence predictions for pseudo-labeling"""
        confident_items = []
        
        # Query recent predictions
        recent_predictions = await self.query_prediction_log(
            confidence_min=self.confidence_threshold,
            time_window=timedelta(hours=24)
        )
        
        for pred in recent_predictions:
            # Verify prediction stability
            if await self.is_prediction_stable(pred):
                feedback = FeedbackItem(
                    feedback_id=f"confident_{pred['id']}",
                    sample_id=pred['sample_id'],
                    feedback_type=FeedbackType.HIGH_CONFIDENCE,
                    original_prediction={
                        'pattern': pred['pattern'],
                        'confidence': pred['confidence']
                    },
                    corrected_label=pred['pattern'],  # Use as correct label
                    confidence_delta=0.0,
                    user_id=None,
                    timestamp=pred['timestamp'],
                    metadata={'model_version': pred['model_version']}
                )
                confident_items.append(feedback)
        
        logger.info(f"Collected {len(confident_items)} high-confidence predictions")
        return confident_items
    
    async def get_edge_cases(self) -> List[FeedbackItem]:
        """Identify and collect edge cases"""
        edge_cases = []
        
        # Use edge case detector
        recent_samples = await self.get_recent_samples()
        detected_edges = self.edge_case_detector.detect(recent_samples)
        
        for edge in detected_edges:
            feedback = FeedbackItem(
                feedback_id=f"edge_{edge['id']}",
                sample_id=edge['sample_id'],
                feedback_type=FeedbackType.EDGE_CASE,
                original_prediction=edge['prediction'],
                corrected_label=None,  # Needs manual labeling
                confidence_delta=edge['uncertainty'],
                user_id=None,
                timestamp=datetime.now(),
                metadata={
                    'edge_type': edge['type'],
                    'anomaly_score': edge['anomaly_score']
                }
            )
            edge_cases.append(feedback)
        
        logger.info(f"Detected {len(edge_cases)} edge cases")
        return edge_cases
    
    async def get_model_disagreements(self) -> List[FeedbackItem]:
        """Get samples where models disagree"""
        disagreements = []
        
        # Query ensemble predictions
        ensemble_results = await self.query_ensemble_predictions()
        
        for result in ensemble_results:
            if result['disagreement_score'] > 0.3:
                feedback = FeedbackItem(
                    feedback_id=f"disagree_{result['id']}",
                    sample_id=result['sample_id'],
                    feedback_type=FeedbackType.MODEL_DISAGREEMENT,
                    original_prediction=result['predictions'],
                    corrected_label=None,  # Needs resolution
                    confidence_delta=result['disagreement_score'],
                    user_id=None,
                    timestamp=result['timestamp'],
                    metadata={
                        'model_predictions': result['model_predictions'],
                        'voting_result': result['voting_result']
                    }
                )
                disagreements.append(feedback)
        
        logger.info(f"Found {len(disagreements)} model disagreements")
        return disagreements
    
    async def trigger_model_update(self):
        """Trigger model retraining with accumulated feedback"""
        logger.info("Triggering model update with feedback data")
        
        # Create learning batch
        batch = LearningBatch(
            batch_id=f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            feedback_items=self.feedback_buffer.copy(),
            created_at=datetime.now()
        )
        
        # Prepare training data
        training_data = await self.prepare_training_data(batch)
        
        # Validate data quality
        if not await self.validate_training_data(training_data):
            logger.warning("Training data failed validation, skipping update")
            return
        
        # Retrain model
        new_model = await self.retrain_model(training_data)
        
        # Evaluate improvement
        improvement = await self.evaluate_model_improvement(new_model)
        
        if improvement > 0:
            # Deploy new model
            await self.deploy_model(new_model)
            batch.model_version = new_model.version
            batch.performance_delta = improvement
            logger.info(f"Deployed new model with {improvement:.2%} improvement")
        else:
            logger.info("New model did not improve performance, keeping current")
        
        # Clear buffer
        self.feedback_buffer.clear()
        
        # Store batch for audit
        await self.store_learning_batch(batch)
    
    async def prepare_training_data(self, batch: LearningBatch) -> pd.DataFrame:
        """Prepare training data from feedback batch"""
        training_samples = []
        
        for item in batch.feedback_items:
            # Load original sample
            original_sample = await self.load_sample(item.sample_id)
            
            # Apply feedback
            if item.feedback_type == FeedbackType.USER_CORRECTION:
                # Use corrected label with high weight
                sample = {
                    **original_sample,
                    'pattern_type': item.corrected_label,
                    'confidence': 1.0,
                    'weight': 2.0,  # Higher weight for corrections
                    'source': 'user_correction'
                }
                
            elif item.feedback_type == FeedbackType.HIGH_CONFIDENCE:
                # Use as is with normal weight
                sample = {
                    **original_sample,
                    'pattern_type': item.corrected_label,
                    'confidence': item.original_prediction['confidence'],
                    'weight': 1.0,
                    'source': 'high_confidence'
                }
                
            elif item.feedback_type == FeedbackType.EDGE_CASE:
                # Request manual labeling
                label = await self.request_manual_label(item)
                if label:
                    sample = {
                        **original_sample,
                        'pattern_type': label,
                        'confidence': 0.8,
                        'weight': 1.5,  # Higher weight for edge cases
                        'source': 'edge_case'
                    }
                else:
                    continue  # Skip if no label available
                    
            elif item.feedback_type == FeedbackType.MODEL_DISAGREEMENT:
                # Use voting or manual resolution
                resolved_label = await self.resolve_disagreement(item)
                if resolved_label:
                    sample = {
                        **original_sample,
                        'pattern_type': resolved_label,
                        'confidence': 0.7,
                        'weight': 1.2,
                        'source': 'disagreement_resolved'
                    }
                else:
                    continue
            
            training_samples.append(sample)
        
        # Convert to DataFrame
        df = pd.DataFrame(training_samples)
        
        # Balance dataset if needed
        df = self.balance_dataset(df)
        
        return df
    
    def balance_dataset(self, df: pd.DataFrame) -> pd.DataFrame:
        """Balance dataset to prevent bias"""
        # Count samples per pattern
        pattern_counts = df['pattern_type'].value_counts()
        
        # Determine target count (median)
        target_count = int(pattern_counts.median())
        
        balanced_dfs = []
        for pattern, count in pattern_counts.items():
            pattern_df = df[df['pattern_type'] == pattern]
            
            if count > target_count:
                # Downsample
                pattern_df = pattern_df.sample(n=target_count, random_state=42)
            elif count < target_count * 0.5:
                # Upsample if severely underrepresented
                n_samples = min(target_count, count * 2)
                pattern_df = pattern_df.sample(n=n_samples, replace=True, random_state=42)
            
            balanced_dfs.append(pattern_df)
        
        return pd.concat(balanced_dfs, ignore_index=True)
    
    async def retrain_model(self, training_data: pd.DataFrame):
        """Retrain model with new data"""
        from ..model_training import ModelTrainer
        
        trainer = ModelTrainer(self.model_registry)
        
        # Load current model
        current_model = await trainer.load_latest_model()
        
        # Incremental training
        new_model = await trainer.incremental_train(
            base_model=current_model,
            new_data=training_data,
            epochs=10,
            learning_rate=0.001
        )
        
        return new_model
    
    async def evaluate_model_improvement(self, new_model) -> float:
        """Evaluate if new model improves performance"""
        # Load test set
        test_data = await self.load_test_data()
        
        # Evaluate current model
        current_model = await self.load_current_model()
        current_metrics = await self.model_evaluator.evaluate(current_model, test_data)
        
        # Evaluate new model
        new_metrics = await self.model_evaluator.evaluate(new_model, test_data)
        
        # Calculate improvement
        improvements = []
        for metric in ['accuracy', 'precision', 'recall', 'f1']:
            if metric in current_metrics and metric in new_metrics:
                improvement = (new_metrics[metric] - current_metrics[metric]) / current_metrics[metric]
                improvements.append(improvement)
        
        avg_improvement = np.mean(improvements)
        
        logger.info(f"Model improvement: {avg_improvement:.2%}")
        logger.info(f"Current metrics: {current_metrics}")
        logger.info(f"New metrics: {new_metrics}")
        
        return avg_improvement
    
    async def is_prediction_stable(self, prediction: Dict) -> bool:
        """Check if a prediction is stable across time"""
        # Get historical predictions for same pattern
        history = await self.get_prediction_history(
            prediction['sample_id'],
            time_window=timedelta(hours=6)
        )
        
        if len(history) < 3:
            return False
        
        # Check consistency
        patterns = [h['pattern'] for h in history]
        confidences = [h['confidence'] for h in history]
        
        # Pattern should be consistent
        if len(set(patterns)) > 1:
            return False
        
        # Confidence should be stable
        confidence_std = np.std(confidences)
        if confidence_std > 0.1:
            return False
        
        return True
    
    async def request_manual_label(self, feedback_item: FeedbackItem) -> Optional[str]:
        """Request manual labeling for uncertain samples"""
        # Add to labeling queue
        await self.add_to_labeling_queue({
            'sample_id': feedback_item.sample_id,
            'feedback_id': feedback_item.feedback_id,
            'priority': 'high' if feedback_item.feedback_type == FeedbackType.EDGE_CASE else 'normal',
            'metadata': feedback_item.metadata
        })
        
        # Wait for label (with timeout)
        try:
            label = await asyncio.wait_for(
                self.get_manual_label(feedback_item.sample_id),
                timeout=3600  # 1 hour timeout
            )
            return label
        except asyncio.TimeoutError:
            logger.warning(f"Timeout waiting for manual label for {feedback_item.sample_id}")
            return None
    
    async def resolve_disagreement(self, feedback_item: FeedbackItem) -> Optional[str]:
        """Resolve model disagreements"""
        predictions = feedback_item.metadata.get('model_predictions', {})
        
        # Try majority voting
        if predictions:
            pattern_votes = defaultdict(int)
            for model, pred in predictions.items():
                pattern_votes[pred['pattern']] += pred['confidence']
            
            # Get pattern with highest weighted votes
            best_pattern = max(pattern_votes.items(), key=lambda x: x[1])
            
            # If confidence is high enough, use it
            if best_pattern[1] > 0.7 * len(predictions):
                return best_pattern[0]
        
        # Otherwise request manual resolution
        return await self.request_manual_label(feedback_item)


class EdgeCaseDetector:
    """Detect edge cases in production data"""
    
    def detect(self, samples: List[Dict]) -> List[Dict]:
        """Detect edge cases using multiple methods"""
        edge_cases = []
        
        for sample in samples:
            # Anomaly detection
            anomaly_score = self.calculate_anomaly_score(sample)
            
            # Uncertainty detection
            uncertainty = self.calculate_uncertainty(sample)
            
            # Novelty detection
            novelty_score = self.calculate_novelty(sample)
            
            # Combine scores
            edge_score = (anomaly_score + uncertainty + novelty_score) / 3
            
            if edge_score > 0.7:
                edge_cases.append({
                    'id': sample['id'],
                    'sample_id': sample['sample_id'],
                    'prediction': sample['prediction'],
                    'type': self.classify_edge_type(anomaly_score, uncertainty, novelty_score),
                    'anomaly_score': anomaly_score,
                    'uncertainty': uncertainty,
                    'novelty_score': novelty_score
                })
        
        return edge_cases
    
    def calculate_anomaly_score(self, sample: Dict) -> float:
        """Calculate anomaly score using isolation forest or similar"""
        # Simplified implementation
        features = sample.get('features', [])
        if not features:
            return 0.0
        
        # Check if features are outliers
        feature_array = np.array(features)
        mean = np.mean(feature_array)
        std = np.std(feature_array)
        
        # Z-score based anomaly
        z_scores = np.abs((feature_array - mean) / (std + 1e-8))
        anomaly_score = np.mean(z_scores > 3)  # Features > 3 std dev
        
        return min(anomaly_score * 2, 1.0)  # Scale to [0, 1]
    
    def calculate_uncertainty(self, sample: Dict) -> float:
        """Calculate prediction uncertainty"""
        prediction = sample.get('prediction', {})
        
        # Entropy-based uncertainty
        if 'probabilities' in prediction:
            probs = np.array(prediction['probabilities'])
            entropy = -np.sum(probs * np.log(probs + 1e-8))
            max_entropy = np.log(len(probs))
            return entropy / max_entropy if max_entropy > 0 else 0.0
        
        # Confidence-based uncertainty
        confidence = prediction.get('confidence', 1.0)
        return 1.0 - confidence
    
    def calculate_novelty(self, sample: Dict) -> float:
        """Calculate novelty score"""
        # Check if pattern combination is novel
        metadata = sample.get('metadata', {})
        
        # Simple heuristic: new language/pattern combination
        if metadata.get('is_new_combination'):
            return 0.8
        
        # Feature space distance from known samples
        if 'nearest_neighbor_distance' in metadata:
            # Normalize distance to [0, 1]
            distance = metadata['nearest_neighbor_distance']
            return min(distance / 10.0, 1.0)
        
        return 0.0
    
    def classify_edge_type(self, anomaly: float, uncertainty: float, novelty: float) -> str:
        """Classify the type of edge case"""
        scores = {
            'anomaly': anomaly,
            'uncertainty': uncertainty,
            'novelty': novelty
        }
        
        return max(scores.items(), key=lambda x: x[1])[0]


class ModelEvaluator:
    """Evaluate model performance"""
    
    async def evaluate(self, model, test_data: pd.DataFrame) -> Dict[str, float]:
        """Evaluate model on test data"""
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
        
        # Get predictions
        X = test_data.drop(['pattern_type'], axis=1)
        y_true = test_data['pattern_type']
        y_pred = model.predict(X)
        
        # Calculate metrics
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred, average='weighted'),
            'recall': recall_score(y_true, y_pred, average='weighted'),
            'f1': f1_score(y_true, y_pred, average='weighted')
        }
        
        # Add pattern-specific metrics
        pattern_metrics = {}
        for pattern in y_true.unique():
            pattern_mask = y_true == pattern
            if pattern_mask.sum() > 0:
                pattern_metrics[f'{pattern}_f1'] = f1_score(
                    y_true[pattern_mask],
                    y_pred[pattern_mask],
                    average='binary',
                    pos_label=pattern
                )
        
        metrics.update(pattern_metrics)
        
        return metrics


class ActiveLearningSelector:
    """Select samples for active learning"""
    
    def select_samples(self, unlabeled_pool: List[Dict], n_samples: int) -> List[Dict]:
        """Select most informative samples for labeling"""
        if not unlabeled_pool:
            return []
        
        # Calculate informativeness scores
        scores = []
        for sample in unlabeled_pool:
            score = self.calculate_informativeness(sample)
            scores.append((score, sample))
        
        # Sort by score and select top n
        scores.sort(key=lambda x: x[0], reverse=True)
        selected = [sample for score, sample in scores[:n_samples]]
        
        return selected
    
    def calculate_informativeness(self, sample: Dict) -> float:
        """Calculate how informative a sample would be for learning"""
        score = 0.0
        
        # Uncertainty (most important)
        if 'uncertainty' in sample:
            score += sample['uncertainty'] * 0.4
        
        # Diversity from existing labeled data
        if 'diversity_score' in sample:
            score += sample['diversity_score'] * 0.3
        
        # Representativeness
        if 'density_score' in sample:
            score += sample['density_score'] * 0.2
        
        # Query by committee disagreement
        if 'disagreement_score' in sample:
            score += sample['disagreement_score'] * 0.1
        
        return score


async def main():
    """Example usage of continuous learning pipeline"""
    # Initialize pipeline
    pipeline = ContinuousLearningPipeline(
        model_registry='gs://ccl-models',
        data_store='gs://ccl-training-data'
    )
    
    # Start continuous learning loop
    while True:
        try:
            # Collect feedback
            feedback = await pipeline.collect_production_feedback()
            logger.info(f"Collected {len(feedback)} feedback items")
            
            # Sleep before next collection
            await asyncio.sleep(3600)  # 1 hour
            
        except Exception as e:
            logger.error(f"Error in continuous learning: {e}")
            await asyncio.sleep(300)  # 5 minutes on error


if __name__ == '__main__':
    asyncio.run(main())