# CCL Training Data Acquisition Strategy

## Overview
Multi-source approach to build a comprehensive training dataset for pattern detection. This strategy addresses the critical gap identified in the Pattern Detection PRP review, enabling high-quality ML model training for detecting coding patterns, anti-patterns, security vulnerabilities, and architectural patterns.

## Data Sources

### 1. Open Source Repositories (Primary Source)
**Target Volume**: 10,000 repositories, 100M+ lines of code
**Timeline**: 4 weeks

#### Selection Criteria
- GitHub stars > 1000 (quality indicator)
- Active maintenance (commits in last 6 months)
- Diverse languages (Java, Python, Go, JavaScript, Rust)
- Explicit patterns in documentation
- Permissive licenses (MIT, Apache 2.0, BSD)

#### Collection Pipeline
```python
def collect_open_source_data():
    """
    1. Query GitHub API for repositories
    2. Filter by criteria
    3. Clone repositories
    4. Extract code + documentation
    5. Initial pattern labeling
    """
```

#### Repository Categories
- **Web Frameworks**: React, Vue, Angular, Django, Rails
- **Backend Services**: Spring Boot, Express, FastAPI
- **Databases**: PostgreSQL, MongoDB, Redis implementations
- **Cloud Native**: Kubernetes operators, Terraform modules
- **Design Pattern Examples**: Repositories explicitly demonstrating patterns

### 2. Synthetic Data Generation (Augmentation)
**Target Volume**: 50,000 synthetic examples
**Timeline**: 2 weeks

#### Pattern Templates
```python
# Example: Generate variations of Singleton pattern
def generate_singleton_variations():
    templates = [
        "eager_initialization",
        "lazy_initialization", 
        "thread_safe_singleton",
        "enum_singleton"
    ]
    
    for template in templates:
        for language in ["java", "python", "go", "typescript"]:
            generate_pattern(template, language)
```

#### Synthetic Generation Strategy
- **Pattern Variations**: Generate multiple valid implementations of each pattern
- **Anti-Pattern Examples**: Create known bad implementations
- **Edge Cases**: Generate boundary conditions and unusual implementations
- **Language Idioms**: Respect language-specific conventions

### 3. Community Contributions (Ongoing)
**Target**: 1000 labeled examples/month
**Incentive**: Free CCL credits

#### Contribution Platform
- Web interface for pattern submission
- Automated validation
- Community voting on quality
- Reward system

#### Contribution Types
- **Pattern Examples**: Submit working pattern implementations
- **Anti-Pattern Reports**: Identify problematic code
- **Pattern Corrections**: Fix misclassified patterns
- **New Pattern Discovery**: Propose emerging patterns

### 4. Enterprise Partnerships (Future)
**Target**: 5 enterprise partners
**Timeline**: 6 months

#### Partnership Model
- Anonymized code analysis
- Shared pattern library
- Revenue sharing on patterns

#### Privacy Guarantees
- All code anonymized before processing
- No proprietary information retained
- Aggregate patterns only
- Legal agreements for data usage

## Data Collection Implementation

### GitHub Crawler Architecture
```python
class GitHubDataCollector:
    def __init__(self, github_token: str):
        self.github = Github(github_token)
        self.storage_client = storage.Client()
        
    async def collect_repositories(self, criteria: Dict[str, Any]) -> List[Repository]:
        """
        Collect repositories matching criteria
        """
        query = self._build_search_query(criteria)
        repos = self.github.search_repositories(query)
        
        validated_repos = []
        for repo in repos:
            if await self._validate_repository(repo):
                validated_repos.append(repo)
                
        return validated_repos
    
    async def _validate_repository(self, repo: Repository) -> bool:
        """
        Validate repository meets all criteria
        """
        # Check license
        if not self._is_permissive_license(repo.license):
            return False
            
        # Check activity
        if not self._is_actively_maintained(repo):
            return False
            
        # Check quality indicators
        if not self._meets_quality_threshold(repo):
            return False
            
        return True
```

### Synthetic Data Generator
```python
class PatternSynthesizer:
    def __init__(self, pattern_templates: Dict[str, Any]):
        self.templates = pattern_templates
        self.ast_generator = ASTGenerator()
        
    def generate_pattern_variations(
        self, 
        pattern_type: str, 
        language: str, 
        variations: int = 10
    ) -> List[CodeSnippet]:
        """
        Generate synthetic pattern variations
        """
        base_template = self.templates[pattern_type][language]
        variations = []
        
        for i in range(variations):
            # Vary implementation details
            variation = self._apply_variations(base_template, {
                'naming_style': random.choice(['camelCase', 'snake_case']),
                'visibility': random.choice(['public', 'private', 'protected']),
                'implementation_style': random.choice(['verbose', 'concise']),
                'error_handling': random.choice(['exceptions', 'return_codes']),
                'thread_safety': random.choice([True, False])
            })
            
            variations.append(variation)
            
        return variations
```

## Quality Assurance

### License Validation
```python
PERMISSIVE_LICENSES = [
    'MIT', 'Apache-2.0', 'BSD-3-Clause', 'BSD-2-Clause',
    'ISC', 'Unlicense', 'CC0-1.0'
]

def validate_license(license_text: str) -> bool:
    """
    Ensure license allows ML training usage
    """
    if not license_text:
        return False
        
    for permissive in PERMISSIVE_LICENSES:
        if permissive.lower() in license_text.lower():
            return True
            
    return False
```

### Data Deduplication
```python
def deduplicate_code_samples(samples: List[CodeSnippet]) -> List[CodeSnippet]:
    """
    Remove duplicate or near-duplicate code samples
    """
    seen_hashes = set()
    unique_samples = []
    
    for sample in samples:
        # Normalize code (remove comments, whitespace)
        normalized = normalize_code(sample.code)
        
        # Generate semantic hash
        semantic_hash = generate_semantic_hash(normalized)
        
        if semantic_hash not in seen_hashes:
            seen_hashes.add(semantic_hash)
            unique_samples.append(sample)
            
    return unique_samples
```

## Privacy and Ethics

### PII Removal
```python
def remove_pii(code: str) -> str:
    """Remove personally identifiable information"""
    # Remove email addresses
    code = re.sub(r'[\w\.-]+@[\w\.-]+\.\w+', '<EMAIL>', code)
    
    # Remove API keys
    code = re.sub(r'api[_-]?key\s*=\s*["\'][\w\-]+["\']', 'api_key="REDACTED"', code)
    
    # Remove credentials
    code = re.sub(r'password\s*=\s*["\'][^"\']+["\']', 'password="REDACTED"', code)
    
    # Remove IP addresses
    code = re.sub(r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b', '0.0.0.0', code)
    
    # Remove names in comments
    code = remove_names_from_comments(code)
    
    return code
```

### Ethical Guidelines
1. **Respect Licenses**: Only use code with explicit permission
2. **Privacy First**: Remove all PII before processing
3. **No Malicious Code**: Filter out security exploits
4. **Attribution**: Maintain source attribution where required
5. **Transparency**: Clear documentation of data sources

## Data Versioning and Management

### Version Control
```yaml
dataset_version: v1.0.0
created_date: 2024-01-15
sources:
  github:
    repos_count: 10000
    last_crawl: 2024-01-14
  synthetic:
    patterns_generated: 50000
    generator_version: v2.1.0
  community:
    contributions: 1234
    last_update: 2024-01-15
```

### Storage Organization
```
gs://ccl-ml-training-data/
├── raw/
│   ├── github/
│   │   ├── 2024-01-15/
│   │   └── metadata.json
│   ├── synthetic/
│   └── community/
├── processed/
│   ├── features/
│   ├── labels/
│   └── embeddings/
└── versions/
    ├── v1.0.0/
    └── latest -> v1.0.0/
```

## Continuous Data Collection

### Automated Pipeline
```python
class ContinuousDataCollector:
    def __init__(self):
        self.scheduler = BackgroundScheduler()
        self.collectors = {
            'github': GitHubDataCollector(),
            'community': CommunityDataCollector(),
            'synthetic': PatternSynthesizer()
        }
        
    def start(self):
        # Daily GitHub crawl
        self.scheduler.add_job(
            self.collect_github_data,
            'cron',
            hour=2,  # 2 AM UTC
            id='github_daily_crawl'
        )
        
        # Hourly community contributions
        self.scheduler.add_job(
            self.process_community_contributions,
            'interval',
            hours=1,
            id='community_processing'
        )
        
        # Weekly synthetic generation
        self.scheduler.add_job(
            self.generate_synthetic_data,
            'cron',
            day_of_week='sun',
            hour=0,
            id='synthetic_weekly'
        )
        
        self.scheduler.start()
```

## Success Metrics

### Data Quality Metrics
- **Coverage**: Number of patterns covered (target: 100+)
- **Diversity**: Language and framework distribution
- **Quality**: Average confidence score of labels
- **Volume**: Total training samples (target: 1M+)
- **Freshness**: Age of newest samples

### Collection Efficiency
- **Crawl Rate**: Repositories processed per hour
- **Valid Data Rate**: Percentage passing validation
- **Storage Efficiency**: Compression and deduplication rate
- **Cost per Sample**: Infrastructure cost / samples collected

## Risk Mitigation

### Technical Risks
1. **API Rate Limits**: Implement exponential backoff and request pooling
2. **Storage Costs**: Use lifecycle policies and compression
3. **Processing Bottlenecks**: Distribute processing with Apache Beam
4. **Data Quality**: Automated validation and human review

### Legal Risks
1. **License Violations**: Automated license checking
2. **Copyright Claims**: Clear documentation of fair use
3. **Privacy Violations**: Comprehensive PII scanning
4. **Data Retention**: Clear policies and automated deletion

## Implementation Timeline

### Week 1-2: Infrastructure Setup
- [ ] Set up GCS buckets with lifecycle policies
- [ ] Configure GitHub API access and rate limiting
- [ ] Deploy Apache Beam pipeline infrastructure
- [ ] Create BigQuery datasets and tables

### Week 3-4: Initial Data Collection
- [ ] Crawl first 1000 GitHub repositories
- [ ] Generate 10,000 synthetic examples
- [ ] Set up community contribution portal
- [ ] Validate and process initial dataset

### Week 5-6: Quality and Scaling
- [ ] Implement comprehensive quality checks
- [ ] Scale to 5000+ repositories
- [ ] Add advanced synthetic generation
- [ ] Begin community outreach

### Week 7-8: Production Readiness
- [ ] Automate entire pipeline
- [ ] Set up monitoring and alerting
- [ ] Create data versioning system
- [ ] Document and train team

## Budget Estimation

### Infrastructure Costs (Monthly)
- **Storage**: 10TB in GCS (~$200)
- **Compute**: Dataflow processing (~$500)
- **API Costs**: GitHub API quota (~$100)
- **BigQuery**: Storage and queries (~$300)
- **Total**: ~$1,100/month

### One-time Costs
- **Development**: 2 engineers x 2 months
- **Legal Review**: License compliance audit
- **Community Platform**: Web interface development
- **Total Setup**: ~$50,000

## Next Steps

1. **Legal Approval**: Review data collection policies
2. **Infrastructure Setup**: Deploy GCP resources
3. **Pilot Program**: Start with 100 repositories
4. **Quality Validation**: Manual review of initial results
5. **Full Deployment**: Scale to production targets