-- Big<PERSON><PERSON>y Cost Tracking Setup for CCL ML Operations
-- This script creates tables and views for tracking ML operation costs

-- Create cost tracking table for ML operations
CREATE TABLE IF NOT EXISTS `ccl-platform.ml_operations.cost_tracking` (
  operation_id STRING NOT NULL,
  operation_type STRING NOT NULL,
  service STRING NOT NULL,
  pipeline_name STRING,
  job_name STRING,
  region STRING,
  
  -- Resource usage
  bytes_processed INT64,
  slot_ms INT64,
  cpu_hours NUMERIC,
  memory_gb_hours NUMERIC,
  storage_gb_hours NUMERIC,
  
  -- Cost breakdown
  estimated_cost_usd NUMERIC,
  actual_cost_usd NUMERIC,
  compute_cost_usd NUMERIC,
  storage_cost_usd NUMERIC,
  network_cost_usd NUMERIC,
  
  -- Timestamps
  operation_start_time TIMESTAMP,
  operation_end_time TIMESTAMP,
  cost_calculation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP(),
  
  -- Metadata
  metadata JSON,
  labels ARRAY<STRUCT<key STRING, value STRING>>,
  
  -- Partitioning and clustering
  operation_date DATE GENERATED ALWAYS AS (DATE(operation_start_time)) STORED
)
PARTITION BY operation_date
CLUSTER BY service, operation_type, region;

-- Create cost analysis view for daily aggregations
CREATE OR REPLACE VIEW `ccl-platform.ml_operations.daily_cost_analysis` AS
SELECT 
  operation_date,
  service,
  operation_type,
  region,
  COUNT(*) as operation_count,
  SUM(bytes_processed) as total_bytes_processed,
  SUM(slot_ms) / 1000 as total_slot_seconds,
  SUM(cpu_hours) as total_cpu_hours,
  SUM(memory_gb_hours) as total_memory_gb_hours,
  SUM(storage_gb_hours) as total_storage_gb_hours,
  SUM(actual_cost_usd) as total_actual_cost,
  SUM(estimated_cost_usd) as total_estimated_cost,
  SUM(compute_cost_usd) as total_compute_cost,
  SUM(storage_cost_usd) as total_storage_cost,
  SUM(network_cost_usd) as total_network_cost,
  AVG(actual_cost_usd) as avg_operation_cost,
  MAX(actual_cost_usd) as max_operation_cost,
  MIN(actual_cost_usd) as min_operation_cost
FROM `ccl-platform.ml_operations.cost_tracking`
WHERE operation_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)
GROUP BY operation_date, service, operation_type, region
ORDER BY operation_date DESC, total_actual_cost DESC;

-- Create monthly cost trends view
CREATE OR REPLACE VIEW `ccl-platform.ml_operations.monthly_cost_trends` AS
SELECT 
  EXTRACT(YEAR FROM operation_date) as year,
  EXTRACT(MONTH FROM operation_date) as month,
  service,
  operation_type,
  COUNT(*) as operation_count,
  SUM(actual_cost_usd) as monthly_cost,
  SUM(bytes_processed) / (1024*1024*1024*1024) as tb_processed, -- Convert to TB
  SUM(cpu_hours) as total_cpu_hours,
  AVG(actual_cost_usd) as avg_operation_cost,
  SUM(actual_cost_usd) / SUM(bytes_processed) * (1024*1024*1024*1024) as cost_per_tb
FROM `ccl-platform.ml_operations.cost_tracking`
WHERE operation_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH)
GROUP BY year, month, service, operation_type
ORDER BY year DESC, month DESC, monthly_cost DESC;

-- Create cost efficiency metrics view
CREATE OR REPLACE VIEW `ccl-platform.ml_operations.cost_efficiency` AS
SELECT 
  service,
  operation_type,
  
  -- Efficiency metrics
  AVG(bytes_processed / NULLIF(actual_cost_usd, 0)) as bytes_per_dollar,
  AVG(slot_ms / NULLIF(actual_cost_usd * 1000, 0)) as slot_seconds_per_dollar,
  AVG(CASE 
    WHEN operation_end_time IS NOT NULL AND operation_start_time IS NOT NULL 
    THEN TIMESTAMP_DIFF(operation_end_time, operation_start_time, SECOND) 
    END) as avg_duration_seconds,
  
  -- Cost variance analysis
  STDDEV(actual_cost_usd) as cost_std_dev,
  (STDDEV(actual_cost_usd) / NULLIF(AVG(actual_cost_usd), 0)) * 100 as cost_coefficient_variation,
  
  -- Resource utilization
  AVG(cpu_hours) as avg_cpu_hours,
  AVG(memory_gb_hours) as avg_memory_gb_hours,
  AVG(storage_gb_hours) as avg_storage_gb_hours,
  
  -- Recent stats (last 30 days)
  COUNT(*) as total_operations,
  SUM(actual_cost_usd) as total_cost,
  MAX(operation_date) as last_operation_date
  
FROM `ccl-platform.ml_operations.cost_tracking`
WHERE operation_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
GROUP BY service, operation_type
HAVING COUNT(*) >= 5  -- Only include operations with sufficient data
ORDER BY total_cost DESC;

-- Create cost alerts view for unusual spending
CREATE OR REPLACE VIEW `ccl-platform.ml_operations.cost_alerts` AS
WITH daily_baselines AS (
  SELECT 
    service,
    operation_type,
    AVG(actual_cost_usd) as avg_daily_cost,
    STDDEV(actual_cost_usd) as daily_cost_stddev
  FROM `ccl-platform.ml_operations.cost_tracking`
  WHERE operation_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
    AND operation_date < CURRENT_DATE()
  GROUP BY service, operation_type
),
today_costs AS (
  SELECT 
    service,
    operation_type,
    SUM(actual_cost_usd) as today_cost,
    COUNT(*) as today_operations
  FROM `ccl-platform.ml_operations.cost_tracking`
  WHERE operation_date = CURRENT_DATE()
  GROUP BY service, operation_type
)
SELECT 
  tc.service,
  tc.operation_type,
  tc.today_cost,
  tc.today_operations,
  db.avg_daily_cost,
  db.daily_cost_stddev,
  (tc.today_cost - db.avg_daily_cost) / NULLIF(db.daily_cost_stddev, 0) as z_score,
  CASE 
    WHEN tc.today_cost > db.avg_daily_cost + (2 * db.daily_cost_stddev) THEN 'HIGH_COST_ALERT'
    WHEN tc.today_cost > db.avg_daily_cost + (1.5 * db.daily_cost_stddev) THEN 'MODERATE_COST_ALERT'
    WHEN tc.today_cost < db.avg_daily_cost - (1.5 * db.daily_cost_stddev) THEN 'LOW_USAGE_ALERT'
    ELSE 'NORMAL'
  END as alert_level,
  ((tc.today_cost - db.avg_daily_cost) / NULLIF(db.avg_daily_cost, 0)) * 100 as cost_change_percent
FROM today_costs tc
JOIN daily_baselines db 
  ON tc.service = db.service 
  AND tc.operation_type = db.operation_type
WHERE db.avg_daily_cost > 0  -- Only alert on operations with historical cost data
ORDER BY ABS((tc.today_cost - db.avg_daily_cost) / NULLIF(db.daily_cost_stddev, 0)) DESC;

-- Create budget tracking table
CREATE TABLE IF NOT EXISTS `ccl-platform.ml_operations.budget_tracking` (
  budget_id STRING NOT NULL,
  budget_name STRING NOT NULL,
  service STRING NOT NULL,
  operation_type STRING,
  
  -- Budget amounts
  monthly_budget_usd NUMERIC NOT NULL,
  quarterly_budget_usd NUMERIC NOT NULL,
  annual_budget_usd NUMERIC NOT NULL,
  
  -- Alert thresholds
  warning_threshold_percent NUMERIC DEFAULT 75,
  critical_threshold_percent NUMERIC DEFAULT 90,
  
  -- Metadata
  budget_owner STRING,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP(),
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP(),
  is_active BOOLEAN DEFAULT TRUE,
  
  -- Labels for categorization
  labels ARRAY<STRUCT<key STRING, value STRING>>
);

-- Create budget utilization view
CREATE OR REPLACE VIEW `ccl-platform.ml_operations.budget_utilization` AS
WITH monthly_spend AS (
  SELECT 
    service,
    operation_type,
    EXTRACT(YEAR FROM operation_date) as year,
    EXTRACT(MONTH FROM operation_date) as month,
    SUM(actual_cost_usd) as monthly_spend
  FROM `ccl-platform.ml_operations.cost_tracking`
  WHERE operation_date >= DATE_TRUNC(DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH), MONTH)
  GROUP BY service, operation_type, year, month
),
current_month_spend AS (
  SELECT 
    service,
    operation_type,
    SUM(actual_cost_usd) as current_month_spend
  FROM `ccl-platform.ml_operations.cost_tracking`
  WHERE operation_date >= DATE_TRUNC(CURRENT_DATE(), MONTH)
  GROUP BY service, operation_type
)
SELECT 
  bt.budget_id,
  bt.budget_name,
  bt.service,
  bt.operation_type,
  bt.monthly_budget_usd,
  bt.quarterly_budget_usd,
  bt.annual_budget_usd,
  bt.warning_threshold_percent,
  bt.critical_threshold_percent,
  
  -- Current month utilization
  COALESCE(cms.current_month_spend, 0) as current_month_spend,
  (COALESCE(cms.current_month_spend, 0) / bt.monthly_budget_usd) * 100 as monthly_utilization_percent,
  
  -- Annual utilization (rolling 12 months)
  COALESCE(SUM(ms.monthly_spend), 0) as annual_spend,
  (COALESCE(SUM(ms.monthly_spend), 0) / bt.annual_budget_usd) * 100 as annual_utilization_percent,
  
  -- Budget status
  CASE 
    WHEN (COALESCE(cms.current_month_spend, 0) / bt.monthly_budget_usd) * 100 >= bt.critical_threshold_percent THEN 'CRITICAL'
    WHEN (COALESCE(cms.current_month_spend, 0) / bt.monthly_budget_usd) * 100 >= bt.warning_threshold_percent THEN 'WARNING'
    ELSE 'OK'
  END as budget_status,
  
  -- Projected monthly spend based on current usage
  CASE 
    WHEN EXTRACT(DAY FROM CURRENT_DATE()) > 1 THEN
      (COALESCE(cms.current_month_spend, 0) / EXTRACT(DAY FROM CURRENT_DATE())) * 
      EXTRACT(DAY FROM LAST_DAY(CURRENT_DATE()))
    ELSE COALESCE(cms.current_month_spend, 0)
  END as projected_monthly_spend
  
FROM `ccl-platform.ml_operations.budget_tracking` bt
LEFT JOIN current_month_spend cms 
  ON bt.service = cms.service 
  AND (bt.operation_type = cms.operation_type OR bt.operation_type IS NULL)
LEFT JOIN monthly_spend ms 
  ON bt.service = ms.service 
  AND (bt.operation_type = ms.operation_type OR bt.operation_type IS NULL)
WHERE bt.is_active = TRUE
GROUP BY 
  bt.budget_id, bt.budget_name, bt.service, bt.operation_type,
  bt.monthly_budget_usd, bt.quarterly_budget_usd, bt.annual_budget_usd,
  bt.warning_threshold_percent, bt.critical_threshold_percent,
  cms.current_month_spend
ORDER BY monthly_utilization_percent DESC;

-- Insert sample budget entries
INSERT INTO `ccl-platform.ml_operations.budget_tracking` 
(budget_id, budget_name, service, operation_type, monthly_budget_usd, quarterly_budget_usd, annual_budget_usd, budget_owner) 
VALUES
  ('budget-001', 'Pattern Mining Monthly Budget', 'pattern-mining', 'training', 1000.00, 3000.00, 12000.00, 'ml-team'),
  ('budget-002', 'Query Intelligence Processing', 'query-intelligence', 'embedding_generation', 500.00, 1500.00, 6000.00, 'ai-team'),
  ('budget-003', 'Analysis Engine Processing', 'analysis-engine', 'ast_parsing', 800.00, 2400.00, 9600.00, 'backend-team'),
  ('budget-004', 'Data Pipeline Operations', 'data-pipeline', NULL, 2000.00, 6000.00, 24000.00, 'data-team');

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_cost_tracking_service_type 
ON `ccl-platform.ml_operations.cost_tracking` (service, operation_type);

CREATE INDEX IF NOT EXISTS idx_cost_tracking_date_cost 
ON `ccl-platform.ml_operations.cost_tracking` (operation_date, actual_cost_usd);

-- Grant permissions to the ML operations team
GRANT SELECT ON `ccl-platform.ml_operations.cost_tracking` TO 'group:<EMAIL>';
GRANT SELECT ON `ccl-platform.ml_operations.daily_cost_analysis` TO 'group:<EMAIL>';
GRANT SELECT ON `ccl-platform.ml_operations.monthly_cost_trends` TO 'group:<EMAIL>';
GRANT SELECT ON `ccl-platform.ml_operations.cost_efficiency` TO 'group:<EMAIL>';
GRANT SELECT ON `ccl-platform.ml_operations.cost_alerts` TO 'group:<EMAIL>';
GRANT SELECT ON `ccl-platform.ml_operations.budget_utilization` TO 'group:<EMAIL>';

-- Grant INSERT/UPDATE permissions to service accounts
GRANT INSERT, UPDATE ON `ccl-platform.ml_operations.cost_tracking` TO 'serviceAccount:<EMAIL>';