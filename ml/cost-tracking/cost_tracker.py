#!/usr/bin/env python3
"""
BigQuery Cost Tracking for CCL ML Operations
Tracks and analyzes costs for machine learning operations
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
from dataclasses import dataclass, asdict
from google.cloud import bigquery, monitoring_v3
from google.cloud.bigquery import job
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class MLOperationCost:
    """Cost tracking data for ML operations"""
    operation_id: str
    operation_type: str  # 'training', 'inference', 'data_processing', 'embedding_generation'
    service: str  # 'pattern-mining', 'query-intelligence', 'analysis-engine'
    pipeline_name: Optional[str] = None
    job_name: Optional[str] = None
    region: str = 'us-central1'
    
    # Resource usage
    bytes_processed: int = 0
    slot_ms: int = 0
    cpu_hours: float = 0.0
    memory_gb_hours: float = 0.0
    storage_gb_hours: float = 0.0
    
    # Cost breakdown
    estimated_cost_usd: float = 0.0
    actual_cost_usd: float = 0.0
    compute_cost_usd: float = 0.0
    storage_cost_usd: float = 0.0
    network_cost_usd: float = 0.0
    
    # Timestamps
    operation_start_time: Optional[datetime] = None
    operation_end_time: Optional[datetime] = None
    
    # Metadata
    metadata: Dict[str, Any] = None
    labels: List[Dict[str, str]] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.labels is None:
            self.labels = []


class BigQueryCostTracker:
    """Track costs for BigQuery and related ML operations"""
    
    def __init__(self, project_id: str = 'ccl-platform'):
        self.project_id = project_id
        self.client = bigquery.Client(project=project_id)
        self.dataset_id = 'ml_operations'
        self.table_id = 'cost_tracking'
        
        # GCP pricing (as of 2024 - update as needed)
        self.pricing = {
            'bigquery_storage_gb_month': 0.02,
            'bigquery_query_tb': 5.00,
            'compute_engine_cpu_hour': 0.033,
            'compute_engine_memory_gb_hour': 0.0044,
            'cloud_storage_standard_gb_month': 0.020,
            'vertex_ai_training_cpu_hour': 0.063,
            'vertex_ai_prediction_cpu_hour': 0.056,
        }
    
    def start_operation_tracking(self, 
                                operation_type: str, 
                                service: str, 
                                pipeline_name: Optional[str] = None,
                                **kwargs) -> str:
        """Start tracking a new ML operation"""
        operation_id = str(uuid.uuid4())
        
        operation = MLOperationCost(
            operation_id=operation_id,
            operation_type=operation_type,
            service=service,
            pipeline_name=pipeline_name,
            operation_start_time=datetime.utcnow(),
            **kwargs
        )
        
        # Store initial tracking data
        self._store_operation_start(operation)
        
        logger.info(f"Started cost tracking for operation {operation_id} "
                   f"({service}/{operation_type})")
        
        return operation_id
    
    def update_operation_cost(self, 
                             operation_id: str, 
                             cost_data: Dict[str, Any]) -> None:
        """Update cost data for an ongoing operation"""
        
        # Get current operation data
        operation = self._get_operation(operation_id)
        if not operation:
            logger.error(f"Operation {operation_id} not found")
            return
        
        # Update with new cost data
        for key, value in cost_data.items():
            if hasattr(operation, key):
                setattr(operation, key, value)
        
        # Calculate estimated costs
        operation.estimated_cost_usd = self._calculate_estimated_cost(operation)
        
        # Update in database
        self._update_operation_in_db(operation)
        
        logger.info(f"Updated cost tracking for operation {operation_id}: "
                   f"${operation.estimated_cost_usd:.4f}")
    
    def finish_operation_tracking(self, 
                                 operation_id: str, 
                                 final_cost_data: Optional[Dict[str, Any]] = None) -> MLOperationCost:
        """Finish tracking an operation and calculate final costs"""
        
        operation = self._get_operation(operation_id)
        if not operation:
            logger.error(f"Operation {operation_id} not found")
            return None
        
        # Update with final data
        if final_cost_data:
            for key, value in final_cost_data.items():
                if hasattr(operation, key):
                    setattr(operation, key, value)
        
        operation.operation_end_time = datetime.utcnow()
        
        # Get actual costs from billing API if available
        operation.actual_cost_usd = self._get_actual_cost(operation)
        
        # If no actual cost available, use estimated cost
        if operation.actual_cost_usd == 0:
            operation.actual_cost_usd = self._calculate_estimated_cost(operation)
        
        # Store final cost data
        self._update_operation_in_db(operation)
        
        logger.info(f"Finished cost tracking for operation {operation_id}: "
                   f"${operation.actual_cost_usd:.4f} "
                   f"({operation.operation_end_time - operation.operation_start_time})")
        
        return operation
    
    def track_bigquery_job(self, job_reference: job.Job) -> str:
        """Track costs for a BigQuery job"""
        
        operation_id = self.start_operation_tracking(
            operation_type='bigquery_query',
            service='data-pipeline',
            job_name=job_reference.job_id,
            metadata={'job_type': job_reference.job_type}
        )
        
        # Wait for job completion (with timeout)
        timeout = 3600  # 1 hour timeout
        start_time = time.time()
        
        while not job_reference.done() and (time.time() - start_time) < timeout:
            time.sleep(30)  # Check every 30 seconds
        
        if job_reference.done():
            # Get job statistics
            stats = job_reference._properties.get('statistics', {})
            query_stats = stats.get('query', {})
            
            cost_data = {
                'bytes_processed': int(query_stats.get('totalBytesProcessed', 0)),
                'slot_ms': int(query_stats.get('totalSlotMs', 0)),
                'metadata': {
                    'job_id': job_reference.job_id,
                    'creation_time': stats.get('creationTime'),
                    'start_time': stats.get('startTime'),
                    'end_time': stats.get('endTime'),
                    'cache_hit': query_stats.get('cacheHit', False),
                    'total_bytes_billed': query_stats.get('totalBytesBilled', 0)
                }
            }
            
            return self.finish_operation_tracking(operation_id, cost_data)
        else:
            logger.warning(f"BigQuery job {job_reference.job_id} did not complete within timeout")
            return self.finish_operation_tracking(operation_id)
    
    def get_cost_summary(self, 
                        service: Optional[str] = None,
                        operation_type: Optional[str] = None,
                        days: int = 30) -> Dict[str, Any]:
        """Get cost summary for specified period"""
        
        query = f"""
        SELECT 
            service,
            operation_type,
            COUNT(*) as operation_count,
            SUM(actual_cost_usd) as total_cost,
            AVG(actual_cost_usd) as avg_cost,
            MAX(actual_cost_usd) as max_cost,
            SUM(bytes_processed) as total_bytes,
            SUM(cpu_hours) as total_cpu_hours
        FROM `{self.project_id}.{self.dataset_id}.{self.table_id}`
        WHERE operation_date >= DATE_SUB(CURRENT_DATE(), INTERVAL {days} DAY)
        """
        
        conditions = []
        if service:
            conditions.append(f"AND service = '{service}'")
        if operation_type:
            conditions.append(f"AND operation_type = '{operation_type}'")
        
        if conditions:
            query += " " + " ".join(conditions)
        
        query += " GROUP BY service, operation_type ORDER BY total_cost DESC"
        
        results = []
        for row in self.client.query(query):
            results.append(dict(row))
        
        return {
            'summary': results,
            'period_days': days,
            'generated_at': datetime.utcnow().isoformat()
        }
    
    def get_cost_alerts(self) -> List[Dict[str, Any]]:
        """Get current cost alerts"""
        
        query = f"""
        SELECT *
        FROM `{self.project_id}.{self.dataset_id}.cost_alerts`
        WHERE alert_level IN ('HIGH_COST_ALERT', 'MODERATE_COST_ALERT')
        ORDER BY ABS(z_score) DESC
        """
        
        alerts = []
        for row in self.client.query(query):
            alerts.append(dict(row))
        
        return alerts
    
    def get_budget_status(self) -> List[Dict[str, Any]]:
        """Get current budget utilization status"""
        
        query = f"""
        SELECT *
        FROM `{self.project_id}.{self.dataset_id}.budget_utilization`
        WHERE budget_status IN ('WARNING', 'CRITICAL')
        ORDER BY monthly_utilization_percent DESC
        """
        
        budget_status = []
        for row in self.client.query(query):
            budget_status.append(dict(row))
        
        return budget_status
    
    def _store_operation_start(self, operation: MLOperationCost) -> None:
        """Store initial operation data"""
        
        table_ref = self.client.dataset(self.dataset_id).table(self.table_id)
        
        # Convert operation to dict for BigQuery
        row_data = {
            'operation_id': operation.operation_id,
            'operation_type': operation.operation_type,
            'service': operation.service,
            'pipeline_name': operation.pipeline_name,
            'job_name': operation.job_name,
            'region': operation.region,
            'operation_start_time': operation.operation_start_time,
            'metadata': json.dumps(operation.metadata),
            'labels': [{'key': k, 'value': v} for label in operation.labels for k, v in label.items()],
        }
        
        errors = self.client.insert_rows_json(table_ref, [row_data])
        if errors:
            logger.error(f"Error inserting operation start data: {errors}")
    
    def _update_operation_in_db(self, operation: MLOperationCost) -> None:
        """Update operation data in database"""
        
        # Use MERGE statement to update existing record
        merge_query = f"""
        MERGE `{self.project_id}.{self.dataset_id}.{self.table_id}` AS target
        USING (
            SELECT 
                '{operation.operation_id}' as operation_id,
                {operation.bytes_processed} as bytes_processed,
                {operation.slot_ms} as slot_ms,
                {operation.cpu_hours} as cpu_hours,
                {operation.memory_gb_hours} as memory_gb_hours,
                {operation.storage_gb_hours} as storage_gb_hours,
                {operation.estimated_cost_usd} as estimated_cost_usd,
                {operation.actual_cost_usd} as actual_cost_usd,
                {operation.compute_cost_usd} as compute_cost_usd,
                {operation.storage_cost_usd} as storage_cost_usd,
                {operation.network_cost_usd} as network_cost_usd,
                TIMESTAMP('{operation.operation_end_time.isoformat()}') as operation_end_time,
                '{json.dumps(operation.metadata)}' as metadata
        ) AS source
        ON target.operation_id = source.operation_id
        WHEN MATCHED THEN 
            UPDATE SET 
                bytes_processed = source.bytes_processed,
                slot_ms = source.slot_ms,
                cpu_hours = source.cpu_hours,
                memory_gb_hours = source.memory_gb_hours,
                storage_gb_hours = source.storage_gb_hours,
                estimated_cost_usd = source.estimated_cost_usd,
                actual_cost_usd = source.actual_cost_usd,
                compute_cost_usd = source.compute_cost_usd,
                storage_cost_usd = source.storage_cost_usd,
                network_cost_usd = source.network_cost_usd,
                operation_end_time = source.operation_end_time,
                metadata = source.metadata
        """
        
        self.client.query(merge_query).result()
    
    def _get_operation(self, operation_id: str) -> Optional[MLOperationCost]:
        """Retrieve operation data from database"""
        
        query = f"""
        SELECT *
        FROM `{self.project_id}.{self.dataset_id}.{self.table_id}`
        WHERE operation_id = '{operation_id}'
        LIMIT 1
        """
        
        results = list(self.client.query(query))
        if not results:
            return None
        
        row = results[0]
        
        # Convert back to MLOperationCost object
        operation = MLOperationCost(
            operation_id=row.operation_id,
            operation_type=row.operation_type,
            service=row.service,
            pipeline_name=row.pipeline_name,
            job_name=row.job_name,
            region=row.region,
            bytes_processed=row.bytes_processed or 0,
            slot_ms=row.slot_ms or 0,
            cpu_hours=row.cpu_hours or 0.0,
            memory_gb_hours=row.memory_gb_hours or 0.0,
            storage_gb_hours=row.storage_gb_hours or 0.0,
            estimated_cost_usd=row.estimated_cost_usd or 0.0,
            actual_cost_usd=row.actual_cost_usd or 0.0,
            compute_cost_usd=row.compute_cost_usd or 0.0,
            storage_cost_usd=row.storage_cost_usd or 0.0,
            network_cost_usd=row.network_cost_usd or 0.0,
            operation_start_time=row.operation_start_time,
            operation_end_time=row.operation_end_time,
            metadata=json.loads(row.metadata) if row.metadata else {},
        )
        
        return operation
    
    def _calculate_estimated_cost(self, operation: MLOperationCost) -> float:
        """Calculate estimated cost based on resource usage"""
        
        total_cost = 0.0
        
        # BigQuery query cost
        if operation.bytes_processed > 0:
            tb_processed = operation.bytes_processed / (1024**4)  # Convert to TB
            total_cost += tb_processed * self.pricing['bigquery_query_tb']
        
        # Compute costs
        if operation.cpu_hours > 0:
            if operation.operation_type in ['training', 'model_serving']:
                total_cost += operation.cpu_hours * self.pricing['vertex_ai_training_cpu_hour']
            else:
                total_cost += operation.cpu_hours * self.pricing['compute_engine_cpu_hour']
        
        # Memory costs
        if operation.memory_gb_hours > 0:
            total_cost += operation.memory_gb_hours * self.pricing['compute_engine_memory_gb_hour']
        
        # Storage costs
        if operation.storage_gb_hours > 0:
            total_cost += operation.storage_gb_hours * self.pricing['cloud_storage_standard_gb_month'] / (24 * 30)  # Convert to hourly
        
        return round(total_cost, 6)
    
    def _get_actual_cost(self, operation: MLOperationCost) -> float:
        """Get actual cost from GCP billing API (simplified)"""
        
        # Note: In a real implementation, this would query the Cloud Billing API
        # For now, we'll return 0 to indicate no actual cost data available
        # The estimated cost will be used instead
        
        try:
            # Placeholder for actual billing API integration
            # This would require the Cloud Billing API and proper service account permissions
            pass
        except Exception as e:
            logger.warning(f"Could not retrieve actual cost data: {e}")
        
        return 0.0


def create_cost_tracking_dashboard():
    """Create a Grafana dashboard configuration for cost tracking"""
    
    dashboard_config = {
        "dashboard": {
            "id": None,
            "uid": "ccl-ml-cost-tracking",
            "title": "CCL ML Cost Tracking",
            "tags": ["ccl", "ml", "costs", "bigquery"],
            "timezone": "browser",
            "schemaVersion": 30,
            "version": 1,
            "refresh": "1m",
            "time": {
                "from": "now-30d",
                "to": "now"
            },
            "panels": [
                {
                    "id": 1,
                    "title": "Daily Cost Trend",
                    "type": "graph",
                    "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
                    "targets": [{
                        "expr": "sum by (service) (daily_ml_cost_usd)",
                        "legendFormat": "{{service}}",
                        "refId": "A"
                    }]
                },
                {
                    "id": 2,
                    "title": "Cost by Operation Type",
                    "type": "piechart",
                    "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
                    "targets": [{
                        "expr": "sum by (operation_type) (ml_operation_cost_usd)",
                        "legendFormat": "{{operation_type}}",
                        "refId": "A"
                    }]
                },
                {
                    "id": 3,
                    "title": "Budget Utilization",
                    "type": "bargauge",
                    "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8},
                    "targets": [{
                        "expr": "ml_budget_utilization_percent",
                        "legendFormat": "{{service}} - {{operation_type}}",
                        "refId": "A"
                    }],
                    "fieldConfig": {
                        "defaults": {
                            "unit": "percent",
                            "min": 0,
                            "max": 100,
                            "thresholds": {
                                "steps": [
                                    {"color": "green", "value": None},
                                    {"color": "yellow", "value": 75},
                                    {"color": "red", "value": 90}
                                ]
                            }
                        }
                    }
                },
                {
                    "id": 4,
                    "title": "Cost Efficiency Metrics",
                    "type": "table",
                    "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16},
                    "targets": [{
                        "expr": "ml_cost_efficiency",
                        "format": "table",
                        "refId": "A"
                    }]
                }
            ]
        }
    }
    
    return dashboard_config


# Example usage and integration with data pipeline
def main():
    """Example usage of the cost tracker"""
    
    tracker = BigQueryCostTracker()
    
    # Example: Track a pattern mining training operation
    operation_id = tracker.start_operation_tracking(
        operation_type='training',
        service='pattern-mining',
        pipeline_name='pattern_detection_training',
        metadata={'model_type': 'sklearn_classifier', 'dataset_size': '1M_samples'}
    )
    
    # Simulate some processing...
    time.sleep(2)
    
    # Update with resource usage
    tracker.update_operation_cost(operation_id, {
        'cpu_hours': 2.5,
        'memory_gb_hours': 8.0,
        'bytes_processed': 5 * 1024**3  # 5 GB
    })
    
    # Finish tracking
    final_operation = tracker.finish_operation_tracking(operation_id)
    
    print(f"Operation {operation_id} completed")
    print(f"Total cost: ${final_operation.actual_cost_usd:.4f}")
    
    # Get cost summary
    summary = tracker.get_cost_summary(service='pattern-mining', days=7)
    print(f"Cost summary: {summary}")
    
    # Check for alerts
    alerts = tracker.get_cost_alerts()
    if alerts:
        print(f"Cost alerts: {alerts}")


if __name__ == '__main__':
    main()