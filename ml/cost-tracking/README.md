# ML Cost Tracking System

A comprehensive BigQuery-based cost tracking and monitoring system for CCL machine learning operations.

## Overview

This system tracks, analyzes, and monitors costs for all ML operations across the CCL platform, providing:

- **Real-time Cost Tracking**: Monitor costs as operations execute
- **Budget Management**: Set and monitor budgets with alerts
- **Cost Analytics**: Analyze spending patterns and efficiency
- **Automated Alerts**: Get notified of unusual spending or budget overruns
- **Grafana Dashboards**: Visualize costs and trends

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   ML Services   │───▶│  Cost Tracker    │───▶│    BigQuery     │
│                 │    │                  │    │                 │
│ • Pattern Mining│    │ • Operation Start│    │ • Cost Tables   │
│ • Query Intel   │    │ • Resource Track │    │ • Analytics     │
│ • Analysis Eng  │    │ • Cost Calculate │    │ • Views         │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   GCP Billing    │    │    Grafana      │
                       │      API         │    │   Dashboard     │
                       └──────────────────┘    └─────────────────┘
```

## Features

### Cost Tracking
- **Operation-level tracking**: Track individual ML operations
- **Resource usage monitoring**: CPU, memory, storage, network
- **Real-time cost estimation**: Calculate costs as operations run
- **Multi-service support**: Pattern mining, query intelligence, analysis engine

### Budget Management
- **Budget definition**: Set monthly, quarterly, and annual budgets
- **Utilization monitoring**: Track budget consumption in real-time
- **Alert thresholds**: Warning and critical alert levels
- **Projected spending**: Forecast monthly costs based on current usage

### Analytics & Insights
- **Cost trends**: Daily, monthly, and yearly cost analysis
- **Efficiency metrics**: Cost per operation, bytes processed per dollar
- **Service comparison**: Compare costs across different services
- **Anomaly detection**: Identify unusual spending patterns

### Alerting
- **Budget alerts**: Notify when budgets are exceeded
- **Cost anomalies**: Alert on unusual spending spikes
- **Efficiency warnings**: Notify when operations become inefficient
- **Custom thresholds**: Configure alerts based on your needs

## Quick Start

### 1. Setup BigQuery Tables

```bash
# Run the setup SQL script
bq query --use_legacy_sql=false < setup-cost-tracking.sql
```

### 2. Install Python Dependencies

```bash
pip install google-cloud-bigquery google-cloud-monitoring
```

### 3. Use the Cost Tracker

```python
from cost_tracker import BigQueryCostTracker

# Initialize tracker
tracker = BigQueryCostTracker(project_id='your-project')

# Start tracking an operation
operation_id = tracker.start_operation_tracking(
    operation_type='training',
    service='pattern-mining',
    pipeline_name='pattern_detection_v2'
)

# Update with resource usage
tracker.update_operation_cost(operation_id, {
    'cpu_hours': 2.5,
    'memory_gb_hours': 8.0,
    'bytes_processed': 5 * 1024**3  # 5 GB
})

# Finish tracking
tracker.finish_operation_tracking(operation_id)
```

### 4. View Dashboards

Import the Grafana dashboard:
```bash
# Import the dashboard JSON
curl -X POST \
  http://localhost:3000/api/dashboards/db \
  -H 'Content-Type: application/json' \
  -d @infrastructure/monitoring/dashboards/ml-cost-tracking.json
```

## Database Schema

### Core Tables

#### `cost_tracking`
Main table for storing operation cost data:
- `operation_id`: Unique identifier for each operation
- `operation_type`: Type of operation (training, inference, etc.)
- `service`: Service name (pattern-mining, query-intelligence, etc.)
- `bytes_processed`: Amount of data processed
- `cpu_hours`, `memory_gb_hours`: Resource usage
- `actual_cost_usd`: Final cost in USD
- `operation_start_time`, `operation_end_time`: Timing data

#### `budget_tracking`
Budget definitions and thresholds:
- `budget_id`: Unique budget identifier
- `service`: Associated service
- `monthly_budget_usd`: Monthly budget limit
- `warning_threshold_percent`: Warning alert threshold
- `critical_threshold_percent`: Critical alert threshold

### Analytics Views

#### `daily_cost_analysis`
Daily cost aggregations by service and operation type

#### `monthly_cost_trends`
Monthly cost trends and comparisons

#### `cost_efficiency`
Efficiency metrics and performance indicators

#### `cost_alerts`
Real-time cost alerts and anomalies

#### `budget_utilization`
Budget usage and status tracking

## API Reference

### BigQueryCostTracker Class

#### Methods

**`start_operation_tracking(operation_type, service, **kwargs)`**
- Start tracking a new ML operation
- Returns: `operation_id` string

**`update_operation_cost(operation_id, cost_data)`**
- Update cost data for an ongoing operation
- `cost_data`: Dictionary of resource usage and costs

**`finish_operation_tracking(operation_id, final_cost_data=None)`**
- Complete operation tracking and calculate final costs
- Returns: `MLOperationCost` object

**`track_bigquery_job(job_reference)`**
- Automatically track a BigQuery job
- Returns: `MLOperationCost` object

**`get_cost_summary(service=None, operation_type=None, days=30)`**
- Get cost summary for specified period
- Returns: Summary dictionary

**`get_cost_alerts()`**
- Get current cost alerts
- Returns: List of alert dictionaries

**`get_budget_status()`**
- Get budget utilization status
- Returns: List of budget status dictionaries

## Configuration

### Pricing Configuration

Update pricing in `cost_tracker.py`:

```python
self.pricing = {
    'bigquery_storage_gb_month': 0.02,
    'bigquery_query_tb': 5.00,
    'compute_engine_cpu_hour': 0.033,
    'compute_engine_memory_gb_hour': 0.0044,
    'cloud_storage_standard_gb_month': 0.020,
    'vertex_ai_training_cpu_hour': 0.063,
    'vertex_ai_prediction_cpu_hour': 0.056,
}
```

### Budget Configuration

Add budgets via SQL:

```sql
INSERT INTO `ccl-platform.ml_operations.budget_tracking` 
(budget_id, budget_name, service, operation_type, monthly_budget_usd, quarterly_budget_usd, annual_budget_usd, budget_owner) 
VALUES
  ('budget-005', 'New Service Budget', 'new-service', 'training', 1500.00, 4500.00, 18000.00, 'team-lead');
```

## Integration Examples

### Apache Beam Pipeline Integration

```python
import apache_beam as beam
from cost_tracker import BigQueryCostTracker

class CostTrackingDoFn(beam.DoFn):
    def setup(self):
        self.tracker = BigQueryCostTracker()
        self.operation_id = self.tracker.start_operation_tracking(
            operation_type='data_processing',
            service='data-pipeline',
            pipeline_name='pattern_extraction'
        )
    
    def process(self, element):
        # Process element
        yield processed_element
    
    def teardown(self):
        self.tracker.finish_operation_tracking(self.operation_id)
```

### Vertex AI Training Integration

```python
from google.cloud import aiplatform
from cost_tracker import BigQueryCostTracker

def train_model_with_cost_tracking():
    tracker = BigQueryCostTracker()
    
    # Start tracking
    operation_id = tracker.start_operation_tracking(
        operation_type='training',
        service='pattern-mining',
        metadata={'model_type': 'custom_classifier'}
    )
    
    try:
        # Train model
        job = aiplatform.CustomTrainingJob(...)
        job.run()
        
        # Update with training metrics
        tracker.update_operation_cost(operation_id, {
            'cpu_hours': job.state.resource_usage.cpu_hours,
            'memory_gb_hours': job.state.resource_usage.memory_gb_hours
        })
        
    finally:
        # Finish tracking
        tracker.finish_operation_tracking(operation_id)
```

## Monitoring & Alerting

### Cost Alert Types

1. **High Cost Alert**: Daily cost exceeds 2 standard deviations above average
2. **Moderate Cost Alert**: Daily cost exceeds 1.5 standard deviations above average
3. **Budget Warning**: Monthly budget utilization exceeds warning threshold (75%)
4. **Budget Critical**: Monthly budget utilization exceeds critical threshold (90%)
5. **Low Usage Alert**: Unusually low usage detected

### Setting Up Alerts

#### Slack Integration

```python
import requests

def send_cost_alert_to_slack(alert_data):
    webhook_url = "your-slack-webhook-url"
    
    message = {
        "text": f"💰 Cost Alert: {alert_data['service']} - {alert_data['operation_type']}",
        "attachments": [{
            "color": "danger" if alert_data['alert_level'] == 'HIGH_COST_ALERT' else "warning",
            "fields": [
                {"title": "Today's Cost", "value": f"${alert_data['today_cost']:.2f}", "short": True},
                {"title": "Average Cost", "value": f"${alert_data['avg_daily_cost']:.2f}", "short": True},
                {"title": "Change", "value": f"{alert_data['cost_change_percent']:.1f}%", "short": True}
            ]
        }]
    }
    
    requests.post(webhook_url, json=message)

# Use in monitoring script
tracker = BigQueryCostTracker()
alerts = tracker.get_cost_alerts()

for alert in alerts:
    send_cost_alert_to_slack(alert)
```

#### Email Alerts

```python
import smtplib
from email.mime.text import MIMEText

def send_budget_alert_email(budget_data):
    if budget_data['budget_status'] in ['WARNING', 'CRITICAL']:
        subject = f"Budget Alert: {budget_data['service']} - {budget_data['budget_status']}"
        body = f"""
        Budget utilization for {budget_data['service']} has reached {budget_data['monthly_utilization_percent']:.1f}%.
        
        Current month spend: ${budget_data['current_month_spend']:.2f}
        Monthly budget: ${budget_data['monthly_budget_usd']:.2f}
        Projected monthly spend: ${budget_data['projected_monthly_spend']:.2f}
        
        Status: {budget_data['budget_status']}
        """
        
        # Send email (configure SMTP settings)
        msg = MIMEText(body)
        msg['Subject'] = subject
        msg['From'] = '<EMAIL>'
        msg['To'] = '<EMAIL>'
        
        # smtp.send_message(msg)
```

## Performance Optimization

### Query Optimization

1. **Partitioning**: Tables are partitioned by `operation_date`
2. **Clustering**: Clustered by `service`, `operation_type`, `region`
3. **Indexes**: Optimized indexes for common query patterns

### Cost Optimization

1. **Data Retention**: Automatic data lifecycle management
2. **Aggregated Views**: Pre-computed aggregations for faster queries
3. **Sampling**: Use sampling for large-scale analytics when appropriate

### Monitoring Overhead

- **Target**: <1% overhead on ML operations
- **Batch Updates**: Use batch operations to minimize API calls
- **Async Processing**: Non-blocking cost tracking

## Troubleshooting

### Common Issues

#### High BigQuery Costs
```sql
-- Check query costs
SELECT 
  job_id,
  total_bytes_processed / (1024*1024*1024*1024) as tb_processed,
  total_bytes_processed * 5.0 / (1024*1024*1024*1024) as estimated_cost_usd
FROM `ccl-platform.region-us.INFORMATION_SCHEMA.JOBS_BY_PROJECT`
WHERE creation_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
ORDER BY total_bytes_processed DESC
```

#### Missing Cost Data
```python
# Check for operations without cost data
tracker = BigQueryCostTracker()
query = """
SELECT operation_id, service, operation_type
FROM `ccl-platform.ml_operations.cost_tracking`
WHERE actual_cost_usd = 0 AND operation_end_time IS NOT NULL
"""
results = list(tracker.client.query(query))
```

#### Alert Fatigue
- Adjust alert thresholds in `budget_tracking` table
- Use alert correlation to reduce duplicate notifications
- Implement alert snoozing functionality

### Logs and Debugging

Enable debug logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

tracker = BigQueryCostTracker()
# All operations will now include debug information
```

## Security

### Permissions

Required BigQuery permissions:
- `bigquery.tables.create`
- `bigquery.tables.updateData`
- `bigquery.datasets.get`
- `bigquery.jobs.create`

Required IAM roles:
- `roles/bigquery.dataEditor`
- `roles/bigquery.jobUser`

### Data Privacy

- No PII is stored in cost tracking tables
- Operation metadata is anonymized
- Access is restricted via IAM and dataset-level permissions

## Development

### Adding New Services

1. Update service list in cost tracker
2. Add service-specific pricing if needed
3. Create service-specific budget entries
4. Update Grafana dashboard filters

### Custom Metrics

Add custom cost metrics:

```python
class CustomCostTracker(BigQueryCostTracker):
    def track_custom_operation(self, operation_data):
        # Custom tracking logic
        operation_id = self.start_operation_tracking(
            operation_type='custom',
            service='custom-service',
            **operation_data
        )
        return operation_id
```

### Testing

```python
# Unit tests
python -m pytest tests/test_cost_tracker.py

# Integration tests
python -m pytest tests/test_cost_integration.py

# Load tests
python tests/load_test_cost_tracking.py
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Update documentation
5. Submit a pull request

## License

This cost tracking system is part of the CCL platform and follows the same licensing terms.