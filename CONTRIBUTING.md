# Contributing to CCL Platform

Thank you for your interest in contributing to the CCL Platform. As this is a proprietary software project, contributions are limited to authorized team members and partners.

## Access Requirements

To contribute to this project, you must:

1. Be an authorized employee, contractor, or partner of CCL Platform
2. Have signed the appropriate NDA and contributor agreements
3. Have been granted access to the repository by the project administrators

## Code of Conduct

All contributors are expected to maintain the highest standards of professionalism and adhere to our internal code of conduct. Any violations should be reported to project leadership immediately.

## Development Process

We follow the phased development approach documented in `PHASED-DEVELOPMENT-APPROACH.md`. Please familiarize yourself with:

1. Current phase and priorities
2. Service boundaries and responsibilities
3. Integration contracts in `contracts/`
4. PRPs (Product Requirements Prompts) in `PRPs/`

### Service-Specific Guidelines

Each service has its own language and patterns:

* **Repository Analysis** (Rust) - Follow Rust idioms and safety patterns
* **Query Intelligence** (Python) - Follow PEP 8 and type hints
* **Pattern Mining** (Python) - Include ML model versioning
* **Marketplace** (Go) - Follow Go idioms and error handling
* **Web/Collaboration** (TypeScript) - Use strict mode and no `any` types

### Setting Up Development Environment

```bash
# Clone the repository (requires authorization)
git clone https://github.com/ccl-platform/episteme.git
cd episteme

# Start development environment
make dev-up

# Run tests
make test

# Check specific service
cd services/{service-name}
make test
```

### Coding Standards

1. **Documentation**: All public APIs must be documented
2. **Testing**: Maintain >90% test coverage
3. **Security**: No hardcoded secrets, follow security guidelines
4. **Performance**: Profile code for operations >100ms
5. **Contracts**: Validate against schemas in `contracts/`
6. **Confidentiality**: No proprietary information in comments or logs

### Commit Messages

We follow conventional commits:

```
feat: add new feature
fix: fix bug
docs: update documentation
style: formatting changes
refactor: code restructuring
test: add tests
chore: maintenance tasks
security: security-related changes
```

### Working with AI Agents

If you're using AI agents for development:

1. Read the specific prompt in `ai-agent-prompts/` for your task
2. Follow the pre-implementation checklist
3. Validate against contracts frequently
4. Run all validation commands before submitting
5. Ensure no proprietary information is exposed to external AI services

## Code Review Process

All code changes must go through our internal review process:

1. Create a branch following the naming convention: `feature/JIRA-123-description`
2. Submit a pull request with a complete description
3. Ensure all CI checks pass
4. Obtain approval from at least two authorized reviewers
5. Merge only after all security and compliance checks pass

### Review Criteria

1. **Functionality**: Does it work as intended?
2. **Tests**: Are there adequate tests?
3. **Documentation**: Is it properly documented?
4. **Style**: Does it follow our coding standards?
5. **Security**: Have security implications been considered?
6. **Performance**: Does it meet performance requirements?
7. **Contracts**: Does it comply with integration contracts?
8. **IP Compliance**: Does it respect intellectual property guidelines?

## Security Guidelines

1. Never commit credentials, keys, or sensitive configuration
2. Use Secret Manager for all secrets
3. Follow the principle of least privilege
4. Report security vulnerabilities <NAME_EMAIL>
5. All dependencies must be approved by the security team

## Intellectual Property

1. All contributions become the property of CCL Platform
2. Do not include code from external sources without proper licensing review
3. Ensure all third-party dependencies are properly licensed
4. Document the source of any algorithms or patterns from external sources

## Communication

* Internal Slack channels for team communication
* JIRA for issue tracking
* Confluence for internal documentation
* Weekly team meetings for coordination

## Compliance

All contributors must comply with:
* Company security policies
* Data protection regulations (GDPR, CCPA)
* Industry standards (SOC2, HIPAA where applicable)
* Export control regulations

## Questions?

For questions about contributing, please contact:
* Technical questions: <EMAIL>
* Access requests: <EMAIL>
* Security concerns: <EMAIL>

---

**Document Classification**: Internal Use Only  
**Last Updated**: 2025-01-07  
**Version**: 1.0.0