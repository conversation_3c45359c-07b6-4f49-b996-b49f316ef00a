# /validate - Output Validation

Validate code quality, test coverage, and compliance with project standards.

## Usage
```
/validate [--type <validation-type>] [--fix] [--report]
```

## Validation Types

### Code Quality
```
/validate --type code
```
- Linting (ESLint, Black, gofmt, rustfmt)
- Code complexity analysis
- Duplicate code detection
- Security vulnerability scanning
- Dependency audit

### Test Coverage
```
/validate --type tests
```
- Unit test coverage (min 80%)
- Integration test presence
- E2E test scenarios
- Test quality metrics
- Missing test identification

### API Contracts
```
/validate --type api
```
- OpenAPI specification compliance
- Contract testing
- Breaking change detection
- Version compatibility
- Documentation completeness

### Performance
```
/validate --type performance
```
- Response time benchmarks
- Memory usage analysis
- Database query optimization
- Bundle size checks
- Load testing results

### Security
```
/validate --type security
```
- OWASP compliance
- Secret scanning
- Dependency vulnerabilities
- Access control validation
- Encryption verification

## Options

### Auto-Fix
```
/validate --fix
```
- Automatically fix linting issues
- Format code properly
- Update dependencies
- Generate missing tests
- Add required documentation

### Generate Report
```
/validate --report
```
- Comprehensive validation report
- Actionable recommendations
- Priority-based fixes
- Trend analysis
- Compliance scorecard

## Integration

### Pre-Commit Validation
```
/validate --type code --fix
/validate --type tests
```

### Pre-Deploy Validation
```
/validate --type all --report
```

### Continuous Validation
```
/validate --watch
```

## Success Criteria
- ✅ All linting passes
- ✅ Test coverage > 80%
- ✅ No security vulnerabilities
- ✅ API contracts validated
- ✅ Performance benchmarks met

## Examples
```
# Quick validation
/validate

# Full validation with fixes
/validate --type all --fix

# Security-focused validation
/validate --type security --report

# Watch mode for continuous validation
/validate --watch --type code
```