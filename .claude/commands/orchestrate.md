# /orchestrate - Multi-Agent Task Orchestration

This command orchestrates multiple Claude Code agents to work on complex tasks in parallel using the SPARC methodology.

## Usage
```bash
/orchestrate <task-description> [options]
```

## Options
- `--agents <number>` - Number of agents to deploy (default: 3, max: 10)
- `--strategy <type>` - Execution strategy: `sparc`, `parallel`, `sequential` (default: sparc)
- `--timeout <minutes>` - Maximum execution time (default: 120)
- `--branch <name>` - Git branch for agent work (default: feature/orchestrated-{timestamp})

## Examples

### Basic Orchestration
```bash
/orchestrate "Build authentication service with JWT"
```

### Complex Multi-Service Build
```bash
/orchestrate "Implement complete CCL analysis engine with Rust" --agents 5 --strategy sparc
```

### Parallel Component Development
```bash
/orchestrate "Create React dashboard with 10 components" --agents 4 --strategy parallel
```

## SPARC Strategy Workflow

When using `--strategy sparc`, the orchestration follows these phases:

1. **Specification** (Sequential)
   - Architect agent creates detailed specifications
   - Reviews requirements and constraints
   - Outputs to `sparc/specifications/`

2. **Pseudocode** (Parallel)
   - Multiple developer agents work on algorithms
   - Each agent handles specific components
   - Outputs to `sparc/pseudocode/`

3. **Architecture** (Collaborative)
   - Architect and DevOps agents design system
   - All agents review and provide input
   - Outputs to `sparc/architecture/`

4. **Refinement** (Parallel)
   - Developer agents implement code
   - Test agent creates and runs tests
   - Security agent performs audits
   - Outputs to `sparc/refinements/`

5. **Completion** (Sequential)
   - QA agent validates everything
   - DevOps agent prepares deployment
   - Outputs to `sparc/completion/`

## Agent Assignment Logic

The orchestrator automatically assigns agents based on task analysis:

```python
# Automatic agent assignment based on keywords
if "frontend" or "react" or "ui" in task:
    assign FrontendAgent()
if "backend" or "api" or "service" in task:
    assign BackendAgent()
if "database" or "schema" in task:
    assign DatabaseAgent()
if "test" in task:
    assign TestAgent()
if "security" in task:
    assign SecurityAgent()
```

## Parallel Execution Strategy

When using `--strategy parallel`:

1. Task is decomposed into independent subtasks
2. Each agent gets a subtask
3. Agents work simultaneously
4. Results are merged when all complete

Example decomposition:
```
Task: "Build user management system"
├── Agent-1: Create user model and database schema
├── Agent-2: Implement user service and API
├── Agent-3: Build user interface components
└── Agent-4: Write tests and documentation
```

## Sequential Execution Strategy

When using `--strategy sequential`:

1. Tasks are ordered by dependency
2. Each agent completes before next starts
3. Output from one agent feeds into next
4. Ensures proper build order

## Progress Monitoring

Track orchestration progress:
```bash
# Check status
/orchestrate status

# View agent activities
/orchestrate logs --agent AGENT-001

# See real-time dashboard
/orchestrate dashboard
```

## Output Structure

Orchestration creates organized output:
```
orchestration-{timestamp}/
├── agents/
│   ├── AGENT-001/
│   ├── AGENT-002/
│   └── AGENT-003/
├── sparc/
│   ├── specifications/
│   ├── pseudocode/
│   ├── architecture/
│   ├── refinements/
│   └── completion/
├── logs/
│   └── orchestration.log
└── summary.md
```

## Error Handling

The orchestrator handles failures gracefully:

- **Agent Failure**: Reassigns task to available agent
- **Timeout**: Saves partial progress and reports
- **Merge Conflict**: Attempts automatic resolution
- **Quality Gate Failure**: Triggers refinement phase

## Advanced Options

### Custom Agent Configuration
```bash
/orchestrate "Complex task" --agents 5 --agent-config custom-agents.yaml
```

### Specific Phase Execution
```bash
/orchestrate "Task" --phase refinement --continue-from checkpoint.json
```

### Dry Run Mode
```bash
/orchestrate "Task" --dry-run
```

## Integration with Other Commands

Orchestrate integrates with other commands:

- `/sparc` - Executes SPARC phases
- `/validate` - Runs quality gates
- `/merge-work` - Combines agent outputs
- `/status` - Shows orchestration status

## Best Practices

1. **Start Small**: Test with 2-3 agents before scaling up
2. **Clear Specifications**: Better specs lead to better results
3. **Monitor Progress**: Check dashboard regularly
4. **Review Outputs**: Validate each phase before proceeding
5. **Use Branches**: Keep main branch clean during orchestration

## Troubleshooting

### Agents Not Starting
- Check `monitoring/logs/` for errors
- Ensure sufficient system resources
- Verify Git access permissions

### Slow Progress
- Reduce number of agents
- Simplify task specifications
- Check for blocking operations

### Merge Conflicts
- Use `/merge-work` command manually
- Review agent branches
- Adjust task decomposition

## Configuration

Default orchestration settings in `.claude/orchestration.yaml`:
```yaml
defaults:
  max_agents: 10
  default_strategy: sparc
  timeout_minutes: 120
  auto_merge: true
  quality_gates: true
  
agent_pool:
  min_instances: 2
  max_instances: 10
  startup_delay: 5s
  health_check: 30s
```

Remember: Orchestration is powerful but complex. Start with simple tasks and gradually increase complexity as you become familiar with the system.