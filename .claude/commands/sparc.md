# /sparc - SPARC Methodology Execution

Execute specific phases of the SPARC (Specification, Pseudocode, Architecture, Refinement, Completion) methodology for systematic development.

## Usage
```bash
/sparc <phase> <component> [options]
```

## Phases
- `init` - Initialize new SPARC cycle
- `specify` - Create specifications
- `pseudocode` - Design algorithms
- `architect` - Design system architecture
- `refine` - Iterative improvements
- `complete` - Finalize implementation
- `status` - Check current phase status
- `validate` - Validate phase completion

## Examples

### Initialize New SPARC Cycle
```bash
/sparc init "Authentication Service"
```

### Execute Specific Phase
```bash
/sparc specify auth-service
/sparc pseudocode auth-service --component login
/sparc architect auth-service --focus database
/sparc refine auth-service --iteration 2
/sparc complete auth-service
```

### Check Status
```bash
/sparc status auth-service
/sparc validate specification auth-service
```

## Phase Details

### 1. Specification Phase
```bash
/sparc specify <component> [--template standard|detailed|minimal]
```

Creates comprehensive specifications including:
- Functional requirements
- Non-functional requirements
- User stories with acceptance criteria
- API specifications
- Data models
- Constraints and limitations
- Success metrics

Output: `sparc/specifications/{component}-spec.md`

### 2. Pseudocode Phase
```bash
/sparc pseudocode <component> [--algorithm <name>] [--parallel]
```

Develops algorithms and logic flows:
- Main algorithm design
- Data flow diagrams
- Error handling strategies
- Edge case handling
- Complexity analysis

Output: `sparc/pseudocode/{component}-pseudocode.md`

### 3. Architecture Phase
```bash
/sparc architect <component> [--diagram] [--review]
```

Creates technical architecture:
- System diagrams (auto-generated if --diagram)
- Component interfaces
- Database schemas
- Integration designs
- Technology stack decisions
- Deployment architecture

Output: `sparc/architecture/{component}-architecture.md`

### 4. Refinement Phase
```bash
/sparc refine <component> [--iteration <number>] [--focus <area>]
```

Iterative improvement process:
- Code review feedback integration
- Performance optimization
- Security hardening
- Test coverage improvement
- Refactoring for maintainability

Focus areas: `performance`, `security`, `testing`, `documentation`

Output: `sparc/refinements/{component}-refinement-{iteration}.md`

### 5. Completion Phase
```bash
/sparc complete <component> [--checklist] [--deploy-ready]
```

Finalizes implementation:
- Requirements compliance check
- Test results summary
- Documentation verification
- Performance benchmarks
- Deployment checklist
- Sign-off preparation

Output: `sparc/completion/{component}-completion.md`

## Validation Commands

### Validate Phase Completion
```bash
/sparc validate <phase> <component>
```

Checks if phase meets quality gates:
- Specification: All requirements documented
- Pseudocode: Logic complete and reviewed
- Architecture: All systems designed
- Refinement: Tests passing, coverage met
- Completion: All checklists complete

### Validate Entire SPARC Cycle
```bash
/sparc validate all <component>
```

## Advanced Options

### Templates
Use predefined templates for consistency:
```bash
/sparc specify auth-service --template enterprise
/sparc architect payment-service --template microservice
```

### Parallel Execution
For components with multiple sub-parts:
```bash
/sparc pseudocode user-service --parallel --parts "crud,auth,profile"
```

### Continue from Checkpoint
Resume interrupted phases:
```bash
/sparc refine api-gateway --continue --checkpoint iteration-2
```

### Generate Reports
Create phase summary reports:
```bash
/sparc report specification auth-service
/sparc report all user-management
```

## Integration with Orchestration

SPARC commands integrate with `/orchestrate`:

```bash
# Orchestrate automatically uses SPARC
/orchestrate "Build user service" --strategy sparc

# Or manually control SPARC phases
/orchestrate "Design system" --phase architecture
```

## Quality Gates

Each phase has specific quality gates:

### Specification Gates
- [ ] All functional requirements listed
- [ ] User stories have acceptance criteria
- [ ] API contracts defined
- [ ] Data models complete
- [ ] Constraints documented

### Pseudocode Gates
- [ ] All algorithms documented
- [ ] Error cases handled
- [ ] Complexity analyzed
- [ ] Data flows mapped
- [ ] Edge cases identified

### Architecture Gates
- [ ] System diagrams created
- [ ] All interfaces defined
- [ ] Database schema complete
- [ ] Integration points mapped
- [ ] Deployment plan ready

### Refinement Gates
- [ ] Code review complete
- [ ] Tests passing (>80% coverage)
- [ ] Performance targets met
- [ ] Security scan passed
- [ ] Documentation updated

### Completion Gates
- [ ] All requirements implemented
- [ ] Full test suite passing
- [ ] Documentation complete
- [ ] Benchmarks met
- [ ] Deployment ready

## Output Organization

SPARC creates structured output:
```
sparc/
├── specifications/
│   ├── auth-service-spec.md
│   └── templates/
├── pseudocode/
│   ├── auth-service-pseudocode.md
│   └── algorithms/
├── architecture/
│   ├── auth-service-architecture.md
│   ├── diagrams/
│   └── schemas/
├── refinements/
│   ├── auth-service-refinement-1.md
│   ├── auth-service-refinement-2.md
│   └── feedback/
├── completion/
│   ├── auth-service-completion.md
│   ├── benchmarks/
│   └── deployment/
└── reports/
    └── auth-service-summary.md
```

## Best Practices

1. **Complete Phases Sequentially**: Don't skip ahead
2. **Document Decisions**: Include rationale in each phase
3. **Iterate in Refinement**: Multiple iterations are normal
4. **Validate Frequently**: Check gates before proceeding
5. **Use Templates**: Ensures consistency across components

## Troubleshooting

### Phase Won't Complete
- Check quality gates with `/sparc validate`
- Review phase requirements in SPARC.md
- Look for [BLOCKED] tags in output

### Validation Failures
- Run `/sparc report <phase> <component>` for details
- Address specific gate failures
- Re-run validation after fixes

### Lost Progress
- SPARC auto-saves checkpoints
- Use `--continue` flag to resume
- Check `sparc/{phase}/checkpoints/`

## Configuration

SPARC settings in `.claude/sparc-config.yaml`:
```yaml
phases:
  specification:
    required_sections: [requirements, stories, api, models]
    template: standard
    review_required: true
    
  architecture:
    auto_diagram: true
    diagram_tool: mermaid
    review_agents: [architect, devops]
    
quality_gates:
  strict_mode: true
  min_coverage: 80
  security_scan: true
  
output:
  format: markdown
  include_timestamps: true
  version_control: true
```

Remember: SPARC ensures systematic, high-quality development. Trust the process - each phase builds critical foundation for the next.