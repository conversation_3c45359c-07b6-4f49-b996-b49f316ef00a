# /parallel - Parallel Task Execution

Execute multiple independent tasks simultaneously across multiple Claude Code agents for maximum efficiency.

## Usage
```bash
/parallel <task-list> [options]
```

## Options
- `--max-agents <number>` - Maximum concurrent agents (default: 5, max: 10)
- `--strategy <type>` - Distribution strategy: `balanced`, `specialized`, `round-robin` (default: balanced)
- `--timeout <minutes>` - Timeout per task (default: 30)
- `--merge` - Automatically merge results when complete
- `--branch-prefix <prefix>` - Git branch prefix (default: parallel/)

## Examples

### Basic Parallel Execution
```bash
/parallel "task1: Create user model, task2: Build auth service, task3: Design UI components"
```

### Complex Service Development
```bash
/parallel --max-agents 8 --strategy specialized "
  frontend: Build React dashboard with 5 pages,
  backend: Create REST API with 10 endpoints,
  database: Design and implement schema,
  auth: Implement OAuth2 integration,
  tests: Write comprehensive test suite,
  docs: Create API documentation
"
```

### Component-Based Development
```bash
/parallel --merge --branch-prefix feature/components/ "
  header: Create header component,
  footer: Create footer component,
  sidebar: Create sidebar navigation,
  dashboard: Create dashboard layout,
  profile: Create user profile component
"
```

## Task List Format

### Inline Format
```bash
/parallel "task1: Description, task2: Description, task3: Description"
```

### File-Based Format
```bash
/parallel --file tasks.yaml
```

tasks.yaml:
```yaml
tasks:
  - id: api-users
    description: Create user management API
    agent: backend
    priority: high
    
  - id: ui-dashboard
    description: Build admin dashboard
    agent: frontend
    priority: high
    
  - id: db-schema
    description: Design database schema
    agent: database
    priority: critical
    
  - id: auth-system
    description: Implement authentication
    agent: security
    depends_on: [db-schema]
```

## Distribution Strategies

### Balanced Strategy (Default)
Distributes tasks evenly across available agents:
```
Agent-1: Task 1, Task 4, Task 7
Agent-2: Task 2, Task 5, Task 8
Agent-3: Task 3, Task 6, Task 9
```

### Specialized Strategy
Assigns tasks to agents based on expertise:
```
Frontend-Agent: All UI tasks
Backend-Agent: All API tasks
Database-Agent: All data tasks
Test-Agent: All testing tasks
```

### Round-Robin Strategy
Simple circular assignment:
```
Agent-1: Task 1
Agent-2: Task 2
Agent-3: Task 3
Agent-1: Task 4 (cycles back)
```

## Dependency Management

Define task dependencies for proper execution order:

```bash
/parallel --file tasks-with-deps.yaml
```

tasks-with-deps.yaml:
```yaml
tasks:
  - id: database
    description: Create database schema
    
  - id: models
    description: Create data models
    depends_on: [database]
    
  - id: api
    description: Build REST API
    depends_on: [models]
    
  - id: frontend
    description: Create UI
    depends_on: [api]
    
  - id: tests
    description: Write tests
    depends_on: [api, frontend]
```

## Progress Monitoring

### Real-Time Status
```bash
/parallel status
```

Output:
```
Parallel Execution Status
========================
Total Tasks: 6
Completed: 2 ✓
In Progress: 3 ⟳
Pending: 1 ⏸

Agent-001 [Frontend]: Building dashboard... 75%
Agent-002 [Backend]: Creating API... 60%
Agent-003 [Database]: Schema complete ✓
Agent-004 [Test]: Writing tests... 40%
Agent-005 [Docs]: Documentation complete ✓
```

### Detailed Task View
```bash
/parallel status --task api-users
```

### Agent Performance
```bash
/parallel stats
```

Shows:
- Tasks per agent
- Average completion time
- Success rate
- Resource usage

## Result Merging

### Automatic Merge
With `--merge` flag, results are automatically combined:

```bash
/parallel --merge "component1, component2, component3"
```

Creates:
```
parallel-results/
├── merged/
│   ├── components/
│   ├── tests/
│   └── docs/
├── task-reports/
└── merge-summary.md
```

### Manual Merge
```bash
/parallel merge --session-id abc123
```

### Conflict Resolution
If merge conflicts occur:
```bash
/parallel resolve-conflicts --strategy newest
```

Strategies: `newest`, `largest`, `manual`, `ai-assisted`

## Error Handling

### Task Failure Recovery
```bash
# Retry failed tasks
/parallel retry --failed

# Retry specific task
/parallel retry --task api-users

# Skip failed tasks and continue
/parallel continue --skip-failed
```

### Partial Results
Even if some tasks fail, successful results are preserved:
```
parallel-session-123/
├── completed/
│   ├── task-1-success/
│   └── task-3-success/
├── failed/
│   └── task-2-error.log
└── session-summary.md
```

## Advanced Features

### Resource Allocation
Specify resource requirements per task:
```yaml
tasks:
  - id: heavy-processing
    description: Process large dataset
    resources:
      memory: high
      cpu: high
      priority: 1
      
  - id: light-task
    description: Simple configuration
    resources:
      memory: low
      cpu: low
      priority: 3
```

### Custom Agent Assignment
```bash
/parallel --assign "task1:architect, task2:frontend, task3:backend"
```

### Checkpointing
Enable checkpoints for long-running tasks:
```bash
/parallel --checkpoint-interval 10m "large-task-list"
```

### Pipeline Mode
Chain parallel executions:
```bash
/parallel --pipeline "
  stage1: [task1, task2, task3],
  stage2: [task4, task5],
  stage3: [task6]
"
```

## Integration with Other Commands

### With SPARC
```bash
# Parallel SPARC phases
/parallel --strategy sparc "
  spec: Write specifications,
  pseudo: Create pseudocode,
  arch: Design architecture
"
```

### With Orchestrate
```bash
# Orchestrate can use parallel internally
/orchestrate "Build system" --strategy parallel --agents 5
```

## Best Practices

1. **Identify Independent Tasks**: Ensure tasks can truly run in parallel
2. **Balance Workload**: Distribute evenly to avoid bottlenecks
3. **Set Realistic Timeouts**: Account for task complexity
4. **Monitor Progress**: Check status regularly
5. **Plan for Failures**: Have recovery strategy ready

## Configuration

Default settings in `.claude/parallel-config.yaml`:
```yaml
execution:
  max_concurrent_agents: 10
  default_max_agents: 5
  task_timeout_minutes: 30
  retry_attempts: 2
  checkpoint_interval: 10m
  
strategies:
  default: balanced
  specialized_mapping:
    frontend: [ui, component, react, vue]
    backend: [api, service, server]
    database: [schema, migration, query]
    
merge:
  auto_merge: false
  conflict_strategy: manual
  preserve_history: true
  
monitoring:
  update_interval: 5s
  detailed_logs: true
  performance_tracking: true
```

## Troubleshooting

### Tasks Not Starting
- Check available system resources
- Verify no circular dependencies
- Ensure Git branches are accessible

### Slow Execution
- Reduce concurrent agents
- Check for resource constraints
- Review task complexity

### Merge Conflicts
- Use branch isolation
- Review task boundaries
- Apply conflict resolution strategy

Remember: Parallel execution is powerful for independent tasks. Always verify task independence before parallel execution to avoid conflicts and ensure quality results.