# /sync - Agent Knowledge Synchronization

Synchronize knowledge and progress with other agents working on the project.

## Usage
```
/sync [--all | --agent <agent-id> | --topic <topic>]
```

## Modes

### Full Sync
```
/sync --all
```
- Synchronizes with all active agents
- Updates shared knowledge bank
- Merges learned patterns
- Resolves conflicts

### Agent-Specific Sync
```
/sync --agent agent-backend-1
```
- Sync with specific agent
- Exchange relevant information
- Coordinate on shared components

### Topic-Based Sync
```
/sync --topic "api-contracts"
/sync --topic "database-schema"
```
- Sync knowledge about specific topics
- Useful for coordination on interfaces

## Shared Knowledge Types
1. **Architectural Decisions**
   - Design patterns used
   - Technology choices
   - API contracts

2. **Code Patterns**
   - Discovered patterns
   - Best practices
   - Anti-patterns to avoid

3. **Dependencies**
   - Package versions
   - Configuration settings
   - Integration points

4. **Progress Updates**
   - Completed tasks
   - Current blockers
   - Next steps

## Knowledge Bank Structure
```json
{
  "agents": {
    "agent-id": {
      "last_sync": "timestamp",
      "completed_tasks": [],
      "current_task": {},
      "discovered_patterns": [],
      "decisions": []
    }
  },
  "shared": {
    "api_contracts": {},
    "database_schemas": {},
    "configuration": {},
    "patterns": []
  }
}
```

## Conflict Resolution
- Timestamp-based precedence
- Architectural decisions trump implementation details
- Manual resolution for critical conflicts
- Automatic merging for non-conflicting updates

## Best Practices
1. Sync frequently to avoid conflicts
2. Document important decisions
3. Share reusable patterns
4. Communicate blockers early
5. Use topic-based sync for focused coordination