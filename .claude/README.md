# CCL Platform Context Engineering System

## Overview

The `.claude/` directory contains a comprehensive context engineering system designed for CCL platform development during Phase 4 - Core Features Implementation. This system enables AI-assisted development with intelligent context management, memory persistence, and automated workflow orchestration.

**Current Status**: Phase 4 - Active development of Repository Analysis API (75% complete) with AI agent integration.

## Directory Structure

```
.claude/
├── README.md                        # This documentation
├── settings.local.json             # CCL-specific Claude Code configuration
├── commands/                       # Development workflow commands
│   ├── initialize-context-system.md
│   ├── integrate-task-system.md
│   ├── setup-dev-environment.md
│   └── start-dev-session.md
├── config/
│   └── context_integration.yaml    # Context system configuration
├── logs/
│   ├── archive/                    # Archived logs
│   ├── integration_test_report.json
│   ├── validation_report.json
│   └── phase4_status.json         # Current Phase 4 status
├── memory/
│   ├── memory.json                 # Persistent context and session data
│   ├── current_session             # Current active session ID
│   ├── doc_memory.json            # Documentation context cache
│   ├── task_memory.json           # Task tracking memory
│   ├── prp_memory.json            # PRP workflow memory
│   ├── tool_memory.json           # Tool usage patterns
│   ├── last_sync.json             # Last synchronization timestamp
│   └── sessions/
│       └── archive/                # Archived session files
└── scripts/
    ├── context_loader.py           # Context loading and management
    ├── session_manager.sh          # Session lifecycle management
    ├── integration_manager.py      # Integration orchestration
    ├── test_integrations.py        # Integration testing
    └── validate_context_system.py  # System validation
```

## System Components

### 1. Context Management

**Purpose**: Intelligent loading and management of project context during development sessions.

**Key Files**:
- `scripts/context_loader.py` - Loads project context, task status, PRP content, and AI agent prompts
- `memory/memory.json` - Persistent context storage with Phase 4 project state
- `config/context_integration.yaml` - Context loading priorities and integration settings

**Phase 4 Context Priorities**:
- **High**: PLANNING.md, CLAUDE.md, TASK.md, PHASED-DEVELOPMENT-APPROACH.md, ai-agent-prompts/
- **Medium**: PRPs/, services/analysis-engine/, examples/, docs/architecture/
- **Low**: docs/business/, .claude/memory/sessions/

### 2. Session Management

**Purpose**: Development session lifecycle management with memory persistence and restoration.

**Key Files**:
- `scripts/session_manager.sh` - Session creation, restoration, and cleanup
- `memory/current_session` - Active session identifier
- `memory/sessions/` - Individual session data storage

**Session Commands**:
```bash
# Create new development session
.claude/scripts/session_manager.sh create

# Restore previous session
.claude/scripts/session_manager.sh restore [session_id]

# List available sessions
.claude/scripts/session_manager.sh list

# Show session status
.claude/scripts/session_manager.sh status
```

### 3. Task Integration

**Purpose**: Integration with TASK.md for automated task tracking and progress monitoring.

**Key Files**:
- `memory/task_memory.json` - Task completion history and patterns
- `commands/integrate-task-system.md` - Task workflow integration documentation

**Current Task Status** (Phase 4):
- **analysis-engine**: 75% complete (AI agent actively developing Rust service)
- **Infrastructure**: Phase 3 completed (100% validation passed)
- **Upcoming**: query-intelligence (Sprint 2), pattern-mining (Sprint 3), marketplace (Sprint 4)

### 4. Development Environment

**Purpose**: Automated development environment setup and validation for Phase 4 requirements.

**Key Files**:
- `commands/setup-dev-environment.md` - Environment setup procedures
- `settings.local.json` - Claude Code permissions and CCL platform configuration

**Phase 4 Tool Requirements**:
- **Rust**: 1.70+ (analysis-engine development)
- **Tree-sitter**: AST parsing
- **GCP CLI**: Infrastructure management
- **Docker**: Containerization
- **Python/Go/Node.js**: Upcoming services

### 5. Memory & Learning

**Purpose**: Persistent memory system for context continuity and learning from development patterns.

**Memory Components**:
- **Project Overview**: Phase 4 status, AI agent progress, infrastructure state
- **Architecture Patterns**: Microservices design, technology stack decisions
- **Development Standards**: Phase 3 completion, repository best practices
- **Current Tasks**: Active work, validation status, upcoming sprints

### 6. Validation & Testing

**Purpose**: Comprehensive validation of context system and integration points.

**Key Files**:
- `scripts/validate_context_system.py` - System validation
- `scripts/test_integrations.py` - Integration testing
- `logs/validation_report.json` - Latest validation results

**Validation Commands**:
```bash
# Validate complete context system
python3 .claude/scripts/validate_context_system.py

# Test all integrations
python3 .claude/scripts/test_integrations.py

# Validate Phase 4 specific components
python3 .claude/scripts/validate_phase4_setup.py
```

## Phase 4 Specific Features

### AI Agent Integration

The context system is specifically configured for AI agent development of the analysis engine:

- **Active Development**: Rust service implementation (75% complete)
- **Module Tracking**: config/settings.rs, config/mod.rs implemented
- **Next Modules**: handlers, ast_parser, api_routes
- **Validation**: Continuous integration with Phase 4 validation gates

### Analysis Engine Context

- **Service Directory**: `services/analysis-engine/` monitored for changes
- **Configuration**: Comprehensive settings for GCP integration, performance tuning
- **AST Parsing**: Tree-sitter integration for multi-language support
- **AI Integration**: Vertex AI embedding generation for code analysis

### Sprint Planning Integration

- **Current Sprint**: Repository Analysis API (Weeks 1-3)
- **Progress Tracking**: 75% completion with module-level granularity
- **Upcoming Sprints**: Query Intelligence, Pattern Detection, Marketplace
- **Validation Gates**: Phase 4 specific checkpoints and success criteria

## Configuration

### Claude Code Settings

**File**: `settings.local.json`

Key configurations:
- **Permissions**: Filesystem operations, GCP integration, AI tools
- **Context Engineering**: Memory management, task integration, workflow orchestration
- **CCL Platform**: Phase 4 status, AI agent integration, architecture validation

### Context Integration

**File**: `config/context_integration.yaml`

Key settings:
- **Task Management**: TASK.md synchronization, progress tracking
- **PRP Workflow**: Automatic loading of relevant PRPs
- **Development Tools**: Rust, GCP, Docker integration
- **Memory Settings**: Session retention, context caching, cleanup

## Usage Examples

### Starting a Development Session

```bash
# Initialize complete context system
.claude/commands/initialize-context-system

# Setup development environment for Phase 4
.claude/commands/setup-dev-environment

# Start new development session
.claude/commands/start-dev-session

# Work on specific task
.claude/scripts/session_manager.sh create
```

### Context Loading

```bash
# Load project context
python3 .claude/scripts/context_loader.py --load-context

# Create new session with context
python3 .claude/scripts/context_loader.py --create-session

# Restore previous session
python3 .claude/scripts/context_loader.py --restore-session session_20250107_160000
```

### Memory Management

```bash
# Save current session
.claude/scripts/session_manager.sh save

# Clean up old sessions (keep last 7 days)
.claude/scripts/session_manager.sh cleanup 7

# Show memory status
.claude/scripts/session_manager.sh status
```

## Maintenance

### Regular Tasks

1. **Session Cleanup**: Archives sessions older than 30 days automatically
2. **Memory Optimization**: Compresses and optimizes memory files daily
3. **Context Validation**: Runs system validation checks weekly
4. **Log Rotation**: Rotates logs when they exceed 10MB

### Manual Maintenance

```bash
# Clean up old data
.claude/scripts/session_manager.sh cleanup
find .claude/logs -name "*.log" -mtime +30 -delete

# Validate system integrity
python3 .claude/scripts/validate_context_system.py

# Reset context system (if needed)
rm -rf .claude/memory/sessions/*
.claude/commands/initialize-context-system
```

## Troubleshooting

### Common Issues

1. **Session Not Found**: Use `list` command to see available sessions
2. **Context Loading Failed**: Check file permissions and project structure
3. **Memory Corruption**: Run validation script and reinitialize if needed
4. **Integration Errors**: Verify tool installations and configurations

### Debug Commands

```bash
# Check system status
.claude/scripts/session_manager.sh status

# Validate integrations
python3 .claude/scripts/test_integrations.py

# Check context priorities
cat .claude/config/context_integration.yaml

# View recent logs
tail -f .claude/logs/*.log
```

## Development Guidelines

### Adding New Components

1. Update `context_integration.yaml` with new file patterns
2. Modify `context_loader.py` to include new context sources
3. Add validation tests to `test_integrations.py`
4. Document new features in this README

### Integration with CCL Services

1. Follow service boundary restrictions (Rust only in analysis-engine)
2. Use established GCP service patterns
3. Implement validation commands alongside features
4. Update memory structure for new service patterns

---

## Contact & Support

For issues with the context engineering system:
1. Check validation reports in `.claude/logs/`
2. Run integration tests with `test_integrations.py`
3. Review session status with `session_manager.sh status`
4. Consult CLAUDE.md for CCL-specific development guidelines

**Last Updated**: 2025-01-07 (Phase 4 Active Development)  
**Version**: 1.0.0 (CCL Platform Phase 4)  
**Maintainer**: CCL Platform Team