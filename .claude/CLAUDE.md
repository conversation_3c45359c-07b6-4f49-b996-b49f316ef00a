# CCL Platform AI Orchestration Configuration

This is the enhanced Claude Code configuration for multi-agent orchestration of the CCL platform development.

## Project Context
Building the Codebase Context Layer (CCL) - a cloud-native, AI-powered architectural intelligence platform using autonomous multi-agent development with Claude Code and Cline.

## Orchestration Rules

### Multi-Agent Coordination
- Maximum 10 concurrent Claude Code agents
- Each agent must have a specific persona and role
- Agents communicate through Git commits and shared memory
- Use SPARC methodology for all development tasks
- Implement feedback loops for autonomous operation

### Agent Communication Protocol
1. **Git-based Communication**
   - Agents push work to feature branches
   - Use descriptive commit messages with agent ID
   - Format: `[AGENT-{ID}] {persona}: {description}`
   - Example: `[AGENT-001] architect: Design authentication service API`

2. **Shared Memory System**
   - Write important decisions to `.claude/memory/`
   - Read memory files before starting tasks
   - Update `project-context.md` with new learnings
   - Document patterns in `patterns-learned.md`

3. **Task Handoff Protocol**
   - Complete tasks must include handoff notes
   - Next agent reads handoff before continuing
   - Use TODO markers for incomplete items
   - Tag blockers with `[BLOCKED: reason]`

### SPARC Methodology Implementation
Follow these phases for every major task:

1. **Specification** (`sparc/specifications/`)
   - Write detailed requirements
   - Include acceptance criteria
   - Define success metrics
   - Document constraints

2. **Pseudocode** (`sparc/pseudocode/`)
   - Design algorithms in plain language
   - Map out data flows
   - Identify key components
   - Plan error handling

3. **Architecture** (`sparc/architecture/`)
   - Create system diagrams
   - Define interfaces
   - Design data models
   - Plan integration points

4. **Refinement** (`sparc/refinements/`)
   - Iterative improvements
   - Performance optimization
   - Code review feedback
   - Test results

5. **Completion** (`sparc/completion/`)
   - Final implementation
   - Documentation
   - Test coverage report
   - Deployment readiness

### Quality Gates
Every task must pass these checks:
- [ ] Unit tests written and passing (80% coverage minimum)
- [ ] Integration tests for service interactions
- [ ] Code reviewed by reviewer agent
- [ ] Documentation updated
- [ ] No security vulnerabilities
- [ ] Performance benchmarks met

### Agent Specializations

#### Architect Agent
- System design and technology decisions
- API contract definitions
- Database schema design
- Performance architecture
- Commands: `/arc`, `/design`, `/optimize`

#### Frontend Developer Agent
- React/Next.js implementation
- UI/UX implementation
- Client-side state management
- Responsive design
- Commands: `/build --frontend`, `/component`

#### Backend Developer Agent  
- Service implementation (Go/Python/Rust)
- API endpoint development
- Business logic
- Data processing
- Commands: `/build --backend`, `/api`

#### ML Engineer Agent
- Pattern detection algorithms
- Model training pipelines
- Embeddings generation
- AI/ML integrations
- Commands: `/ml`, `/train`, `/optimize-model`

#### Database Engineer Agent
- Schema design and optimization
- Query performance tuning
- Data migration scripts
- Backup strategies
- Commands: `/schema`, `/migrate`, `/optimize-db`

#### DevOps Agent
- Infrastructure as Code
- CI/CD pipelines
- Monitoring setup
- Deployment automation
- Commands: `/deploy`, `/infra`, `/monitor`

#### Test Engineer Agent
- Test strategy and planning
- Test implementation
- Test automation
- Performance testing
- Commands: `/test`, `/tdd`, `/benchmark`

#### Security Agent
- Security analysis
- Vulnerability scanning
- Compliance checks
- Security best practices
- Commands: `/security`, `/audit`, `/compliance`

### Parallel Execution Guidelines
- Identify independent tasks for parallel work
- Maximum 3 agents per service/component
- Use feature branches for isolation
- Merge conflicts handled by integration agent
- Progress tracked in `monitoring/logs/`

### Context Management
- Each agent starts with core context from CLAUDE.md
- Load relevant files based on persona
- Use semantic compression for large files
- Share discoveries in memory system
- Maximum context per agent: 200K tokens

### Error Handling Protocol
1. Attempt automatic recovery
2. Log error with full context
3. Tag as `[ERROR: description]`
4. Notify orchestrator
5. Create recovery task if needed

### Performance Targets
- Code generation: 500+ lines/hour per agent
- Test coverage: 80% minimum
- Build time: <5 minutes
- Response time: <100ms for APIs
- Uptime: 99.99% target

### Resource Limits
- Memory per agent: 8GB
- CPU per agent: 2 cores
- Disk space: 10GB per agent
- Network: Shared bandwidth
- Concurrent file access: Use locks

## Custom Commands

### `/orchestrate <task> [--agents N] [--strategy sparc|parallel]`
Launch multiple agents for complex tasks

### `/sparc <phase> <component>`
Execute specific SPARC phase for component

### `/status`
Show all agent status and progress

### `/merge-work`
Combine work from multiple agents

### `/validate <component>`
Run quality gates on component

## Important Files
- `orchestration/launch_agents.py` - Agent pool management
- `orchestration/task_decomposer.py` - Task breakdown logic
- `.mcp.json` - MCP server configuration
- `SPARC.md` - Detailed SPARC methodology
- `ORCHESTRATION.md` - System operation guide

## Monitoring
- Check `monitoring/dashboard/index.html` for real-time status
- Agent logs in `monitoring/logs/{agent-id}.log`
- Metrics collected in `monitoring/metrics.json`
- Performance data in `monitoring/performance.csv`

Remember: We're building an autonomous system that builds systems. Quality, coordination, and clear communication between agents are paramount.