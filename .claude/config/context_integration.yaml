# Context Integration Configuration for CCL Platform Phase 4 Development
# Defines how context system integrates with development workflows during active feature implementation
# Updated for AI agent integration and analysis engine development

integrations:
  task_management:
    enabled: true
    sync_interval: 300  # 5 minutes
    auto_update_tasks: true
    task_completion_memory: true
    task_file: "TASK.md"
    
  prp_workflow:
    enabled: true
    auto_load_related_prps: true
    context_expansion: true
    validation_tracking: true
    prp_directory: "PRPs"
    
  documentation:
    enabled: true
    auto_sync: true
    change_detection: true
    context_updates: true
    watch_files:
      - "*.md"
      - "docs/**/*.md"
      - "PRPs/**/*.md"
    
  development_tools:
    enabled: true
    tool_integration: ["git", "docker", "gcloud", "cargo", "rustc", "tree-sitter"]
    command_history: true
    error_context: true
    auto_save_on_error: true
    ai_agent_integration: true          # Support for AI agent development
    rust_compilation_monitoring: true   # Analysis engine specific
    phase4_validation_commands: true    # Phase 4 validation gates

context_priorities:
  always_load:
    - "PLANNING.md"                        # Architecture and technology decisions
    - "CLAUDE.md"                         # AI assistant context and rules
    - "TASK.md"                           # Current tasks and AI agent progress
    - "PHASED-DEVELOPMENT-APPROACH.md"    # Phase 4 active development status
    - "PRPs/architecture-patterns.md"     # System design patterns
    - "PRPs/implementation-guide.md"      # Implementation standards
    - "ai-agent-prompts/phase4-features/" # Phase 4 implementation guides
    
  load_on_demand:
    - "docs/architecture/"
    - "examples/"
    - "PRPs/feature-specifications.md"
    - "PRPs/testing-patterns.md"
    
  cache_frequently_used:
    - "PRPs/templates/"
    - "docs/api/"
    - "context-engineering-intro/"
    - ".claude/memory/"

memory_settings:
  max_sessions: 50
  session_retention_days: 30
  auto_cleanup: true
  compression: true
  
  context_cache:
    enabled: true
    max_size_mb: 100
    ttl_minutes: 60
    
  persistent_memory:
    enabled: true
    sync_frequency: "on_change"
    backup_enabled: true
    backup_frequency: "daily"

session_management:
  auto_create_on_start: true
  auto_save_on_exit: true
  session_timeout_minutes: 480  # 8 hours
  
  context_restoration:
    enabled: true
    restore_task_state: true
    restore_open_files: true
    restore_command_history: true
    
  collaboration:
    enabled: false  # Future feature
    shared_sessions: false
    real_time_sync: false

validation_settings:
  context_validation:
    enabled: true
    validate_on_load: true
    validate_on_save: true
    
  integration_tests:
    enabled: true
    run_on_init: true
    
  memory_integrity:
    enabled: true
    check_frequency: "daily"
    auto_repair: true

logging:
  level: "INFO"
  file: ".claude/logs/context_system.log"
  max_size_mb: 10
  backup_count: 5
  
  components:
    context_loader: "DEBUG"
    session_manager: "INFO"
    memory_system: "INFO"
    integrations: "INFO"