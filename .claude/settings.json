{"version": "1.0.0", "project": "CCL Platform", "behavior": {"autoAccept": {"enabled": false, "allowList": ["file:read:*", "terminal:ls", "terminal:pwd", "terminal:git status"], "denyList": ["file:delete:*", "terminal:rm *", "terminal:sudo *"]}, "confirmationRequired": ["file:write:*.env", "file:write:*.key", "terminal:git push", "terminal:npm publish"]}, "context": {"maxTokens": 200000, "includePatterns": ["**/*.md", "**/*.py", "**/*.js", "**/*.ts", "**/*.go", "**/*.rs", "**/*.yaml", "**/*.json"], "excludePatterns": ["**/node_modules/**", "**/target/**", "**/__pycache__/**", "**/*.log", "**/dist/**", "**/build/**"]}, "memory": {"persistent": true, "location": ".claude/memory/", "maxSize": "100MB"}, "collaboration": {"gitIntegration": true, "commitPrefix": "[<PERSON>]", "branchPrefix": "claude/", "pullRequestTemplate": true}, "tools": {"mcp": {"enabled": true, "servers": ["filesystem", "git", "postgres", "memory", "puppeteer", "sequential-thinking"]}, "custom": {"orchestrate": {"command": "python orchestration/launch_agents.py", "description": "Launch multi-agent orchestration"}, "validate": {"command": "make validate", "description": "Run code validation"}}}, "output": {"format": "markdown", "codeBlocks": true, "syntaxHighlighting": true, "lineNumbers": true}, "logging": {"enabled": true, "level": "info", "file": "monitoring/agent_logs/claude-{timestamp}.log", "format": "json"}, "performance": {"caching": true, "parallelism": true, "maxConcurrentTasks": 5}}