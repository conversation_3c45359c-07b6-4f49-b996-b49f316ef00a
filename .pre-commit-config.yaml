repos:
  # General formatting and linting
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: detect-private-key
      - id: mixed-line-ending
        args: ['--fix=lf']

  # Python specific
  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
        language_version: python3.11
        files: ^services/(query-intelligence|pattern-mining)/

  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.1.11
    hooks:
      - id: ruff
        args: [--fix]
        files: ^services/(query-intelligence|pattern-mining)/

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
        files: ^services/(query-intelligence|pattern-mining)/

  # Go specific
  - repo: https://github.com/golangci/golangci-lint
    rev: v1.55.2
    hooks:
      - id: golangci-lint
        files: ^services/marketplace/

  # Rust specific
  - repo: local
    hooks:
      - id: rust-linting
        name: Rust linting
        entry: cargo fmt --
        language: system
        files: \.rs$
        pass_filenames: true
      
      - id: rust-clippy
        name: Rust clippy
        entry: cargo clippy -- -D warnings
        language: system
        files: \.rs$
        pass_filenames: false

  # TypeScript/JavaScript specific
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        files: \.(js|ts|jsx|tsx|css|scss|json|yaml|yml|md)$
        exclude: ^(services/analysis-engine|services/marketplace|services/pattern-mining|services/query-intelligence)/

  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.56.0
    hooks:
      - id: eslint
        files: \.(js|ts|jsx|tsx)$
        additional_dependencies:
          - eslint@8.56.0
          - typescript@5.3.0
          - '@typescript-eslint/parser@6.14.0'
          - '@typescript-eslint/eslint-plugin@6.14.0'

  # Security scanning
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: ['--baseline', '.secrets.baseline']

  # Dockerfile linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint
        files: Dockerfile.*

  # Shell script linting
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.9.0.6
    hooks:
      - id: shellcheck
        files: \.(sh|bash)$

  # Markdown linting
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.38.0
    hooks:
      - id: markdownlint
        args: ['--fix']

  # Contract validation
  - repo: local
    hooks:
      - id: validate-contracts
        name: Validate JSON schemas
        entry: python scripts/validate-contracts.py
        language: python
        files: ^contracts/schemas/.*\.json$
        additional_dependencies: [jsonschema]

# Configuration
default_language_version:
  python: python3.11

fail_fast: false
exclude: |
  (?x)^(
    .venv/|
    venv/|
    node_modules/|
    target/|
    dist/|
    build/|
    .*\.min\.js|
    .*\.min\.css|
    vendor/
  )