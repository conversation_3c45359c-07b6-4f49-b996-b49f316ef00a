-- Create schemas
CREATE SCHEMA IF NOT EXISTS analysis;
CREATE SCHEMA IF NOT EXISTS marketplace;
CREATE SCHEMA IF NOT EXISTS patterns;
CREATE SCHEMA IF NOT EXISTS collaboration;

-- Create user for each service
CREATE USER analysis_service WITH PASSWORD 'analysis_dev';
CREATE USER query_service WITH PASSWORD 'query_dev';
CREATE USER pattern_service WITH PASSWORD 'pattern_dev';
CREATE USER marketplace_service WITH PASSWORD 'marketplace_dev';
CREATE USER collaboration_service WITH PASSWORD 'collaboration_dev';

-- Grant permissions
GRANT ALL ON SCHEMA analysis TO analysis_service;
GRANT ALL ON SCHEMA marketplace TO marketplace_service;
GRANT ALL ON SCHEMA patterns TO pattern_service;
GRANT ALL ON SCHEMA collaboration TO collaboration_service;

-- Grant query_service read access to all schemas
GRANT USAGE ON SCHEMA analysis TO query_service;
GRANT SELECT ON ALL TABLES IN SCHEMA analysis TO query_service;
GRANT USAGE ON SCHEMA patterns TO query_service;
GRANT SELECT ON ALL TABLES IN SCHEMA patterns TO query_service;

-- Create sample development data tables
CREATE TABLE IF NOT EXISTS analysis.repositories (
    id SERIAL PRIMARY KEY,
    url VARCHAR(500) NOT NULL,
    name VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS patterns.detected_patterns (
    id SERIAL PRIMARY KEY,
    repository_id INTEGER REFERENCES analysis.repositories(id),
    pattern_type VARCHAR(100) NOT NULL,
    confidence DECIMAL(3,2) NOT NULL,
    file_path VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample development data
INSERT INTO analysis.repositories (url, name, status) VALUES
    ('https://github.com/golang/go', 'golang/go', 'completed'),
    ('https://github.com/rust-lang/rust', 'rust-lang/rust', 'analyzing'),
    ('https://github.com/facebook/react', 'facebook/react', 'pending'),
    ('https://github.com/tensorflow/tensorflow', 'tensorflow/tensorflow', 'completed');

INSERT INTO patterns.detected_patterns (repository_id, pattern_type, confidence, file_path) VALUES
    (1, 'singleton', 0.95, 'src/runtime/proc.go'),
    (1, 'factory', 0.88, 'src/net/http/server.go'),
    (4, 'observer', 0.92, 'tensorflow/python/ops/variable_scope.py'),
    (4, 'strategy', 0.87, 'tensorflow/core/kernels/ops_util.cc');

-- Create indexes for better performance
CREATE INDEX idx_repositories_status ON analysis.repositories(status);
CREATE INDEX idx_repositories_created_at ON analysis.repositories(created_at DESC);
CREATE INDEX idx_patterns_repository_id ON patterns.detected_patterns(repository_id);
CREATE INDEX idx_patterns_type ON patterns.detected_patterns(pattern_type);

-- Grant future table permissions
ALTER DEFAULT PRIVILEGES IN SCHEMA analysis GRANT SELECT ON TABLES TO query_service;
ALTER DEFAULT PRIVILEGES IN SCHEMA patterns GRANT SELECT ON TABLES TO query_service;