version: '3.8'

services:
  # Core Infrastructure
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: ccl_dev
      POSTGRES_PASSWORD: dev_password
      POSTGRES_DB: ccl_local
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts/postgres:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ccl_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # GCP Emulators
  spanner-emulator:
    image: gcr.io/cloud-spanner-emulator/emulator:latest
    ports:
      - "9010:9010"
      - "9020:9020"

  firestore-emulator:
    image: google/cloud-sdk:latest
    command: gcloud beta emulators firestore start --host-port=0.0.0.0:8080
    ports:
      - "8080:8080"
    environment:
      FIRESTORE_PROJECT_ID: ccl-local

  pubsub-emulator:
    image: google/cloud-sdk:latest
    command: gcloud beta emulators pubsub start --host-port=0.0.0.0:8085
    ports:
      - "8085:8085"
    environment:
      PUBSUB_PROJECT_ID: ccl-local

  storage-emulator:
    image: fsouza/fake-gcs-server:latest
    command: -scheme http -public-host storage.ccl.local:4443
    ports:
      - "4443:4443"
    volumes:
      - ./data/gcs:/data

  # Observability
  otel-collector:
    image: otel/opentelemetry-collector-contrib:latest
    command: ["--config=/etc/otel-collector-config.yaml"]
    volumes:
      - ./config/otel-collector-config.yaml:/etc/otel-collector-config.yaml
    ports:
      - "4317:4317"  # OTLP gRPC
      - "4318:4318"  # OTLP HTTP
      - "9464:9464"  # Prometheus exporter
    depends_on:
      - jaeger
      - prometheus

  jaeger:
    image: jaegertracing/all-in-one:latest
    environment:
      COLLECTOR_OTLP_ENABLED: true
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Jaeger collector

  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
    ports:
      - "9090:9090"

  grafana:
    image: grafana/grafana:latest
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_INSTALL_PLUGINS: grafana-clock-panel
    volumes:
      - ./config/grafana/provisioning:/etc/grafana/provisioning
      - grafana_data:/var/lib/grafana
    ports:
      - "3000:3000"
    depends_on:
      - prometheus

  # CCL Services (built locally)
  analysis-engine:
    build:
      context: ../services/analysis-engine
      dockerfile: Dockerfile.dev
    volumes:
      - ../services/analysis-engine:/app
      - cargo_cache:/usr/local/cargo
      - target_cache:/app/target
    environment:
      RUST_LOG: debug
      DATABASE_URL: ***********************************************/ccl_local
      REDIS_URL: redis://redis:6379
      OTEL_EXPORTER_OTLP_ENDPOINT: http://otel-collector:4317
      SPANNER_EMULATOR_HOST: spanner-emulator:9010
      STORAGE_EMULATOR_HOST: http://storage-emulator:4443
    ports:
      - "8001:8001"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: cargo watch -x run

  query-intelligence:
    build:
      context: ../services/query-intelligence
      dockerfile: Dockerfile.dev
    volumes:
      - ../services/query-intelligence:/app
      - pip_cache:/root/.cache/pip
    environment:
      PYTHONUNBUFFERED: 1
      DATABASE_URL: ***********************************************/ccl_local
      REDIS_URL: redis://redis:6379
      VERTEX_AI_PROJECT: ccl-local
      VERTEX_AI_LOCATION: us-central1
      GOOGLE_APPLICATION_CREDENTIALS: /app/credentials/dev-key.json
      OTEL_EXPORTER_OTLP_ENDPOINT: http://otel-collector:4317
      SPANNER_EMULATOR_HOST: spanner-emulator:9010
      FIRESTORE_EMULATOR_HOST: firestore-emulator:8080
    ports:
      - "8002:8002"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: python -m uvicorn main:app --reload --host 0.0.0.0 --port 8002

  pattern-mining:
    build:
      context: ../services/pattern-mining
      dockerfile: Dockerfile.dev
    volumes:
      - ../services/pattern-mining:/app
      - pip_cache:/root/.cache/pip
    environment:
      PYTHONUNBUFFERED: 1
      DATABASE_URL: ***********************************************/ccl_local
      BIGQUERY_PROJECT: ccl-local
      BIGQUERY_DATASET: patterns
      GOOGLE_APPLICATION_CREDENTIALS: /app/credentials/dev-key.json
      OTEL_EXPORTER_OTLP_ENDPOINT: http://otel-collector:4317
      SPANNER_EMULATOR_HOST: spanner-emulator:9010
      STORAGE_EMULATOR_HOST: http://storage-emulator:4443
    ports:
      - "8003:8003"
    depends_on:
      postgres:
        condition: service_healthy
    command: python -m uvicorn main:app --reload --host 0.0.0.0 --port 8003

  marketplace:
    build:
      context: ../services/marketplace
      dockerfile: Dockerfile.dev
    volumes:
      - ../services/marketplace:/app
      - go_cache:/go/pkg/mod
    environment:
      DATABASE_URL: ***********************************************/ccl_local
      SPANNER_EMULATOR_HOST: spanner-emulator:9010
      STORAGE_EMULATOR_HOST: http://storage-emulator:4443
      FIRESTORE_EMULATOR_HOST: firestore-emulator:8080
      STRIPE_SECRET_KEY: sk_test_dummy
      OTEL_EXPORTER_OTLP_ENDPOINT: http://otel-collector:4317
    ports:
      - "8004:8004"
    depends_on:
      postgres:
        condition: service_healthy
      spanner-emulator:
        condition: service_started
    command: air

  collaboration:
    build:
      context: ../services/collaboration
      dockerfile: Dockerfile.dev
    volumes:
      - ../services/collaboration:/app
      - node_modules_collab:/app/node_modules
    environment:
      NODE_ENV: development
      DATABASE_URL: ***********************************************/ccl_local
      REDIS_URL: redis://redis:6379
      FIRESTORE_EMULATOR_HOST: firestore-emulator:8080
      OTEL_EXPORTER_OTLP_ENDPOINT: http://otel-collector:4317
    ports:
      - "8005:8005"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: npm run dev

  web:
    build:
      context: ../services/web
      dockerfile: Dockerfile.dev
    volumes:
      - ../services/web:/app
      - node_modules_web:/app/node_modules
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:8000
      NEXT_PUBLIC_WS_URL: ws://localhost:8005
    ports:
      - "3001:3000"
    command: npm run dev

  # API Gateway (nginx)
  api-gateway:
    image: nginx:alpine
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "8000:80"
    depends_on:
      - analysis-engine
      - query-intelligence
      - pattern-mining
      - marketplace

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
  cargo_cache:
  target_cache:
  pip_cache:
  go_cache:
  node_modules_collab:
  node_modules_web:

networks:
  default:
    name: ccl-dev-network