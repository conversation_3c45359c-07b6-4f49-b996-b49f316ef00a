# Docker Compose Override for Local Development
# This file is automatically loaded by docker-compose
# Use it for personal development environment customizations

version: '3.8'

services:
  # Example: Change ports if defaults conflict
  # api-gateway:
  #   ports:
  #     - "8080:80"
  
  # Example: Mount additional volumes
  # analysis-engine:
  #   volumes:
  #     - ~/.cargo/registry:/usr/local/cargo/registry
  
  # Example: Add environment variables
  # query-intelligence:
  #   environment:
  #     - CUSTOM_VAR=value
  
  # Example: Resource limits for constrained environments
  # pattern-mining:
  #   deploy:
  #     resources:
  #       limits:
  #         cpus: '1'
  #         memory: 1G
  
  # Example: Enable debug mode for specific service
  # marketplace:
  #   environment:
  #     - DEBUG=true
  #     - VERBOSE_LOGGING=true
  
  # Example: Use different image tag
  # web:
  #   image: ccl/web:dev-latest
  
  # Example: Add development tools container
  # devtools:
  #   image: alpine:latest
  #   command: sleep infinity
  #   volumes:
  #     - ..:/workspace
  #   network_mode: host