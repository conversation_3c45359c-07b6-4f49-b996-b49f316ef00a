global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    environment: 'development'
    region: 'local'

scrape_configs:
  # CCL Services
  - job_name: 'analysis-engine'
    static_configs:
      - targets: ['analysis-engine:8001']
        labels:
          service: 'analysis-engine'
          language: 'rust'
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'query-intelligence'
    static_configs:
      - targets: ['query-intelligence:8002']
        labels:
          service: 'query-intelligence'
          language: 'python'
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'pattern-mining'
    static_configs:
      - targets: ['pattern-mining:8003']
        labels:
          service: 'pattern-mining'
          language: 'python'
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'marketplace'
    static_configs:
      - targets: ['marketplace:8004']
        labels:
          service: 'marketplace'
          language: 'go'
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'collaboration'
    static_configs:
      - targets: ['collaboration:8005']
        labels:
          service: 'collaboration'
          language: 'typescript'
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Infrastructure
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
        labels:
          service: 'postgres'
          type: 'database'

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
        labels:
          service: 'redis'
          type: 'cache'

  - job_name: 'nginx'
    static_configs:
      - targets: ['api-gateway:80']
        labels:
          service: 'nginx'
          type: 'gateway'

  # Self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'otel-collector'
    static_configs:
      - targets: ['otel-collector:9464']
        labels:
          service: 'otel-collector'
          type: 'observability'