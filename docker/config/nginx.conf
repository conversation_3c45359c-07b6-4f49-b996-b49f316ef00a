events {
    worker_connections 1024;
}

http {
    upstream analysis {
        server analysis-engine:8001;
    }
    
    upstream query {
        server query-intelligence:8002;
    }
    
    upstream patterns {
        server pattern-mining:8003;
    }
    
    upstream marketplace {
        server marketplace:8004;
    }
    
    upstream collaboration {
        server collaboration:8005;
    }
    
    server {
        listen 80;
        server_name api.ccl.local localhost;
        
        # CORS headers
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
        add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            return 204;
        }
        
        # Service routing
        location /api/v1/analysis {
            proxy_pass http://analysis;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/v1/query {
            proxy_pass http://query;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/v1/patterns {
            proxy_pass http://patterns;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/v1/marketplace {
            proxy_pass http://marketplace;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # WebSocket support for collaboration
        location /ws {
            proxy_pass http://collaboration;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Health check endpoint
        location /health {
            access_log off;
            add_header 'Content-Type' 'application/json';
            return 200 '{"status":"healthy"}';
        }
        
        # Default route
        location / {
            return 404 '{"error":"Not Found"}';
            add_header Content-Type application/json;
        }
    }
}