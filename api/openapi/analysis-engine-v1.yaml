openapi: 3.0.3
info:
  title: Repository Analysis API
  description: API for analyzing code repositories and generating AST data
  version: 1.0.0
  contact:
    name: CCL Platform Team
    email: <EMAIL>
servers:
  - url: https://api.ccl-platform.com/analysis/v1
    description: Production server
  - url: https://staging-api.ccl-platform.com/analysis/v1
    description: Staging server
  - url: http://localhost:8080/api/v1
    description: Local development

paths:
  /analyze:
    post:
      summary: Start repository analysis
      description: Initiates analysis of a code repository
      operationId: startAnalysis
      tags:
        - Analysis
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AnalysisRequest'
      responses:
        '202':
          description: Analysis started
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalysisResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/RateLimited'

  /analyze/{id}:
    get:
      summary: Get analysis status
      description: Returns the current status of an analysis job
      operationId: getAnalysisStatus
      tags:
        - Analysis
      parameters:
        - $ref: '#/components/parameters/AnalysisId'
      responses:
        '200':
          description: Analysis status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalysisStatus'
        '404':
          $ref: '#/components/responses/NotFound'

  /analyze/{id}/results:
    get:
      summary: Get analysis results
      description: Returns the complete analysis results including AST data
      operationId: getAnalysisResults
      tags:
        - Analysis
      parameters:
        - $ref: '#/components/parameters/AnalysisId'
      responses:
        '200':
          description: Analysis results
          content:
            application/json:
              schema:
                $ref: 'https://ccl.dev/schemas/ast-output-v1.json'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Analysis still in progress
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /languages:
    get:
      summary: List supported languages
      description: Returns list of programming languages supported for analysis
      operationId: listLanguages
      tags:
        - Metadata
      responses:
        '200':
          description: Supported languages
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LanguageList'

  /health:
    get:
      summary: Health check
      description: Returns service health status
      operationId: healthCheck
      tags:
        - System
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthStatus'

components:
  schemas:
    AnalysisRequest:
      type: object
      required:
        - repository_url
      properties:
        repository_url:
          type: string
          format: uri
          description: Git repository URL (HTTPS or SSH)
          example: https://github.com/user/repo
        branch:
          type: string
          description: Branch to analyze
          default: main
          example: develop
        commit:
          type: string
          description: Specific commit SHA
          pattern: '^[a-f0-9]{40}$'
        languages:
          type: array
          items:
            type: string
          description: Filter analysis to specific languages
        webhook_url:
          type: string
          format: uri
          description: URL to receive completion webhook

    AnalysisResponse:
      type: object
      required:
        - id
        - status
        - created_at
      properties:
        id:
          type: string
          description: Analysis job ID
          example: analysis_abc123def456
        status:
          $ref: '#/components/schemas/AnalysisStatusEnum'
        created_at:
          type: string
          format: date-time
        estimated_completion:
          type: string
          format: date-time

    AnalysisStatus:
      type: object
      required:
        - id
        - status
        - progress
      properties:
        id:
          type: string
        status:
          $ref: '#/components/schemas/AnalysisStatusEnum'
        progress:
          type: integer
          minimum: 0
          maximum: 100
          description: Progress percentage
        current_operation:
          type: string
          description: Current operation being performed
        files_processed:
          type: integer
        total_files:
          type: integer
        errors:
          type: array
          items:
            type: string

    AnalysisStatusEnum:
      type: string
      enum:
        - pending
        - cloning
        - analyzing
        - completed
        - failed
        - cancelled

    LanguageList:
      type: object
      properties:
        languages:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                example: Python
              extension:
                type: array
                items:
                  type: string
                example: [.py, .pyw]
              parser_version:
                type: string
                example: tree-sitter-python@0.20.0

    HealthStatus:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, degraded, unhealthy]
        version:
          type: string
        services:
          type: object
          additionalProperties:
            type: string

    ErrorResponse:
      type: object
      required:
        - error
        - message
      properties:
        error:
          type: string
        message:
          type: string
        details:
          type: object
        request_id:
          type: string

  parameters:
    AnalysisId:
      name: id
      in: path
      required: true
      description: Analysis job ID
      schema:
        type: string
        pattern: '^analysis_[a-zA-Z0-9]{16}$'

  responses:
    BadRequest:
      description: Invalid request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    
    Unauthorized:
      description: Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    
    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    
    RateLimited:
      description: Rate limit exceeded
      headers:
        X-RateLimit-Limit:
          schema:
            type: integer
        X-RateLimit-Remaining:
          schema:
            type: integer
        X-RateLimit-Reset:
          schema:
            type: integer
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    
    apiKey:
      type: apiKey
      in: header
      name: X-API-Key

security:
  - bearerAuth: []
  - apiKey: []

tags:
  - name: Analysis
    description: Repository analysis operations
  - name: Metadata
    description: Service metadata endpoints
  - name: System
    description: System operations