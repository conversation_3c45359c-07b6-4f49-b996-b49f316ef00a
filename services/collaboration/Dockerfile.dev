FROM node:20-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Create non-root user
RUN useradd -m -s /bin/bash nodedev
RUN chown -R nodedev:nodedev /app

USER nodedev

# Copy package files
COPY --chown=nodedev:nodedev package*.json ./

# Install dependencies
RUN npm install || npm init -y

# Copy source code
COPY --chown=nodedev:nodedev . .

# Create placeholder if index.js doesn't exist
RUN if [ ! -f index.js ] && [ ! -f src/index.js ] && [ ! -f src/index.ts ]; then \
    echo 'const express = require("express");\n\
const http = require("http");\n\
const WebSocket = require("ws");\n\
\n\
const app = express();\n\
const server = http.createServer(app);\n\
const wss = new WebSocket.Server({ server });\n\
\n\
app.use(express.json());\n\
\n\
app.get("/health", (req, res) => {\n\
    res.json({\n\
        status: "healthy",\n\
        service: "collaboration",\n\
        version: "0.1.0"\n\
    });\n\
});\n\
\n\
app.get("/", (req, res) => {\n\
    res.send("Collaboration Service");\n\
});\n\
\n\
wss.on("connection", (ws) => {\n\
    console.log("New WebSocket connection");\n\
    \n\
    ws.on("message", (message) => {\n\
        console.log("Received:", message.toString());\n\
        ws.send(`Echo: ${message}`);\n\
    });\n\
    \n\
    ws.on("close", () => {\n\
        console.log("WebSocket connection closed");\n\
    });\n\
});\n\
\n\
const PORT = process.env.PORT || 8005;\n\
server.listen(PORT, () => {\n\
    console.log(`Collaboration service listening on port ${PORT}`);\n\
});' > index.js; \
fi

# Install minimal dependencies if package.json was created
RUN if [ ! -f node_modules/express/package.json ]; then \
    npm install express ws; \
fi

# Update package.json with dev script if needed
RUN if [ -f package.json ] && ! grep -q '"dev"' package.json; then \
    npm pkg set scripts.dev="nodemon index.js || node index.js"; \
    npm install --save-dev nodemon || true; \
fi

# Development command
CMD ["npm", "run", "dev"]