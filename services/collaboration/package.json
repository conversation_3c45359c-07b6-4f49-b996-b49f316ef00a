{"name": "@ccl/collaboration", "version": "0.1.0", "description": "CCL Real-time Collaboration Service", "main": "dist/index.js", "scripts": {"dev": "nodemon", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .ts", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.6.0", "yjs": "^13.6.0", "y-websocket": "^1.5.0", "@google-cloud/firestore": "^7.1.0", "winston": "^3.11.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.0"}}