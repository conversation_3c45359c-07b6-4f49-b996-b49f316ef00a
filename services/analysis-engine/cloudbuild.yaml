steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}:${COMMIT_SHA}'
      - '-t'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}:latest'
      - '-f'
      - 'services/analysis-engine/Dockerfile'
      - 'services/analysis-engine'

  # Push the container image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - '--all-tags'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}'

  # Deploy to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - '${_SERVICE_NAME}'
      - '--image'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}:${COMMIT_SHA}'
      - '--region'
      - '${_REGION}'
      - '--platform'
      - 'managed'
      - '--port'
      - '8001'
      - '--memory'
      - '${_MEMORY}'
      - '--cpu'
      - '${_CPU}'
      - '--timeout'
      - '300s'
      - '--concurrency'
      - '100'
      - '--min-instances'
      - '${_MIN_INSTANCES}'
      - '--max-instances'
      - '${_MAX_INSTANCES}'
      - '--set-env-vars'
      - 'RUST_LOG=info,GCP_PROJECT_ID=${PROJECT_ID},SPANNER_INSTANCE_ID=${_SPANNER_INSTANCE},SPANNER_DATABASE_ID=${_SPANNER_DATABASE},STORAGE_BUCKET=${_STORAGE_BUCKET},PUBSUB_TOPIC=${_PUBSUB_TOPIC},VERTEX_AI_LOCATION=${_REGION},REDIS_URL=${_REDIS_URL}'
      - '--service-account'
      - '${_SERVICE_ACCOUNT}'
      - '--vpc-connector'
      - '${_VPC_CONNECTOR}'
      - '--allow-unauthenticated'

# Substitutions for different environments
substitutions:
  _REGION: us-central1
  _REPOSITORY: ccl-services
  _SERVICE_NAME: analysis-engine
  _MEMORY: 4Gi
  _CPU: '4'
  _MIN_INSTANCES: '1'
  _MAX_INSTANCES: '100'
  _SPANNER_INSTANCE: ccl-production
  _SPANNER_DATABASE: ccl-main
  _STORAGE_BUCKET: ccl-analysis-artifacts
  _PUBSUB_TOPIC: analysis-events
  _REDIS_URL: redis://10.0.0.3:6379
  _SERVICE_ACCOUNT: analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com
  _VPC_CONNECTOR: ccl-vpc-connector

options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'

timeout: 1200s