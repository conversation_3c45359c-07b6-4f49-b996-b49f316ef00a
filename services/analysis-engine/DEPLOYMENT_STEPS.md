# 🚀 Analysis Engine Deployment Steps

## Pre-Deployment Checklist

### 1. Verify GCP Authentication
```bash
# Check current account
gcloud auth list

# If needed, authenticate
gcloud auth login

# Set project
gcloud config set project vibe-match-463114
```

### 2. Enable Required Services
```bash
# Enable required APIs
gcloud services enable \
  run.googleapis.com \
  artifactregistry.googleapis.com \
  cloudbuild.googleapis.com \
  spanner.googleapis.com \
  storage.googleapis.com \
  pubsub.googleapis.com \
  aiplatform.googleapis.com
```

### 3. Create Service Account (if not exists)
```bash
# Create service account
gcloud iam service-accounts create analysis-engine \
  --display-name="Analysis Engine Service Account"

# Grant necessary roles
gcloud projects add-iam-policy-binding vibe-match-463114 \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/spanner.databaseUser"

gcloud projects add-iam-policy-binding vibe-match-463114 \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/storage.objectUser"

gcloud projects add-iam-policy-binding vibe-match-463114 \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/pubsub.publisher"

gcloud projects add-iam-policy-binding vibe-match-463114 \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/aiplatform.user"
```

### 4. Create Required Resources (if not exist)
```bash
# Create Spanner instance
gcloud spanner instances create ccl-production \
  --config=regional-us-central1 \
  --description="CCL Production" \
  --nodes=1

# Create database
gcloud spanner databases create ccl-main \
  --instance=ccl-production

# Create storage bucket
gsutil mb -p vibe-match-463114 -l us-central1 gs://ccl-analysis-artifacts

# Create Pub/Sub topic
gcloud pubsub topics create analysis-events
```

## Deployment Options

### Option 1: Direct Docker Build & Deploy
```bash
# Configure Docker for GCR
gcloud auth configure-docker

# Build locally
docker build -t gcr.io/vibe-match-463114/analysis-engine:latest .

# Push to GCR
docker push gcr.io/vibe-match-463114/analysis-engine:latest

# Deploy to Cloud Run
gcloud run deploy analysis-engine \
  --image gcr.io/vibe-match-463114/analysis-engine:latest \
  --platform managed \
  --region us-central1 \
  --port 8001 \
  --memory 4Gi \
  --cpu 4 \
  --timeout 300s \
  --concurrency 100 \
  --min-instances 1 \
  --max-instances 100 \
  --service-account <EMAIL> \
  --allow-unauthenticated
```

### Option 2: Cloud Build
```bash
# From repository root
gcloud builds submit \
  --config services/analysis-engine/cloudbuild.yaml \
  services/analysis-engine
```

### Option 3: Use Deploy Script
```bash
cd services/analysis-engine
./deploy.sh
```

## Post-Deployment Verification

### 1. Get Service URL
```bash
SERVICE_URL=$(gcloud run services describe analysis-engine \
  --platform managed \
  --region us-central1 \
  --format 'value(status.url)')

echo "Service deployed at: $SERVICE_URL"
```

### 2. Test Endpoints
```bash
# Health check
curl ${SERVICE_URL}/health

# Readiness check
curl ${SERVICE_URL}/health/ready

# View logs
gcloud logging read "resource.type=cloud_run_revision \
  AND resource.labels.service_name=analysis-engine" \
  --limit 50
```

### 3. Run Load Tests
```bash
# Install dependencies
npm install

# Run load tests
cd tests/load
npx artillery run analysis-api.yaml --target ${SERVICE_URL}
```

## Troubleshooting

### Permission Errors
If you encounter permission errors:
1. Ensure you're using the correct GCP account
2. Verify project permissions
3. Check if APIs are enabled
4. Ensure service account has required roles

### Build Failures
1. Check Docker daemon is running
2. Verify Dockerfile syntax
3. Ensure all required files are present
4. Check build logs for specific errors

### Deployment Failures
1. Check Cloud Run quotas
2. Verify service account permissions
3. Check if resources (Spanner, Storage) exist
4. Review deployment logs

## Required Permissions

The deploying user needs these roles:
- Cloud Run Admin
- Service Account User
- Storage Admin
- Spanner Database Admin
- Pub/Sub Admin
- Artifact Registry Admin (or Container Registry Admin)

---

For detailed deployment guidance, see the [Operations Guide](/docs/analysis-engine/guides/operations-guide.md).