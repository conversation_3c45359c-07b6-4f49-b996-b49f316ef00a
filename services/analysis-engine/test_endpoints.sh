#!/bin/bash

# Test script to validate Analysis Engine endpoints according to PRP

echo "Testing Analysis Engine API endpoints on port 8001..."
echo "======================================================="

# 1. Test health endpoint
echo -e "\n1. Testing /health endpoint:"
curl -f http://localhost:8001/health || echo "FAILED"

# 2. Test ready endpoint
echo -e "\n\n2. Testing /ready endpoint:"
curl -f http://localhost:8001/ready || echo "FAILED"

# 3. Test metrics endpoint
echo -e "\n\n3. Testing /metrics endpoint:"
curl -f http://localhost:8001/metrics | head -5 || echo "FAILED"

# 4. Test languages endpoint
echo -e "\n\n4. Testing /api/v1/languages endpoint:"
LANGUAGES=$(curl -s http://localhost:8001/api/v1/languages | jq '.languages | length')
echo "Supported languages: $LANGUAGES (should be 25+)"

# 5. Test analysis POST endpoint
echo -e "\n\n5. Testing POST /api/v1/analysis endpoint:"
curl -X POST http://localhost:8001/api/v1/analysis \
  -H "Content-Type: application/json" \
  -d '{
    "repository_url": "https://github.com/example/test.git",
    "branch": "main",
    "include_patterns": ["src/**/*.rs"],
    "exclude_patterns": ["target/**"],
    "analysis_depth": "Full",
    "languages": ["rust"],
    "enable_patterns": true,
    "enable_embeddings": true
  }' || echo "FAILED"

echo -e "\n\nTest complete. Verify all endpoints return expected responses."