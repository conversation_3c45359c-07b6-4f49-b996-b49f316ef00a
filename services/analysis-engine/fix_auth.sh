#!/bin/bash
# This script fixes the authentication issue by using google-cloud-spanner instead of gcloud-spanner

echo "Fixing authentication issue..."

# Update Cargo.toml to use google-cloud-spanner instead
sed -i.bak 's/gcloud-sdk = "0.27.3"/# gcloud-sdk = "0.27.3" # Removed due to token-source issue/' Cargo.toml

# Add google-cloud-spanner
echo 'google-cloud-spanner = "0.30.0"' >> Cargo.toml
echo 'google-cloud-storage = "0.22.1"' >> Cargo.toml

echo "Dependencies updated. You'll need to update the code to use the new client libraries."