# This file specifies files that are *not* uploaded to Google Cloud
# using gcloud. It follows the same syntax as .gitignore, with the addition of
# "#!include" directives (which insert the entries of the given .gitignore-style
# file at that point).

.gcloudignore
.git
.gitignore

# Rust build artifacts
target/

# Node modules
node_modules/

# Test files
tests/
benches/

# Documentation
*.md
docs/

# Local environment
.env
.env.*

# IDE files
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db

# Coverage
coverage/
*.html

# Temporary files
*.tmp
*.log