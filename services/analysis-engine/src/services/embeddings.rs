use anyhow::{Context, Result};
use crate::models::{FileAnalysis, CodeEmbedding};
use serde::{Deserialize, Serialize};
use reqwest::Client;
use std::env;
use std::time::Duration;
use tokio::time::sleep;
use std::sync::Arc;
use tokio::sync::Mutex;
use chrono::{DateTime, Utc};

const MAX_RETRIES: u32 = 3;
const INITIAL_RETRY_DELAY: Duration = Duration::from_millis(1000);
const MAX_RETRY_DELAY: Duration = Duration::from_secs(60);
const VERTEX_AI_TIMEOUT: Duration = Duration::from_secs(30);
const EMBEDDING_DIMENSION: usize = 768; // text-embedding-004 dimension

#[derive(Debug, Serialize)]
struct EmbeddingRequest {
    instances: Vec<EmbeddingInstance>,
}

#[derive(Debug, Serialize, Clone)]
struct EmbeddingInstance {
    content: String,
}

#[derive(Debug, Deserialize)]
struct EmbeddingResponse {
    predictions: Vec<Prediction>,
}

#[derive(Debug, Deserialize)]
struct Prediction {
    embeddings: EmbeddingValues,
}

#[derive(Debug, Deserialize)]
struct EmbeddingValues {
    values: Vec<f32>,
}

// Circuit breaker states
#[derive(Debug, Clone)]
enum CircuitState {
    Closed,
    Open(DateTime<Utc>),
    HalfOpen,
}

pub struct EmbeddingsService {
    client: Client,
    project_id: String,
    location: String,
    circuit_state: Arc<Mutex<CircuitState>>,
    failure_count: Arc<Mutex<u32>>,
    failure_threshold: u32,
    reset_timeout: Duration,
}

impl EmbeddingsService {
    pub async fn new() -> Result<Self> {
        let project_id = env::var("GCP_PROJECT_ID")
            .unwrap_or_else(|_| "vibe-match-463114".to_string());
        let location = env::var("GCP_REGION").unwrap_or_else(|_| "us-central1".to_string());

        // Create HTTP client with timeout
        let client = Client::builder()
            .timeout(VERTEX_AI_TIMEOUT)
            .build()
            .context("Failed to create HTTP client")?;

        Ok(Self {
            client,
            project_id,
            location,
            circuit_state: Arc::new(Mutex::new(CircuitState::Closed)),
            failure_count: Arc::new(Mutex::new(0)),
            failure_threshold: 5,
            reset_timeout: Duration::from_secs(60),
        })
    }

    #[cfg(test)]
    pub fn new_for_test() -> Self {
        let client = Client::new();

        Self {
            client,
            project_id: "vibe-match-463114".to_string(),
            location: "us-central1".to_string(),
            circuit_state: Arc::new(Mutex::new(CircuitState::Closed)),
            failure_count: Arc::new(Mutex::new(0)),
            failure_threshold: 5,
            reset_timeout: Duration::from_secs(60),
        }
    }

    pub async fn generate_embeddings(&self, analyses: &[FileAnalysis]) -> Result<Vec<CodeEmbedding>> {
        let mut embeddings = Vec::new();
        
        // Process in batches to avoid exceeding API limits
        const BATCH_SIZE: usize = 5;
        
        for (batch_idx, chunk) in analyses.chunks(BATCH_SIZE).enumerate() {
            // Check circuit breaker
            if !self.check_circuit_breaker().await? {
                tracing::warn!(
                    "Circuit breaker is open, returning empty embeddings for batch {}",
                    batch_idx
                );
                
                // Return placeholder embeddings when circuit is open
                for _ in chunk {
                    embeddings.push(self.create_empty_embedding());
                }
                continue;
            }

            let instances: Vec<EmbeddingInstance> = chunk
                .iter()
                .map(|ast| {
                    let content = self.extract_code_content(ast);
                    EmbeddingInstance { content }
                })
                .collect();

            if instances.is_empty() {
                continue;
            }

            // Generate embeddings with retry logic
            match self.call_vertex_ai_with_retry(&instances).await {
                Ok(batch_embeddings) => {
                    embeddings.extend(batch_embeddings);
                    self.record_success().await;
                }
                Err(e) => {
                    tracing::error!(
                        "Failed to generate embeddings for batch {}: {}",
                        batch_idx, e
                    );
                    self.record_failure().await;
                    
                    // Return empty embeddings for failed batch
                    for _ in chunk {
                        embeddings.push(self.create_empty_embedding());
                    }
                }
            }
            
            // Add delay between batches to respect rate limits
            if batch_idx < analyses.chunks(BATCH_SIZE).len() - 1 {
                sleep(Duration::from_millis(500)).await;
            }
        }

        Ok(embeddings)
    }

    async fn call_vertex_ai_with_retry(
        &self,
        instances: &[EmbeddingInstance],
    ) -> Result<Vec<CodeEmbedding>> {
        let mut retry_delay = INITIAL_RETRY_DELAY;
        
        for attempt in 0..MAX_RETRIES {
            match self.call_vertex_ai(instances).await {
                Ok(embeddings) => return Ok(embeddings),
                Err(e) => {
                    // Check if error is retryable
                    if !self.is_retryable_error(&e) || attempt == MAX_RETRIES - 1 {
                        return Err(e);
                    }
                    
                    tracing::warn!(
                        "Attempt {} failed, retrying in {:?}: {}",
                        attempt + 1,
                        retry_delay,
                        e
                    );
                    
                    sleep(retry_delay).await;
                    
                    // Exponential backoff with jitter
                    retry_delay = std::cmp::min(
                        retry_delay * 2 + Duration::from_millis(rand::random::<u64>() % 1000),
                        MAX_RETRY_DELAY,
                    );
                }
            }
        }
        
        Err(anyhow::anyhow!("All retry attempts exhausted"))
    }

    async fn call_vertex_ai(&self, instances: &[EmbeddingInstance]) -> Result<Vec<CodeEmbedding>> {
        // Get authentication token
        let auth_token = self.get_auth_token().await?;
        
        let endpoint = format!(
            "https://{}-aiplatform.googleapis.com/v1/projects/{}/locations/{}/publishers/google/models/text-embedding-004:predict",
            self.location, self.project_id, self.location
        );

        let response = self.client
            .post(&endpoint)
            .bearer_auth(&auth_token)
            .json(&EmbeddingRequest {
                instances: instances.to_vec(),
            })
            .send()
            .await
            .context("Failed to send request to Vertex AI")?;

        let status = response.status();
        
        if !status.is_success() {
            let error_body = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            return Err(anyhow::anyhow!(
                "Vertex AI request failed with status {}: {}",
                status,
                error_body
            ));
        }

        let embedding_response: EmbeddingResponse = response
            .json()
            .await
            .context("Failed to parse Vertex AI response")?;

        // Convert predictions to CodeEmbedding
        let embeddings = embedding_response.predictions
            .into_iter()
            .enumerate()
            .map(|(i, prediction)| {
                // Validate embedding dimension
                if prediction.embeddings.values.len() != EMBEDDING_DIMENSION {
                    tracing::warn!(
                        "Unexpected embedding dimension: {} (expected {})",
                        prediction.embeddings.values.len(),
                        EMBEDDING_DIMENSION
                    );
                }
                
                CodeEmbedding {
                    chunk_id: format!("chunk_{:016x}", i),
                    vector: prediction.embeddings.values,
                    model: "text-embedding-004".to_string(),
                    metadata: Some(crate::models::EmbeddingMetadata {
                        tokens_used: None, // Vertex AI doesn't return token count
                        created_at: Some(Utc::now()),
                    }),
                }
            })
            .collect();

        Ok(embeddings)
    }

    async fn get_auth_token(&self) -> Result<String> {
        // For Cloud Run, we'll use the metadata server for authentication
        // The GCP SDKs will automatically use the service account associated with the Cloud Run service
        // For now, return empty token as the client libraries will handle auth automatically
        Ok(String::new())
    }

    fn extract_code_content(&self, analysis: &FileAnalysis) -> String {
        let mut content = String::new();
        
        // Add file path as context
        content.push_str(&format!("File: {}\n", analysis.path));
        
        // Add language
        content.push_str(&format!("Language: {}\n", analysis.language));
        
        // Add symbols if available
        if let Some(symbols) = &analysis.symbols {
            content.push_str("Symbols:\n");
            for symbol in symbols.iter().take(10) { // Limit to avoid exceeding token limits
                content.push_str(&format!("- {} {}\n", symbol.symbol_type, symbol.name));
            }
        }
        
        // Add code metrics
        content.push_str(&format!(
            "Metrics: {} LOC, complexity {}\n",
            analysis.metrics.lines_of_code,
            analysis.metrics.complexity
        ));
        
        // Add a portion of the actual code if available
        if let Some(text) = &analysis.ast.text {
            // Take first 2000 characters for embedding (to stay within token limits)
            let preview = text.chars().take(2000).collect::<String>();
            content.push_str(&format!("\nCode snippet:\n{}", preview));
        }
        
        content
    }

    fn create_empty_embedding(&self) -> CodeEmbedding {
        CodeEmbedding {
            chunk_id: format!("empty_{:016x}", rand::random::<u64>()),
            vector: vec![0.0; EMBEDDING_DIMENSION],
            model: "text-embedding-004".to_string(),
            metadata: Some(crate::models::EmbeddingMetadata {
                tokens_used: Some(0),
                created_at: Some(Utc::now()),
            }),
        }
    }

    fn is_retryable_error(&self, error: &anyhow::Error) -> bool {
        // Check if error message indicates a retryable condition
        let error_str = error.to_string().to_lowercase();
        
        error_str.contains("timeout") ||
        error_str.contains("temporarily unavailable") ||
        error_str.contains("429") || // Rate limit
        error_str.contains("500") || // Internal server error
        error_str.contains("502") || // Bad gateway
        error_str.contains("503") || // Service unavailable
        error_str.contains("504")    // Gateway timeout
    }

    // Circuit breaker implementation
    async fn check_circuit_breaker(&self) -> Result<bool> {
        let mut state = self.circuit_state.lock().await;
        
        match *state {
            CircuitState::Closed => Ok(true),
            CircuitState::Open(reset_time) => {
                if Utc::now() > reset_time {
                    *state = CircuitState::HalfOpen;
                    tracing::info!("Circuit breaker transitioning to half-open");
                    Ok(true)
                } else {
                    Ok(false)
                }
            }
            CircuitState::HalfOpen => Ok(true),
        }
    }

    async fn record_success(&self) {
        let mut state = self.circuit_state.lock().await;
        let mut failures = self.failure_count.lock().await;
        
        *failures = 0;
        
        if matches!(*state, CircuitState::HalfOpen) {
            *state = CircuitState::Closed;
            tracing::info!("Circuit breaker closed after successful request");
        }
    }

    async fn record_failure(&self) {
        let mut state = self.circuit_state.lock().await;
        let mut failures = self.failure_count.lock().await;
        
        *failures += 1;
        
        if *failures >= self.failure_threshold {
            let reset_time = Utc::now() + chrono::Duration::from_std(self.reset_timeout)
                .unwrap_or_else(|_| chrono::Duration::seconds(300)); // Default to 5 minutes
            *state = CircuitState::Open(reset_time);
            *failures = 0;
            
            tracing::error!(
                "Circuit breaker opened after {} failures, will reset at {}",
                self.failure_threshold,
                reset_time
            );
        }
    }
}


#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_empty_embedding_dimension() {
        let service = EmbeddingsService::new_for_test();
        let embedding = service.create_empty_embedding();

        assert_eq!(embedding.vector.len(), EMBEDDING_DIMENSION);
        assert_eq!(embedding.model, "text-embedding-004");
    }

    #[tokio::test]
    async fn test_retryable_error_detection() {
        let service = EmbeddingsService::new_for_test();
        
        let timeout_error = anyhow::anyhow!("Request timeout");
        assert!(service.is_retryable_error(&timeout_error));
        
        let rate_limit_error = anyhow::anyhow!("Status: 429 Too Many Requests");
        assert!(service.is_retryable_error(&rate_limit_error));
        
        let auth_error = anyhow::anyhow!("Authentication failed");
        assert!(!service.is_retryable_error(&auth_error));
    }
}