use crate::config::ServiceConfig;
use crate::git::GitService;
use crate::metrics::MetricsService;
use crate::models::*;
use crate::parser::TreeSitterParser;
use crate::services::embeddings::EmbeddingsService;
use crate::services::language_detector::LanguageDetector;
use crate::storage::{PubSubOperations, SpannerOperations, StorageOperations, CacheManager};
use anyhow::{Context, Result};
use chrono::Utc;
use std::collections::HashMap;
use rayon::prelude::*;
use std::path::{Path, PathBuf};
use std::sync::atomic::{AtomicUsize, Ordering};
use std::sync::Arc;
use tokio::sync::{mpsc, broadcast};
use walkdir::WalkDir;

pub struct AnalysisService {
    spanner_client: Option<Arc<SpannerOperations>>,
    storage_client: Arc<StorageOperations>,
    pubsub_client: Arc<PubSubOperations>,
    cache_manager: Arc<CacheManager>,
    config: Arc<ServiceConfig>,
    git_service: Arc<GitService>,
    language_detector: Arc<LanguageDetector>,
    parser: Arc<TreeSitterParser>,
    embeddings_service: Arc<EmbeddingsService>,
}

impl AnalysisService {
    pub async fn new(
        spanner_client: Option<Arc<SpannerOperations>>,
        storage_client: Arc<StorageOperations>,
        pubsub_client: Arc<PubSubOperations>,
        cache_manager: Arc<CacheManager>,
        config: Arc<ServiceConfig>,
    ) -> Result<Self> {
        let embeddings_service = EmbeddingsService::new().await
            .context("Failed to create embeddings service")?;
            
        Ok(Self {
            spanner_client,
            storage_client,
            pubsub_client,
            cache_manager,
            config,
            git_service: Arc::new(GitService::new()),
            language_detector: Arc::new(LanguageDetector::new()),
            parser: Arc::new(TreeSitterParser::new().map_err(|e| {
                anyhow::anyhow!("Failed to create TreeSitter parser: {}", e)
            })?),
            embeddings_service: Arc::new(embeddings_service),
        })
    }

    pub async fn start_analysis(
        &self,
        analysis_id: String,
        request: AnalysisRequest,
        user_id: String,
        progress_broadcast: broadcast::Sender<ProgressUpdate>,
    ) -> Result<()> {
        let (progress_tx, mut progress_rx) = mpsc::channel::<ProgressUpdate>(100);

        // Spawn task to handle progress updates
        let analysis_id_clone = analysis_id.clone();
        let pubsub_clone = self.pubsub_client.clone();
        let broadcast_tx = progress_broadcast.clone();
        tokio::spawn(async move {
            while let Some(update) = progress_rx.recv().await {
                tracing::info!("Analysis {} progress: {:?}", analysis_id_clone, update);
                
                // Broadcast to WebSocket clients
                if let Err(e) = broadcast_tx.send(update.clone()) {
                    tracing::warn!("Failed to broadcast progress update: {}", e);
                }
                
                // Also publish to PubSub for external consumers
                if let Err(e) = pubsub_clone.publish_progress(&update).await {
                    tracing::error!("Failed to publish progress update to PubSub: {}", e);
                }
            }
        });

        // Execute analysis
        match self
            .analyze_repository(&request, progress_tx.clone(), &user_id)
            .await
        {
            Ok(result) => {
                tracing::info!("Analysis {} completed successfully", analysis_id);
                self.store_analysis_result(&result).await?;
                self.publish_completion_event(&result).await?;

                if let Some(webhook_url) = &request.webhook_url {
                    self.send_webhook_notification(webhook_url, &result)
                        .await
                        .unwrap_or_else(|e| tracing::warn!("Failed to send webhook: {}", e));
                }
            }
            Err(e) => {
                tracing::error!("Analysis {} failed: {}", analysis_id, e);
                let mut result = AnalysisResult::default();
                result.id = analysis_id;
                result.status = AnalysisStatus::Failed;
                result.error_message = Some(e.to_string());
                result.user_id = user_id;
                self.store_analysis_result(&result).await?;
                self.publish_completion_event(&result).await?;
            }
        }

        Ok(())
    }

    async fn analyze_repository(
        &self,
        opts: &AnalysisRequest,
        progress_tx: mpsc::Sender<ProgressUpdate>,
        user_id: &str,
    ) -> Result<AnalysisResult> {
        let analysis_id = uuid::Uuid::new_v4().to_string();
        let start_time = Utc::now();
        let mut performance_metrics = PerformanceMetrics::default();
        
        // Check cache for existing analysis results with commit hash validation
        let cache_key = format!("{}:{}", opts.repository_url, opts.branch.as_ref().unwrap_or(&"main".to_string()));

        // Get current commit hash from remote repository
        match self.git_service.get_remote_commit_hash(&opts.repository_url, &opts.branch).await {
            Ok(current_commit_hash) => {
                if let Ok(Some(cached_analyses)) = self.cache_manager.get_analysis_with_commit_check(&cache_key, &current_commit_hash).await {
                    tracing::info!("Cache hit with fresh commit hash for repository: {}", opts.repository_url);

                    // Return cached result
                    return Ok(AnalysisResult {
                        id: analysis_id,
                        repository_url: opts.repository_url.clone(),
                        branch: opts.branch.clone().unwrap_or_else(|| "main".to_string()),
                        status: AnalysisStatus::Completed,
                        started_at: start_time,
                        completed_at: Some(Utc::now()),
                        duration_seconds: Some((Utc::now() - start_time).num_seconds() as u64),
                        file_count: cached_analyses.len(),
                        success_rate: 1.0,
                        progress: Some(100.0),
                        current_stage: Some("Completed".to_string()),
                        estimated_completion: None,
                        patterns: vec![],
                        languages: HashMap::new(),
                        embeddings: None,
                        successful_analyses: Some(cached_analyses),
                        user_id: "anonymous".to_string(),
                        webhook_url: None,
                        failed_files: vec![],
                        metrics: None, // Could be enhanced to cache metrics too
                        performance_metrics: Some(performance_metrics),
                        error_message: None,
                    });
                } else {
                    tracing::info!("Cache miss or stale cache for repository: {} (commit: {})", opts.repository_url, current_commit_hash);
                }
            }
            Err(e) => {
                tracing::warn!("Failed to get remote commit hash for cache validation: {}", e);
                // Continue with normal analysis if we can't get commit hash
            }
        }

        // Clone repository with timing
        self.send_progress(&progress_tx, &analysis_id, 5.0, "Cloning repository")
            .await?;
        let clone_start = std::time::Instant::now();
        let repo_path = self
            .git_service
            .clone_repository_with_auth(&opts.repository_url, &opts.branch, &analysis_id)
            .await
            .context("Failed to clone repository")?;
        performance_metrics.clone_duration_ms = clone_start.elapsed().as_millis() as u64;

        // Get the actual commit hash from the cloned repository
        let actual_commit_hash = self.git_service.get_current_commit_hash(&repo_path)
            .context("Failed to get commit hash from cloned repository")?;
        tracing::info!("Cloned repository {} at commit {}", opts.repository_url, actual_commit_hash);

        // Detect languages with timing
        self.send_progress(&progress_tx, &analysis_id, 15.0, "Detecting languages")
            .await?;
        let lang_start = std::time::Instant::now();
        let languages = self
            .language_detector
            .detect_languages_with_stats(&repo_path)?;
        self.language_detector
            .validate_supported_languages(&languages, &opts.languages)?;
        performance_metrics.language_detection_ms = lang_start.elapsed().as_millis() as u64;

        // Collect files with timing
        self.send_progress(&progress_tx, &analysis_id, 25.0, "Collecting source files")
            .await?;
        let collect_start = std::time::Instant::now();
        let files =
            self.collect_source_files(&repo_path, &opts.include_patterns, &opts.exclude_patterns)?;
        performance_metrics.file_collection_ms = collect_start.elapsed().as_millis() as u64;

        // Parse files with timing
        self.send_progress(&progress_tx, &analysis_id, 35.0, "Parsing source files")
            .await?;
        let parse_start = std::time::Instant::now();
        let ast_results = self
            .parse_files_parallel(&files, progress_tx.clone(), analysis_id.clone())
            .await?;
        let (successful_analyses, failed_files) = self.partition_results(ast_results);
        performance_metrics.ast_parsing_ms = parse_start.elapsed().as_millis() as u64;
        performance_metrics.files_analyzed = files.len() as u64;
        performance_metrics.successful_parses = successful_analyses.len() as u64;
        performance_metrics.failed_parses = failed_files.len() as u64;

        // Extract metrics with timing
        self.send_progress(&progress_tx, &analysis_id, 70.0, "Extracting metrics")
            .await?;
        let metrics_start = std::time::Instant::now();
        let metrics_service = MetricsService::new();
        let metrics = metrics_service.calculate_repository_metrics(&successful_analyses);
        performance_metrics.metrics_calculation_ms = metrics_start.elapsed().as_millis() as u64;

        // Detect patterns with timing
        let patterns = if opts.enable_patterns {
            self.send_progress(&progress_tx, &analysis_id, 85.0, "Detecting patterns")
                .await?;
            let pattern_start = std::time::Instant::now();
            let detected_patterns = self.detect_patterns(&successful_analyses).await?;
            performance_metrics.pattern_detection_ms = pattern_start.elapsed().as_millis() as u64;
            performance_metrics.patterns_detected = detected_patterns.len() as u64;
            detected_patterns
        } else {
            vec![]
        };

        // Generate embeddings with timing
        let embeddings = if opts.enable_embeddings {
            self.send_progress(&progress_tx, &analysis_id, 95.0, "Generating embeddings")
                .await?;
            let embedding_start = std::time::Instant::now();
            let generated_embeddings = self.embeddings_service
                .generate_embeddings(&successful_analyses)
                .await?;
            performance_metrics.embedding_generation_ms = embedding_start.elapsed().as_millis() as u64;
            performance_metrics.embeddings_generated = generated_embeddings.len() as u64;
            Some(generated_embeddings)
        } else {
            None
        };

        // Cleanup
        let cleanup_start = std::time::Instant::now();
        self.git_service.cleanup_repository(&repo_path).await?;
        performance_metrics.cleanup_ms = cleanup_start.elapsed().as_millis() as u64;

        // Calculate total processing time
        performance_metrics.total_processing_ms = (Utc::now() - start_time).num_milliseconds() as u64;
        
        // Track memory usage
        performance_metrics.memory_peak_mb = self.get_peak_memory_usage();

        self.send_progress(&progress_tx, &analysis_id, 100.0, "Analysis complete")
            .await?;
        
        // Cache the successful analyses for future use with commit hash
        if let Err(e) = self.cache_manager.set_analysis_with_commit(&cache_key, &successful_analyses, &actual_commit_hash).await {
            tracing::warn!("Failed to cache analysis results: {}", e);
            // Don't fail the analysis if caching fails
        }
        
        // Cache patterns separately for pattern detection reuse
        if !patterns.is_empty() {
            for analysis in &successful_analyses {
                let file_patterns: Vec<String> = patterns
                    .iter()
                    .filter(|p| p.location.file_path == analysis.path)
                    .map(|p| serde_json::to_string(p).unwrap_or_default())
                    .collect();
                
                if !file_patterns.is_empty() {
                    if let Err(e) = self.cache_manager.set_patterns(&analysis.content_hash, &file_patterns).await {
                        tracing::warn!("Failed to cache patterns for file {}: {}", analysis.path, e);
                    }
                }
            }
        }
        
        // Cache embeddings separately for embedding reuse
        if let Some(ref emb_list) = embeddings {
            for embedding in emb_list {
                if let Err(e) = self.cache_manager.set_embeddings(&embedding.chunk_id, &embedding.vector).await {
                    tracing::warn!("Failed to cache embedding for chunk {}: {}", embedding.chunk_id, e);
                }
            }
        }

        Ok(AnalysisResult {
            id: analysis_id.clone(),
            repository_url: opts.repository_url.clone(),
            branch: opts.branch.clone().unwrap_or_else(|| "main".to_string()),
            status: AnalysisStatus::Completed,
            started_at: start_time,
            completed_at: Some(Utc::now()),
            duration_seconds: Some((Utc::now() - start_time).num_seconds() as u64),
            progress: Some(100.0),
            current_stage: Some("Completed".to_string()),
            estimated_completion: None,
            metrics: Some(metrics),
            patterns,
            languages,
            embeddings,
            error_message: None,
            failed_files,
            successful_analyses: Some(successful_analyses.clone()),
            user_id: user_id.to_string(),
            webhook_url: opts.webhook_url.clone(),
            file_count: files.len(),
            success_rate: (successful_analyses.len() as f64 / files.len() as f64) * 100.0,
            performance_metrics: Some(performance_metrics),
        })
    }

    fn collect_source_files(
        &self,
        repo_path: &Path,
        include_patterns: &[String],
        exclude_patterns: &[String],
    ) -> Result<Vec<PathBuf>> {
        let mut files = Vec::new();
        let default_exclude = ["*/target/*", "*/node_modules/*", "*.lock"];

        for entry in WalkDir::new(repo_path)
            .follow_links(true)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                let path = entry.path();
                let relative_path = path.strip_prefix(repo_path)?;
                let path_str = relative_path.to_string_lossy();

                let is_default_excluded = default_exclude.iter().any(|p| glob_match::glob_match(p, &path_str));
                if is_default_excluded {
                    continue;
                }

                let included = include_patterns.is_empty()
                    || include_patterns
                        .iter()
                        .any(|p| glob_match::glob_match(p, &path_str));

                let excluded = exclude_patterns
                    .iter()
                    .any(|p| glob_match::glob_match(p, &path_str));

                if included && !excluded {
                    if let Ok(metadata) = entry.metadata() {
                        if metadata.len() <= self.config.analysis.max_file_size_mb * 1024 * 1024 {
                            files.push(path.to_path_buf());
                        }
                    }
                }
            }
        }
        Ok(files)
    }

    async fn parse_files_parallel(
        &self,
        files: &[PathBuf],
        progress_tx: mpsc::Sender<ProgressUpdate>,
        analysis_id: String,
    ) -> Result<Vec<Result<FileAnalysis, ParseError>>> {
        let total_files = files.len();
        let progress_counter = Arc::new(AtomicUsize::new(0));
        let parser = self.parser.clone();
        let timeout_seconds = self.config.analysis.analysis_timeout_seconds;
        
        // Memory optimization: Process files in batches to control memory usage
        const BATCH_SIZE: usize = 100; // Process 100 files at a time
        let mut all_results = Vec::with_capacity(files.len());
        
        // Check available memory before processing
        let available_memory = self.get_available_memory();
        let batch_size = if available_memory < 1024 * 1024 * 1024 { // Less than 1GB
            std::cmp::min(BATCH_SIZE / 2, 50) // Reduce batch size
        } else {
            BATCH_SIZE
        };
        
        for chunk in files.chunks(batch_size) {
            let chunk_results: Vec<_> = chunk
                .par_iter()
                .map(|file_path| {
                let rt = tokio::runtime::Handle::current();
                let progress_tx = progress_tx.clone();
                let analysis_id = analysis_id.clone();
                let progress_counter = progress_counter.clone();
                let parser = parser.clone();

                rt.block_on(async move {
                    let result = tokio::time::timeout(
                        std::time::Duration::from_secs(timeout_seconds),
                        parser.parse_file(file_path),
                    )
                    .await;

                    let parsed_result = match result {
                        Ok(Ok(ast)) => Ok(ast),
                        Ok(Err(e)) => Err(e),
                        Err(_) => Err(ParseError {
                            file_path: file_path.to_string_lossy().to_string(),
                            error_type: ParseErrorType::Timeout,
                            message: "Parse timeout".to_string(),
                            position: None,
                        }),
                    };

                    let completed = progress_counter.fetch_add(1, Ordering::Relaxed) + 1;
                    if completed % 10 == 0 || completed == total_files {
                        let progress = 35.0 + (completed as f64 / total_files as f64) * 35.0;
                        let _ = progress_tx
                            .send(ProgressUpdate {
                                analysis_id: analysis_id.clone(),
                                progress,
                                stage: format!("Parsed {}/{} files", completed, total_files),
                                message: None,
                                timestamp: Utc::now(),
                                files_processed: Some(completed),
                                total_files: Some(total_files),
                            })
                            .await;
                    }

                    parsed_result
                })
            })
            .collect();
            
            all_results.extend(chunk_results);
            
            // Force garbage collection between batches for memory-constrained environments
            if available_memory < 2 * 1024 * 1024 * 1024 { // Less than 2GB
                // Give the system a chance to reclaim memory
                tokio::time::sleep(std::time::Duration::from_millis(10)).await;
            }
        }

        Ok(all_results)
    }
    
    fn get_available_memory(&self) -> u64 {
        // Platform-specific memory check
        #[cfg(target_os = "linux")]
        {
            if let Ok(contents) = std::fs::read_to_string("/proc/meminfo") {
                for line in contents.lines() {
                    if line.starts_with("MemAvailable:") {
                        if let Some(kb_str) = line.split_whitespace().nth(1) {
                            if let Ok(kb) = kb_str.parse::<u64>() {
                                return kb * 1024; // Convert KB to bytes
                            }
                        }
                    }
                }
            }
        }
        
        // Default to 4GB if we can't determine
        4 * 1024 * 1024 * 1024
    }
    
    fn get_peak_memory_usage(&self) -> u64 {
        // Platform-specific memory usage tracking
        #[cfg(target_os = "linux")]
        {
            if let Ok(status) = std::fs::read_to_string("/proc/self/status") {
                for line in status.lines() {
                    if line.starts_with("VmPeak:") {
                        if let Some(kb_str) = line.split_whitespace().nth(1) {
                            if let Ok(kb) = kb_str.parse::<u64>() {
                                return kb / 1024; // Convert KB to MB
                            }
                        }
                    }
                }
            }
        }
        
        // Default to 0 if we can't determine
        0
    }

    fn partition_results(
        &self,
        results: Vec<Result<FileAnalysis, ParseError>>,
    ) -> (Vec<FileAnalysis>, Vec<FailedFile>) {
        let mut successful_analyses = Vec::new();
        let mut failed_files = Vec::new();

        for result in results {
            match result {
                Ok(analysis) => successful_analyses.push(analysis),
                Err(e) => failed_files.push(FailedFile {
                    file_path: e.file_path,
                    error_message: e.message,
                    error_type: e.error_type.into(),
                }),
            }
        }

        (successful_analyses, failed_files)
    }

    async fn detect_patterns(&self, analyses: &[FileAnalysis]) -> Result<Vec<DetectedPattern>> {
        use crate::services::pattern_detector::PatternDetector;
        
        let detector = PatternDetector::new();
        let mut all_patterns = Vec::new();
        
        // Detect patterns in each file using proper AST analysis
        for analysis in analyses {
            let patterns = detector.detect_patterns(analysis);
            all_patterns.extend(patterns);
        }
        
        // Publish pattern detection events if patterns were found
        if !all_patterns.is_empty() {
            let pattern_count = all_patterns.len();
            let pattern_types: std::collections::HashSet<_> = all_patterns
                .iter()
                .map(|p| format!("{:?}", p.pattern_type))
                .collect();
            
            for pattern_type in pattern_types {
                let count = all_patterns
                    .iter()
                    .filter(|p| format!("{:?}", p.pattern_type) == pattern_type)
                    .count();
                
                if let Err(e) = self.pubsub_client
                    .publish_pattern_detected(&analyses[0].path, &pattern_type, count)
                    .await {
                    tracing::warn!("Failed to publish pattern detected event: {}", e);
                }
            }
            
            tracing::info!(
                "Detected {} patterns across {} files",
                pattern_count,
                analyses.len()
            );
        }
        
        Ok(all_patterns)
    }

    async fn send_progress(
        &self,
        tx: &mpsc::Sender<ProgressUpdate>,
        analysis_id: &str,
        progress: f64,
        stage: &str,
    ) -> Result<()> {
        tx.send(ProgressUpdate {
            analysis_id: analysis_id.to_string(),
            progress,
            stage: stage.to_string(),
            message: None,
            timestamp: Utc::now(),
            files_processed: None,
            total_files: None,
        })
        .await
        .map_err(|e| anyhow::anyhow!("Failed to send progress: {}", e))
    }

    async fn store_analysis_result(&self, result: &AnalysisResult) -> Result<()> {
        // Store in Spanner if available
        if let Some(spanner) = &self.spanner_client {
            spanner.store_analysis(result).await?;
        } else {
            tracing::warn!("Spanner not available - skipping database storage for analysis {}", result.id);
        }
        
        // Always store in cloud storage
        self.storage_client.store_analysis_results(result).await?;
        Ok(())
    }

    async fn publish_completion_event(&self, result: &AnalysisResult) -> Result<()> {
        self.pubsub_client.publish_analysis_event(result).await
    }

    async fn send_webhook_notification(
        &self,
        webhook_url: &str,
        result: &AnalysisResult,
    ) -> Result<()> {
        let client = reqwest::Client::new();
        client
            .post(webhook_url)
            .json(result)
            .send()
            .await?
            .error_for_status()?;
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::storage::redis_client::RedisClient;
    use tempfile::TempDir;
    use std::fs;

    fn create_test_config() -> ServiceConfig {
        ServiceConfig {
            service: crate::config::ServiceSettings {
                name: "analysis-engine-test".to_string(),
                version: "0.1.0".to_string(),
                port: 8001,
                host: "127.0.0.1".to_string(),
                environment: crate::config::Environment::Development,
            },
            gcp: crate::config::GcpSettings {
                project_id: "test-project".to_string(),
                spanner_instance: "test-instance".to_string(),
                spanner_database: "test-database".to_string(),
                storage_bucket: "test-bucket".to_string(),
                pubsub_topic: "test-topic".to_string(),
                region: "us-central1".to_string(),
            },
            analysis: crate::config::AnalysisSettings {
                max_concurrent_analyses: 50,
                max_repository_size_gb: 10,
                analysis_timeout_seconds: 300,
                max_file_size_mb: 10,
                temp_dir: "/tmp/test".to_string(),
                supported_languages: vec![
                    "rust".to_string(),
                    "python".to_string(),
                    "javascript".to_string(),
                    "typescript".to_string(),
                ],
            },
            security: crate::config::SecuritySettings {
                enable_auth: false,
                api_key_header: "x-api-key".to_string(),
                jwt_secret: Some("test-secret".to_string()),
                cors_origins: vec!["*".to_string()],
            },
            observability: crate::config::ObservabilitySettings {
                enable_tracing: false,
                enable_metrics: false,
                log_level: "info".to_string(),
                otel_endpoint: None,
            },
        }
    }

    fn create_test_repo(temp_dir: &TempDir) -> PathBuf {
        let repo_path = temp_dir.path().to_path_buf();
        
        // Create some test files
        fs::create_dir_all(repo_path.join("src")).unwrap();
        fs::write(
            repo_path.join("src/main.rs"),
            r#"
fn main() {
    println!("Hello, world!");
}

fn add(a: i32, b: i32) -> i32 {
    a + b
}

#[test]
fn test_add() {
    assert_eq!(add(2, 2), 4);
}
"#,
        ).unwrap();
        
        fs::write(
            repo_path.join("src/lib.rs"),
            r#"
pub struct Config {
    pub name: String,
    pub value: i32,
}

impl Config {
    pub fn new(name: String, value: i32) -> Self {
        Self { name, value }
    }
}
"#,
        ).unwrap();
        
        // Create a file to be excluded
        fs::create_dir_all(repo_path.join("target")).unwrap();
        fs::write(repo_path.join("target/debug.rs"), "// Should be excluded").unwrap();
        
        repo_path
    }

    #[test]
    fn test_partition_results_simple() {
        let config = Arc::new(create_test_config());
        
        // Create a simple service instance without real GCP clients
        // We'll test the partition_results method directly
        let results = vec![
            Ok(FileAnalysis {
                path: "test1.rs".to_string(),
                language: "rust".to_string(),
                content_hash: "hash1".to_string(),
                size_bytes: Some(100),
                ast: AstNode {
                    node_type: "root".to_string(),
                    name: None,
                    range: Range {
                        start: Position { line: 0, column: 0, byte: 0 },
                        end: Position { line: 10, column: 0, byte: 100 },
                    },
                    children: vec![],
                    properties: None,
                    text: None,
                },
                metrics: FileMetrics {
                    lines_of_code: 10,
                    total_lines: Some(15),
                    complexity: 1,
                    maintainability_index: 80.0,
                    function_count: Some(2),
                    class_count: Some(0),
                    comment_ratio: Some(0.3),
                },
                chunks: Some(vec![]),
                symbols: Some(vec![]),
            }),
            Err(ParseError {
                file_path: "test2.rs".to_string(),
                error_type: ParseErrorType::ParseError,
                message: "Failed to parse".to_string(),
                position: None,
            }),
        ];
        
        // Test partitioning directly without creating a full service
        let mut successful_analyses = Vec::new();
        let mut failed_files = Vec::new();

        for result in results {
            match result {
                Ok(analysis) => successful_analyses.push(analysis),
                Err(e) => failed_files.push(FailedFile {
                    file_path: e.file_path,
                    error_message: e.message,
                    error_type: e.error_type.into(),
                }),
            }
        }
        
        assert_eq!(successful_analyses.len(), 1);
        assert_eq!(failed_files.len(), 1);
        assert_eq!(successful_analyses[0].path, "test1.rs");
        assert_eq!(failed_files[0].file_path, "test2.rs");
    }

    #[tokio::test]
    async fn test_send_progress_channel() {
        let (tx, mut rx) = mpsc::channel(10);
        
        // Test sending progress update directly
        let update = ProgressUpdate {
            analysis_id: "test-id".to_string(),
            progress: 50.0,
            stage: "Processing".to_string(),
            message: None,
            timestamp: Utc::now(),
            files_processed: None,
            total_files: None,
        };
        
        tx.send(update.clone()).await.unwrap();
        
        let received = rx.recv().await.unwrap();
        assert_eq!(received.analysis_id, "test-id");
        assert_eq!(received.progress, 50.0);
        assert_eq!(received.stage, "Processing");
    }

    #[test]
    fn test_file_collection_logic() {
        let temp_dir = TempDir::new().unwrap();
        let repo_path = create_test_repo(&temp_dir);
        
        // Test glob matching logic
        let files: Vec<PathBuf> = WalkDir::new(&repo_path)
            .follow_links(true)
            .into_iter()
            .filter_map(|e| e.ok())
            .filter(|e| e.file_type().is_file())
            .map(|e| e.path().to_path_buf())
            .collect();
        
        assert!(files.len() >= 2); // At least main.rs and lib.rs
        
        // Test exclude patterns
        let default_exclude = ["*/target/*", "*/node_modules/*", "*.lock"];
        let filtered_files: Vec<PathBuf> = files
            .into_iter()
            .filter(|path| {
                let relative = path.strip_prefix(&repo_path).unwrap();
                let path_str = relative.to_string_lossy();
                !default_exclude.iter().any(|p| glob_match::glob_match(p, &path_str))
            })
            .collect();
        
        // Debug: print files found
        for file in &filtered_files {
            if let Some(relative) = file.strip_prefix(&repo_path).ok() {
                println!("Found file: {}", relative.display());
            }
        }
        assert!(filtered_files.len() >= 2); // Should have at least main.rs and lib.rs
    }
}
