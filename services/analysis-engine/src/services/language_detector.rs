use anyhow::Result;
use std::path::Path;
use std::collections::HashMap;
use crate::models::LanguageStats;
use tokei::{Config, Languages};

pub struct LanguageDetector;

impl LanguageDetector {
    pub fn new() -> Self {
        Self
    }

    pub fn detect_languages_with_stats(&self, repo_path: &Path) -> Result<HashMap<String, LanguageStats>> {
        let mut languages = Languages::new();
        let config = Config::default();
        languages.get_statistics(&[repo_path], &[], &config);

        let mut stats = HashMap::new();
        let total_lines: usize = languages.iter().map(|(_, stat)| stat.lines()).sum();
        
        for (language, language_stat) in languages.iter() {
            let lines = language_stat.lines();
            let percentage = if total_lines > 0 {
                (lines as f64 / total_lines as f64) * 100.0
            } else {
                0.0
            };
            
            stats.insert(
                language.to_string().to_lowercase(),
                LanguageStats {
                    files: language_stat.reports.len(),
                    lines,
                    bytes: None,
                    percentage,
                },
            );
        }
        Ok(stats)
    }

    pub fn validate_supported_languages(
        &self,
        languages: &HashMap<String, LanguageStats>,
        requested_languages: &[String],
    ) -> Result<()> {
        if requested_languages.is_empty() {
            return Ok(());
        }

        for lang in requested_languages {
            if !languages.contains_key(lang) {
                return Err(anyhow::anyhow!("Unsupported language: {}", lang));
            }
        }

        Ok(())
    }
}