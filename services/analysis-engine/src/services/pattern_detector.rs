use crate::models::{FileAnalysis, DetectedPattern, PatternType, PatternLocation, Range, AstNode};
#[cfg(test)]
use crate::models::Position;
use std::collections::HashMap;

/// Pattern detection engine that analyzes AST nodes to find code patterns
pub struct PatternDetector {
    // Pattern-specific configuration
    long_method_threshold: usize,
    complexity_threshold: usize,
}

impl PatternDetector {
    pub fn new() -> Self {
        Self {
            long_method_threshold: 50,
            complexity_threshold: 10,
        }
    }

    /// Detect all patterns in a file analysis
    pub fn detect_patterns(&self, analysis: &FileAnalysis) -> Vec<DetectedPattern> {
        let mut patterns = Vec::new();

        // Traverse AST and detect patterns
        let mut visitor = PatternVisitor::new(&analysis.path);
        
        // Visit the AST
        traverse_ast(&analysis.ast, &mut visitor);

        // Extract detected patterns
        patterns.extend(visitor.singleton_patterns());
        patterns.extend(visitor.factory_patterns());
        patterns.extend(visitor.long_method_patterns(self.long_method_threshold));
        patterns.extend(visitor.sql_injection_patterns());
        patterns.extend(visitor.code_smell_patterns(self.complexity_threshold));

        patterns
    }
}

/// Visitor pattern for traversing AST and detecting patterns
struct PatternVisitor {
    file_path: String,
    current_class: Option<String>,
    current_method: Option<String>,
    classes: HashMap<String, ClassInfo>,
    methods: HashMap<String, MethodInfo>,
    sql_queries: Vec<SqlQueryInfo>,
    #[allow(dead_code)]
    code_smells: Vec<CodeSmellInfo>,
}

#[derive(Debug)]
struct ClassInfo {
    name: String,
    has_private_constructor: bool,
    has_static_instance: bool,
    implements_factory: bool,
    range: Range,
}

#[derive(Debug)]
struct MethodInfo {
    name: String,
    #[allow(dead_code)]
    class_name: Option<String>,
    line_count: usize,
    complexity: usize,
    range: Range,
}

#[derive(Debug)]
struct SqlQueryInfo {
    location: Range,
    has_string_concat: bool,
    query_type: String,
}

#[derive(Debug)]
#[allow(dead_code)]
struct CodeSmellInfo {
    smell_type: String,
    location: Range,
    description: String,
}

impl PatternVisitor {
    fn new(file_path: &str) -> Self {
        Self {
            file_path: file_path.to_string(),
            current_class: None,
            current_method: None,
            classes: HashMap::new(),
            methods: HashMap::new(),
            sql_queries: Vec::new(),
            code_smells: Vec::new(),
        }
    }

    fn visit(&mut self, node: &AstNode) {
        match node.node_type.as_str() {
            // Class/Type definitions
            "class_definition" | "class_declaration" | "struct_item" | "impl_item" => {
                self.visit_class(node);
            }
            
            // Method/Function definitions
            "method_definition" | "function_definition" | "function_item" => {
                self.visit_method(node);
            }
            
            // Constructor definitions
            "constructor_definition" | "init_definition" => {
                self.visit_constructor(node);
            }
            
            // SQL-related patterns
            "call_expression" | "method_call" => {
                self.visit_call_expression(node);
            }
            
            // Check for static fields/methods
            "field_definition" | "property_definition" => {
                self.visit_field(node);
            }
            
            _ => {}
        }
    }

    fn visit_class(&mut self, node: &AstNode) {
        if let Some(name) = &node.name {
            self.current_class = Some(name.clone());
            
            let class_info = ClassInfo {
                name: name.clone(),
                has_private_constructor: false,
                has_static_instance: false,
                implements_factory: self.is_factory_class(node),
                range: node.range.clone(),
            };
            
            self.classes.insert(name.clone(), class_info);
        }
    }

    fn visit_method(&mut self, node: &AstNode) {
        if let Some(name) = &node.name {
            self.current_method = Some(name.clone());
            
            let line_count = node.range.end.line - node.range.start.line;
            let complexity = self.calculate_complexity(node);
            
            let method_info = MethodInfo {
                name: name.clone(),
                class_name: self.current_class.clone(),
                line_count: line_count as usize,
                complexity,
                range: node.range.clone(),
            };
            
            self.methods.insert(
                format!("{:?}::{}", self.current_class, name),
                method_info,
            );

            // Check for getInstance pattern (Singleton)
            if name.to_lowercase().contains("instance") || name == "getInstance" {
                if let Some(class_name) = &self.current_class {
                    if let Some(class_info) = self.classes.get_mut(class_name) {
                        class_info.has_static_instance = true;
                    }
                }
            }
        }
    }

    fn visit_constructor(&mut self, node: &AstNode) {
        // Check if constructor is private (for Singleton pattern)
        if let Some(class_name) = &self.current_class {
            let is_private = self.is_private_member(node);
            
            if is_private {
                if let Some(class_info) = self.classes.get_mut(class_name) {
                    class_info.has_private_constructor = true;
                }
            }
        }
    }

    fn visit_call_expression(&mut self, node: &AstNode) {
        // Check for SQL query patterns
        if self.is_sql_query(node) {
            let has_concat = self.has_string_concatenation(node);
            
            self.sql_queries.push(SqlQueryInfo {
                location: node.range.clone(),
                has_string_concat: has_concat,
                query_type: self.get_sql_query_type(node),
            });
        }
    }

    fn visit_field(&mut self, node: &AstNode) {
        // Check for static instance fields (Singleton pattern)
        if let Some(name) = &node.name {
            if self.is_static_field(node) && name.to_lowercase().contains("instance") {
                if let Some(class_name) = &self.current_class {
                    if let Some(class_info) = self.classes.get_mut(class_name) {
                        class_info.has_static_instance = true;
                    }
                }
            }
        }
    }

    // Pattern extraction methods

    fn singleton_patterns(&self) -> Vec<DetectedPattern> {
        self.classes
            .values()
            .filter(|class| class.has_private_constructor && class.has_static_instance)
            .map(|class| DetectedPattern {
                pattern_id: format!("singleton_{}", uuid::Uuid::new_v4()),
                pattern_type: PatternType::DesignPattern,
                confidence: 0.9,
                location: PatternLocation {
                    file_path: self.file_path.clone(),
                    range: class.range.clone(),
                },
                description: Some(format!(
                    "Singleton pattern detected in class '{}': private constructor with static instance",
                    class.name
                )),
            })
            .collect()
    }

    fn factory_patterns(&self) -> Vec<DetectedPattern> {
        self.classes
            .values()
            .filter(|class| class.implements_factory)
            .map(|class| DetectedPattern {
                pattern_id: format!("factory_{}", uuid::Uuid::new_v4()),
                pattern_type: PatternType::DesignPattern,
                confidence: 0.85,
                location: PatternLocation {
                    file_path: self.file_path.clone(),
                    range: class.range.clone(),
                },
                description: Some(format!(
                    "Factory pattern detected in class '{}'",
                    class.name
                )),
            })
            .collect()
    }

    fn long_method_patterns(&self, threshold: usize) -> Vec<DetectedPattern> {
        self.methods
            .values()
            .filter(|method| method.line_count > threshold)
            .map(|method| DetectedPattern {
                pattern_id: format!("long_method_{}", uuid::Uuid::new_v4()),
                pattern_type: PatternType::CodeSmell,
                confidence: 1.0,
                location: PatternLocation {
                    file_path: self.file_path.clone(),
                    range: method.range.clone(),
                },
                description: Some(format!(
                    "Method '{}' is too long ({} lines). Consider breaking it into smaller methods",
                    method.name, method.line_count
                )),
            })
            .collect()
    }

    fn sql_injection_patterns(&self) -> Vec<DetectedPattern> {
        self.sql_queries
            .iter()
            .filter(|query| query.has_string_concat)
            .map(|query| DetectedPattern {
                pattern_id: format!("sql_injection_{}", uuid::Uuid::new_v4()),
                pattern_type: PatternType::SecurityIssue,
                confidence: 0.95,
                location: PatternLocation {
                    file_path: self.file_path.clone(),
                    range: query.location.clone(),
                },
                description: Some(format!(
                    "Potential SQL injection vulnerability: {} query uses string concatenation. Use parameterized queries instead",
                    query.query_type
                )),
            })
            .collect()
    }

    fn code_smell_patterns(&self, complexity_threshold: usize) -> Vec<DetectedPattern> {
        let mut patterns = Vec::new();

        // High complexity methods
        for method in self.methods.values() {
            if method.complexity > complexity_threshold {
                patterns.push(DetectedPattern {
                    pattern_id: format!("high_complexity_{}", uuid::Uuid::new_v4()),
                    pattern_type: PatternType::CodeSmell,
                    confidence: 0.9,
                    location: PatternLocation {
                        file_path: self.file_path.clone(),
                        range: method.range.clone(),
                    },
                    description: Some(format!(
                        "Method '{}' has high cyclomatic complexity ({}). Consider refactoring",
                        method.name, method.complexity
                    )),
                });
            }
        }

        patterns
    }

    // Helper methods

    fn is_factory_class(&self, node: &AstNode) -> bool {
        // Check if class name contains "Factory" or has create methods
        if let Some(name) = &node.name {
            if name.contains("Factory") {
                return true;
            }
        }

        // Check for create/build methods in the class
        node.children.iter().any(|child| {
            matches!(child.node_type.as_str(), "method_definition" | "function_definition") &&
            child.name.as_ref().map_or(false, |n| 
                n.starts_with("create") || n.starts_with("build") || n.starts_with("make")
            )
        })
    }

    fn is_private_member(&self, node: &AstNode) -> bool {
        // Check modifiers or visibility annotations
        if let Some(properties) = &node.properties {
            if let Some(visibility) = properties.get("visibility") {
                return visibility.as_str() == Some("private");
            }
            if let Some(modifiers) = properties.get("modifiers") {
                if let Some(mods) = modifiers.as_array() {
                    return mods.iter().any(|m| m.as_str() == Some("private"));
                }
            }
        }
        false
    }

    fn is_static_field(&self, node: &AstNode) -> bool {
        // Check if field has static modifier
        if let Some(properties) = &node.properties {
            if let Some(modifiers) = properties.get("modifiers") {
                if let Some(mods) = modifiers.as_array() {
                    return mods.iter().any(|m| m.as_str() == Some("static"));
                }
            }
        }
        false
    }

    fn calculate_complexity(&self, node: &AstNode) -> usize {
        let mut complexity = 1; // Base complexity

        // Count decision points
        for child in &node.children {
            match child.node_type.as_str() {
                "if_statement" | "conditional_expression" => complexity += 1,
                "while_statement" | "for_statement" | "do_statement" => complexity += 1,
                "case_statement" | "when_clause" => complexity += 1,
                "catch_clause" | "except_clause" => complexity += 1,
                "binary_expression" => {
                    if let Some(operator) = child.properties.as_ref()
                        .and_then(|p| p.get("operator"))
                        .and_then(|o| o.as_str()) {
                        if operator == "&&" || operator == "||" {
                            complexity += 1;
                        }
                    }
                }
                _ => {}
            }
            
            // Recurse into child nodes
            complexity += self.calculate_complexity(child);
        }

        complexity
    }

    fn is_sql_query(&self, node: &AstNode) -> bool {
        // Check if this is a database query call
        if let Some(text) = &node.text {
            let lower = text.to_lowercase();
            return lower.contains("select ") || 
                   lower.contains("insert ") || 
                   lower.contains("update ") || 
                   lower.contains("delete ") ||
                   lower.contains("query(") ||
                   lower.contains("execute(");
        }
        false
    }

    fn has_string_concatenation(&self, node: &AstNode) -> bool {
        // Check for string concatenation in SQL queries
        for child in &node.children {
            match child.node_type.as_str() {
                "binary_expression" => {
                    if let Some(operator) = child.properties.as_ref()
                        .and_then(|p| p.get("operator"))
                        .and_then(|o| o.as_str()) {
                        if operator == "+" || operator == "." || operator == "||" {
                            return true;
                        }
                    }
                }
                "template_string" | "interpolated_string" => return true,
                _ => {}
            }
            
            if self.has_string_concatenation(child) {
                return true;
            }
        }
        false
    }

    fn get_sql_query_type(&self, node: &AstNode) -> String {
        if let Some(text) = &node.text {
            let lower = text.to_lowercase();
            if lower.contains("select") {
                return "SELECT".to_string();
            } else if lower.contains("insert") {
                return "INSERT".to_string();
            } else if lower.contains("update") {
                return "UPDATE".to_string();
            } else if lower.contains("delete") {
                return "DELETE".to_string();
            }
        }
        "UNKNOWN".to_string()
    }
}

/// Traverse the AST using the visitor pattern
fn traverse_ast(node: &AstNode, visitor: &mut PatternVisitor) {
    visitor.visit(node);
    
    for child in &node.children {
        traverse_ast(child, visitor);
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_singleton_detection() {
        // Test singleton pattern detection
        let detector = PatternDetector::new();
        
        // Create a mock AST for a singleton class
        let ast = AstNode {
            node_type: "class_definition".to_string(),
            name: Some("SingletonClass".to_string()),
            range: Range {
                start: Position { line: 1, column: 0, byte: 0 },
                end: Position { line: 20, column: 0, byte: 500 },
            },
            children: vec![
                // Private constructor
                AstNode {
                    node_type: "constructor_definition".to_string(),
                    name: Some("constructor".to_string()),
                    range: Range {
                        start: Position { line: 3, column: 4, byte: 50 },
                        end: Position { line: 5, column: 4, byte: 100 },
                    },
                    properties: Some(serde_json::json!({
                        "visibility": "private"
                    })),
                    children: vec![],
                    text: None,
                },
                // Static instance method
                AstNode {
                    node_type: "method_definition".to_string(),
                    name: Some("getInstance".to_string()),
                    range: Range {
                        start: Position { line: 7, column: 4, byte: 150 },
                        end: Position { line: 10, column: 4, byte: 250 },
                    },
                    properties: Some(serde_json::json!({
                        "modifiers": ["static"]
                    })),
                    children: vec![],
                    text: None,
                },
            ],
            properties: None,
            text: None,
        };

        let analysis = FileAnalysis {
            path: "test.ts".to_string(),
            language: "typescript".to_string(),
            content_hash: "test_hash".to_string(),
            size_bytes: Some(1000),
            ast,
            metrics: crate::models::FileMetrics::default(),
            chunks: None,
            symbols: None,
        };

        let patterns = detector.detect_patterns(&analysis);
        
        assert!(patterns.iter().any(|p| matches!(p.pattern_type, PatternType::DesignPattern)));
    }
}