use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ParsedAst {
    pub file_path: String,
    pub language: String,
    pub root_node: AstNode,
    pub symbols: Vec<Symbol>,
    pub imports: Vec<Import>,
    pub exports: Vec<Export>,
    pub metadata: FileMetadata,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AstNode {
    pub node_type: String,
    pub start_position: Position,
    pub end_position: Position,
    pub children: Vec<AstNode>,
    pub text: Option<String>,
    pub is_named: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Position {
    pub line: usize,
    pub column: usize,
    pub byte: usize,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Symbol {
    pub name: String,
    pub symbol_type: SymbolType,
    pub start_position: Position,
    pub end_position: Position,
    pub visibility: Visibility,
    pub documentation: Option<String>,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum SymbolType {
    Function,
    Class,
    Interface,
    Enum,
    Struct,
    Module,
    Variable,
    Constant,
    Method,
    Property,
    Type,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Visibility {
    Public,
    Private,
    Protected,
    Internal,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Import {
    pub module_name: String,
    pub imported_names: Vec<String>,
    pub is_wildcard: bool,
    pub alias: Option<String>,
    pub line_number: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Export {
    pub exported_name: String,
    pub export_type: ExportType,
    pub line_number: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExportType {
    Function,
    Class,
    Variable,
    Type,
    Default,
    Module,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileMetadata {
    pub line_count: usize,
    pub character_count: usize,
    pub has_syntax_errors: bool,
    pub complexity_score: f64,
    pub language_version: Option<String>,
}

#[derive(Debug, Clone)]
pub struct ParseError {
    pub file_path: String,
    pub error_type: ParseErrorType,
    pub message: String,
    pub position: Option<Position>,
}

#[derive(Debug, Clone)]
pub enum ParseErrorType {
    ParseError,
    SyntaxError,
    UnsupportedLanguage,
    FileTooLarge,
    Timeout,
    Other,
}

impl std::fmt::Display for ParseError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "Parse error in {}: {} - {}",
            self.file_path,
            match &self.error_type {
                ParseErrorType::ParseError => "Parse Error",
                ParseErrorType::SyntaxError => "Syntax Error",
                ParseErrorType::UnsupportedLanguage => "Unsupported Language",
                ParseErrorType::FileTooLarge => "File Too Large",
                ParseErrorType::Timeout => "Timeout",
                ParseErrorType::Other => "Other Error",
            },
            self.message
        )
    }
}

impl std::error::Error for ParseError {}