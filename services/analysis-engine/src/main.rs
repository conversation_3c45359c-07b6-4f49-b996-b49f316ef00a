use anyhow::Result;
use axum::{
    Router,
    routing::{get, post, delete},
};
use std::net::SocketAddr;
use tower_http::{
    cors::CorsLayer,
    trace::TraceLayer,
    compression::CompressionLayer,
};
use tracing::info;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

pub mod config;
mod api;
mod auth;
mod models;
mod services;
mod metrics;
mod storage;
mod git;
mod parser;

#[tokio::main]
async fn main() -> Result<()> {
    // Load environment variables from .env file
    dotenv::dotenv().ok();

    // Initialize tracing
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "analysis_engine=debug,tower_http=debug".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    info!("Starting Analysis Engine service on port 8001");

    // Build the application
    let app = create_app().await?;

    // Run the server on port 8001 as specified in PRP
    let addr = SocketAddr::from(([0, 0, 0, 0], 8001));
    info!("Analysis Engine listening on {}", addr);
    
    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}

async fn create_app() -> Result<Router> {
    // Initialize services
    let state = api::AppState::new().await?;
    
    // Create rate limiter
    let rate_limiter = api::middleware::rate_limit::create_rate_limiter();

    // Build router with all required endpoints from PRP
    let app = Router::new()
        // Core analysis endpoints
        .route("/api/v1/analysis", post(api::handlers::create_analysis))
        .route("/api/v1/analysis", get(api::handlers::list_analyses))
        .route("/api/v1/analysis/{id}", get(api::handlers::get_analysis))
        .route("/api/v1/analysis/{id}/results", get(api::handlers::get_analysis_results))
        .route("/api/v1/analysis/{id}", delete(api::handlers::cancel_analysis))
        .route("/api/v1/analysis/{id}/status", get(api::handlers::get_analysis_status))
        .route("/api/v1/analysis/{id}/download", get(api::handlers::download_analysis))
        .route("/api/v1/analysis/{id}/metrics", get(api::handlers::get_analysis_metrics))
        .route("/api/v1/analysis/{id}/patterns", get(api::handlers::get_analysis_patterns))
        
        // WebSocket endpoint for real-time progress
        .route("/ws/analysis/{id}", get(api::handlers::websocket_handler))
        
        // Health and monitoring endpoints
        .route("/health", get(api::handlers::health))
        .route("/ready", get(api::handlers::ready))
        .route("/metrics", get(api::handlers::metrics))
        
        // Languages endpoint
        .route("/api/v1/languages", get(api::handlers::supported_languages))
        
        // Add middleware layers (order matters - auth should be before rate limiting)
        .layer(axum::middleware::from_fn_with_state(
            state.clone(),
            api::middleware::auth::auth_middleware,
        ))
        .layer(axum::middleware::from_fn(api::middleware::request_id_middleware))
        .layer(axum::middleware::from_fn_with_state(
            rate_limiter,
            api::middleware::rate_limit::rate_limit_middleware,
        ))
        .layer(CorsLayer::permissive())
        .layer(CompressionLayer::new())
        .layer(TraceLayer::new_for_http())
        .with_state(state);

    Ok(app)
}