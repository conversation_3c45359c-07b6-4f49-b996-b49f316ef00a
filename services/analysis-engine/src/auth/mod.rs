use anyhow::Result;
use google_cloud_auth::{create_token_source, Config};
use std::sync::Arc;

/// Create a token source for Google Cloud authentication
/// This will automatically use the appropriate authentication method:
/// - Service account key file if GOOGLE_APPLICATION_CREDENTIALS is set
/// - Metadata server if running on GCP (Cloud Run, GCE, etc.)
/// - User credentials if running locally with gcloud auth
pub async fn create_gcp_token_source() -> Result<Arc<dyn google_cloud_auth::token::TokenSource>> {
    let config = Config {
        // Use default audience for Google Cloud APIs
        audience: None,
        // Scopes for Spanner, Storage, and Pub/Sub
        scopes: Some(&[
            "https://www.googleapis.com/auth/spanner.data",
            "https://www.googleapis.com/auth/devstorage.read_write",
            "https://www.googleapis.com/auth/pubsub",
            "https://www.googleapis.com/auth/cloud-platform",
        ]),
        sub: None,
    };
    
    let token_source = create_token_source(config).await?;
    Ok(Arc::new(token_source))
}