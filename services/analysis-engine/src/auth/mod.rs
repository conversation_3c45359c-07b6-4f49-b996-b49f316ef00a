use anyhow::Result;
use google_cloud_auth::token::DefaultTokenSourceProvider;
use google_cloud_auth::project::Config as ProjectConfig;
use google_cloud_token::{TokenSourceProvider, TokenSource};
use std::sync::Arc;

/// Create a token source for Google Cloud authentication
/// This will automatically use the appropriate authentication method:
/// - Service account key file if GOOGLE_APPLICATION_CREDENTIALS is set
/// - Metadata server if running on GCP (Cloud Run, GCE, etc.)
/// - User credentials if running locally with gcloud auth
pub async fn create_gcp_token_source() -> Result<Arc<dyn TokenSource>> {
    // Use the default token source provider which handles all auth methods
    let provider = DefaultTokenSourceProvider::new(ProjectConfig {
        scopes: Some(&[
            "https://www.googleapis.com/auth/spanner.data",
            "https://www.googleapis.com/auth/devstorage.read_write",
            "https://www.googleapis.com/auth/pubsub",
            "https://www.googleapis.com/auth/cloud-platform",
        ]),
        ..Default::default()
    }).await?;
    
    let token_source = provider.token_source();
    Ok(token_source)
}