use crate::models::{RepositoryMetrics, FileAnalysis, FileMetrics};

pub struct MetricsService;

impl MetricsService {
    pub fn new() -> Self {
        Self
    }

    pub fn calculate_repository_metrics(&self, analyses: &[FileAnalysis]) -> RepositoryMetrics {
        let total_files = analyses.len() as u32;
        let mut total_lines = 0u32;
        let mut total_complexity = 0u32;
        let mut maintainability_sum = 0.0;
        let mut tech_debt_minutes = 0u32;
        
        for analysis in analyses {
            if let Some(total) = analysis.metrics.total_lines {
                total_lines += total;
            }
            total_complexity += analysis.metrics.complexity;
            maintainability_sum += analysis.metrics.maintainability_index;
            
            // Estimate technical debt based on complexity and maintainability
            let file_debt = self.estimate_technical_debt(&analysis.metrics);
            tech_debt_minutes += file_debt;
        }
        
        let average_complexity = if total_files > 0 {
            Some(total_complexity as f64 / total_files as f64)
        } else {
            None
        };
        
        let maintainability_score = if total_files > 0 {
            Some(maintainability_sum / total_files as f64)
        } else {
            None
        };
        
        RepositoryMetrics {
            total_files,
            total_lines,
            total_complexity,
            average_complexity,
            maintainability_score,
            technical_debt_minutes: Some(tech_debt_minutes),
            test_coverage_estimate: self.estimate_test_coverage(analyses),
        }
    }
    
    fn estimate_technical_debt(&self, metrics: &FileMetrics) -> u32 {
        // Simple estimation based on complexity and maintainability
        let base_debt = if metrics.complexity > 10 { 30 } else { 0 };
        let maintainability_debt = if metrics.maintainability_index < 50.0 { 60 } else { 0 };
        base_debt + maintainability_debt
    }
    
    fn estimate_test_coverage(&self, analyses: &[FileAnalysis]) -> Option<f64> {
        // Simple heuristic: check for test files
        let test_files = analyses.iter()
            .filter(|a| a.path.contains("test") || a.path.contains("spec"))
            .count();
            
        if test_files > 0 {
            let ratio = test_files as f64 / analyses.len() as f64;
            Some((ratio * 100.0).min(90.0)) // Cap at 90% as an estimate
        } else {
            Some(0.0)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_repository_metrics_calculation() {
        let service = MetricsService::new();
        let analyses = vec![]; // Empty for now
        let metrics = service.calculate_repository_metrics(&analyses);
        
        assert_eq!(metrics.total_files, 0);
        assert_eq!(metrics.total_lines, 0);
        assert_eq!(metrics.total_complexity, 0);
    }
}