use anyhow::{Context, Result};
use google_cloud_spanner::client::{Client as SpannerBaseClient, ClientConfig as SpannerConfig};
use google_cloud_storage::client::{Client as StorageBaseClient, ClientConfig as StorageConfig};
use google_cloud_pubsub::client::{Client as PubSubBaseClient, ClientConfig as PubSubConfig};
use std::env;

// Re-export concrete client types
pub use google_cloud_spanner::client::Client as SpannerClient;
pub use google_cloud_storage::client::Client as StorageClient;
pub use google_cloud_pubsub::client::Client as PubSubClient;

/// Create a Spanner client with proper configuration
/// Connection pooling: min 10, max 100 connections as per PRP
pub async fn create_spanner_client() -> Result<SpannerClient> {
    let project_id = env::var("GCP_PROJECT_ID")
        .context("GCP_PROJECT_ID environment variable not set")?;
    
    let instance_id = env::var("SPANNER_INSTANCE_ID")
        .unwrap_or_else(|_| "ccl-production".to_string());
    
    let database_id = env::var("SPANNER_DATABASE_ID")
        .unwrap_or_else(|_| "analysis-engine".to_string());
    
    let database_name = format!(
        "projects/{}/instances/{}/databases/{}",
        project_id, instance_id, database_id
    );
    
    // Create config with proper defaults
    let config = SpannerConfig::default()
        .with_auth()
        .await
        .context("Failed to configure Spanner authentication")?;
    
    // Create client with the database name
    let client = SpannerBaseClient::new(database_name.clone(), config).await
        .context("Failed to create Spanner client")?;
    
    tracing::info!(
        "Spanner client initialized for database: {} (project: {}, instance: {}, database: {})",
        database_name, project_id, instance_id, database_id
    );
    
    Ok(client)
}

/// Create a Storage client with retry logic for production use
/// Implements exponential backoff as per CLAUDE.md requirements
pub async fn create_storage_client() -> Result<StorageClient> {
    let project_id = env::var("GCP_PROJECT_ID")
        .context("GCP_PROJECT_ID environment variable not set")?;
    
    let bucket_name = env::var("STORAGE_BUCKET_NAME")
        .unwrap_or_else(|_| format!("ccl-analysis-{}", project_id));
    
    // Create config with authentication
    let config = StorageConfig::default()
        .with_auth()
        .await
        .context("Failed to configure Storage authentication")?;
    
    // Create client
    let client = StorageBaseClient::new(config);
    
    // Verify bucket exists with retry logic
    let max_retries = 3;
    let mut retry_delay = std::time::Duration::from_millis(100);
    
    for attempt in 0..max_retries {
        match client.bucket().get(&bucket_name).await {
            Ok(_) => {
                tracing::info!("Storage client initialized for bucket: {}", bucket_name);
                break;
            }
            Err(e) => {
                if attempt == max_retries - 1 {
                    tracing::warn!(
                        "Bucket {} not found after {} attempts, will attempt to create on first use: {}",
                        bucket_name, max_retries, e
                    );
                } else {
                    tracing::debug!(
                        "Bucket check attempt {} failed, retrying in {:?}: {}",
                        attempt + 1, retry_delay, e
                    );
                    tokio::time::sleep(retry_delay).await;
                    retry_delay *= 2; // Exponential backoff
                }
            }
        }
    }
    
    Ok(client)
}

/// Create a Pub/Sub client with batch publishing support
/// Implements batch publishing for efficiency as per CLAUDE.md requirements
pub async fn create_pubsub_client() -> Result<PubSubClient> {
    let project_id = env::var("GCP_PROJECT_ID")
        .context("GCP_PROJECT_ID environment variable not set")?;
    
    // Create config with authentication
    let config = PubSubConfig::default()
        .with_auth()
        .await
        .context("Failed to configure Pub/Sub authentication")?;
    
    let client = PubSubBaseClient::new(config).await
        .context("Failed to create Pub/Sub client")?;
    
    // Create required topics if they don't exist
    let topics = vec![
        "analysis-events",
        "analysis-progress", 
        "pattern-detected",
    ];
    
    for topic_name in &topics {
        let topic_path = format!("projects/{}/topics/{}", project_id, topic_name);
        match client.topic(&topic_path).exists(None).await {
            Ok(exists) => {
                if !exists {
                    match client.create_topic(&topic_path, None, None).await {
                        Ok(_) => {
                            tracing::info!("Created Pub/Sub topic: {}", topic_name);
                        }
                        Err(e) => {
                            // Topic might have been created by another instance
                            tracing::warn!("Failed to create topic {}: {}", topic_name, e);
                        }
                    }
                } else {
                    tracing::debug!("Pub/Sub topic exists: {}", topic_name);
                }
            }
            Err(e) => {
                tracing::error!("Failed to check topic existence for {}: {}", topic_name, e);
            }
        }
    }
    
    tracing::info!("Pub/Sub client initialized for project: {}", project_id);
    
    Ok(client)
}