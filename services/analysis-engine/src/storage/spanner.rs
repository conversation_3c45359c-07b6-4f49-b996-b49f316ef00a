use google_cloud_spanner::client::{Client, Error as SpannerError};
use google_cloud_spanner::statement::Statement;
use google_cloud_gax::retry::TryAs;
use crate::models::{AnalysisResult, AnalysisStatus, ListAnalysesParams, RepositoryMetrics, DetectedPattern};
use anyhow::{Result, Context};

// Custom error type for Spanner transactions
#[derive(thiserror::Error, Debug)]
pub enum TransactionError {
    #[error("JSON serialization error: {0}")]
    Json(#[from] serde_json::Error),
    #[error(transparent)]
    Spanner(#[from] SpannerError),
}

impl TryAs<tonic::Status> for TransactionError {
    fn try_as(&self) -> Option<&tonic::Status> {
        match self {
            TransactionError::Spanner(SpannerError::GRPC(status)) => Some(status),
            _ => None,
        }
    }
}

impl From<tonic::Status> for TransactionError {
    fn from(status: tonic::Status) -> Self {
        Self::Spanner(SpannerError::GRPC(status))
    }
}


pub struct SpannerOperations {
    pub client: Client,
    #[allow(dead_code)]
    database: String,
    #[allow(dead_code)]
    project_id: String,
    #[allow(dead_code)]
    instance_id: String,
    #[allow(dead_code)]
    database_id: String,
}

impl SpannerOperations {
    pub async fn new(client: Client) -> Result<Self> {
        let project_id = std::env::var("GCP_PROJECT_ID")
            .context("GCP_PROJECT_ID environment variable is required but not set")?;
        let instance_id = std::env::var("SPANNER_INSTANCE_ID")
            .unwrap_or_else(|_| "ccl-production".to_string());
        let database_id = std::env::var("SPANNER_DATABASE_ID")
            .unwrap_or_else(|_| "analysis-engine".to_string());
        let database = format!("projects/{}/instances/{}/databases/{}", project_id, instance_id, database_id);

        Ok(Self { 
            client, 
            database,
            project_id,
            instance_id,
            database_id,
        })
    }

    pub async fn store_analysis(&self, analysis: &AnalysisResult) -> Result<()> {
        use std::sync::Arc;

        // Clone the necessary data and wrap in Arc for sharing
        let analysis_id = Arc::new(analysis.id.clone());
        let repository_url = Arc::new(analysis.repository_url.clone());
        let branch = Arc::new(analysis.branch.clone());
        let status = Arc::new(analysis.status.to_string());
        let started_at = Arc::new(analysis.started_at.to_rfc3339());
        let completed_at = Arc::new(analysis.completed_at.map(|t| t.to_rfc3339()));
        let duration_seconds = Arc::new(analysis.duration_seconds);
        let metrics = Arc::new(serde_json::to_string(&analysis.metrics)?);
        let patterns = Arc::new(serde_json::to_string(&analysis.patterns)?);
        let languages = Arc::new(serde_json::to_string(&analysis.languages)?);
        let embeddings = Arc::new(serde_json::to_string(&analysis.embeddings)?);
        let error_message = Arc::new(analysis.error_message.clone());
        let user_id = Arc::new(analysis.user_id.clone());
        let file_count = Arc::new(analysis.file_count as i64);
        let success_rate = Arc::new(analysis.success_rate);

        let (_, _) = self.client
            .read_write_transaction(|tx| {
                let analysis_id = analysis_id.clone();
                let repository_url = repository_url.clone();
                let branch = branch.clone();
                let status = status.clone();
                let started_at = started_at.clone();
                let completed_at = completed_at.clone();
                let duration_seconds = duration_seconds.clone();
                let metrics = metrics.clone();
                let patterns = patterns.clone();
                let languages = languages.clone();
                let embeddings = embeddings.clone();
                let error_message = error_message.clone();
                let user_id = user_id.clone();
                let file_count = file_count.clone();
                let success_rate = success_rate.clone();

                Box::pin(async move {
                    let mut statement = Statement::new(
                        "INSERT INTO analyses (analysis_id, repository_url, branch, status, started_at, completed_at, duration_seconds, metrics, patterns, languages, embeddings, error_message, user_id, file_count, success_rate)
                         VALUES (@analysis_id, @repository_url, @branch, @status, @started_at, @completed_at, @duration_seconds, @metrics, @patterns, @languages, @embeddings, @error_message, @user_id, @file_count, @success_rate)
                         ON DUPLICATE KEY UPDATE
                         status = @status, completed_at = @completed_at, duration_seconds = @duration_seconds, metrics = @metrics, patterns = @patterns, languages = @languages, embeddings = @embeddings, error_message = @error_message, success_rate = @success_rate"
                    );
                    statement.add_param("analysis_id", &*analysis_id);
                    statement.add_param("repository_url", &*repository_url);
                    statement.add_param("branch", &*branch);
                    statement.add_param("status", &*status);
                    statement.add_param("started_at", &*started_at);
                    if let Some(completed_at) = &*completed_at {
                        statement.add_param("completed_at", completed_at);
                    } else {
                        statement.add_param("completed_at", &Option::<String>::None);
                    }
                    if let Some(duration) = *duration_seconds {
                        statement.add_param("duration_seconds", &(duration as f64));
                    } else {
                        statement.add_param("duration_seconds", &Option::<f64>::None);
                    }
                    statement.add_param("metrics", &*metrics);
                    statement.add_param("patterns", &*patterns);
                    statement.add_param("languages", &*languages);
                    statement.add_param("embeddings", &*embeddings);
                    statement.add_param("error_message", &*error_message);
                    statement.add_param("user_id", &*user_id);
                    statement.add_param("file_count", &*file_count);
                    statement.add_param("success_rate", &*success_rate);

                    tx.update(statement).await?;
                    Ok::<(), SpannerError>(())
                })
            })
            .await
            .map_err(|e| anyhow::anyhow!("Failed to store analysis: {}", e))?;
        Ok(())
    }

    pub async fn get_analysis(&self, analysis_id: &str) -> Result<Option<AnalysisResult>> {
        let mut tx = self.client.read_only_transaction().await?;
        let mut statement = Statement::new("SELECT * FROM analyses WHERE analysis_id = @analysis_id");
        statement.add_param("analysis_id", &analysis_id.to_string());

        let mut reader = tx.query(statement).await?;
        if let Some(row) = reader.next().await? {
            let analysis: AnalysisResult = row.try_into()
                .map_err(|e| anyhow::anyhow!("Failed to convert row to AnalysisResult: {}", e))?;
            return Ok(Some(analysis));
        }
        Ok(None)
    }

    pub async fn list_analyses(&self, params: &ListAnalysesParams) -> Result<Vec<AnalysisResult>> {
        let mut tx = self.client.read_only_transaction().await?;
        let mut sql = "SELECT * FROM analyses WHERE 1=1".to_string();
        let mut statement = Statement::new("");

        // Use parameterized queries to prevent SQL injection
        if let Some(status) = &params.status {
            sql.push_str(" AND status = @status");
            statement.add_param("status", &status.to_string());
        }
        if let Some(repo_url) = &params.repository_url {
            sql.push_str(" AND repository_url = @repository_url");
            statement.add_param("repository_url", repo_url);
        }
        if let Some(created_after) = &params.created_after {
            sql.push_str(" AND started_at >= @created_after");
            statement.add_param("created_after", &created_after.to_rfc3339());
        }
        if let Some(created_before) = &params.created_before {
            sql.push_str(" AND started_at <= @created_before");
            statement.add_param("created_before", &created_before.to_rfc3339());
        }

        sql.push_str(" ORDER BY started_at DESC");

        let page = params.page.unwrap_or(1);
        let per_page = params.per_page.unwrap_or(20).min(100); // Cap at 100 for safety
        let offset = (page - 1) * per_page;

        sql.push_str(" LIMIT @limit OFFSET @offset");

        // Create a new statement with the complete SQL
        let mut statement = Statement::new(&sql);

        // Re-add all the parameters
        if let Some(status) = &params.status {
            statement.add_param("status", &status.to_string());
        }
        if let Some(repo_url) = &params.repository_url {
            statement.add_param("repository_url", repo_url);
        }
        if let Some(created_after) = &params.created_after {
            statement.add_param("created_after", &created_after.to_rfc3339());
        }
        if let Some(created_before) = &params.created_before {
            statement.add_param("created_before", &created_before.to_rfc3339());
        }
        statement.add_param("limit", &(per_page as i64));
        statement.add_param("offset", &(offset as i64));

        let mut reader = tx.query(statement).await?;
        let mut results = Vec::new();
        while let Some(row) = reader.next().await? {
            let analysis: AnalysisResult = row.try_into()
                .map_err(|e| anyhow::anyhow!("Failed to convert row to AnalysisResult: {}", e))?;
            results.push(analysis);
        }
        Ok(results)
    }

    pub async fn get_analysis_metrics(&self, analysis_id: &str) -> Result<Option<RepositoryMetrics>> {
        let mut tx = self.client.read_only_transaction().await?;
        let mut statement = Statement::new("SELECT metrics FROM analyses WHERE analysis_id = @analysis_id");
        statement.add_param("analysis_id", &analysis_id.to_string());

        let mut reader = tx.query(statement).await?;
        if let Some(row) = reader.next().await? {
            let metrics_json: String = row.column_by_name("metrics")?;
            let metrics: RepositoryMetrics = serde_json::from_str(&metrics_json)?;
            return Ok(Some(metrics));
        }
        Ok(None)
    }

    pub async fn get_analysis_patterns(&self, analysis_id: &str) -> Result<Option<Vec<DetectedPattern>>> {
        let mut tx = self.client.read_only_transaction().await?;
        let mut statement = Statement::new("SELECT patterns FROM analyses WHERE analysis_id = @analysis_id");
        statement.add_param("analysis_id", &analysis_id.to_string());

        let mut reader = tx.query(statement).await?;
        if let Some(row) = reader.next().await? {
            let patterns_json: String = row.column_by_name("patterns")?;
            let patterns: Vec<DetectedPattern> = serde_json::from_str(&patterns_json)?;
            return Ok(Some(patterns));
        }
        Ok(None)
    }

    pub async fn health_check(&self) -> Result<()> {
        // Simple health check - execute a basic query
        let mut tx = self.client.read_only_transaction().await?;
        let statement = Statement::new("SELECT 1");
        let mut reader = tx.query(statement).await?;

        // Just check if we can execute the query without errors
        if reader.next().await?.is_some() {
            Ok(())
        } else {
            Err(anyhow::anyhow!("Spanner health check failed: no response"))
        }
    }
    
    pub async fn read_only_transaction(&self) -> Result<()> {
        // For now, we'll use regular client methods which handle transactions internally
        // The google-cloud-spanner crate manages transactions differently than gcloud-spanner
        Ok(())
    }
}

// Implement TryFrom<Row> for AnalysisResult
impl TryFrom<google_cloud_spanner::row::Row> for AnalysisResult {
    type Error = anyhow::Error;

    fn try_from(row: google_cloud_spanner::row::Row) -> Result<Self, Self::Error> {
        use chrono::{DateTime, Utc};

        let id: String = row.column_by_name("analysis_id")
            .map_err(|e| anyhow::anyhow!("Failed to get analysis_id: {}", e))?;
        let repository_url: String = row.column_by_name("repository_url")
            .map_err(|e| anyhow::anyhow!("Failed to get repository_url: {}", e))?;
        let branch: String = row.column_by_name("branch")
            .map_err(|e| anyhow::anyhow!("Failed to get branch: {}", e))?;
        let status_str: String = row.column_by_name("status")
            .map_err(|e| anyhow::anyhow!("Failed to get status: {}", e))?;
        let status = match status_str.as_str() {
            "pending" => AnalysisStatus::Pending,
            "inprogress" => AnalysisStatus::InProgress,
            "completed" => AnalysisStatus::Completed,
            "failed" => AnalysisStatus::Failed,
            _ => AnalysisStatus::Failed,
        };

        let started_at_str: String = row.column_by_name("started_at")
            .map_err(|e| anyhow::anyhow!("Failed to get started_at: {}", e))?;
        let started_at = DateTime::parse_from_rfc3339(&started_at_str)
            .map_err(|e| anyhow::anyhow!("Failed to parse started_at: {}", e))?
            .with_timezone(&Utc);

        let completed_at: Option<DateTime<Utc>> = row.column_by_name::<Option<String>>("completed_at")
            .map_err(|e| anyhow::anyhow!("Failed to get completed_at: {}", e))?
            .map(|s| DateTime::parse_from_rfc3339(&s).map(|dt| dt.with_timezone(&Utc)))
            .transpose()
            .map_err(|e| anyhow::anyhow!("Failed to parse completed_at: {}", e))?;

        let duration_seconds: Option<f64> = row.column_by_name("duration_seconds")
            .map_err(|e| anyhow::anyhow!("Failed to get duration_seconds: {}", e))?;
        let duration_seconds = duration_seconds.map(|d| d as u64);

        let metrics_json: String = row.column_by_name("metrics")
            .map_err(|e| anyhow::anyhow!("Failed to get metrics: {}", e))?;
        let metrics: Option<RepositoryMetrics> = Some(serde_json::from_str(&metrics_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse metrics JSON: {}", e))?);

        let patterns_json: String = row.column_by_name("patterns")
            .map_err(|e| anyhow::anyhow!("Failed to get patterns: {}", e))?;
        let patterns: Vec<DetectedPattern> = serde_json::from_str(&patterns_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse patterns JSON: {}", e))?;

        let languages_json: String = row.column_by_name("languages")
            .map_err(|e| anyhow::anyhow!("Failed to get languages: {}", e))?;
        let languages: std::collections::HashMap<String, crate::models::LanguageStats> = serde_json::from_str(&languages_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse languages JSON: {}", e))?;

        let embeddings_json: String = row.column_by_name("embeddings")
            .map_err(|e| anyhow::anyhow!("Failed to get embeddings: {}", e))?;
        let embeddings: Option<Vec<crate::models::CodeEmbedding>> = Some(serde_json::from_str(&embeddings_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse embeddings JSON: {}", e))?);

        let error_message: Option<String> = row.column_by_name("error_message")
            .map_err(|e| anyhow::anyhow!("Failed to get error_message: {}", e))?;
        let user_id: String = row.column_by_name("user_id")
            .map_err(|e| anyhow::anyhow!("Failed to get user_id: {}", e))?;
        let file_count: i64 = row.column_by_name("file_count")
            .map_err(|e| anyhow::anyhow!("Failed to get file_count: {}", e))?;
        let success_rate: f64 = row.column_by_name("success_rate")
            .map_err(|e| anyhow::anyhow!("Failed to get success_rate: {}", e))?;

        Ok(AnalysisResult {
            id,
            repository_url,
            branch,
            status,
            started_at,
            completed_at,
            duration_seconds,
            progress: None,
            current_stage: None,
            estimated_completion: None,
            metrics,
            patterns,
            languages,
            embeddings,
            error_message,
            failed_files: Vec::new(),
            user_id,
            webhook_url: None,
            file_count: file_count as usize,
            success_rate,
            performance_metrics: None,
        })
    }
}
