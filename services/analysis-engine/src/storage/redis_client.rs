use anyhow::{Context, Result};
use redis::{AsyncCommands, Client};
use std::env;

pub struct RedisClient {
    client: Client,
}

impl RedisClient {
    pub async fn new() -> Result<Self> {
        let redis_url = env::var("REDIS_URL")
            .unwrap_or_else(|_| "redis://127.0.0.1:6379".to_string());
        
        let client = Client::open(redis_url)
            .context("Failed to create Redis client")?;
        
        // Test connection
        let mut conn = client.get_multiplexed_async_connection().await
            .context("Failed to connect to Redis")?;
        
        let _: String = redis::cmd("PING")
            .query_async(&mut conn)
            .await
            .context("Redis ping failed")?;
        
        Ok(Self { client })
    }
    
    /// Check and update rate limit for a given key
    /// Returns (allowed, remaining_requests, reset_time_seconds)
    pub async fn check_rate_limit(
        &self,
        key: &str,
        limit: i64,
        window_seconds: u64,
    ) -> Result<(bool, i64, u64)> {
        let mut conn = self.client.get_multiplexed_async_connection().await
            .context("Failed to get Redis connection")?;
        
        let rate_limit_key = format!("rate_limit:{}", key);
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        // Use sliding window algorithm with Redis sorted sets
        let window_start = now - window_seconds;
        
        // Remove old entries using ZREMRANGEBYSCORE command
        let _: i64 = redis::cmd("ZREMRANGEBYSCORE")
            .arg(&rate_limit_key)
            .arg(0)
            .arg(window_start)
            .query_async(&mut conn)
            .await?;
        
        // Count current requests in window
        let count: i64 = conn.zcard(&rate_limit_key).await?;
        
        if count < limit {
            // Add current request
            let _: i64 = conn.zadd(&rate_limit_key, now, now).await?;
            
            // Set expiry
            let _: () = conn.expire(&rate_limit_key, window_seconds as i64).await?;
            
            let remaining = limit - count - 1;
            let reset_time = now + window_seconds;
            
            Ok((true, remaining, reset_time))
        } else {
            // Get oldest entry to determine reset time
            let oldest: Vec<(String, f64)> = conn.zrange_withscores(&rate_limit_key, 0, 0).await?;
            let reset_time = if let Some((_, score)) = oldest.first() {
                (*score as u64) + window_seconds
            } else {
                now + window_seconds
            };
            
            Ok((false, 0, reset_time))
        }
    }
    
    /// Get cached analysis result
    pub async fn get_cached_analysis(&self, key: &str) -> Result<Option<String>> {
        let mut conn = self.client.get_multiplexed_async_connection().await
            .context("Failed to get Redis connection")?;
        
        let result: Option<String> = conn.get(key).await
            .context("Failed to get cached value")?;
        
        Ok(result)
    }
    
    /// Set cached analysis result with TTL
    pub async fn set_cached_analysis(
        &self,
        key: &str,
        value: &str,
        ttl_seconds: u64,
    ) -> Result<()> {
        let mut conn = self.client.get_multiplexed_async_connection().await
            .context("Failed to get Redis connection")?;
        
        let _: () = conn.set_ex(key, value, ttl_seconds).await
            .context("Failed to set cached value")?;
        
        Ok(())
    }
    
    /// Check if Redis is healthy
    #[allow(dead_code)]
    pub async fn health_check(&self) -> Result<()> {
        let mut conn = self.client.get_multiplexed_async_connection().await
            .context("Failed to get Redis connection")?;
        
        let _: String = redis::cmd("PING")
            .query_async(&mut conn)
            .await
            .context("Redis health check failed")?;
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    #[ignore] // Requires Redis to be running
    async fn test_rate_limiting() {
        let client = RedisClient::new().await.unwrap();
        
        // Test rate limiting
        let key = "test_user";
        let limit = 5;
        let window = 60; // 1 minute
        
        // First 5 requests should be allowed
        for i in 0..5 {
            let (allowed, remaining, _) = client.check_rate_limit(key, limit, window).await.unwrap();
            assert!(allowed, "Request {} should be allowed", i + 1);
            assert_eq!(remaining, limit - i - 1);
        }
        
        // 6th request should be blocked
        let (allowed, remaining, _) = client.check_rate_limit(key, limit, window).await.unwrap();
        assert!(!allowed, "6th request should be blocked");
        assert_eq!(remaining, 0);
    }
}