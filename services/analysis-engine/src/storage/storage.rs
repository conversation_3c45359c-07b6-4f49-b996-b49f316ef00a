use crate::models::AnalysisResult;
use crate::storage::gcp_clients::StorageClient;
use anyhow::{Context, Result};
use google_cloud_storage::http::objects::download::Range;
use google_cloud_storage::http::objects::get::GetObjectRequest;
use google_cloud_storage::http::objects::list::ListObjectsRequest;
use google_cloud_storage::http::objects::upload::{Media, UploadObjectRequest, UploadType};
use std::env;

pub struct StorageOperations {
    client: StorageClient,
    bucket_name: String,
}

impl StorageOperations {
    pub async fn new(client: StorageClient) -> Result<Self> {
        let project_id = env::var("GCP_PROJECT_ID")
            .context("GCP_PROJECT_ID environment variable not set")?;
        
        let bucket_name = env::var("STORAGE_BUCKET_NAME")
            .unwrap_or_else(|_| format!("ccl-analysis-{}", project_id));
        
        Ok(Self { client, bucket_name })
    }

    pub async fn store_analysis_results(&self, analysis: &AnalysisResult) -> Result<String> {
        let object_name = format!("analysis_results/{}.json", analysis.id);
        let content = serde_json::to_vec(analysis)?;

        self.client
            .upload_object(
                &UploadObjectRequest {
                    bucket: self.bucket_name.clone(),
                    ..Default::default()
                },
                content,
                &UploadType::Simple(Media {
                    name: std::borrow::Cow::Owned(object_name.clone()),
                    content_type: std::borrow::Cow::Borrowed("application/json"),
                    content_length: None,
                }),
            )
            .await
            .context("Failed to upload analysis results")?;

        Ok(format!("gs://{}/{}", self.bucket_name, object_name))
    }

    pub async fn get_analysis_results(&self, analysis_id: &str) -> Result<Option<AnalysisResult>> {
        let object_name = format!("analysis_results/{}.json", analysis_id);

        let req = GetObjectRequest {
            bucket: self.bucket_name.clone(),
            object: object_name.clone(),
            ..Default::default()
        };

        match self.client.download_object(&req, &Range::default()).await {
            Ok(content) => {
                let analysis: AnalysisResult = serde_json::from_slice(&content)
                    .context("Failed to deserialize analysis results")?;
                Ok(Some(analysis))
            }
            Err(e) => {
                if e.to_string().contains("404") {
                    Ok(None)
                } else {
                    Err(e.into())
                }
            }
        }
    }

    pub async fn list_analysis_results(&self, prefix: Option<String>) -> Result<Vec<String>> {
        let req = ListObjectsRequest {
            bucket: self.bucket_name.clone(),
            prefix: prefix.or_else(|| Some("analysis_results/".to_string())),
            ..Default::default()
        };

        let response = self.client.list_objects(&req).await?;
        
        let mut results = Vec::new();
        if let Some(items) = response.items {
            for item in items {
                if let Some(name) = item.name {
                    results.push(name);
                }
            }
        }

        Ok(results)
    }

    pub async fn delete_analysis_results(&self, analysis_id: &str) -> Result<()> {
        let object_name = format!("analysis_results/{}.json", analysis_id);

        self.client
            .delete_object(&google_cloud_storage::http::objects::delete::DeleteObjectRequest {
                bucket: self.bucket_name.clone(),
                object: object_name,
                ..Default::default()
            })
            .await
            .context("Failed to delete analysis results")?;

        Ok(())
    }
}