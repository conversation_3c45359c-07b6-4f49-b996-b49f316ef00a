use axum::{
    extract::State,
    http::Status<PERSON><PERSON>,
    J<PERSON>,
    response::IntoResponse,
};
use serde::{Deserialize, Serialize};
use crate::api::AppState;

#[derive(Debug, Serialize, Deserialize)]
pub struct HealthResponse {
    pub status: String,
    pub service: String,
    pub version: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ReadyResponse {
    pub ready: bool,
    pub service: String,
    pub checks: HealthChecks,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct HealthChecks {
    pub spanner: bool,
    pub storage: bool,
    pub pubsub: bool,
}

// GET /health - Basic health check
pub async fn health() -> impl IntoResponse {
    Json(HealthResponse {
        status: "healthy".to_string(),
        service: "analysis-engine".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
    })
}

// GET /ready - Readiness check with dependency validation
pub async fn ready(State(state): State<AppState>) -> impl IntoResponse {
    // Check Spanner connectivity
    let spanner_ok = check_spanner(&state).await;
    
    // Check Storage connectivity
    let storage_ok = check_storage(&state).await;
    
    // Check Pub/Sub connectivity
    let pubsub_ok = check_pubsub(&state).await;

    let all_ready = spanner_ok && storage_ok && pubsub_ok;

    let response = ReadyResponse {
        ready: all_ready,
        service: "analysis-engine".to_string(),
        checks: HealthChecks {
            spanner: spanner_ok,
            storage: storage_ok,
            pubsub: pubsub_ok,
        },
    };

    if all_ready {
        (StatusCode::OK, Json(response))
    } else {
        (StatusCode::SERVICE_UNAVAILABLE, Json(response))
    }
}

// GET /metrics - Prometheus metrics endpoint
pub async fn metrics() -> impl IntoResponse {
    use prometheus::{Encoder, TextEncoder};
    
    let encoder = TextEncoder::new();
    let metric_families = prometheus::gather();
    let mut buffer = vec![];
    
    match encoder.encode(&metric_families, &mut buffer) {
        Ok(_) => (
            StatusCode::OK,
            [(axum::http::header::CONTENT_TYPE, "text/plain; version=0.0.4")],
            buffer,
        ),
        Err(_) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            [(axum::http::header::CONTENT_TYPE, "text/plain")],
            b"Failed to encode metrics".to_vec(),
        ),
    }
}

async fn check_spanner(state: &AppState) -> bool {
    // Simple health check - try to execute a basic query
    match &state.spanner {
        Some(spanner) => match spanner.health_check().await {
            Ok(_) => true,
            Err(e) => {
                tracing::warn!("Spanner health check failed: {}", e);
                false
            }
        },
        None => {
            tracing::warn!("Spanner not configured - running in memory mode");
            false
        }
    }
}

async fn check_storage(state: &AppState) -> bool {
    // Simple health check - try to check bucket access
    match state.storage.health_check().await {
        Ok(_) => true,
        Err(e) => {
            tracing::warn!("Storage health check failed: {}", e);
            false
        }
    }
}

async fn check_pubsub(state: &AppState) -> bool {
    // Simple health check - try to check topic access
    match state.pubsub.health_check().await {
        Ok(_) => true,
        Err(e) => {
            tracing::warn!("PubSub health check failed: {}", e);
            false
        }
    }
}