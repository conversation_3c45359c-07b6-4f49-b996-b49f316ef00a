use axum::{
    extract::{Request, State},
    middleware::Next,
    response::Response,
};
use jsonwebtoken::{decode, encode, Al<PERSON><PERSON>m, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Valida<PERSON>};
use serde::{Deserialize, Serialize};
use crate::api::AppState;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};

#[derive(Debug, Serialize)]
pub struct AuthError {
    pub error: String,
    pub error_code: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct Claims {
    sub: String,  // Subject (user ID)
    exp: u64,     // Expiration time
    iat: u64,     // Issued at
    aud: String,  // Audience
}

// Simplified auth middleware for deployment
pub async fn auth_middleware(
    State(_state): State<Arc<AppState>>,
    mut request: Request,
    next: Next,
) -> Response {
    // For now, allow all requests for testing
    // TODO: Implement proper authentication once Spanner API is understood
    
    // Add a default user ID to the request
    request.extensions_mut().insert("test-user".to_string());
    
    next.run(request).await
}

// JWT helper functions
pub fn create_jwt(user_id: &str, secret: &str) -> Result<String, String> {
    let expiration = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .map_err(|_| "Failed to get system time")?
        .as_secs() + 3600; // 1 hour

    let claims = Claims {
        sub: user_id.to_string(),
        exp: expiration,
        iat: SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|_| "Failed to get system time")?
            .as_secs(),
        aud: "analysis-engine".to_string(),
    };

    encode(
        &Header::default(),
        &claims,
        &EncodingKey::from_secret(secret.as_bytes()),
    )
    .map_err(|e| format!("Failed to create JWT: {}", e))
}

pub fn verify_jwt(token: &str, secret: &str) -> Result<String, String> {
    let mut validation = Validation::new(Algorithm::HS256);
    validation.set_audience(&["analysis-engine"]);

    let token_data = decode::<Claims>(
        token,
        &DecodingKey::from_secret(secret.as_bytes()),
        &validation,
    )
    .map_err(|e| format!("Invalid JWT: {}", e))?;

    Ok(token_data.claims.sub)
}