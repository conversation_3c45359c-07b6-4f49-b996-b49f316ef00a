use axum::{
    body::Body,
    http::{Request, HeaderValue},
    middleware::Next,
    response::Response,
};
use uuid::Uuid;

pub async fn request_id_middleware(
    mut req: Request<Body>,
    next: Next,
) -> Response {
    // Check if request ID already exists
    let request_id = if let Some(existing_id) = req.headers().get("x-request-id") {
        existing_id.clone()
    } else {
        // Generate new request ID
        let new_id = Uuid::new_v4().to_string();
        HeaderValue::from_str(&new_id).unwrap_or_else(|_| HeaderValue::from_static("unknown"))
    };

    // Add request ID to extensions for use in handlers
    req.extensions_mut().insert(request_id.clone());

    // Add request ID to request headers
    req.headers_mut().insert("x-request-id", request_id.clone());

    // Call the next middleware/handler
    let mut response = next.run(req).await;

    // Add request ID to response headers
    response.headers_mut().insert("x-request-id", request_id);

    response
}
