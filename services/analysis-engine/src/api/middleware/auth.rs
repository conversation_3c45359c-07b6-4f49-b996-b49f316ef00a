use axum::{
    extract::{Request, State},
    http::{HeaderMap, StatusCode, header},
    middleware::Next,
    response::{IntoResponse, Response},
};
use jsonwebtoken::{decode, decode_header, Algorithm, DecodingKey, Validation};
use serde::{Deserialize, Serialize};
use sha2::{Sha256, Digest};
use crate::api::AppState;
use crate::storage::SpannerOperations;
use google_cloud_spanner::statement::Statement;
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use dashmap::DashMap;

#[derive(Debug, Serialize)]
pub struct AuthError {
    pub error: String,
    pub error_code: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct Claims {
    sub: String,  // Subject (user ID)
    exp: u64,     // Expiration time
    iat: u64,     // Issued at
    aud: String,  // Audience
}

#[derive(Debug)]
#[allow(dead_code)]
struct ApiKeyInfo {
    user_id: String,
    rate_limit: i64,
    expires_at: Option<SystemTime>,
}

#[derive(Debug)]
struct UserInfo {
    rate_limit: i64,
    #[allow(dead_code)]
    subscription_tier: String,
    #[allow(dead_code)]
    is_active: bool,
}

pub async fn auth_middleware(
    State(state): State<AppState>,
    headers: HeaderMap,
    mut request: Request,
    next: Next,
) -> Result<Response, impl IntoResponse> {
    // Allow health check endpoints without auth
    let path = request.uri().path();
    if path == "/health" || path == "/ready" || path == "/metrics" {
        return Ok(next.run(request).await);
    }

    // Check for API key in headers
    let api_key = headers
        .get("x-api-key")
        .and_then(|v| v.to_str().ok());

    // Check for Bearer token
    let auth_header = headers
        .get("authorization")
        .and_then(|v| v.to_str().ok());

    let auth_result = match (api_key, auth_header) {
        (Some(key), _) => match &state.spanner {
            Some(spanner) => validate_api_key(key, spanner).await,
            None => {
                tracing::warn!("API key validation skipped - database unavailable");
                // In memory mode, use a default user with limited rate limit
                Ok(("memory-user".to_string(), 100))
            }
        },
        (_, Some(header)) if header.starts_with("Bearer ") => {
            let token = &header[7..];
            validate_jwt_token(token, &state).await
        }
        _ => Err("No authentication provided".to_string()),
    };

    match auth_result {
        Ok((user_id, rate_limit)) => {
            // Check rate limiting
            let rate_limit_result = check_rate_limit(&state, &user_id, rate_limit).await;
            
            match rate_limit_result {
                Ok((allowed, remaining, reset_time)) => {
                    if !allowed {
                        let error = AuthError {
                            error: "Rate limit exceeded".to_string(),
                            error_code: "RATE_LIMIT_EXCEEDED".to_string(),
                        };
                        
                        let mut response = (
                            StatusCode::TOO_MANY_REQUESTS,
                            [(header::CONTENT_TYPE, "application/json")],
                            serde_json::to_string(&error).unwrap(),
                        ).into_response();
                        
                        // Add rate limit headers
                        let headers = response.headers_mut();
                        headers.insert("X-RateLimit-Limit", rate_limit.to_string().parse().unwrap());
                        headers.insert("X-RateLimit-Remaining", remaining.to_string().parse().unwrap());
                        headers.insert("X-RateLimit-Reset", reset_time.to_string().parse().unwrap());
                        
                        return Ok(response);
                    }
                    
                    // Add user ID to request extensions for downstream handlers
                    request.extensions_mut().insert(user_id.clone());
                    
                    // Log successful authentication
                    tracing::debug!("Authentication successful for user: {}", user_id);
                    
                    // Add rate limit headers to successful response
                    let mut response = next.run(request).await;
                    let headers = response.headers_mut();
                    headers.insert("X-RateLimit-Limit", rate_limit.to_string().parse().unwrap());
                    headers.insert("X-RateLimit-Remaining", remaining.to_string().parse().unwrap());
                    headers.insert("X-RateLimit-Reset", reset_time.to_string().parse().unwrap());
                    
                    Ok(response)
                }
                Err(e) => {
                    tracing::error!("Rate limiting check failed: {}", e);
                    // If rate limiting fails, allow the request but log the error
                    request.extensions_mut().insert(user_id);
                    Ok(next.run(request).await)
                }
            }
        }
        Err(error) => {
            // Log authentication failure
            tracing::warn!("Authentication failed: {}", error);
            
            Err((
                StatusCode::UNAUTHORIZED,
                axum::Json(AuthError {
                    error,
                    error_code: "UNAUTHORIZED".to_string(),
                }),
            ))
        }
    }
}

async fn validate_api_key(key: &str, spanner: &Arc<SpannerOperations>) -> Result<(String, i64), String> {
    // Hash the API key for secure storage comparison
    let mut hasher = Sha256::new();
    hasher.update(key.as_bytes());
    let key_hash = format!("{:x}", hasher.finalize());

    // Query Spanner for the API key
    let mut statement = Statement::new(
        "SELECT user_id, rate_limit, expires_at, is_active 
         FROM api_keys 
         WHERE key_hash = @key_hash"
    );
    statement.add_param("key_hash", &key_hash);

    let rows = spanner.client.single_use_read_only()
        .query_with_param(statement.sql(), statement.params())
        .await
        .map_err(|e| format!("Failed to query API key: {}", e))?;

    if let Some(row) = rows.get(0) {
        
        // Check if key is active
        let is_active: bool = row.column_by_name("is_active")
            .map_err(|e| format!("Failed to read is_active: {}", e))?;
        
        if !is_active {
            return Err("API key is inactive".to_string());
        }

        // Check expiration
        let expires_at: Option<String> = row.column_by_name("expires_at")
            .ok();
        
        if let Some(expires_str) = expires_at {
            let expires_time = chrono::DateTime::parse_from_rfc3339(&expires_str)
                .map_err(|e| format!("Invalid expiration time format: {}", e))?;
            
            if expires_time < chrono::Utc::now() {
                return Err("API key has expired".to_string());
            }
        }

        // Get user ID
        let user_id: String = row.column_by_name("user_id")
            .map_err(|e| format!("Failed to read user_id: {}", e))?;

        // Get rate limit
        let rate_limit: i64 = row.column_by_name("rate_limit")
            .map_err(|e| format!("Failed to read rate_limit: {}", e))?;

        // Log successful API key validation
        tracing::debug!("API key validated for user: {}, rate_limit: {}", user_id, rate_limit);

        Ok((user_id, rate_limit))
    } else {
        Err("Invalid API key".to_string())
    }
}

async fn validate_user_in_database(user_id: &str, state: &AppState) -> Result<UserInfo, String> {
    // Query Spanner for user information
    let mut statement = Statement::new(
        "SELECT subscription_tier, rate_limit, is_active, created_at 
         FROM users 
         WHERE user_id = @user_id"
    );
    statement.add_param("user_id", &user_id);

    let rows = match &state.spanner {
        Some(spanner) => spanner.client.single_use_read_only()
            .query_with_param(statement.sql(), statement.params())
            .await
            .map_err(|e| format!("Failed to query user: {}", e))?,
        None => {
            // In memory mode, return default user info
            return Ok(UserInfo {
                rate_limit: 100,
                subscription_tier: "memory".to_string(),
                is_active: true,
            });
        }
    };

    if let Some(row) = rows.get(0) {
        
        // Check if user is active
        let is_active: bool = row.column_by_name("is_active")
            .map_err(|e| format!("Failed to read is_active: {}", e))?;
        
        if !is_active {
            return Err("User account is inactive".to_string());
        }

        // Get subscription tier and rate limit
        let subscription_tier: String = row.column_by_name("subscription_tier")
            .unwrap_or_else(|_| "free".to_string());
        
        // Get rate limit based on subscription tier or from database
        let rate_limit: i64 = row.column_by_name("rate_limit")
            .ok()
            .unwrap_or_else(|| {
                // Default rate limits by tier
                match subscription_tier.as_str() {
                    "free" => 1_000,
                    "pro" => 10_000,
                    "team" => 100_000,
                    "enterprise" => i64::MAX,
                    _ => 1_000,
                }
            });

        Ok(UserInfo {
            rate_limit,
            subscription_tier,
            is_active,
        })
    } else {
        Err("User not found".to_string())
    }
}

async fn validate_jwt_token(token: &str, _state: &AppState) -> Result<(String, i64), String> {
    // Decode header to determine key ID if using key rotation
    let _header = decode_header(token)
        .map_err(|e| format!("Invalid JWT header: {}", e))?;

    // Get the public key for verification
    // In production, this would be fetched from a key management service
    // For now, we'll use an environment variable
    let jwt_secret = std::env::var("JWT_SECRET")
        .map_err(|_| "JWT secret not configured".to_string())?;

    let mut validation = Validation::new(Algorithm::HS256);
    validation.set_audience(&["ccl-analysis-engine"]);
    validation.validate_exp = true;
    validation.validate_nbf = true;

    let token_data = decode::<Claims>(
        token,
        &DecodingKey::from_secret(jwt_secret.as_bytes()),
        &validation,
    )
    .map_err(|e| match e.kind() {
        jsonwebtoken::errors::ErrorKind::ExpiredSignature => "Token has expired".to_string(),
        jsonwebtoken::errors::ErrorKind::InvalidToken => "Invalid token format".to_string(),
        jsonwebtoken::errors::ErrorKind::InvalidAudience => "Invalid token audience".to_string(),
        _ => format!("Token validation failed: {}", e),
    })?;

    // Additional validation: check if token was issued too far in the past
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();
    
    let max_token_age = <Duration as DurationExt>::from_days(7).as_secs();
    if now - token_data.claims.iat > max_token_age {
        return Err("Token is too old".to_string());
    }

    // Check if user exists and is active in the database
    let user_id = token_data.claims.sub.clone();
    let user_info = validate_user_in_database(&user_id, _state).await?;
    
    tracing::debug!("JWT validated for user: {} with rate limit: {}", user_id, user_info.rate_limit);
    
    Ok((user_id, user_info.rate_limit))
}

// In-memory rate limiting fallback
lazy_static::lazy_static! {
    static ref RATE_LIMIT_CACHE: DashMap<String, (i64, SystemTime)> = DashMap::new();
}

async fn check_rate_limit(
    state: &AppState,
    user_id: &str,
    limit: i64,
) -> Result<(bool, i64, u64), anyhow::Error> {
    const WINDOW_SECONDS: u64 = 3600; // 1 hour window
    
    // Try Redis first
    if let Some(redis) = &state.redis {
        match redis.check_rate_limit(user_id, limit, WINDOW_SECONDS).await {
            Ok(result) => return Ok(result),
            Err(e) => {
                tracing::warn!("Redis rate limiting failed, falling back to in-memory: {}", e);
            }
        }
    }
    
    // Fallback to in-memory rate limiting
    let now = SystemTime::now();
    
    let mut entry = RATE_LIMIT_CACHE.entry(user_id.to_string()).or_insert((0, now));
    let (count, last_reset) = entry.value_mut();
    
    // Check if we need to reset the window
    let elapsed = now.duration_since(*last_reset).unwrap().as_secs();
    if elapsed >= WINDOW_SECONDS {
        *count = 0;
        *last_reset = now;
    }
    
    if *count < limit {
        *count += 1;
        let remaining = limit - *count;
        let reset_time = last_reset.duration_since(UNIX_EPOCH).unwrap().as_secs() + WINDOW_SECONDS;
        Ok((true, remaining, reset_time))
    } else {
        let reset_time = last_reset.duration_since(UNIX_EPOCH).unwrap().as_secs() + WINDOW_SECONDS;
        Ok((false, 0, reset_time))
    }
}

// Helper trait to add Duration::from_days
trait DurationExt {
    fn from_days(days: u64) -> Duration;
}

impl DurationExt for Duration {
    fn from_days(days: u64) -> Duration {
        Duration::from_secs(days * 24 * 60 * 60)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_api_key_hashing() {
        let key = "test-api-key-12345";
        let mut hasher = Sha256::new();
        hasher.update(key.as_bytes());
        let hash = format!("{:x}", hasher.finalize());
        
        // Verify hash is consistent
        let mut hasher2 = Sha256::new();
        hasher2.update(key.as_bytes());
        let hash2 = format!("{:x}", hasher2.finalize());
        
        assert_eq!(hash, hash2);
        assert_eq!(hash.len(), 64); // SHA256 produces 64 hex characters
    }

    #[tokio::test]
    async fn test_jwt_validation() {
        use jsonwebtoken::{encode, EncodingKey, Header};
        
        std::env::set_var("JWT_SECRET", "test-secret");
        
        let claims = Claims {
            sub: "user123".to_string(),
            exp: (SystemTime::now() + Duration::from_secs(3600))
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            iat: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            aud: "ccl-analysis-engine".to_string(),
        };
        
        let token = encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret("test-secret".as_bytes()),
        )
        .unwrap();
        
        // Mock state would be needed for full test
        // This just verifies the JWT encoding/decoding logic
        assert!(!token.is_empty());
    }
}
