use axum::{
    extract::{Request, State, ConnectInfo},
    http::{StatusCode},
    middleware::Next,
    response::{IntoResponse, Response},
};
use governor::{Quota, RateLimiter};
use governor::state::keyed::DashMapStateStore;
use governor::clock::{Clock, QuantaClock};
use nonzero_ext::*;
use serde::Serialize;
use std::sync::Arc;
use std::net::{IpAddr, SocketAddr};



#[derive(Debug, Serialize)]
pub struct RateLimitError {
    pub error: String,
    pub error_code: String,
    pub retry_after_seconds: u64,
}

pub type SharedRateLimiter = Arc<RateLimiter<IpAddr, DashMapStateStore<IpAddr>, QuantaClock>>;

pub fn create_rate_limiter() -> SharedRateLimiter {
    // Default: 1000 requests per hour for free tier
    let quota = Quota::per_hour(nonzero!(1000u32));
    Arc::new(RateLimiter::keyed(quota))
}

pub async fn rate_limit_middleware(
    State(limiter): State<SharedRateLimiter>,
    ConnectInfo(addr): ConnectInfo<SocketAddr>,
    request: Request,
    next: Next,
) -> Result<Response, impl IntoResponse> {
    // Extract client IP
    let client_ip = addr.ip();

    // Check rate limit
    match limiter.check_key(&client_ip) {
        Ok(_) => Ok(next.run(request).await),
        Err(negative) => {
            let wait_time = negative.wait_time_from(QuantaClock::default().now());
            let retry_after = wait_time.as_secs();

            Err((
                StatusCode::TOO_MANY_REQUESTS,
                axum::Json(RateLimitError {
                    error: "Rate limit exceeded".to_string(),
                    error_code: "RATE_LIMIT_EXCEEDED".to_string(),
                    retry_after_seconds: retry_after,
                }),
            ))
        }
    }
}
