//! This module defines the data structures that match the public API contracts,
//! specifically the `ast-output-v1.json` schema. These are used for serialization
//! and are separate from the internal database models.

use serde::Serialize;
use std::collections::HashMap;
use chrono::{DateTime, Utc};

#[derive(Serialize, Debug)]
pub struct AnalysisOutput {
    pub repository: Repository,
    pub analysis: Analysis,
    pub metadata: Metadata,
}

#[derive(Serialize, Debug)]
pub struct Repository {
    pub id: String,
    pub url: String,
    pub commit: String,
    pub branch: String,
    pub size_bytes: u64,
    pub clone_time_ms: u64,
}

#[derive(Serialize, Debug)]
pub struct Analysis {
    pub files: Vec<FileAnalysis>,
    pub metrics: RepositoryMetrics,
    pub languages: LanguageBreakdown,
    pub embeddings: Vec<CodeEmbedding>,
    pub patterns: Vec<PreDetectedPattern>,
}

#[derive(Serialize, Debug)]
pub struct Metadata {
    pub analysis_id: String,
    pub version: String,
    pub timestamp: DateTime<Utc>,
    pub duration_ms: u64,
    pub performance: PerformanceMetrics,
    pub warnings: Vec<Warning>,
}

#[derive(Serialize, Debug)]
pub struct FileAnalysis {
    pub path: String,
    pub language: String,
    pub content_hash: String,
    pub size_bytes: u64,
    pub ast: ASTNode,
    pub metrics: FileMetrics,
    pub chunks: Vec<CodeChunk>,
    pub symbols: Vec<Symbol>,
}

#[derive(Serialize, Debug)]
pub struct ASTNode {
    #[serde(rename = "type")]
    pub node_type: String,
    pub name: Option<String>,
    pub range: Range,
    pub children: Vec<ASTNode>,
    pub properties: Option<HashMap<String, serde_json::Value>>,
    pub text: Option<String>,
}

#[derive(Serialize, Debug)]
pub struct Range {
    pub start: Position,
    pub end: Position,
}

#[derive(Serialize, Debug)]
pub struct Position {
    pub line: u32,
    pub column: u32,
    pub byte: u32,
}

#[derive(Serialize, Debug)]
pub struct FileMetrics {
    pub lines_of_code: u32,
    pub total_lines: u32,
    pub complexity: u32,
    pub maintainability_index: f64,
    pub function_count: u32,
    pub class_count: u32,
    pub comment_ratio: f64,
}

#[derive(Serialize, Debug, Default)]
pub struct RepositoryMetrics {
    pub total_files: u32,
    pub total_lines: u32,
    pub total_complexity: u32,
    pub average_complexity: f64,
    pub maintainability_score: f64,
    pub technical_debt_minutes: u32,
    pub test_coverage_estimate: Option<f64>,
}

#[derive(Serialize, Debug)]
pub struct LanguageBreakdown {
    pub primary_language: String,
    pub languages: HashMap<String, LanguageStats>,
}

#[derive(Serialize, Debug)]
pub struct LanguageStats {
    pub lines: u32,
    pub files: u32,
    pub percentage: f64,
    pub bytes: u32,
}

#[derive(Serialize, Debug)]
pub struct CodeChunk {
    pub chunk_id: String,
    pub content: String,
    pub range: Range,
    #[serde(rename = "type")]
    pub chunk_type: String,
    pub language: String,
    pub context: Option<ChunkContext>,
}

#[derive(Serialize, Debug)]
pub struct ChunkContext {
    pub parent_symbol: Option<String>,
    pub imports: Vec<String>,
    pub dependencies: Vec<String>,
}

#[derive(Serialize, Debug)]
pub struct CodeEmbedding {
    pub chunk_id: String,
    pub vector: Vec<f32>,
    pub model: String,
    pub metadata: Option<EmbeddingMetadata>,
}

#[derive(Serialize, Debug)]
pub struct EmbeddingMetadata {
    pub tokens_used: u32,
    pub created_at: DateTime<Utc>,
}

#[derive(Serialize, Debug)]
pub struct Symbol {
    pub name: String,
    #[serde(rename = "type")]
    pub symbol_type: String,
    pub range: Range,
    pub visibility: Option<String>,
    pub signature: Option<String>,
    pub documentation: Option<String>,
}

#[derive(Serialize, Debug)]
pub struct PreDetectedPattern {
    pub pattern_id: String,
    pub pattern_type: String,
    pub confidence: f64,
    pub location: PatternLocation,
    pub description: Option<String>,
}

#[derive(Serialize, Debug)]
pub struct PatternLocation {
    pub file_path: String,
    pub range: Range,
}

#[derive(Serialize, Debug, Default)]
pub struct PerformanceMetrics {
    pub parsing_time_ms: u64,
    pub embedding_time_ms: u64,
    pub total_memory_mb: f64,
    pub files_per_second: Option<f64>,
    pub cache_hit_ratio: Option<f64>,
}

#[derive(Serialize, Debug)]
pub struct Warning {
    pub code: String,
    pub message: String,
    pub file_path: Option<String>,
}

use crate::models;

impl From<models::AnalysisResult> for AnalysisOutput {
    fn from(result: models::AnalysisResult) -> Self {
        let mut files = Vec::new();

        // Add successful analyses if available
        if let Some(successful_analyses) = &result.successful_analyses {
            for analysis in successful_analyses {
                files.push(FileAnalysis {
                    path: analysis.path.clone(),
                    language: analysis.language.clone(),
                    content_hash: analysis.content_hash.clone(),
                    size_bytes: analysis.size_bytes.unwrap_or(0),
                    ast: convert_ast_node(&analysis.ast),
                    metrics: convert_file_metrics(&analysis.metrics),
                    chunks: analysis.chunks.as_ref().map(|chunks|
                        chunks.iter().map(|chunk| convert_code_chunk(chunk)).collect()
                    ).unwrap_or_default(),
                    symbols: analysis.symbols.as_ref().map(|symbols|
                        symbols.iter().map(|symbol| convert_symbol(symbol)).collect()
                    ).unwrap_or_default(),
                });
            }
        }

        // Add failed files as error entries
        for failed_file in &result.failed_files {
            files.push(FileAnalysis {
                path: failed_file.file_path.clone(),
                language: "unknown".to_string(),
                content_hash: "".to_string(),
                size_bytes: 0,
                ast: ASTNode {
                    node_type: "error".to_string(),
                    name: None,
                    range: Range {
                        start: Position { line: 0, column: 0, byte: 0 },
                        end: Position { line: 0, column: 0, byte: 0 },
                    },
                    children: vec![],
                    properties: None,
                    text: Some(format!("Error: {}", failed_file.error_message)),
                },
                metrics: FileMetrics {
                    lines_of_code: 0,
                    total_lines: 0,
                    complexity: 0,
                    maintainability_index: 0.0,
                    function_count: 0,
                    class_count: 0,
                    comment_ratio: 0.0,
                },
                chunks: vec![],
                symbols: vec![],
            });
        }

        // Convert language stats
        let languages: HashMap<String, LanguageStats> = result.languages.into_iter()
            .map(|(lang, stats)| (lang.clone(), LanguageStats {
                lines: stats.lines,
                files: stats.files,
                percentage: stats.percentage,
                bytes: stats.bytes,
            }))
            .collect();

        let primary_language = languages.keys()
            .max_by_key(|lang| languages.get(*lang).map(|s| s.lines).unwrap_or(0))
            .cloned()
            .unwrap_or_else(|| "unknown".to_string());

        // Convert patterns
        let patterns: Vec<PreDetectedPattern> = result.patterns.into_iter()
            .map(|pattern| PreDetectedPattern {
                pattern_id: pattern.pattern_id,
                pattern_type: format!("{:?}", pattern.pattern_type),
                confidence: pattern.confidence,
                location: PatternLocation {
                    file_path: pattern.location.file_path,
                    range: Range {
                        start: Position {
                            line: pattern.location.range.start.line,
                            column: pattern.location.range.start.column,
                            byte: pattern.location.range.start.byte,
                        },
                        end: Position {
                            line: pattern.location.range.end.line,
                            column: pattern.location.range.end.column,
                            byte: pattern.location.range.end.byte,
                        },
                    },
                },
                description: pattern.description,
            })
            .collect();

        // Convert embeddings
        let embeddings: Vec<CodeEmbedding> = result.embeddings
            .map(|emb| emb.embeddings.into_iter()
                .map(|(chunk_id, vector)| CodeEmbedding {
                    chunk_id,
                    vector,
                    model: emb.model_version.clone(),
                    metadata: Some(EmbeddingMetadata {
                        tokens_used: 0, // Not available in current model
                        created_at: emb.generated_at,
                    }),
                })
                .collect())
            .unwrap_or_default();

        AnalysisOutput {
            repository: Repository {
                id: result.id.clone(),
                url: result.repository_url,
                commit: "".to_string(), // TODO: Add commit hash to AnalysisResult
                branch: result.branch,
                size_bytes: 0, // TODO: Add repository size to AnalysisResult
                clone_time_ms: 0, // TODO: Add clone time to AnalysisResult
            },
            analysis: Analysis {
                files,
                metrics: result.metrics.map(|m| m.into()).unwrap_or_default(),
                languages: LanguageBreakdown {
                    primary_language,
                    languages,
                },
                embeddings,
                patterns,
            },
            metadata: Metadata {
                analysis_id: result.id,
                version: env!("CARGO_PKG_VERSION").to_string(),
                timestamp: result.completed_at.unwrap_or_else(Utc::now),
                duration_ms: result.duration_seconds.unwrap_or(0) * 1000,
                performance: result.performance_metrics.map(|p| p.into()).unwrap_or_default(),
                warnings: vec![], // TODO: Add warnings to AnalysisResult if needed
            },
        }
    }
}

impl From<models::RepositoryMetrics> for RepositoryMetrics {
    fn from(metrics: models::RepositoryMetrics) -> Self {
        Self {
            total_files: metrics.total_files,
            total_lines: metrics.total_lines,
            total_complexity: metrics.total_complexity,
            average_complexity: metrics.average_complexity.unwrap_or(0.0),
            maintainability_score: metrics.maintainability_score.unwrap_or(0.0),
            technical_debt_minutes: metrics.technical_debt_minutes.unwrap_or(0),
            test_coverage_estimate: metrics.test_coverage_estimate,
        }
    }
}

impl From<models::PerformanceMetrics> for PerformanceMetrics {
    fn from(metrics: models::PerformanceMetrics) -> Self {
        Self {
            parsing_time_ms: metrics.ast_parsing_ms,
            embedding_time_ms: metrics.embedding_generation_ms,
            total_memory_mb: metrics.memory_peak_mb as f64,
            files_per_second: None,
            cache_hit_ratio: None,
        }
    }
}

// Helper conversion functions
fn convert_ast_node(node: &models::AstNode) -> ASTNode {
    ASTNode {
        node_type: node.node_type.clone(),
        name: node.name.clone(),
        range: Range {
            start: Position {
                line: node.range.start.line,
                column: node.range.start.column,
                byte: node.range.start.byte,
            },
            end: Position {
                line: node.range.end.line,
                column: node.range.end.column,
                byte: node.range.end.byte,
            },
        },
        children: node.children.iter().map(convert_ast_node).collect(),
        properties: node.properties.clone(),
        text: node.text.clone(),
    }
}

fn convert_file_metrics(metrics: &models::FileMetrics) -> FileMetrics {
    FileMetrics {
        lines_of_code: metrics.lines_of_code,
        total_lines: metrics.total_lines.unwrap_or(0),
        complexity: metrics.complexity.unwrap_or(0),
        maintainability_index: metrics.maintainability_index.unwrap_or(0.0),
        function_count: metrics.function_count.unwrap_or(0),
        class_count: metrics.class_count.unwrap_or(0),
        comment_ratio: metrics.comment_ratio.unwrap_or(0.0),
    }
}

fn convert_code_chunk(chunk: &models::CodeChunk) -> CodeChunk {
    CodeChunk {
        chunk_id: chunk.chunk_id.clone(),
        content: chunk.content.clone(),
        range: Range {
            start: Position {
                line: chunk.range.start.line,
                column: chunk.range.start.column,
                byte: chunk.range.start.byte,
            },
            end: Position {
                line: chunk.range.end.line,
                column: chunk.range.end.column,
                byte: chunk.range.end.byte,
            },
        },
        chunk_type: format!("{:?}", chunk.chunk_type),
        language: chunk.language.clone(),
        context: chunk.context.as_ref().map(|ctx| ChunkContext {
            parent_symbol: ctx.parent_symbol.clone(),
            imports: ctx.imports.clone(),
            dependencies: ctx.dependencies.clone(),
        }),
    }
}

fn convert_symbol(symbol: &models::Symbol) -> Symbol {
    Symbol {
        name: symbol.name.clone(),
        symbol_type: format!("{:?}", symbol.symbol_type),
        range: Range {
            start: Position {
                line: symbol.range.start.line,
                column: symbol.range.start.column,
                byte: symbol.range.start.byte,
            },
            end: Position {
                line: symbol.range.end.line,
                column: symbol.range.end.column,
                byte: symbol.range.end.byte,
            },
        },
        visibility: symbol.visibility.clone(),
        signature: symbol.signature.clone(),
        documentation: symbol.documentation.clone(),
    }
}
