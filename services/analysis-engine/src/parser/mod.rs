use anyhow::{Context, Result};
use std::path::Path;
use std::collections::HashMap;
use std::sync::RwLock;
use tree_sitter::{<PERSON>rse<PERSON>, Node};
use crate::models::{FileAnalysis, AstNode, Position, Range, Symbol, SymbolType, FileMetrics, ParseError, ParseErrorType, CodeChunk, ChunkType};
use sha2::{Sha256, Digest};
use tokio::io::{AsyncReadExt, BufReader};
use tokio::fs::File;

// Import the language constants from the tree-sitter crates
use tree_sitter_rust::LANGUAGE as RUST_LANGUAGE;
use tree_sitter_python::LANGUAGE as PYTHON_LANGUAGE;
use tree_sitter_javascript::LANGUAGE as JAVASCRIPT_LANGUAGE;
use tree_sitter_typescript::LANGUAGE_TYPESCRIPT;
use tree_sitter_go::LANGUAGE as GO_LANGUAGE;
use tree_sitter_java::LANGUAGE as JAVA_LANGUAGE;
use tree_sitter_c::LANGUAGE as C_LANGUAGE;
use tree_sitter_cpp::LANGUAGE as CPP_LANGUAGE;
// Additional languages - adding stable ones
use tree_sitter_html::language as HTML_LANGUAGE;
use tree_sitter_css::language as CSS_LANGUAGE;
use tree_sitter_json::language as JSON_LANGUAGE;
use tree_sitter_yaml::language as YAML_LANGUAGE;
// use tree_sitter_php::LANGUAGE as PHP_LANGUAGE;
// use tree_sitter_ruby::LANGUAGE as RUBY_LANGUAGE;
// // Mobile languages
// use tree_sitter_swift::LANGUAGE as SWIFT_LANGUAGE;
// use tree_sitter_kotlin::LANGUAGE as KOTLIN_LANGUAGE;
// use tree_sitter_objc::LANGUAGE as OBJC_LANGUAGE;
// // Data languages
// use tree_sitter_sql::LANGUAGE as SQL_LANGUAGE;
// use tree_sitter_r::LANGUAGE as R_LANGUAGE;
// use tree_sitter_julia::LANGUAGE as JULIA_LANGUAGE;
// // Functional languages
// use tree_sitter_haskell::LANGUAGE as HASKELL_LANGUAGE;
// use tree_sitter_scala::LANGUAGE as SCALA_LANGUAGE;
// use tree_sitter_clojure::LANGUAGE as CLOJURE_LANGUAGE;
// use tree_sitter_erlang::LANGUAGE as ERLANG_LANGUAGE;
// use tree_sitter_elixir::LANGUAGE as ELIXIR_LANGUAGE;
// // Other languages
// use tree_sitter_bash::LANGUAGE as BASH_LANGUAGE;
// use tree_sitter_yaml::LANGUAGE as YAML_LANGUAGE;
// use tree_sitter_json::LANGUAGE as JSON_LANGUAGE;
// use tree_sitter_xml::LANGUAGE as XML_LANGUAGE;
// use tree_sitter_markdown::LANGUAGE as MARKDOWN_LANGUAGE;

pub struct TreeSitterParser {
    parsers: HashMap<String, RwLock<Parser>>,
}

impl TreeSitterParser {
    pub fn new() -> Result<Self> {
        let mut parsers = HashMap::new();
        
        let languages = vec![
            // Original languages - these work without version conflicts
            ("rust", RUST_LANGUAGE.into()),
            ("javascript", JAVASCRIPT_LANGUAGE.into()),
            ("typescript", LANGUAGE_TYPESCRIPT.into()),
            ("python", PYTHON_LANGUAGE.into()),
            ("go", GO_LANGUAGE.into()),
            ("java", JAVA_LANGUAGE.into()),
            ("c", C_LANGUAGE.into()),
            ("cpp", CPP_LANGUAGE.into()),
            // Additional stable languages
            ("html", HTML_LANGUAGE().into()),
            ("css", CSS_LANGUAGE().into()),
            ("json", JSON_LANGUAGE().into()),
            ("yaml", YAML_LANGUAGE().into()),
            // ("php", PHP_LANGUAGE.into()),
            // ("ruby", RUBY_LANGUAGE.into()),
            // ("swift", SWIFT_LANGUAGE.into()),
            // ("kotlin", KOTLIN_LANGUAGE.into()),
            // ("objc", OBJC_LANGUAGE.into()),
            // ("sql", SQL_LANGUAGE.into()),
            // ("r", R_LANGUAGE.into()),
            // ("julia", JULIA_LANGUAGE.into()),
            // ("haskell", HASKELL_LANGUAGE.into()),
            // ("scala", SCALA_LANGUAGE.into()),
            // ("clojure", CLOJURE_LANGUAGE.into()),
            // ("erlang", ERLANG_LANGUAGE.into()),
            // ("elixir", ELIXIR_LANGUAGE.into()),
            // ("bash", BASH_LANGUAGE.into()),
            // ("yaml", YAML_LANGUAGE.into()),
            // ("json", JSON_LANGUAGE.into()),
            // ("xml", XML_LANGUAGE.into()),
            // ("markdown", MARKDOWN_LANGUAGE.into()),
        ];

        for (lang_name, language) in languages {
            let mut parser = Parser::new();
            parser.set_language(&language).context(format!("Failed to load {} grammar", lang_name))?;
            parsers.insert(lang_name.to_string(), RwLock::new(parser));
        }

        Ok(Self { parsers })
    }

    pub async fn parse_file(&self, file_path: &Path) -> Result<FileAnalysis, ParseError> {
        // Check file size to determine if we should use streaming
        let metadata = tokio::fs::metadata(file_path).await.map_err(|e| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::Other,
            message: format!("Failed to get file metadata: {}", e),
            position: None,
        })?;
        
        let file_size = metadata.len();
        const LARGE_FILE_THRESHOLD: u64 = 10 * 1024 * 1024; // 10MB
        
        let content = if file_size > LARGE_FILE_THRESHOLD {
            // Use streaming for large files
            self.read_file_streaming(file_path, file_size).await?
        } else {
            // Regular read for smaller files
            tokio::fs::read_to_string(file_path).await.map_err(|e| ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::Other,
                message: e.to_string(),
                position: None,
            })?
        };
        
        let language = self.detect_language(file_path)?;
        
        let lang_parser = self.parsers.get(language).ok_or_else(|| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::UnsupportedLanguage,
            message: "Unsupported language".to_string(),
            position: None,
        })?;

        // Parse using the existing parser with safe RwLock borrowing
        let tree = {
            let mut parser = lang_parser.write().map_err(|_| ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::Other,
                message: "Failed to acquire parser lock".to_string(),
                position: None,
            })?;
            parser.parse(&content, None)
        }.ok_or_else(|| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::ParseError,
            message: "Failed to parse file".to_string(),
            position: None,
        })?;
        
        let root_node = tree.root_node();
        let ast = self.build_ast(&root_node, &content);
        let symbols = self.extract_symbols(&root_node, &content);
        let metadata = self.extract_metadata(&content);

        // Calculate content hash
        let mut hasher = Sha256::new();
        hasher.update(content.as_bytes());
        let content_hash = format!("{:x}", hasher.finalize());
        
        // Get file size
        let file_meta = tokio::fs::metadata(file_path).await.ok();
        let size_bytes = file_meta.as_ref().map(|m| m.len());
        
        // Extract chunks for embedding
        let chunks = self.extract_chunks(&ast, &content, language);
        
        Ok(FileAnalysis {
            path: file_path.to_string_lossy().to_string(),
            language: language.to_string(),
            content_hash,
            size_bytes,
            ast,
            metrics: metadata,
            chunks: Some(chunks),
            symbols: Some(symbols),
        })
    }

    fn detect_language<'a>(&self, file_path: &'a Path) -> Result<&'a str, ParseError> {
        let extension = file_path.extension().and_then(|s| s.to_str()).unwrap_or("");
        match extension {
            "rs" => Ok("rust"),
            "py" => Ok("python"),
            "js" | "mjs" => Ok("javascript"),
            "ts" | "tsx" => Ok("typescript"),
            "go" => Ok("go"),
            "java" => Ok("java"),
            "c" | "h" => Ok("c"),
            "cpp" | "cc" | "cxx" | "hpp" => Ok("cpp"),
            "php" => Ok("php"),
            "rb" => Ok("ruby"),
            "swift" => Ok("swift"),
            "kt" | "kts" => Ok("kotlin"),
            "m" | "mm" => Ok("objc"),
            "scala" => Ok("scala"),
            "hs" => Ok("haskell"),
            "ex" | "exs" => Ok("elixir"),
            "sh" | "bash" => Ok("bash"),
            "html" | "htm" => Ok("html"),
            "css" | "scss" | "sass" => Ok("css"),
            "json" => Ok("json"),
            "yaml" | "yml" => Ok("yaml"),
            "xml" => Ok("xml"),
            "md" | "markdown" => Ok("markdown"),
            "sql" => Ok("sql"),
            "r" | "R" => Ok("r"),
            "jl" => Ok("julia"),
            "clj" | "cljs" | "cljc" => Ok("clojure"),
            "erl" | "hrl" => Ok("erlang"),
            _ => Err(ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::UnsupportedLanguage,
                message: format!("Unsupported file extension: {}", extension),
                position: None,
            }),
        }
    }

    fn build_ast(&self, node: &Node, source: &str) -> AstNode {
        self.build_ast_optimized(node, source, 0, 100) // Max depth of 100
    }
    
    fn build_ast_optimized(&self, node: &Node, source: &str, depth: usize, max_depth: usize) -> AstNode {
        // Optimization: Skip deep traversal for performance
        if depth > max_depth {
            return AstNode {
                node_type: "truncated".to_string(),
                name: Some("...".to_string()),
                range: Range {
                    start: self.convert_position(node.start_position()),
                    end: self.convert_position(node.end_position()),
                },
                children: vec![],
                properties: None,
                text: Some("/* Deep tree truncated for performance */".to_string()),
            };
        }
        
        // Optimization: Only extract text for leaf nodes or important nodes
        let should_extract_text = node.child_count() == 0 || matches!(
            node.kind(),
            "string_literal" | "number_literal" | "identifier" | "comment"
        );
        
        let text = if should_extract_text {
            node.utf8_text(source.as_bytes()).ok().map(|s| s.to_string())
        } else {
            None
        };
        
        let name = node.child_by_field_name("name")
            .and_then(|n| n.utf8_text(source.as_bytes()).ok())
            .map(|s| s.to_string());
        
        // Optimization: Use iterator with early termination for large child lists
        let children: Vec<AstNode> = if node.child_count() > 1000 {
            // For extremely large nodes, sample children
            node.children(&mut node.walk())
                .step_by(10) // Take every 10th child
                .take(100)   // Maximum 100 children
                .map(|child| self.build_ast_optimized(&child, source, depth + 1, max_depth))
                .collect()
        } else {
            node.children(&mut node.walk())
                .map(|child| self.build_ast_optimized(&child, source, depth + 1, max_depth))
                .collect()
        };
        
        AstNode {
            node_type: node.kind().to_string(),
            name,
            range: Range {
                start: Position { 
                    line: node.start_position().row as u32,
                    column: node.start_position().column as u32,
                    byte: node.start_byte() as u32,
                },
                end: Position {
                    line: node.end_position().row as u32,
                    column: node.end_position().column as u32,
                    byte: node.end_byte() as u32,
                },
            },
            children,
            properties: None,
            text,
        }
    }

    fn extract_symbols(&self, node: &Node, source: &str) -> Vec<Symbol> {
        let mut symbols = Vec::new();
        self.traverse_for_symbols(node, source, &mut symbols);
        symbols
    }

    fn traverse_for_symbols(&self, node: &Node, source: &str, symbols: &mut Vec<Symbol>) {
        self.traverse_for_symbols_optimized(node, source, symbols, 0, 50);
    }
    
    fn traverse_for_symbols_optimized(&self, node: &Node, source: &str, symbols: &mut Vec<Symbol>, depth: usize, max_depth: usize) {
        // Optimization: Stop at maximum depth to prevent stack overflow
        if depth > max_depth {
            return;
        }
        
        // Optimization: Early exit for nodes that can't contain symbols
        let node_kind = node.kind();
        if matches!(node_kind, "comment" | "string" | "string_literal" | "number" | "number_literal") {
            return;
        }
        
        let symbol_type = match node_kind {
            "function_item" | "function_declaration" | "method_definition" | "function_definition" => Some(SymbolType::Function),
            "struct_item" | "class_declaration" | "class_definition" => Some(SymbolType::Class),
            "let_declaration" | "const_item" | "variable_declarator" | "const_declaration" => Some(SymbolType::Variable),
            "trait_item" | "interface_declaration" => Some(SymbolType::Interface),
            _ => None,
        };

        if let Some(st) = symbol_type {
            let name_node = node.child_by_field_name("name").or_else(|| node.child_by_field_name("identifier"));
            if let Some(name_node) = name_node {
                if let Ok(name_text) = name_node.utf8_text(source.as_bytes()) {
                    // Optimization: Skip anonymous symbols
                    if !name_text.is_empty() && !name_text.starts_with('_') {
                        symbols.push(Symbol {
                            name: name_text.to_string(),
                            symbol_type: st,
                            range: Range {
                                start: self.convert_position(node.start_position()),
                                end: self.convert_position(node.end_position()),
                            },
                            visibility: None,
                            signature: None,
                            documentation: None,
                        });
                    }
                }
            }
        }

        // Optimization: Use cursor for more efficient traversal
        let mut cursor = node.walk();
        for child in node.children(&mut cursor) {
            self.traverse_for_symbols_optimized(&child, source, symbols, depth + 1, max_depth);
        }
    }

    fn convert_position(&self, point: tree_sitter::Point) -> Position {
        Position {
            line: point.row as u32,
            column: point.column as u32,
            byte: 0, // Tree-sitter doesn't provide byte offset directly in Point - will be set during parsing
        }
    }

    fn extract_metadata(&self, content: &str) -> FileMetrics {
        let lines: Vec<&str> = content.lines().collect();
        let total_lines = lines.len() as u32;
        let mut lines_of_code = 0u32;
        let mut comment_lines = 0u32;
        
        for line in &lines {
            let trimmed = line.trim();
            if !trimmed.is_empty() {
                if trimmed.starts_with("//") || trimmed.starts_with("#") || trimmed.starts_with("/*") || trimmed.starts_with("*") {
                    comment_lines += 1;
                } else {
                    lines_of_code += 1;
                }
            }
        }
        
        let comment_ratio = if lines_of_code > 0 {
            Some(comment_lines as f64 / lines_of_code as f64)
        } else {
            None
        };
        
        FileMetrics {
            lines_of_code,
            total_lines: Some(total_lines),
            complexity: 0, // Will be calculated by metrics service
            maintainability_index: 0.0, // Will be calculated by metrics service
            function_count: None,
            class_count: None,
            comment_ratio,
        }
    }
    
    fn extract_chunks(&self, node: &AstNode, source: &str, language: &str) -> Vec<CodeChunk> {
        let mut chunks = Vec::new();
        let mut chunk_counter = 0;
        
        self.extract_chunks_recursive(node, source, language, &mut chunks, &mut chunk_counter);
        chunks
    }
    
    fn extract_chunks_recursive(&self, node: &AstNode, source: &str, language: &str, chunks: &mut Vec<CodeChunk>, counter: &mut usize) {
        // Determine if this node should be a chunk
        let chunk_type = match node.node_type.as_str() {
            "function_item" | "function_declaration" => Some(ChunkType::Function),
            "struct_item" | "class_declaration" => Some(ChunkType::Class),
            "impl_item" | "method_declaration" => Some(ChunkType::Method),
            "block_statement" | "block" => Some(ChunkType::Block),
            "comment" | "line_comment" | "block_comment" => Some(ChunkType::Comment),
            "use_declaration" | "import_statement" | "import_declaration" => Some(ChunkType::Import),
            _ => None,
        };
        
        if let Some(ct) = chunk_type {
            *counter += 1;
            let chunk_id = format!("chunk_{:016x}", counter);
            
            // For optimized AST, we may need to extract text from source using range
            let text = if let Some(ref node_text) = node.text {
                node_text.clone()
            } else if !source.is_empty() {
                // Extract text from source using byte positions
                // This is a fallback for when the AST node doesn't have text
                let start_byte = node.range.start.byte as usize;
                let end_byte = node.range.end.byte as usize;
                
                if start_byte < source.len() && end_byte <= source.len() && start_byte < end_byte {
                    source[start_byte..end_byte].to_string()
                } else {
                    // If byte positions are not set correctly, try to extract by line/column
                    String::new()
                }
            } else {
                String::new()
            };
            
            if !text.is_empty() && text.len() <= 8192 { // Max length from contract
                chunks.push(CodeChunk {
                    chunk_id,
                    content: text,
                    range: node.range.clone(),
                    chunk_type: ct,
                    language: Some(language.to_string()),
                    context: None,
                });
            }
        }
        
        // Recurse into children
        for child in &node.children {
            self.extract_chunks_recursive(child, source, language, chunks, counter);
        }
    }
    
    async fn read_file_streaming(&self, file_path: &Path, file_size: u64) -> Result<String, ParseError> {
        const BUFFER_SIZE: usize = 64 * 1024; // 64KB buffer
        const MAX_FILE_SIZE: u64 = 100 * 1024 * 1024; // 100MB max
        
        if file_size > MAX_FILE_SIZE {
            return Err(ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::FileTooLarge,
                message: format!("File too large: {} bytes (max: {} bytes)", file_size, MAX_FILE_SIZE),
                position: None,
            });
        }
        
        let file = File::open(file_path).await.map_err(|e| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::Other,
            message: format!("Failed to open file: {}", e),
            position: None,
        })?;
        
        let mut reader = BufReader::with_capacity(BUFFER_SIZE, file);
        let mut content = String::with_capacity(file_size as usize);
        
        // Read file in chunks
        let mut buffer = vec![0u8; BUFFER_SIZE];
        loop {
            let n = reader.read(&mut buffer).await.map_err(|e| ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::Other,
                message: format!("Failed to read file: {}", e),
                position: None,
            })?;
            
            if n == 0 {
                break;
            }
            
            // Convert bytes to string, handling UTF-8 errors
            let chunk = std::str::from_utf8(&buffer[..n]).map_err(|e| ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::Other,
                message: format!("Invalid UTF-8 in file: {}", e),
                position: None,
            })?;
            
            content.push_str(chunk);
        }
        
        tracing::info!("Successfully read large file {} ({} bytes) using streaming", 
                     file_path.display(), file_size);
        
        Ok(content)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use tempfile::TempDir;

    fn create_test_file(dir: &TempDir, name: &str, content: &str) -> std::path::PathBuf {
        let file_path = dir.path().join(name);
        fs::write(&file_path, content).unwrap();
        file_path
    }

    #[tokio::test]
    async fn test_new_parser_creation() {
        let parser = TreeSitterParser::new().unwrap();
        assert_eq!(parser.parsers.len(), 8); // Currently 8 languages supported
        assert!(parser.parsers.contains_key("rust"));
        assert!(parser.parsers.contains_key("python"));
        assert!(parser.parsers.contains_key("javascript"));
        assert!(parser.parsers.contains_key("typescript"));
        assert!(parser.parsers.contains_key("go"));
        assert!(parser.parsers.contains_key("java"));
        assert!(parser.parsers.contains_key("c"));
        assert!(parser.parsers.contains_key("cpp"));
    }

    #[tokio::test]
    async fn test_detect_language() {
        let parser = TreeSitterParser::new().unwrap();
        
        assert_eq!(parser.detect_language(Path::new("test.rs")).unwrap(), "rust");
        assert_eq!(parser.detect_language(Path::new("test.py")).unwrap(), "python");
        assert_eq!(parser.detect_language(Path::new("test.js")).unwrap(), "javascript");
        assert_eq!(parser.detect_language(Path::new("test.ts")).unwrap(), "typescript");
        assert_eq!(parser.detect_language(Path::new("test.go")).unwrap(), "go");
        assert_eq!(parser.detect_language(Path::new("test.java")).unwrap(), "java");
        assert_eq!(parser.detect_language(Path::new("test.c")).unwrap(), "c");
        assert_eq!(parser.detect_language(Path::new("test.cpp")).unwrap(), "cpp");
        
        // Test unsupported extension
        let result = parser.detect_language(Path::new("test.xyz"));
        assert!(result.is_err());
        if let Err(e) = result {
            assert_eq!(e.error_type, ParseErrorType::UnsupportedLanguage);
        }
    }

    #[tokio::test]
    async fn test_parse_rust_file() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let rust_code = r#"
fn main() {
    println!("Hello, world!");
}

struct MyStruct {
    field: String,
}

impl MyStruct {
    fn new() -> Self {
        Self { field: String::new() }
    }
}
"#;
        
        let file_path = create_test_file(&temp_dir, "test.rs", rust_code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        assert_eq!(result.language, "rust");
        assert!(!result.content_hash.is_empty());
        assert!(result.size_bytes.is_some());
        assert!(result.symbols.is_some());
        
        let symbols = result.symbols.unwrap();
        assert!(symbols.iter().any(|s| s.name == "main" && s.symbol_type == SymbolType::Function));
        assert!(symbols.iter().any(|s| s.name == "MyStruct" && s.symbol_type == SymbolType::Class));
        
        assert!(result.chunks.is_some());
        let chunks = result.chunks.unwrap();
        assert!(!chunks.is_empty());
    }

    #[tokio::test]
    async fn test_parse_python_file() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let python_code = r#"
def hello_world():
    print("Hello, world!")

class MyClass:
    def __init__(self):
        self.value = 42
    
    def get_value(self):
        return self.value

hello_world()
"#;
        
        let file_path = create_test_file(&temp_dir, "test.py", python_code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        assert_eq!(result.language, "python");
        assert!(result.metrics.lines_of_code > 0);
        assert!(result.metrics.total_lines.is_some());
    }

    #[tokio::test]
    async fn test_parse_javascript_file() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let js_code = r#"
function greet(name) {
    console.log(`Hello, ${name}!`);
}

const myObject = {
    property: "value",
    method: function() {
        return this.property;
    }
};

greet("World");
"#;
        
        let file_path = create_test_file(&temp_dir, "test.js", js_code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        assert_eq!(result.language, "javascript");
        assert!(!result.ast.children.is_empty());
    }

    #[tokio::test]
    async fn test_parse_invalid_file() {
        let parser = TreeSitterParser::new().unwrap();
        let result = parser.parse_file(Path::new("/nonexistent/file.rs")).await;
        
        assert!(result.is_err());
        if let Err(e) = result {
            assert_eq!(e.error_type, ParseErrorType::Other);
        }
    }

    #[tokio::test]
    async fn test_extract_metadata() {
        let parser = TreeSitterParser::new().unwrap();
        
        let content = r#"// This is a comment
fn main() {
    // Another comment
    println!("Hello");
    println!("World");
}
// End comment"#;
        
        let metrics = parser.extract_metadata(content);
        assert_eq!(metrics.lines_of_code, 4); // 4 non-comment lines
        assert_eq!(metrics.total_lines, Some(7));
        assert!(metrics.comment_ratio.is_some());
    }

    #[tokio::test]
    async fn test_convert_position() {
        let parser = TreeSitterParser::new().unwrap();
        let point = tree_sitter::Point { row: 10, column: 20 };
        let position = parser.convert_position(point);
        
        assert_eq!(position.line, 10);
        assert_eq!(position.column, 20);
        assert_eq!(position.byte, 0);
    }

    #[tokio::test]
    async fn test_build_ast() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let code = "fn test() { }";
        let file_path = create_test_file(&temp_dir, "test.rs", code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        assert!(!result.ast.node_type.is_empty());
        assert!(result.ast.range.start.line == 0);
    }

    #[tokio::test]
    async fn test_extract_chunks() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let code = r#"
fn function1() {
    // Function body
}

struct MyStruct {
    field: i32
}

impl MyStruct {
    fn method1(&self) -> i32 {
        self.field
    }
}
"#;
        
        let file_path = create_test_file(&temp_dir, "test.rs", code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        let chunks = result.chunks.unwrap();
        assert!(chunks.iter().any(|c| c.chunk_type == ChunkType::Function));
        assert!(chunks.iter().any(|c| c.chunk_type == ChunkType::Class));
        assert!(chunks.iter().any(|c| c.chunk_type == ChunkType::Method));
    }

    #[tokio::test]
    async fn test_multiple_language_parsing() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        // Test Go
        let go_code = r#"
package main

func main() {
    fmt.Println("Hello, Go!")
}
"#;
        let go_file = create_test_file(&temp_dir, "test.go", go_code);
        let go_result = parser.parse_file(&go_file).await.unwrap();
        assert_eq!(go_result.language, "go");
        
        // Test Java
        let java_code = r#"
public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, Java!");
    }
}
"#;
        let java_file = create_test_file(&temp_dir, "HelloWorld.java", java_code);
        let java_result = parser.parse_file(&java_file).await.unwrap();
        assert_eq!(java_result.language, "java");
        
        // Test C
        let c_code = r#"
#include <stdio.h>

int main() {
    printf("Hello, C!\n");
    return 0;
}
"#;
        let c_file = create_test_file(&temp_dir, "test.c", c_code);
        let c_result = parser.parse_file(&c_file).await.unwrap();
        assert_eq!(c_result.language, "c");
    }
    
    #[tokio::test]
    async fn test_large_file_streaming() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        // Create a large file (>10MB)
        let mut large_content = String::new();
        large_content.push_str("fn main() {\n");
        // Add enough content to exceed 10MB
        for i in 0..500_000 {
            large_content.push_str(&format!("    println!(\"Line {}\");\n", i));
        }
        large_content.push_str("}\n");
        
        // Verify the content is indeed large
        assert!(large_content.len() > 10 * 1024 * 1024);
        
        let large_file = create_test_file(&temp_dir, "large.rs", &large_content);
        
        // This should use streaming
        let result = parser.parse_file(&large_file).await;
        assert!(result.is_ok());
        
        let analysis = result.unwrap();
        assert_eq!(analysis.language, "rust");
        assert!(analysis.size_bytes.unwrap() > 10 * 1024 * 1024);
    }
    
    #[tokio::test]
    async fn test_file_too_large_error() {
        let parser = TreeSitterParser::new().unwrap();
        
        // Test the streaming method directly with a file that would be too large
        let path = Path::new("/fake/path/too_large.rs");
        let result = parser.read_file_streaming(path, 101 * 1024 * 1024).await;
        
        assert!(result.is_err());
        if let Err(e) = result {
            assert_eq!(e.error_type, ParseErrorType::FileTooLarge);
            assert!(e.message.contains("File too large"));
        }
    }
}
