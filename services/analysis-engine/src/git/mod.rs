use anyhow::{Context, Result};
use git2::{build::<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>tchOptions, RemoteCallbacks};
use std::path::{Path, PathBuf};
use std::env;

pub struct GitService;

impl GitService {
    pub fn new() -> Self {
        Self
    }

    pub async fn clone_repository_with_auth(
        &self,
        url: &str,
        branch: &Option<String>,
        analysis_id: &str,
    ) -> Result<PathBuf> {
        let clone_path = PathBuf::from(format!("/tmp/ccl-analysis/{}", analysis_id));
        if clone_path.exists() {
            tokio::fs::remove_dir_all(&clone_path).await?;
        }
        tokio::fs::create_dir_all(&clone_path).await?;

        let mut callbacks = RemoteCallbacks::new();
        callbacks.credentials(|_url, username_from_url, allowed_types| {
            if allowed_types.is_ssh_key() {
                let user = username_from_url.unwrap_or("git");
                let home = env::var("HOME").unwrap_or_else(|_| "/home/<USER>".to_string());
                let key_path = format!("{}/.ssh/id_rsa", home);
                let private_key = Path::new(&key_path);
                git2::Cred::ssh_key(user, None, private_key, None)
            } else if allowed_types.is_user_pass_plaintext() {
                let token = env::var("GITHUB_TOKEN").unwrap_or_default();
                git2::Cred::userpass_plaintext(&token, "")
            } else {
                Err(git2::Error::from_str("no supported credential type found"))
            }
        });

        let mut fetch_options = FetchOptions::new();
        fetch_options.remote_callbacks(callbacks);

        let mut repo_builder = RepoBuilder::new();
        repo_builder.fetch_options(fetch_options);

        if let Some(branch) = branch {
            repo_builder.branch(branch);
        }

        repo_builder
            .clone(url, &clone_path)
            .context(format!("Failed to clone repository: {}", url))?;

        Ok(clone_path)
    }

    pub async fn cleanup_repository(&self, repo_path: &Path) -> Result<()> {
        tokio::fs::remove_dir_all(repo_path)
            .await
            .context("Failed to cleanup repository")
    }
}
