use anyhow::{Context, Result};
use git2::{build::<PERSON><PERSON><PERSON><PERSON><PERSON>, FetchOptions, RemoteCallbacks};
use std::path::{Path, PathBuf};
use std::env;
use uuid;
use scopeguard;

pub struct GitService;

impl GitService {
    pub fn new() -> Self {
        Self
    }

    pub async fn clone_repository_with_auth(
        &self,
        url: &str,
        branch: &Option<String>,
        analysis_id: &str,
    ) -> Result<PathBuf> {
        let clone_path = PathBuf::from(format!("/tmp/ccl-analysis/{}", analysis_id));
        if clone_path.exists() {
            tokio::fs::remove_dir_all(&clone_path).await?;
        }
        tokio::fs::create_dir_all(&clone_path).await?;

        let mut callbacks = RemoteCallbacks::new();
        callbacks.credentials(|_url, username_from_url, allowed_types| {
            if allowed_types.is_ssh_key() {
                let user = username_from_url.unwrap_or("git");
                let home = env::var("HOME").unwrap_or_else(|_| "/home/<USER>".to_string());
                let key_path = format!("{}/.ssh/id_rsa", home);
                let private_key = Path::new(&key_path);
                git2::Cred::ssh_key(user, None, private_key, None)
            } else if allowed_types.is_user_pass_plaintext() {
                let token = env::var("GITHUB_TOKEN").unwrap_or_default();
                git2::Cred::userpass_plaintext(&token, "")
            } else {
                Err(git2::Error::from_str("no supported credential type found"))
            }
        });

        let mut fetch_options = FetchOptions::new();
        fetch_options.remote_callbacks(callbacks);

        let mut repo_builder = RepoBuilder::new();
        repo_builder.fetch_options(fetch_options);

        if let Some(branch) = branch {
            repo_builder.branch(branch);
        }

        repo_builder
            .clone(url, &clone_path)
            .context(format!("Failed to clone repository: {}", url))?;

        Ok(clone_path)
    }

    pub async fn cleanup_repository(&self, repo_path: &Path) -> Result<()> {
        tokio::fs::remove_dir_all(repo_path)
            .await
            .context("Failed to cleanup repository")
    }

    /// Get the current commit hash of a repository
    pub fn get_current_commit_hash(&self, repo_path: &Path) -> Result<String> {
        let repo = git2::Repository::open(repo_path)
            .context("Failed to open repository")?;

        let head = repo.head()
            .context("Failed to get HEAD reference")?;

        let commit = head.peel_to_commit()
            .context("Failed to get commit from HEAD")?;

        Ok(commit.id().to_string())
    }

    /// Get the current commit hash from a remote repository without cloning
    pub async fn get_remote_commit_hash(&self, url: &str, branch: &Option<String>) -> Result<String> {
        // For remote repositories, we need to do a lightweight fetch
        // This is more complex and would require additional git2 setup
        // For now, we'll use a simpler approach by doing a shallow clone
        let temp_id = uuid::Uuid::new_v4().to_string();
        let temp_path = PathBuf::from(format!("/tmp/ccl-hash-check/{}", temp_id));

        // Ensure cleanup happens even if we fail
        let _cleanup_guard = scopeguard::guard(temp_path.clone(), |path| {
            if path.exists() {
                let _ = std::fs::remove_dir_all(&path);
            }
        });

        if temp_path.exists() {
            tokio::fs::remove_dir_all(&temp_path).await?;
        }
        tokio::fs::create_dir_all(&temp_path).await?;

        let mut callbacks = RemoteCallbacks::new();
        callbacks.credentials(|_url, username_from_url, allowed_types| {
            if allowed_types.is_ssh_key() {
                let user = username_from_url.unwrap_or("git");
                let home = env::var("HOME").unwrap_or_else(|_| "/home/<USER>".to_string());
                let key_path = format!("{}/.ssh/id_rsa", home);
                let private_key = Path::new(&key_path);
                git2::Cred::ssh_key(user, None, private_key, None)
            } else if allowed_types.is_user_pass_plaintext() {
                let token = env::var("GITHUB_TOKEN").unwrap_or_default();
                git2::Cred::userpass_plaintext(&token, "")
            } else {
                Err(git2::Error::from_str("no supported credential type found"))
            }
        });

        let mut fetch_options = FetchOptions::new();
        fetch_options.remote_callbacks(callbacks);

        let mut repo_builder = RepoBuilder::new();
        repo_builder.fetch_options(fetch_options);

        // Shallow clone with depth 1 to get just the latest commit
        repo_builder.clone_local(git2::build::CloneLocal::None);

        if let Some(branch) = branch {
            repo_builder.branch(branch);
        }

        let repo = repo_builder
            .clone(url, &temp_path)
            .context(format!("Failed to shallow clone repository for hash check: {}", url))?;

        let head = repo.head()
            .context("Failed to get HEAD reference")?;

        let commit = head.peel_to_commit()
            .context("Failed to get commit from HEAD")?;

        Ok(commit.id().to_string())
    }
}
