#!/bin/bash
set -e

# Configuration
export PROJECT_ID=${PROJECT_ID:-vibe-match-463114}
export REGION=${REGION:-us-central1}
export SERVICE_NAME=analysis-engine
export IMAGE_NAME=gcr.io/${PROJECT_ID}/${SERVICE_NAME}

echo "🚀 Deploying Analysis Engine to Cloud Run"
echo "Project: ${PROJECT_ID}"
echo "Region: ${REGION}"
echo "Service: ${SERVICE_NAME}"

# Build and push image
echo "📦 Building Docker image..."
docker build -t ${IMAGE_NAME}:latest .

echo "📤 Pushing image to Container Registry..."
docker push ${IMAGE_NAME}:latest

# Deploy to Cloud Run
echo "☁️ Deploying to Cloud Run..."
gcloud run deploy ${SERVICE_NAME} \
  --image ${IMAGE_NAME}:latest \
  --platform managed \
  --region ${REGION} \
  --port 8001 \
  --memory 4Gi \
  --cpu 4 \
  --timeout 300s \
  --concurrency 100 \
  --min-instances 1 \
  --max-instances 100 \
  --set-env-vars "RUST_LOG=info" \
  --set-env-vars "GCP_PROJECT_ID=${PROJECT_ID}" \
  --set-env-vars "SPANNER_INSTANCE_ID=ccl-production" \
  --set-env-vars "SPANNER_DATABASE_ID=ccl-main" \
  --set-env-vars "STORAGE_BUCKET=ccl-analysis-artifacts" \
  --set-env-vars "PUBSUB_TOPIC=analysis-events" \
  --set-env-vars "VERTEX_AI_LOCATION=${REGION}" \
  --allow-unauthenticated

# Get service URL
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} \
  --platform managed \
  --region ${REGION} \
  --format 'value(status.url)')

echo "✅ Deployment complete!"
echo "Service URL: ${SERVICE_URL}"

# Test deployment
echo "🧪 Testing deployment..."
curl -s ${SERVICE_URL}/health | jq .