#!/bin/bash
# Local testing script for Analysis Engine

echo "🧪 Testing Analysis Engine Locally"

# Build the service
echo "📦 Building service..."
cargo build --release

# Set test environment variables
export RUST_LOG=debug
export PORT=8001
export GCP_PROJECT_ID=vibe-match-463114
export SPANNER_INSTANCE_ID=ccl-production
export SPANNER_DATABASE_ID=ccl-main
export STORAGE_BUCKET=ccl-analysis-artifacts
export PUBSUB_TOPIC=analysis-events
export VERTEX_AI_LOCATION=us-central1
export JWT_SECRET=test-secret-key

# Run the service in background
echo "🚀 Starting service..."
./target/release/analysis-engine &
SERVICE_PID=$!

# Wait for service to start
echo "⏳ Waiting for service to start..."
sleep 5

# Test endpoints
echo "🧪 Testing endpoints..."

echo -e "\n1. Health Check:"
curl -s http://localhost:8001/health | jq .

echo -e "\n2. Ready Check:"
curl -s http://localhost:8001/health/ready | jq .

echo -e "\n3. List Patterns:"
curl -s http://localhost:8001/api/v1/patterns | jq .

# Stop the service
echo -e "\n🛑 Stopping service..."
kill $SERVICE_PID

echo "✅ Local testing complete!"