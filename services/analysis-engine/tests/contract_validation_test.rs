use analysis_engine::models::*;
use jsonschema::{Draft, JSONSchema};
use serde_json::{json, Value};
use chrono::Utc;

// Load the contract schema
const AST_OUTPUT_SCHEMA: &str = include_str!("../../../contracts/schemas/ast-output-v1.json");

#[test]
fn test_ast_output_contract_validation() {
    // Parse the schema
    let schema_json: Value = serde_json::from_str(AST_OUTPUT_SCHEMA).expect("Invalid schema JSON");
    let compiled = JSONSchema::options()
        .with_draft(Draft::Draft7)
        .compile(&schema_json)
        .expect("Failed to compile schema");

    // Create a sample analysis result that matches the contract
    let analysis_output = create_valid_ast_output();
    
    // Convert to JSON
    let output_json = serde_json::to_value(&analysis_output).expect("Failed to serialize output");
    
    // Validate against schema
    let result = compiled.validate(&output_json);
    if let Err(errors) = result {
        let error_messages: Vec<String> = errors
            .map(|e| format!("Validation error at {}: {}", e.instance_path, e))
            .collect();
        panic!("Schema validation failed:\n{}", error_messages.join("\n"));
    }
}

#[test]
fn test_file_analysis_contract_compliance() {
    let schema_json: Value = serde_json::from_str(AST_OUTPUT_SCHEMA).expect("Invalid schema JSON");
    let compiled = JSONSchema::options()
        .with_draft(Draft::Draft7)
        .compile(&schema_json)
        .expect("Failed to compile schema");

    // Test FileAnalysis structure - create JSON directly to avoid null serialization issues
    let file_analysis_json = json!({
        "path": "src/main.rs",
        "language": "rust",
        "content_hash": "a".repeat(64),
        "size_bytes": 1024,
        "ast": {
            "type": "source_file",
            "range": {
                "start": {"line": 0, "column": 0, "byte": 0},
                "end": {"line": 100, "column": 0, "byte": 1024}
            },
            "children": []
        },
        "metrics": {
            "lines_of_code": 50,
            "total_lines": 75,
            "complexity": 10,
            "maintainability_index": 85.5,
            "function_count": 5,
            "class_count": 2,
            "comment_ratio": 0.3
        },
        "chunks": [{
            "chunk_id": format!("chunk_{:016x}", 1),
            "content": "fn main() { }",
            "range": {
                "start": {"line": 0, "column": 0, "byte": 0},
                "end": {"line": 0, "column": 13, "byte": 13}
            },
            "type": "function",
            "language": "rust"
        }],
        "symbols": [{
            "name": "main",
            "type": "function",
            "range": {
                "start": {"line": 0, "column": 0, "byte": 0},
                "end": {"line": 0, "column": 13, "byte": 13}
            },
            "signature": "fn main()"
        }]
    });

    // Wrap in full output structure
    let output = json!({
        "repository": {
            "id": format!("repo_{:016x}", 12345),
            "url": "https://github.com/test/repo",
            "commit": "a".repeat(40),
            "branch": "main"
        },
        "analysis": {
            "files": [file_analysis_json],
            "metrics": {
                "total_files": 1,
                "total_lines": 75,
                "total_complexity": 10
            },
            "languages": {
                "primary_language": "rust",
                "languages": {
                    "rust": {
                        "lines": 50,
                        "files": 1,
                        "percentage": 100.0
                    }
                }
            },
            "embeddings": []
        },
        "metadata": {
            "analysis_id": format!("analysis_{:016x}", 123),
            "version": "1.0.0",
            "timestamp": Utc::now().to_rfc3339(),
            "duration_ms": 1000
        }
    });

    let result = compiled.validate(&output);
    if result.is_err() {
        if let Err(errors) = compiled.validate(&output) {
            for error in errors {
                eprintln!("Validation error at {}: {}", error.instance_path, error);
            }
        }
    }
    assert!(result.is_ok(), "FileAnalysis should comply with contract");
}

#[test]
fn test_pattern_detection_contract() {
    // Test pattern detection output matches contract
    let pattern = DetectedPattern {
        pattern_id: "singleton_pattern_001".to_string(),
        pattern_type: PatternType::DesignPattern,
        confidence: 0.95,
        location: PatternLocation {
            file_path: "src/patterns/singleton.rs".to_string(),
            range: Range {
                start: Position { line: 10, column: 0, byte: 200 },
                end: Position { line: 25, column: 1, byte: 500 },
            },
        },
        description: Some("Singleton pattern implementation detected".to_string()),
    };

    // Serialize and validate pattern structure
    let pattern_json = serde_json::to_value(&pattern).expect("Failed to serialize pattern");
    
    // Check required fields exist
    assert!(pattern_json["pattern_id"].is_string());
    assert!(pattern_json["pattern_type"].is_string());
    assert!(pattern_json["confidence"].is_number());
    assert!(pattern_json["location"].is_object());
}

#[test]
fn test_code_embedding_contract() {
    let embedding = CodeEmbedding {
        chunk_id: format!("chunk_{:016x}", 1),
        vector: vec![0.1; 768], // 768-dimensional vector
        model: "text-embedding-ada-002".to_string(),
        metadata: Some(EmbeddingMetadata {
            tokens_used: Some(150),
            created_at: Some(Utc::now()),
        }),
    };

    let embedding_json = serde_json::to_value(&embedding).expect("Failed to serialize embedding");
    
    // Verify vector dimensions
    assert_eq!(embedding_json["vector"].as_array().unwrap().len(), 768);
    assert!(embedding_json["model"].as_str().unwrap().contains("embedding"));
}

#[test]
fn test_invalid_schema_validation() {
    let schema_json: Value = serde_json::from_str(AST_OUTPUT_SCHEMA).expect("Invalid schema JSON");
    let compiled = JSONSchema::options()
        .with_draft(Draft::Draft7)
        .compile(&schema_json)
        .expect("Failed to compile schema");

    // Create invalid output (missing required fields)
    let invalid_output = json!({
        "repository": {
            "url": "https://github.com/test/repo"
            // Missing required fields: id, commit, branch
        },
        "analysis": {
            "files": []
            // Missing required fields: metrics, languages, embeddings
        }
        // Missing required field: metadata
    });

    let result = compiled.validate(&invalid_output);
    assert!(result.is_err(), "Invalid output should fail validation");
}

#[test]
fn test_chunk_id_pattern_validation() {
    // Test that chunk IDs follow the required pattern
    let valid_chunk_id = format!("chunk_{:016x}", 12345);
    assert!(valid_chunk_id.starts_with("chunk_"));
    assert_eq!(valid_chunk_id.len(), 22); // "chunk_" + 16 hex chars

    let invalid_chunk_ids = vec![
        "chunk_123", // Too short
        "chunk_xyz123", // Non-hex characters
        "invalid_0123456789abcdef", // Wrong prefix
    ];

    for invalid_id in invalid_chunk_ids {
        let chunk = CodeChunk {
            chunk_id: invalid_id.to_string(),
            content: "test".to_string(),
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 0, column: 4, byte: 4 },
            },
            chunk_type: ChunkType::Block,
            language: Some("rust".to_string()),
            context: None,
        };

        // This would fail contract validation due to pattern mismatch
        let chunk_json = serde_json::to_value(&chunk).unwrap();
        assert!(!chunk_json["chunk_id"].as_str().unwrap().starts_with("chunk_") 
            || chunk_json["chunk_id"].as_str().unwrap().len() != 22);
    }
}

#[test]
fn test_performance_metrics_contract() {
    let metrics = PerformanceMetrics {
        total_processing_ms: 5000,
        clone_duration_ms: 1000,
        language_detection_ms: 100,
        file_collection_ms: 200,
        ast_parsing_ms: 2000,
        metrics_calculation_ms: 500,
        pattern_detection_ms: 800,
        embedding_generation_ms: 400,
        cleanup_ms: 100,
        files_analyzed: 50,
        successful_parses: 48,
        failed_parses: 2,
        patterns_detected: 15,
        embeddings_generated: 200,
        memory_peak_mb: 1024,
        cpu_usage_percent: 85.5,
    };

    let metrics_json = serde_json::to_value(&metrics).expect("Failed to serialize metrics");
    
    // Verify all timing fields are present and non-negative
    assert!(metrics_json["total_processing_ms"].as_u64().unwrap() >= 0);
    assert!(metrics_json["memory_peak_mb"].as_u64().unwrap() >= 0);
    assert!(metrics_json["cpu_usage_percent"].as_f64().unwrap() >= 0.0);
}

// Helper function to create a valid AST output structure
fn create_valid_ast_output() -> Value {
    json!({
        "repository": {
            "id": format!("repo_{:016x}", 12345),
            "url": "https://github.com/example/repo",
            "commit": "a".repeat(40),
            "branch": "main",
            "size_bytes": 1048576,
            "clone_time_ms": 2500
        },
        "analysis": {
            "files": [{
                "path": "src/main.rs",
                "language": "rust",
                "content_hash": "a".repeat(64),
                "size_bytes": 1024,
                "ast": {
                    "type": "source_file",
                    "range": {
                        "start": {"line": 0, "column": 0, "byte": 0},
                        "end": {"line": 50, "column": 0, "byte": 1024}
                    },
                    "children": []
                },
                "metrics": {
                    "lines_of_code": 40,
                    "complexity": 5,
                    "maintainability_index": 85.0
                }
            }],
            "metrics": {
                "total_files": 1,
                "total_lines": 40,
                "total_complexity": 5,
                "average_complexity": 5.0,
                "maintainability_score": 85.0
            },
            "languages": {
                "primary_language": "rust",
                "languages": {
                    "rust": {
                        "lines": 40,
                        "files": 1,
                        "percentage": 100.0,
                        "bytes": 1024
                    }
                }
            },
            "embeddings": [{
                "chunk_id": format!("chunk_{:016x}", 1),
                "vector": vec![0.1; 768],
                "model": "text-embedding-ada-002",
                "metadata": {
                    "tokens_used": 150,
                    "created_at": Utc::now().to_rfc3339()
                }
            }],
            "patterns": [{
                "pattern_id": "pattern_001",
                "pattern_type": "design_pattern",
                "confidence": 0.95,
                "location": {
                    "file_path": "src/main.rs",
                    "range": {
                        "start": {"line": 10, "column": 0, "byte": 200},
                        "end": {"line": 20, "column": 0, "byte": 400}
                    }
                },
                "description": "Singleton pattern detected"
            }]
        },
        "metadata": {
            "analysis_id": format!("analysis_{:016x}", 123),
            "version": "1.0.0",
            "timestamp": Utc::now().to_rfc3339(),
            "duration_ms": 5000,
            "performance": {
                "parsing_time_ms": 2000,
                "embedding_time_ms": 1000,
                "total_memory_mb": 256.5,
                "files_per_second": 10.0,
                "cache_hit_ratio": 0.85
            },
            "warnings": []
        }
    })
}