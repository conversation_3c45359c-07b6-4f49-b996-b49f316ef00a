#[cfg(test)]
mod integration_tests {
    use futures::StreamExt;
    use reqwest::{StatusCode, Client};
    use serde_json::json;
    use std::time::Duration;
    use tokio::time::sleep;

    const BASE_URL: &str = "http://localhost:8001";
    const API_KEY: &str = "test-api-key";

    fn client_with_auth() -> Client {
        Client::builder()
            .default_headers({
                let mut headers = reqwest::header::HeaderMap::new();
                headers.insert("X-API-Key", API_KEY.parse().unwrap());
                headers
            })
            .timeout(Duration::from_secs(30))
            .build()
            .unwrap()
    }

    #[tokio::test]
    #[ignore = "requires running server"]
    async fn test_health_endpoint() {
        let response = reqwest::get(&format!("{}/health", BASE_URL))
            .await
            .expect("Failed to send request");

        assert_eq!(response.status(), StatusCode::from_u16(200).unwrap());
        
        let body: serde_json::Value = response.json().await.expect("Failed to parse JSON");
        assert_eq!(body["status"], "healthy");
        assert_eq!(body["service"], "analysis-engine");
    }

    #[tokio::test]
    #[ignore = "requires running server"]
    async fn test_supported_languages() {
        let response = reqwest::get(&format!("{}/api/v1/languages", BASE_URL))
            .await
            .expect("Failed to send request");

        assert_eq!(response.status(), StatusCode::from_u16(200).unwrap());
        
        let body: serde_json::Value = response.json().await.expect("Failed to parse JSON");
        let languages = body["languages"].as_array().expect("Expected array");
        
        // Currently we support 8 languages due to tree-sitter version conflicts
        assert!(languages.len() >= 8, "Expected at least 8 languages, got {}", languages.len());
        
        // Verify some key languages are present
        let language_strs: Vec<&str> = languages
            .iter()
            .map(|v| v.as_str().unwrap())
            .collect();
        
        assert!(language_strs.contains(&"rust"));
        assert!(language_strs.contains(&"javascript"));
        assert!(language_strs.contains(&"typescript"));
        assert!(language_strs.contains(&"python"));
        assert!(language_strs.contains(&"go"));
        assert!(language_strs.contains(&"java"));
        assert!(language_strs.contains(&"c"));
        assert!(language_strs.contains(&"cpp"));
    }

    #[tokio::test]
    #[ignore = "requires running server"]
    async fn test_analysis_creation() {
        let client = client_with_auth();
        let request_body = json!({
            "repository_url": "https://github.com/rust-lang/rust-clippy.git",
            "branch": "master",
            "include_patterns": ["src/**/*.rs"],
            "exclude_patterns": ["target/**", "tests/**"],
            "languages": ["rust"],
            "enable_patterns": true,
            "enable_embeddings": false
        });

        let response = client
            .post(&format!("{}/api/v1/analysis", BASE_URL))
            .json(&request_body)
            .send()
            .await
            .expect("Failed to send request");

        assert_eq!(response.status(), StatusCode::from_u16(201).unwrap());
        
        let body: serde_json::Value = response.json().await.expect("Failed to parse JSON");
        assert!(body["analysis_id"].is_string());
        assert_eq!(body["status"], "Pending");
        assert!(body["progress_url"].is_string());
        
        // Store the analysis_id for later tests
        let analysis_id = body["analysis_id"].as_str().unwrap();
        println!("Created analysis: {}", analysis_id);
    }

    #[tokio::test]
    #[ignore = "requires running server"]
    async fn test_concurrent_analysis_limit() {
        // Test that we can handle 50+ concurrent analyses as per PRP
        let client = reqwest::Client::new();
        let mut handles = vec![];

        for i in 0..55 {
            let client = client.clone();
            let handle = tokio::spawn(async move {
                let request_body = json!({
                    "repository_url": format!("https://github.com/example/test{}.git", i),
                    "branch": "main",
                    "include_patterns": ["src/**/*.rs"],
                    "exclude_patterns": ["target/**"],
                    "analysis_depth": "Shallow",
                    "languages": ["rust"],
                    "enable_patterns": false,
                    "enable_embeddings": false
                });

                client
                    .post("http://localhost:8001/api/v1/analysis")
                    .json(&request_body)
                    .send()
                    .await
            });
            handles.push(handle);
        }

        let results = futures::future::join_all(handles).await;
        
        let successful_count = results
            .iter()
            .filter(|r| r.is_ok() && r.as_ref().unwrap().is_ok())
            .count();
        
        // At least 50 should succeed
        assert!(successful_count >= 50, "Expected at least 50 successful requests, got {}", successful_count);
    }

    #[tokio::test]
    #[ignore = "requires running server"]
    async fn test_websocket_connection() {
        use tokio_tungstenite::{connect_async, tungstenite::Message};

        let url = "ws://localhost:8001/ws/analysis/test-id";
        let (ws_stream, _) = connect_async(url)
            .await
            .expect("Failed to connect to WebSocket");

        let (_write, mut read) = ws_stream.split();
        
        // Should receive initial status message
        if let Some(Ok(msg)) = read.next().await {
            match msg {
                Message::Text(text) => {
                    let json: serde_json::Value = serde_json::from_str(&text)
                        .expect("Failed to parse WebSocket message");
                    assert!(json["progress"].is_number());
                    assert!(json["stage"].is_string());
                }
                _ => panic!("Expected text message"),
            }
        }
    }

    #[tokio::test]
    #[ignore = "requires running server"]
    async fn test_full_analysis_workflow() {
        let client = client_with_auth();
        
        // Step 1: Create analysis
        let request_body = json!({
            "repository_url": "https://github.com/tokio-rs/mini-redis.git",
            "branch": "master",
            "include_patterns": ["src/**/*.rs"],
            "exclude_patterns": ["tests/**"],
            "languages": ["rust"],
            "enable_patterns": true,
            "enable_embeddings": false
        });

        let response = client
            .post(&format!("{}/api/v1/analysis", BASE_URL))
            .json(&request_body)
            .send()
            .await
            .expect("Failed to create analysis");

        assert_eq!(response.status(), StatusCode::from_u16(201).unwrap());
        let creation_body: serde_json::Value = response.json().await.unwrap();
        let analysis_id = creation_body["analysis_id"].as_str().unwrap();
        
        // Step 2: Check analysis status
        sleep(Duration::from_secs(5)).await;
        
        let status_response = client
            .get(&format!("{}/api/v1/analysis/{}", BASE_URL, analysis_id))
            .send()
            .await
            .expect("Failed to get analysis status");
            
        assert_eq!(status_response.status(), StatusCode::from_u16(200).unwrap());
        let status_body: serde_json::Value = status_response.json().await.unwrap();
        assert!(status_body["status"].is_string());
        assert!(status_body["progress"].is_number());
        
        // Step 3: List analyses
        let list_response = client
            .get(&format!("{}/api/v1/analyses", BASE_URL))
            .send()
            .await
            .expect("Failed to list analyses");
            
        assert_eq!(list_response.status(), StatusCode::from_u16(200).unwrap());
        let list_body: serde_json::Value = list_response.json().await.unwrap();
        assert!(list_body["analyses"].is_array());
        assert!(list_body["total"].is_number());
    }

    #[tokio::test]
    #[ignore = "requires running server"]
    async fn test_pattern_detection() {
        let client = client_with_auth();
        
        // Create analysis with pattern detection enabled
        let request_body = json!({
            "repository_url": "https://github.com/rust-lang/rust-clippy.git",
            "branch": "master",
            "include_patterns": ["clippy_lints/src/lifetimes.rs"],
            "exclude_patterns": [],
            "languages": ["rust"],
            "enable_patterns": true,
            "enable_embeddings": false
        });

        let response = client
            .post(&format!("{}/api/v1/analysis", BASE_URL))
            .json(&request_body)
            .send()
            .await
            .expect("Failed to create analysis");

        assert_eq!(response.status(), StatusCode::from_u16(201).unwrap());
        let body: serde_json::Value = response.json().await.unwrap();
        let analysis_id = body["analysis_id"].as_str().unwrap();
        
        // Wait for analysis to complete (or timeout)
        let mut attempts = 0;
        loop {
            sleep(Duration::from_secs(10)).await;
            
            let status_response = client
                .get(&format!("{}/api/v1/analysis/{}", BASE_URL, analysis_id))
                .send()
                .await
                .expect("Failed to get analysis status");
                
            let status_body: serde_json::Value = status_response.json().await.unwrap();
            let status = status_body["status"].as_str().unwrap();
            
            if status == "Completed" || status == "Failed" {
                if status == "Completed" {
                    // Check if patterns were detected
                    assert!(status_body["patterns"].is_array());
                    let patterns = status_body["patterns"].as_array().unwrap();
                    println!("Detected {} patterns", patterns.len());
                }
                break;
            }
            
            attempts += 1;
            if attempts > 30 { // 5 minutes timeout
                panic!("Analysis timeout");
            }
        }
    }

    #[tokio::test]
    #[ignore = "requires running server"]
    async fn test_authentication_required() {
        let client = Client::new(); // No auth headers
        
        let response = client
            .get(&format!("{}/api/v1/analyses", BASE_URL))
            .send()
            .await
            .expect("Failed to send request");
            
        assert_eq!(response.status(), StatusCode::from_u16(401).unwrap());
    }

    #[tokio::test]
    #[ignore = "requires running server"]
    async fn test_rate_limiting() {
        let client = client_with_auth();
        
        // Send multiple requests quickly to trigger rate limiting
        let mut responses = vec![];
        for _ in 0..10 {
            let response = client
                .get(&format!("{}/api/v1/languages", BASE_URL))
                .send()
                .await
                .expect("Failed to send request");
            responses.push(response);
        }
        
        // Check for rate limit headers
        for response in &responses {
            assert!(response.headers().contains_key("x-ratelimit-limit"));
            assert!(response.headers().contains_key("x-ratelimit-remaining"));
            assert!(response.headers().contains_key("x-ratelimit-reset"));
        }
    }

    #[tokio::test]
    #[ignore = "requires running server"]
    async fn test_error_handling_invalid_repository() {
        let client = client_with_auth();
        
        let request_body = json!({
            "repository_url": "https://github.com/invalid/does-not-exist.git",
            "branch": "main",
            "include_patterns": ["**/*.rs"],
            "exclude_patterns": [],
            "languages": ["rust"],
            "enable_patterns": false,
            "enable_embeddings": false
        });

        let response = client
            .post(&format!("{}/api/v1/analysis", BASE_URL))
            .json(&request_body)
            .send()
            .await
            .expect("Failed to send request");

        // Should still accept the request
        assert_eq!(response.status(), StatusCode::from_u16(201).unwrap());
        
        let body: serde_json::Value = response.json().await.unwrap();
        let analysis_id = body["analysis_id"].as_str().unwrap();
        
        // Wait and check if it failed
        sleep(Duration::from_secs(30)).await;
        
        let status_response = client
            .get(&format!("{}/api/v1/analysis/{}", BASE_URL, analysis_id))
            .send()
            .await
            .expect("Failed to get status");
            
        let status_body: serde_json::Value = status_response.json().await.unwrap();
        // The analysis should eventually fail
        if status_body["status"].as_str().unwrap() == "Failed" {
            assert!(status_body["error_message"].is_string());
        }
    }

    #[tokio::test]
    #[ignore = "requires running server"]
    async fn test_multi_language_analysis() {
        let client = client_with_auth();
        
        // Test with a multi-language repository
        let request_body = json!({
            "repository_url": "https://github.com/denoland/deno.git",
            "branch": "main",
            "include_patterns": ["cli/**/*.rs", "core/**/*.js", "ext/**/*.ts"],
            "exclude_patterns": ["**/tests/**", "**/testdata/**"],
            "languages": ["rust", "javascript", "typescript"],
            "enable_patterns": true,
            "enable_embeddings": false
        });

        let response = client
            .post(&format!("{}/api/v1/analysis", BASE_URL))
            .json(&request_body)
            .send()
            .await
            .expect("Failed to create analysis");

        assert_eq!(response.status(), StatusCode::from_u16(201).unwrap());
        let body: serde_json::Value = response.json().await.unwrap();
        assert!(body["analysis_id"].is_string());
    }

    #[tokio::test]
    #[ignore = "requires running server"]
    async fn test_webhook_notification() {
        let client = client_with_auth();
        
        // Use webhook.site or similar for testing
        let request_body = json!({
            "repository_url": "https://github.com/serde-rs/serde.git",
            "branch": "master",
            "include_patterns": ["serde/src/lib.rs"],
            "exclude_patterns": [],
            "languages": ["rust"],
            "enable_patterns": false,
            "enable_embeddings": false,
            "webhook_url": "https://webhook.site/test-webhook"
        });

        let response = client
            .post(&format!("{}/api/v1/analysis", BASE_URL))
            .json(&request_body)
            .send()
            .await
            .expect("Failed to create analysis");

        assert_eq!(response.status(), StatusCode::from_u16(201).unwrap());
        let body: serde_json::Value = response.json().await.unwrap();
        assert_eq!(body["webhook_url"], "https://webhook.site/test-webhook");
    }

    #[tokio::test]
    #[ignore = "requires running server"]
    async fn test_cache_performance() {
        let client = client_with_auth();
        
        // First analysis - should be slower
        let request_body = json!({
            "repository_url": "https://github.com/rust-lang/log.git",
            "branch": "master",
            "include_patterns": ["src/**/*.rs"],
            "exclude_patterns": [],
            "languages": ["rust"],
            "enable_patterns": true,
            "enable_embeddings": false
        });

        let start1 = std::time::Instant::now();
        let response1 = client
            .post(&format!("{}/api/v1/analysis", BASE_URL))
            .json(&request_body)
            .send()
            .await
            .expect("Failed to create first analysis");
        let duration1 = start1.elapsed();

        assert_eq!(response1.status(), StatusCode::from_u16(201).unwrap());
        
        // Wait for first analysis to complete
        sleep(Duration::from_secs(60)).await;
        
        // Second analysis of same repo - should be faster due to caching
        let start2 = std::time::Instant::now();
        let response2 = client
            .post(&format!("{}/api/v1/analysis", BASE_URL))
            .json(&request_body)
            .send()
            .await
            .expect("Failed to create second analysis");
        let duration2 = start2.elapsed();

        assert_eq!(response2.status(), StatusCode::from_u16(201).unwrap());
        
        println!("First analysis took: {:?}", duration1);
        println!("Second analysis took: {:?}", duration2);
        
        // Second should be noticeably faster due to caching
        // (though this is not guaranteed in all environments)
    }
}