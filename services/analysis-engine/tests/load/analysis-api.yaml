config:
  target: "http://localhost:8001"
  phases:
    - duration: 30
      arrivalRate: 1
      name: "Warm up"
    - duration: 60
      arrivalRate: 10
      name: "Ramp up"
    - duration: 120
      arrivalRate: 100
      name: "Sustained load"
  processor: "./processor.js"
  variables:
    apiKey: "test-api-key-123"
  
scenarios:
  - name: "Analysis Workflow"
    weight: 60
    flow:
      - post:
          url: "/api/v1/analyze"
          headers:
            x-api-key: "{{ apiKey }}"
            Content-Type: "application/json"
          json:
            repository_url: "https://github.com/test/small-repo"
            branch: "main"
            enable_patterns: true
            enable_embeddings: true
            languages: ["rust", "python", "javascript"]
          capture:
            - json: "$.analysis_id"
              as: "analysisId"
          expect:
            - statusCode: 202
      
      - think: 2
      
      - loop:
        - get:
            url: "/api/v1/analyze/{{ analysisId }}/status"
            headers:
              x-api-key: "{{ apiKey }}"
            expect:
              - statusCode: 200
        - think: 1
        count: 10
      
      - get:
          url: "/api/v1/analyze/{{ analysisId }}"
          headers:
            x-api-key: "{{ apiKey }}"
          expect:
            - statusCode: 200
            - hasProperty: "status"
            - hasProperty: "metrics"

  - name: "Quick Pattern Detection"
    weight: 30
    flow:
      - post:
          url: "/api/v1/analyze"
          headers:
            x-api-key: "{{ apiKey }}"
            Content-Type: "application/json"
          json:
            repository_url: "https://github.com/test/tiny-repo"
            branch: "main"
            enable_patterns: true
            enable_embeddings: false
            languages: ["rust"]
          expect:
            - statusCode: 202

  - name: "Health Check"
    weight: 10
    flow:
      - get:
          url: "/health"
          expect:
            - statusCode: 200
            - contentType: json
            - hasProperty: "status"

# Performance targets
# - <5min for 1M LOC analysis
# - <100ms API response time (p95)
# - Support 100+ concurrent analyses
# - Memory usage <4GB per analysis