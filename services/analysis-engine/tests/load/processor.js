// Artillery processor for Analysis Engine load tests

module.exports = {
  // Generate dynamic test data
  generateTestData: function(context, events, done) {
    // Generate random repository URLs for testing
    const repos = [
      'https://github.com/rust-lang/rust-clippy',
      'https://github.com/tokio-rs/tokio',
      'https://github.com/serde-rs/serde',
      'https://github.com/actix/actix-web',
      'https://github.com/hyperium/hyper'
    ];
    
    context.vars.testRepo = repos[Math.floor(Math.random() * repos.length)];
    context.vars.testBranch = Math.random() > 0.5 ? 'main' : 'master';
    
    // Generate random language selection
    const allLanguages = ['rust', 'python', 'javascript', 'go', 'java'];
    const numLanguages = Math.floor(Math.random() * 3) + 1;
    context.vars.testLanguages = allLanguages
      .sort(() => 0.5 - Math.random())
      .slice(0, numLanguages);
    
    return done();
  },

  // Custom logging for analysis results
  logAnalysisResult: function(context, events, done) {
    const analysisId = context.vars.analysisId;
    const statusCode = context.vars.lastStatusCode;
    
    if (analysisId && statusCode === 200) {
      console.log(`Analysis ${analysisId} completed successfully`);
    }
    
    return done();
  },

  // Validate response times
  checkResponseTime: function(context, events, done) {
    const responseTime = context.vars.$loopIndex;
    
    if (responseTime > 100) {
      console.warn(`Slow response detected: ${responseTime}ms`);
    }
    
    return done();
  }
};