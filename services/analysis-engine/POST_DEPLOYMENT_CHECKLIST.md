# ✅ Analysis Engine Post-Deployment Checklist

## 🚀 Deployment Status
- [x] Code complete and tested (99% ready)
- [x] Documentation complete
- [x] Docker image builds successfully
- [x] All tests passing (31/31)
- [ ] Deployed to Cloud Run
- [ ] Load tests executed
- [ ] Production verification complete

## 📋 Post-Deployment Verification Steps

### 1. Initial Health Checks
```bash
# Get service URL
export SERVICE_URL=$(gcloud run services describe analysis-engine \
  --platform managed \
  --region us-central1 \
  --format 'value(status.url)')

# Basic health check
curl ${SERVICE_URL}/health
# Expected: {"status":"healthy","timestamp":"2024-..."}

# Readiness check (verifies all dependencies)
curl ${SERVICE_URL}/health/ready
# Expected: {"status":"ready","checks":{"database":"ok","storage":"ok",...}}

# Liveness check
curl ${SERVICE_URL}/health/live
# Expected: {"status":"alive","uptime_seconds":...}
```

### 2. Authentication Testing
```bash
# Test with API key (if configured)
curl -H "X-API-Key: test-key" ${SERVICE_URL}/api/v1/patterns

# Test with JWT token
export JWT_TOKEN="your-jwt-token"
curl -H "Authorization: Bearer ${JWT_TOKEN}" ${SERVICE_URL}/api/v1/analyses
```

### 3. Functionality Testing
```bash
# List supported patterns
curl ${SERVICE_URL}/api/v1/patterns

# Start a test analysis
curl -X POST ${SERVICE_URL}/api/v1/analyze \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -d '{
    "repository_url": "https://github.com/rust-lang/rust-clippy",
    "branch": "main",
    "patterns": ["authentication", "security"],
    "languages": ["rust"]
  }'
```

### 4. WebSocket Testing
```javascript
// Test WebSocket connection
const ws = new WebSocket(`${SERVICE_URL.replace('https', 'wss')}/ws/progress/test-id`);
ws.onopen = () => console.log('Connected');
ws.onmessage = (e) => console.log('Progress:', e.data);
ws.onerror = (e) => console.error('Error:', e);
```

### 5. Performance Verification
```bash
# Install Artillery if not already installed
npm install

# Run load tests
cd tests/load
npx artillery run analysis-api.yaml --target ${SERVICE_URL} --output results.json

# Generate report
npx artillery report results.json --output load-test-report.html

# View key metrics
cat results.json | jq '.aggregate.latency'
```

### 6. Monitoring Setup
```bash
# Create uptime check
gcloud monitoring uptime-checks create analysis-engine-health \
  --display-name="Analysis Engine Health Check" \
  --resource-type=gce-instance \
  --hostname=${SERVICE_URL#https://} \
  --path=/health/ready \
  --port=443 \
  --use-ssl

# Set up alerts
gcloud alpha monitoring policies create \
  --display-name="Analysis Engine High Error Rate" \
  --condition-display-name="Error rate > 5%" \
  --condition-filter='resource.type="cloud_run_revision" 
    resource.labels.service_name="analysis-engine" 
    metric.type="run.googleapis.com/request_count"' \
  --condition-threshold-value=0.05
```

### 7. Log Verification
```bash
# Check for startup errors
gcloud logging read "resource.type=cloud_run_revision \
  AND resource.labels.service_name=analysis-engine \
  AND severity>=ERROR" \
  --limit 50

# Monitor request logs
gcloud logging read "resource.type=cloud_run_revision \
  AND resource.labels.service_name=analysis-engine \
  AND httpRequest.status>=400" \
  --limit 20

# Check performance logs
gcloud logging read "resource.type=cloud_run_revision \
  AND resource.labels.service_name=analysis-engine \
  AND jsonPayload.duration_ms>1000" \
  --limit 10
```

### 8. Security Verification
```bash
# Test rate limiting
for i in {1..150}; do
  curl -s -o /dev/null -w "%{http_code}\n" ${SERVICE_URL}/health
done
# Should see 429 (Too Many Requests) after limit

# Verify CORS headers
curl -I -H "Origin: https://app.ccl.dev" ${SERVICE_URL}/health

# Check security headers
curl -I ${SERVICE_URL}/health | grep -E "(X-Content-Type|X-Frame-Options|Strict-Transport)"
```

### 9. Integration Testing
```bash
# Test Spanner connectivity
curl ${SERVICE_URL}/api/v1/analyses \
  -H "Authorization: Bearer ${JWT_TOKEN}"

# Test Cloud Storage integration (via analysis)
# The analysis should successfully store results

# Test Pub/Sub events (check topic for messages)
gcloud pubsub subscriptions pull analysis-events-sub --auto-ack --limit=10
```

### 10. Documentation Updates
- [ ] Update SERVICE_URL in documentation
- [ ] Record actual performance metrics in LOAD_TEST_REPORT.md
- [ ] Update monitoring dashboard links
- [ ] Document any deployment-specific configurations

## 🚨 Common Post-Deployment Issues

### Issue: Service returns 503
**Solution**: Check if all required GCP services are enabled and accessible

### Issue: Authentication failures
**Solution**: Verify JWT_SECRET is set correctly in environment variables

### Issue: Slow performance
**Solution**: Check min-instances setting and consider increasing

### Issue: WebSocket disconnects
**Solution**: Verify Cloud Run timeout settings (should be >300s)

## 📊 Success Criteria

The deployment is considered successful when:
- [ ] All health endpoints return 200 OK
- [ ] Authentication works for both JWT and API keys
- [ ] Sample analysis completes successfully
- [ ] WebSocket progress tracking works
- [ ] Load tests meet performance SLOs:
  - API response time <100ms (p95)
  - Can handle 100+ concurrent analyses
  - Memory usage stays under 4GB
- [ ] No errors in logs for 30 minutes
- [ ] Monitoring and alerts are configured

## 🎉 Final Steps

Once all checks pass:
1. Update the main README with production service URL
2. Notify the team in #platform-deployments
3. Monitor service for 24 hours
4. Schedule load test for peak hours
5. Plan rollout to additional regions if needed

---

**Deployment Date**: _______________
**Deployed By**: _______________
**Service URL**: _______________
**Version**: 1.0.0