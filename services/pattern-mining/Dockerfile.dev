FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Create non-root user
RUN useradd -m -s /bin/bash pydev
RUN chown -R pydev:pydev /app

USER pydev

# Set Python environment
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PATH="/home/<USER>/.local/bin:${PATH}"

# Install Python dependencies
COPY --chown=pydev:pydev requirements.txt* ./
RUN pip install --user --no-cache-dir -r requirements.txt || \
    pip install --user --no-cache-dir \
        fastapi==0.104.1 \
        uvicorn[standard]==0.24.0 \
        pydantic==2.5.0 \
        sqlalchemy==2.0.23 \
        asyncpg==0.29.0 \
        numpy==1.26.2 \
        scikit-learn==1.3.2 \
        pandas==2.1.4

# Copy source code
COPY --chown=pydev:pydev . .

# Create placeholder if main.py doesn't exist
RUN if [ ! -f main.py ]; then \
    echo 'from fastapi import FastAPI\n\
from pydantic import BaseModel\n\
\n\
app = FastAPI(title="Pattern Mining", version="0.1.0")\n\
\n\
class HealthResponse(BaseModel):\n\
    status: str\n\
    service: str\n\
    version: str\n\
\n\
@app.get("/health", response_model=HealthResponse)\n\
async def health_check():\n\
    return HealthResponse(\n\
        status="healthy",\n\
        service="pattern-mining",\n\
        version="0.1.0"\n\
    )\n\
\n\
@app.get("/")\n\
async def root():\n\
    return {"message": "Pattern Mining Service"}\n' > main.py; \
fi

# Development command with auto-reload
CMD ["python", "-m", "uvicorn", "main:app", "--reload", "--host", "0.0.0.0", "--port", "8003"]