FROM golang:1.21-alpine

# Install system dependencies
RUN apk add --no-cache git gcc musl-dev

WORKDIR /app

# Install air for hot reloading
RUN go install github.com/cosmtrek/air@latest

# Create non-root user
RUN adduser -D -s /bin/sh godev
RUN chown -R godev:godev /app

USER godev

# Copy go mod files
COPY --chown=godev:godev go.mod* go.sum* ./
RUN go mod download || true

# Copy source code
COPY --chown=godev:godev . .

# Create placeholder if main.go doesn't exist
RUN if [ ! -f main.go ]; then \
    echo 'package main\n\
\n\
import (\n\
    "encoding/json"\n\
    "fmt"\n\
    "log"\n\
    "net/http"\n\
)\n\
\n\
type HealthResponse struct {\n\
    Status  string `json:"status"`\n\
    Service string `json:"service"`\n\
    Version string `json:"version"`\n\
}\n\
\n\
func healthHandler(w http.ResponseWriter, r *http.Request) {\n\
    response := HealthResponse{\n\
        Status:  "healthy",\n\
        Service: "marketplace",\n\
        Version: "0.1.0",\n\
    }\n\
    w.Header().Set("Content-Type", "application/json")\n\
    json.NewEncoder(w).Encode(response)\n\
}\n\
\n\
func rootHandler(w http.ResponseWriter, r *http.Request) {\n\
    fmt.Fprintf(w, "Marketplace Service")\n\
}\n\
\n\
func main() {\n\
    http.HandleFunc("/health", healthHandler)\n\
    http.HandleFunc("/", rootHandler)\n\
    \n\
    log.Println("Marketplace service listening on :8004")\n\
    if err := http.ListenAndServe(":8004", nil); err != nil {\n\
        log.Fatal(err)\n\
    }\n\
}' > main.go; \
fi

# Create go.mod if it doesn't exist
RUN if [ ! -f go.mod ]; then \
    go mod init github.com/ccl/marketplace; \
fi

# Create air config
RUN echo 'root = "."\n\
testdata_dir = "testdata"\n\
tmp_dir = "tmp"\n\
\n\
[build]\n\
  args_bin = []\n\
  bin = "./tmp/main"\n\
  cmd = "go build -o ./tmp/main ."\n\
  delay = 1000\n\
  exclude_dir = ["assets", "tmp", "vendor", "testdata"]\n\
  exclude_file = []\n\
  exclude_regex = ["_test.go"]\n\
  exclude_unchanged = false\n\
  follow_symlink = false\n\
  full_bin = ""\n\
  include_dir = []\n\
  include_ext = ["go", "tpl", "tmpl", "html"]\n\
  kill_delay = "0s"\n\
  log = "build-errors.log"\n\
  send_interrupt = false\n\
  stop_on_error = true\n\
\n\
[color]\n\
  app = ""\n\
  build = "yellow"\n\
  main = "magenta"\n\
  runner = "green"\n\
  watcher = "cyan"\n\
\n\
[log]\n\
  time = false\n\
\n\
[misc]\n\
  clean_on_exit = false\n\
\n\
[screen]\n\
  clear_on_rebuild = false' > .air.toml

# Development command with hot reloading
CMD ["air", "-c", ".air.toml"]