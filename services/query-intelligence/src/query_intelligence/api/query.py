from fastapi import APIRouter, Depends
from ..models.query import QueryRequest, QueryResult
from ..services.query_processor import QueryProcessor, get_query_processor

router = APIRouter()

@router.post("/query", response_model=QueryResult)
async def process_query(
    request: QueryRequest,
    query_processor: QueryProcessor = Depends(get_query_processor)
):
    return await query_processor.process_query(request)
