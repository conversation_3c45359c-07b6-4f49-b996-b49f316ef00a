import redis.asyncio as redis
from ..config.settings import settings

class RedisClient:
    def __init__(self):
        self.pool = redis.ConnectionPool.from_url(settings.REDIS_URL, max_connections=10)

    async def get(self, key: str):
        r = redis.Redis(connection_pool=self.pool)
        return await r.get(key)

    async def set(self, key: str, value: str, ttl: int = 3600):
        r = redis.Redis(connection_pool=self.pool)
        await r.set(key, value, ex=ttl)

redis_client = RedisClient()
