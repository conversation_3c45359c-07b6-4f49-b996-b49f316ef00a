import pytest
from unittest.mock import AsyncMock, patch
from query_intelligence.clients.analysis_engine import AnalysisEngineClient

@pytest.mark.asyncio
async def test_get_analysis():
    with patch('httpx.AsyncClient') as mock_client:
        mock_response = AsyncMock()
        mock_response.status_code = 200
        
        async def mock_json():
            return {"files": []}
            
        mock_response.json = mock_json
        
        async def mock_raise_for_status():
            pass
            
        mock_response.raise_for_status = mock_raise_for_status
        
        mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        client = AnalysisEngineClient()
        response = await client.get_analysis("test_repo")
        
        assert response == {"files": []}
        mock_client.return_value.__aenter__.return_value.get.assert_called_once_with("http://analysis-engine:8000/analysis/test_repo")
