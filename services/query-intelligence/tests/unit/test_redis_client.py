import pytest
from unittest.mock import AsyncMock, patch
from query_intelligence.clients.redis import RedisClient

@pytest.mark.asyncio
async def test_redis_client():
    with patch('redis.asyncio.Redis.from_url') as mock_from_url:
        mock_redis = AsyncMock()
        mock_from_url.return_value = mock_redis
        
        client = RedisClient()
        await client.set("test_key", "test_value")
        
        mock_redis.set.assert_called_once_with("test_key", "test_value", ex=3600)
        
        await client.get("test_key")
        mock_redis.get.assert_called_once_with("test_key")
