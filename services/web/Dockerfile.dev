FROM node:20-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Create non-root user
RUN useradd -m -s /bin/bash nodedev
RUN chown -R nodedev:nodedev /app

USER nodedev

# Copy package files
COPY --chown=nodedev:nodedev package*.json ./

# Install dependencies
RUN npm install || npm init -y

# Copy source code
COPY --chown=nodedev:nodedev . .

# Create Next.js app if it doesn't exist
RUN if [ ! -f next.config.js ] && [ ! -f pages/index.js ] && [ ! -f app/page.tsx ]; then \
    echo "Creating placeholder Next.js app..."; \
    npm install next@latest react@latest react-dom@latest; \
    mkdir -p pages; \
    echo 'export default function Home() {\n\
  return (\n\
    <div style={{ padding: "2rem", fontFamily: "system-ui" }}>\n\
      <h1>CCL Platform</h1>\n\
      <p>Welcome to the Codebase Context Layer</p>\n\
      <div style={{ marginTop: "2rem" }}>\n\
        <h2>Service Status</h2>\n\
        <ul>\n\
          <li>Analysis Engine: <a href="http://localhost:8001/health">Check</a></li>\n\
          <li>Query Intelligence: <a href="http://localhost:8002/health">Check</a></li>\n\
          <li>Pattern Mining: <a href="http://localhost:8003/health">Check</a></li>\n\
          <li>Marketplace: <a href="http://localhost:8004/health">Check</a></li>\n\
          <li>Collaboration: <a href="http://localhost:8005/health">Check</a></li>\n\
        </ul>\n\
      </div>\n\
      <div style={{ marginTop: "2rem" }}>\n\
        <h2>Development Tools</h2>\n\
        <ul>\n\
          <li><a href="http://localhost:3000">Grafana Dashboard</a></li>\n\
          <li><a href="http://localhost:16686">Jaeger Tracing</a></li>\n\
          <li><a href="http://localhost:9090">Prometheus Metrics</a></li>\n\
        </ul>\n\
      </div>\n\
    </div>\n\
  );\n\
}' > pages/index.js; \
fi

# Update package.json with scripts if needed
RUN if [ -f package.json ] && ! grep -q '"dev"' package.json; then \
    npm pkg set scripts.dev="next dev"; \
    npm pkg set scripts.build="next build"; \
    npm pkg set scripts.start="next start"; \
fi

# Create next.config.js if it doesn't exist
RUN if [ ! -f next.config.js ]; then \
    echo '/** @type {import("next").NextConfig} */\n\
const nextConfig = {\n\
  reactStrictMode: true,\n\
  swcMinify: true,\n\
  async rewrites() {\n\
    return [\n\
      {\n\
        source: "/api/:path*",\n\
        destination: "http://api-gateway/api/:path*",\n\
      },\n\
    ];\n\
  },\n\
};\n\
\n\
module.exports = nextConfig;' > next.config.js; \
fi

# Development command
CMD ["npm", "run", "dev"]