{"name": "@ccl/web", "version": "0.1.0", "description": "CCL Platform Web Frontend", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@tanstack/react-query": "^5.0.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "axios": "^1.6.0", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "typescript": "^5.3.0", "eslint": "^8.55.0", "eslint-config-next": "14.0.4", "prettier": "^3.1.0"}}