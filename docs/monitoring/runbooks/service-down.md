# Runbook: Service Down Alert

## Alert Information
- **Alert Name**: ServiceDown
- **Severity**: Critical
- **Team**: Platform
- **Dashboard**: [Service Overview](https://grafana.ccl.io/d/ccl-service-overview)
- **SLO Impact**: Immediate impact on availability SLO

## Alert Description
A CCL service has been detected as down (health check failing) for more than 2 minutes. This is a critical incident requiring immediate response.

## Immediate Actions (First 5 Minutes)

### 1. Acknowledge Alert
```bash
# Acknowledge in PagerDuty to prevent escalation
# Note the incident number for tracking
```

### 2. Identify Affected Service
```bash
# Check which service is down
kubectl get deployments -n ccl-production | grep -E "0/|Pending|CrashLoop"

# Get pod status
kubectl get pods -n ccl-production -l app=$SERVICE_NAME
```

### 3. Quick Service Recovery Attempt
```bash
# Try restarting the service
kubectl rollout restart deployment/$SERVICE_NAME -n ccl-production

# Watch rollout status
kubectl rollout status deployment/$SERVICE_NAME -n ccl-production --timeout=2m
```

### 4. Impact Communication
Post in #ccl-incidents:
```
🚨 INCIDENT: $SERVICE_NAME is DOWN
Time detected: $(date)
Impact: [Brief description]
Engineer investigating: @your-name
Updates to follow every 15 min
```

## Detailed Investigation (5-15 Minutes)

### 1. Check Pod Events and Logs

```bash
# Get recent events
kubectl describe pods -n ccl-production -l app=$SERVICE_NAME | grep -A 20 Events

# Check container logs
kubectl logs -n ccl-production -l app=$SERVICE_NAME --tail=100 --timestamps

# If pods are crash looping, check previous container logs
kubectl logs -n ccl-production -l app=$SERVICE_NAME --previous --tail=100
```

### 2. Common Failure Patterns

#### A. Image Pull Failures
```bash
# Check for image pull errors
kubectl describe pods -n ccl-production -l app=$SERVICE_NAME | grep -i "pull"

# Verify image exists
gcloud container images list --repository=gcr.io/ccl-platform-production | grep $SERVICE_NAME
```

#### B. Configuration Issues
```bash
# Check ConfigMaps
kubectl get configmap -n ccl-production | grep $SERVICE_NAME
kubectl describe configmap $SERVICE_NAME-config -n ccl-production

# Check Secrets
kubectl get secrets -n ccl-production | grep $SERVICE_NAME
```

#### C. Resource Exhaustion
```bash
# Check node resources
kubectl top nodes
kubectl describe nodes | grep -A 5 "Allocated resources"

# Check if hitting resource limits
kubectl describe deployment $SERVICE_NAME -n ccl-production | grep -A 5 resources
```

#### D. Database Connection Issues
```bash
# Test database connectivity from a debug pod
kubectl run -it --rm debug --image=gcr.io/google.com/cloudsdktool/cloud-sdk:latest --restart=Never -n ccl-production -- bash

# Inside the debug pod:
nslookup spanner.googleapis.com
nc -zv spanner.googleapis.com 443
```

### 3. Check Recent Changes

```bash
# Check recent deployments
kubectl rollout history deployment/$SERVICE_NAME -n ccl-production

# Check recent config changes
kubectl get configmap $SERVICE_NAME-config -n ccl-production -o yaml | grep "resourceVersion"

# Check GitOps repository for recent merges
git log --oneline -n 20 -- k8s/production/$SERVICE_NAME/
```

## Recovery Procedures

### Option 1: Rollback Deployment
If the issue started after a recent deployment:

```bash
# Rollback to previous version
kubectl rollout undo deployment/$SERVICE_NAME -n ccl-production

# Verify rollback
kubectl rollout status deployment/$SERVICE_NAME -n ccl-production

# Confirm service is healthy
kubectl get pods -n ccl-production -l app=$SERVICE_NAME
```

### Option 2: Scale to Different Nodes
If specific nodes are problematic:

```bash
# Cordon problematic nodes
kubectl cordon node-xxx

# Delete pods to force rescheduling
kubectl delete pods -n ccl-production -l app=$SERVICE_NAME

# Watch new pods come up
kubectl get pods -n ccl-production -l app=$SERVICE_NAME -w
```

### Option 3: Emergency Configuration Override
If configuration is the issue:

```bash
# Create emergency override configmap
kubectl create configmap $SERVICE_NAME-emergency -n ccl-production \
  --from-literal=DB_POOL_SIZE=5 \
  --from-literal=TIMEOUT_SECONDS=30

# Patch deployment to use emergency config
kubectl patch deployment $SERVICE_NAME -n ccl-production \
  --patch '{"spec":{"template":{"spec":{"containers":[{"name":"'$SERVICE_NAME'","envFrom":[{"configMapRef":{"name":"'$SERVICE_NAME'-emergency"}}]}]}}}}'
```

### Option 4: Bypass Service (Degraded Mode)
If service cannot be recovered quickly:

```bash
# Update ingress to bypass failed service
kubectl patch ingress ccl-ingress -n ccl-production --type=json \
  -p='[{"op": "replace", "path": "/spec/rules/0/http/paths/0/backend/service/name", "value": "fallback-service"}]'

# Enable maintenance mode
kubectl set env deployment/web -n ccl-production MAINTENANCE_MODE=true
```

## Verification Steps

After recovery attempts:

1. **Health Check**
   ```bash
   # Internal health check
   kubectl exec -it deployment/$SERVICE_NAME -n ccl-production -- wget -qO- http://localhost:8080/health
   
   # External health check
   curl -s https://api.ccl.io/$SERVICE_NAME/health | jq .
   ```

2. **Metrics Verification**
   ```promql
   up{job="ccl-services", service_name="$SERVICE_NAME"}
   ```

3. **Error Rate Check**
   ```promql
   sum(rate(http_requests_total{service_name="$SERVICE_NAME", status=~"5.."}[5m]))
   ```

4. **User Impact Verification**
   - Check support channels for user reports
   - Verify key user journeys are working
   - Monitor business metrics dashboard

## Communication Updates

### Every 15 Minutes During Incident
```
UPDATE: $SERVICE_NAME incident
Status: [Investigating/Implementing Fix/Monitoring]
Current state: [Description]
Next steps: [What you're doing next]
ETA: [Best estimate or "TBD"]
```

### When Resolved
```
✅ RESOLVED: $SERVICE_NAME incident
Duration: XX minutes
Root cause: [Brief description]
Fix: [What fixed it]
Impact: [Users/requests affected]
Post-mortem scheduled for: [Date/time]
```

## Post-Incident Actions

1. **Immediate (Within 1 hour)**
   - Update incident ticket with timeline
   - Ensure monitoring is catching up
   - Verify no secondary impacts

2. **Within 24 hours**
   - Create post-mortem document
   - Schedule post-mortem meeting
   - Create tickets for follow-up work

3. **Within 48 hours**
   - Conduct post-mortem
   - Update runbooks with learnings
   - Implement quick fixes

## Prevention Checklist

- [ ] Service has proper health checks
- [ ] Deployment uses readiness/liveness probes
- [ ] Resource requests/limits are appropriate
- [ ] Auto-scaling is configured
- [ ] Circuit breakers protect dependencies
- [ ] Canary deployments are used
- [ ] Alerts fire before complete failure

## Escalation

- **0-5 minutes**: On-call engineer
- **5-15 minutes**: Team lead + backup on-call
- **15-30 minutes**: Engineering manager
- **30+ minutes**: Director of Engineering
- **Customer impact**: VP of Engineering + Customer Success

## Related Documentation
- [High Error Rate Runbook](./high-error-rate.md)
- [Kubernetes Troubleshooting](https://kubernetes.io/docs/tasks/debug/)
- [GCP Service Health](https://status.cloud.google.com/)
- [Service Architecture](../../architecture/services/)

---

**Last Updated**: 2024-01-07
**Next Review**: 2024-02-07
**Owner**: Platform Team