# Runbook: High Error Rate Alert

## Alert Information
- **Alert Name**: HighErrorRate / CriticalErrorRate
- **Severity**: Warning (>5%) / Critical (>10%)
- **Team**: Platform
- **Dashboard**: [Service Overview](https://grafana.ccl.io/d/ccl-service-overview)

## Alert Description
The service is experiencing an error rate above acceptable thresholds, indicating potential service degradation or failure.

## Impact Assessment

### Immediate Checks
1. **Which service is affected?**
   - Check alert labels for `service_name`
   - Note the environment (production/staging)

2. **What is the current error rate?**
   ```promql
   sum(rate(http_requests_total{service_name="$SERVICE", status=~"5.."}[5m])) 
   / 
   sum(rate(http_requests_total{service_name="$SERVICE"}[5m])) * 100
   ```

3. **How many users are affected?**
   - Check unique user count in errors
   - Review customer support channels

## Investigation Steps

### 1. Check Recent Deployments
First, determine if this correlates with a recent deployment:

```bash
# Check deployment history
kubectl rollout history deployment/$SERVICE_NAME -n ccl-production

# Get deployment times
kubectl get deployments -n ccl-production -l app=$SERVICE_NAME -o wide

# Check if deployment was recent (within last hour)
kubectl describe deployment/$SERVICE_NAME -n ccl-production | grep -A5 "Events:"
```

### 2. Analyze Error Patterns

Query BigQuery for error details:
```sql
-- Get error distribution for the service
SELECT 
  error_type,
  COUNT(*) as error_count,
  COUNT(DISTINCT user_id) as affected_users,
  ARRAY_AGG(DISTINCT message IGNORE NULLS LIMIT 5) as sample_messages,
  MIN(timestamp) as first_occurrence,
  MAX(timestamp) as last_occurrence
FROM `ccl-platform-production.logs.application_logs`
WHERE service_name = 'SERVICE_NAME'
  AND severity IN ('ERROR', 'CRITICAL')
  AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
GROUP BY error_type
ORDER BY error_count DESC;

-- Get specific error traces
SELECT 
  timestamp,
  message,
  stack_trace,
  trace_id,
  JSON_EXTRACT_SCALAR(metadata, '$.endpoint') as endpoint,
  JSON_EXTRACT_SCALAR(metadata, '$.user_id') as user_id
FROM `ccl-platform-production.logs.application_logs`
WHERE service_name = 'SERVICE_NAME'
  AND severity IN ('ERROR', 'CRITICAL')
  AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 MINUTE)
ORDER BY timestamp DESC
LIMIT 20;
```

### 3. Check Service Dependencies

Verify all dependencies are healthy:

```bash
# Check database connectivity
kubectl exec -it deployment/$SERVICE_NAME -n ccl-production -- nc -zv spanner.googleapis.com 443

# Check Redis connectivity (if applicable)
kubectl exec -it deployment/$SERVICE_NAME -n ccl-production -- redis-cli -h redis-service ping

# Check external API health
curl -s https://status.stripe.com/api/v2/status.json | jq '.status.indicator'
curl -s https://status.cloud.google.com/incidents.json | jq '.[0:5]'
```

### 4. Resource Constraints

Check if the service is resource-constrained:

```bash
# CPU and Memory usage
kubectl top pods -n ccl-production -l app=$SERVICE_NAME

# Check for OOM kills
kubectl get events -n ccl-production --field-selector reason=OOMKilling

# Check pod restarts
kubectl get pods -n ccl-production -l app=$SERVICE_NAME
```

### 5. Examine Distributed Traces

1. Open [Cloud Trace](https://console.cloud.google.com/trace)
2. Filter by:
   - Service: $SERVICE_NAME
   - Time range: Last hour
   - Status: Error
3. Look for patterns in failing traces
4. Identify common failure points

## Remediation Steps

### Quick Fixes

#### 1. If Related to Recent Deployment
```bash
# Rollback to previous version
kubectl rollout undo deployment/$SERVICE_NAME -n ccl-production

# Verify rollback
kubectl rollout status deployment/$SERVICE_NAME -n ccl-production

# Monitor error rate after rollback
```

#### 2. If Resource Constrained
```bash
# Scale up temporarily
kubectl scale deployment/$SERVICE_NAME -n ccl-production --replicas=10

# Or increase resource limits
kubectl set resources deployment/$SERVICE_NAME -n ccl-production \
  --limits=cpu=2,memory=4Gi \
  --requests=cpu=1,memory=2Gi
```

#### 3. If Database Connection Issues
```bash
# Restart pods to reset connections
kubectl rollout restart deployment/$SERVICE_NAME -n ccl-production

# Check connection pool settings
kubectl describe configmap $SERVICE_NAME-config -n ccl-production
```

#### 4. If External API Issues
- Enable circuit breaker if not already enabled
- Increase timeout values temporarily
- Switch to cached/degraded mode if available

### Long-term Fixes

1. **Code Fixes**
   - Review error logs for code issues
   - Implement proper error handling
   - Add retry logic with exponential backoff
   - Improve input validation

2. **Capacity Planning**
   - Review service resource allocation
   - Implement auto-scaling policies
   - Optimize database queries
   - Add caching where appropriate

3. **Monitoring Improvements**
   - Add more granular error metrics
   - Implement canary deployments
   - Add pre-deployment load testing
   - Improve health check coverage

## Communication

### During Incident
1. Update #ccl-incidents channel every 15 minutes
2. Create incident ticket: [Incident Template](https://jira.ccl.io/secure/CreateIssue!default.jspa?pid=10001&issuetype=1)
3. Page additional engineers if needed via PagerDuty

### Post-Incident
1. Schedule post-mortem within 48 hours
2. Document timeline and root cause
3. Create follow-up tickets for fixes
4. Update this runbook with learnings

## Prevention Measures

### Deployment Best Practices
- Always use canary deployments for critical services
- Run load tests before major changes
- Monitor error budgets closely
- Implement feature flags for risky changes

### Code Quality
- Increase test coverage for error paths
- Implement comprehensive error handling
- Use circuit breakers for external dependencies
- Add request validation and rate limiting

## Related Documentation
- [Service Down Runbook](./service-down.md)
- [Deployment Procedures](../../deployment/procedures.md)
- [Error Budget Policy](../slo-management.md)
- [Post-Mortem Template](../../templates/post-mortem.md)

## Metrics Reference

Key metrics to monitor during incident:
- `http_requests_total{status=~"5.."}`
- `http_request_duration_seconds`
- `active_requests`
- `errors_total`
- `circuit_breaker_state`

## Escalation Path

1. **First 30 minutes**: On-call engineer
2. **After 30 minutes**: Team lead
3. **After 1 hour**: Engineering manager
4. **Critical customer impact**: VP of Engineering

---

**Last Updated**: 2024-01-07
**Next Review**: 2024-02-07