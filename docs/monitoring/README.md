# CCL Monitoring & Observability Guide

## Overview

The CCL platform implements a comprehensive monitoring and observability stack that provides real-time insights into system health, performance, and user experience. This guide covers the monitoring architecture, instrumentation, dashboards, alerting, and operational procedures.

## Quick Start

### Accessing Monitoring Tools

- **Grafana Dashboards**: https://grafana.ccl.io
- **Prometheus**: https://prometheus.ccl.io
- **BigQuery Logs**: https://console.cloud.google.com/bigquery?project=ccl-platform-production
- **Cloud Trace**: https://console.cloud.google.com/trace?project=ccl-platform-production
- **Alert Manager**: https://alerts.ccl.io

### Key Dashboards

1. **Service Overview**: Overall platform health
2. **Repository Analysis**: Analysis engine performance
3. **Query Intelligence**: AI query processing metrics
4. **Pattern Detection**: ML pattern mining statistics
5. **Marketplace**: Commerce and transaction metrics
6. **SLO Dashboard**: Service level objectives tracking

## Architecture

### Components

1. **Collection Layer**
   - OpenTelemetry Collectors (metrics, traces)
   - Fluent Bit (logs)
   - Prometheus exporters

2. **Storage Layer**
   - Google Cloud Monitoring (metrics)
   - BigQuery (logs)
   - Cloud Trace (distributed traces)
   - Redis/Memorystore (cache)

3. **Visualization Layer**
   - Grafana (dashboards)
   - Custom UI (business metrics)
   - Alert Manager (notifications)

### Data Flow

```
Services → OpenTelemetry SDK → Collectors → Storage → Visualization
         → Fluent Bit → BigQuery → Analysis
```

## Service Instrumentation

### Adding Metrics to Your Service

#### Rust Service
```rust
use crate::instrumentation::{HTTP_REQUEST_COUNT, track_http_request};

// In your handler
track_http_request("POST", "/api/analyze", async {
    // Your logic here
}).await?;
```

#### Python Service
```python
from instrumentation import track_query_processing

# In your handler
async def process_query(query: str):
    start_time = time.time()
    result = await _process(query)
    
    await track_query_processing(
        query_type="natural_language",
        confidence=result.confidence,
        duration_ms=(time.time() - start_time) * 1000
    )
    return result
```

#### Go Service
```go
import "github.com/ccl/instrumentation"

// In your handler
func (h *Handler) CreatePattern(c *gin.Context) {
    ctx := c.Request.Context()
    instrumentation.TrackPatternPublication(ctx, patternID, userID, category)
}
```

#### TypeScript Service
```typescript
import { trackAPICall, withSpan } from '@ccl/instrumentation';

// In your handler
export async function handleRequest(req: Request): Promise<Response> {
  return withSpan('handleRequest', async (span) => {
    const start = Date.now();
    const response = await processRequest(req);
    
    trackAPICall(
      req.path,
      req.method,
      Date.now() - start,
      response.status
    );
    
    return response;
  });
}
```

## Creating Custom Metrics

### 1. Define the Metric

Add to the appropriate instrumentation library:

```python
# Python example
my_custom_metric = meter.create_histogram(
    name="custom_operation_duration",
    description="Duration of custom operation",
    unit="ms"
)
```

### 2. Record Values

```python
my_custom_metric.record(
    duration_ms,
    {"operation": "type_a", "status": "success"}
)
```

### 3. Add to Dashboard

Update the relevant dashboard JSON to include your metric.

## Logging Best Practices

### Structured Logging

Always use structured logging with consistent fields:

```python
logger.info(
    "Operation completed",
    extra={
        "operation": "pattern_detection",
        "duration_ms": 150,
        "pattern_count": 5,
        "repository_id": "repo123",
        "trace_id": get_current_trace_id()
    }
)
```

### Log Levels

- **DEBUG**: Detailed debugging information
- **INFO**: General informational messages
- **WARNING**: Warning messages for unusual but handled conditions
- **ERROR**: Error messages for failures
- **CRITICAL**: Critical failures requiring immediate attention

### Sensitive Data

Never log sensitive information. The logging pipeline automatically sanitizes:
- Passwords
- API keys
- Credit card numbers
- Email addresses
- JWT tokens

## Alerts

### Alert Hierarchy

1. **Critical**: Service down, data loss risk, security breach
2. **Warning**: High error rate, approaching limits, degraded performance
3. **Info**: Deployment notifications, scheduled maintenance

### Response Times

- **Critical**: Respond within 5 minutes
- **Warning**: Respond within 30 minutes
- **Info**: Review during business hours

### On-Call Procedures

1. Check the specific alert runbook (see `/docs/monitoring/runbooks/`)
2. Assess impact using dashboards
3. Follow mitigation steps
4. Update incident channel
5. Create post-mortem for Critical alerts

## SLO Management

### Current SLOs

| Service | SLO Type | Target | Window |
|---------|----------|--------|--------|
| API | Availability | 99.9% | 30d |
| API | Latency (P95 < 100ms) | 95% | 30d |
| Query Intelligence | Accuracy | 85% | 30d |
| Marketplace | Transaction Success | 99.9% | 30d |

### Error Budget Policy

When error budget consumption reaches:
- **50%**: Restrict to critical deployments only
- **75%**: Engineering leads notified, deployment freeze
- **90%**: All hands on deck, complete freeze

## Querying Logs

### BigQuery Examples

Find errors in the last hour:
```sql
SELECT timestamp, service_name, message, trace_id
FROM `ccl-platform-production.logs.application_logs`
WHERE severity IN ('ERROR', 'CRITICAL')
  AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
ORDER BY timestamp DESC
LIMIT 100
```

Track user journey:
```sql
SELECT timestamp, service_name, message
FROM `ccl-platform-production.logs.application_logs`
WHERE user_id = 'user123'
  AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
ORDER BY timestamp
```

### Common Queries

See `/infrastructure/monitoring/logging/queries/common-queries.sql` for pre-built queries.

## Troubleshooting

### Service Not Appearing in Dashboards

1. Verify service is exposing metrics endpoint
2. Check Prometheus scrape configuration
3. Ensure service has correct labels
4. Verify network connectivity

### Missing Logs

1. Check Fluent Bit DaemonSet status
2. Verify service is outputting JSON logs
3. Check BigQuery ingestion status
4. Review Fluent Bit logs for errors

### High Cardinality Metrics

Symptoms: Prometheus memory usage high, slow queries

Solutions:
1. Review metric labels for high cardinality
2. Remove unnecessary labels
3. Use recording rules for aggregations

## Cost Optimization

### Current Costs (Monthly Estimate)

- Metrics storage: $500
- Log storage: $1,000
- Trace storage: $300
- Data transfer: $200
- Total: ~$2,000/month

### Optimization Strategies

1. **Sampling**: Reduce trace sampling for non-critical paths
2. **Retention**: 
   - Metrics: 6 months hot, 2 years cold
   - Logs: 30 days hot, 90 days cold, 1 year archive
3. **Aggregation**: Pre-aggregate metrics for dashboards
4. **Filtering**: Don't log health checks or static assets

## Adding New Services

### Checklist

- [ ] Add OpenTelemetry instrumentation
- [ ] Configure structured logging
- [ ] Add Prometheus annotations to Kubernetes
- [ ] Create service dashboard
- [ ] Add service-specific alerts
- [ ] Document runbooks
- [ ] Add to SLO definitions
- [ ] Update cost projections

### Example Configuration

See `/examples/monitoring/new-service-setup.md` for complete example.

## Emergency Procedures

### Complete Monitoring Outage

1. Check collector pods: `kubectl get pods -n ccl-system | grep otel`
2. Verify GCP quotas not exceeded
3. Check network connectivity to GCP
4. Failover to local Prometheus if needed

### Dashboard Recovery

All dashboards are version controlled. To restore:

```bash
cd infrastructure/monitoring/dashboards
kubectl apply -f service-overview.json
```

## Contact

- **On-Call**: PagerDuty or #ccl-oncall in Slack
- **Monitoring Team**: #ccl-monitoring in Slack
- **Escalation**: <EMAIL>

## Additional Resources

- [Runbooks](/docs/monitoring/runbooks/) - Alert response procedures
- [OpenTelemetry Docs](https://opentelemetry.io/docs/)
- [Grafana Best Practices](https://grafana.com/docs/grafana/latest/best-practices/)
- [BigQuery SQL Reference](https://cloud.google.com/bigquery/docs/reference/standard-sql/)