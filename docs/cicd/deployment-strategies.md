# Deployment Strategies for CCL Platform

## Overview

The CCL platform uses different deployment strategies based on the environment and service criticality. This document outlines the strategies, their use cases, and implementation details.

## Strategy Types

### 1. Rolling Deployment

**Use Case**: Development environment, non-critical services
**Risk Level**: Low
**Downtime**: None (if properly configured)

#### How It Works
1. Deploy new version alongside old version
2. Gradually replace old instances with new ones
3. Health checks ensure new instances are ready before removing old ones

#### Configuration
```yaml
deployment:
  strategy: rolling
  max_unavailable: 25%
  max_surge: 25%
  health_check_timeout: 300s
```

#### Pros
- Zero downtime
- Simple to implement
- Automatic rollback on health check failure
- Resource efficient

#### Cons
- Mixed versions running simultaneously
- Potential compatibility issues
- Slower rollback process

#### Implementation Example
```bash
# Cloud Run rolling deployment
gcloud run deploy analysis-engine \
  --image=gcr.io/ccl-platform/analysis-engine:v1.2.3 \
  --region=us-central1 \
  --platform=managed
```

### 2. Canary Deployment

**Use Case**: Staging environment, gradual production rollouts
**Risk Level**: Medium
**Downtime**: None

#### How It Works
1. Deploy new version with minimal traffic (5-10%)
2. Monitor metrics and user feedback
3. Gradually increase traffic if metrics are healthy
4. Complete rollout or rollback based on results

#### Traffic Progression
```
Initial: 5% new version, 95% old version
Stage 1: 10% new version, 90% old version
Stage 2: 25% new version, 75% old version
Stage 3: 50% new version, 50% old version
Final:   100% new version
```

#### Configuration
```yaml
deployment:
  strategy: canary
  initial_traffic_percentage: 5
  stages: [10, 25, 50, 100]
  stage_duration: 10m
  success_criteria:
    error_rate_threshold: 0.01  # 1%
    latency_p95_threshold: 1000  # 1 second
```

#### Monitoring During Canary
```bash
# Monitor error rates
gcloud logging read \
  "resource.type=cloud_run_revision AND severity>=ERROR" \
  --limit=100 --freshness=5m

# Monitor latency
gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/request_latencies"' \
  --interval.start-time=$(date -u -d '5 minutes ago' +%Y-%m-%dT%H:%M:%SZ) \
  --interval.end-time=$(date -u +%Y-%m-%dT%H:%M:%SZ)
```

#### Pros
- Early detection of issues
- Limited blast radius
- Real user feedback
- Data-driven decisions

#### Cons
- Complex monitoring required
- Longer deployment time
- Mixed version complexity
- Requires traffic splitting capability

### 3. Blue-Green Deployment

**Use Case**: Production environment, critical services
**Risk Level**: Low (with proper validation)
**Downtime**: Minimal (during traffic switch)

#### How It Works
1. Deploy new version to "green" environment
2. Run comprehensive tests on green environment
3. Switch traffic from "blue" to "green" instantly
4. Keep blue environment as immediate rollback option

#### Environment Setup
```
Blue Environment (Current Production):
- analysis-engine-blue
- 100% traffic

Green Environment (New Version):
- analysis-engine-green
- 0% traffic (during validation)
```

#### Configuration
```yaml
deployment:
  strategy: blue-green
  validation_tests:
    - smoke_tests
    - integration_tests
    - performance_tests
  switch_timeout: 60s
  rollback_timeout: 30s
```

#### Implementation Steps
```bash
# 1. Deploy to green environment
gcloud run deploy analysis-engine-green \
  --image=gcr.io/ccl-platform/analysis-engine:v1.2.3 \
  --no-traffic \
  --tag=green

# 2. Run validation tests
make smoke-test ENV=green

# 3. Switch traffic
gcloud run services update-traffic analysis-engine \
  --to-tags=green=100 \
  --region=us-central1

# 4. Monitor and rollback if needed
if [[ $(check_error_rate) -gt 5 ]]; then
  gcloud run services update-traffic analysis-engine \
    --to-tags=blue=100 \
    --region=us-central1
fi
```

#### Pros
- Instant rollback capability
- Full validation before switch
- No mixed versions in production
- Predictable deployment process

#### Cons
- Requires double resources
- More complex infrastructure
- Database migration challenges
- Higher cost

## Service-Specific Strategies

### Analysis Engine (Rust)
- **Development**: Rolling
- **Staging**: Canary (25% initial)
- **Production**: Blue-Green

**Rationale**: Critical service requiring high reliability

### Query Intelligence (Python)
- **Development**: Rolling
- **Staging**: Canary (10% initial)
- **Production**: Canary (5% initial)

**Rationale**: AI service where gradual validation is important

### Pattern Mining (Python)
- **Development**: Rolling
- **Staging**: Canary (20% initial)
- **Production**: Blue-Green

**Rationale**: ML service with potential for unexpected behavior

### Marketplace (Go)
- **Development**: Rolling
- **Staging**: Canary (25% initial)
- **Production**: Blue-Green

**Rationale**: Financial transactions require high reliability

### Web Frontend (TypeScript)
- **Development**: Rolling
- **Staging**: Canary (50% initial)
- **Production**: Canary (10% initial)

**Rationale**: User-facing service benefits from gradual rollout

### Collaboration (TypeScript)
- **Development**: Rolling
- **Staging**: Canary (25% initial)
- **Production**: Blue-Green

**Rationale**: Real-time service requires careful validation

## Rollback Procedures

### Automatic Rollback Triggers

1. **Health Check Failures**
   ```yaml
   health_check:
     failure_threshold: 3
     timeout: 30s
     interval: 10s
   ```

2. **Error Rate Threshold**
   ```yaml
   error_rate:
     threshold: 5%  # 5% error rate
     duration: 2m   # Over 2 minutes
   ```

3. **Latency Threshold**
   ```yaml
   latency:
     p95_threshold: 2000ms
     duration: 5m
   ```

4. **Custom Metrics**
   ```yaml
   custom_metrics:
     - name: business_metric_drop
       threshold: 10%
       duration: 3m
   ```

### Manual Rollback

```bash
# Emergency rollback script
./scripts/deploy/rollback.sh <service> <environment> [version]

# Examples
./scripts/deploy/rollback.sh analysis-engine production
./scripts/deploy/rollback.sh marketplace staging v1.2.2
```

### Rollback Validation

After rollback, verify:
1. Service health endpoints
2. Key business metrics
3. User-facing functionality
4. Integration points

```bash
# Validation script
./scripts/validate-rollback.sh <service> <environment>
```

## Advanced Deployment Patterns

### Feature Flags Integration

Use feature flags to decouple deployment from feature release:

```yaml
feature_flags:
  new_pattern_detection:
    enabled: false
    rollout_percentage: 0
    
  advanced_query_features:
    enabled: true
    rollout_percentage: 25
```

### Database Migration Strategy

For services with database changes:

1. **Backward Compatible Changes**
   - Deploy code first
   - Run migration after deployment
   - Clean up old columns/tables later

2. **Breaking Changes**
   - Use blue-green with database branching
   - Coordinate with DBA team
   - Plan rollback strategy

### Multi-Region Deployment

For global services:

1. **Region Priority**
   ```
   Primary: us-central1
   Secondary: europe-west1
   Tertiary: asia-southeast1
   ```

2. **Deployment Order**
   - Deploy to secondary regions first
   - Monitor for issues
   - Deploy to primary region last

3. **Traffic Management**
   ```yaml
   traffic_routing:
     us-central1: 60%
     europe-west1: 25%
     asia-southeast1: 15%
   ```

## Monitoring and Metrics

### Key Deployment Metrics

1. **Deployment Frequency**
   - Target: Multiple times per day
   - Measure: Deployments per day per service

2. **Lead Time**
   - Target: < 2 hours from commit to production
   - Measure: Time from commit to deployment

3. **Mean Time to Recovery (MTTR)**
   - Target: < 1 hour
   - Measure: Time from incident to resolution

4. **Change Failure Rate**
   - Target: < 5%
   - Measure: Percentage of deployments causing issues

### Deployment Dashboard

Monitor deployments in real-time:
- [CCL Deployment Dashboard](https://console.cloud.google.com/monitoring/dashboards/custom/ccl-deployments)

### Alerts

Critical deployment alerts:
- Deployment failure
- Rollback executed
- High error rate post-deployment
- Deployment taking too long

## Best Practices

### Pre-Deployment

1. **Code Review**: Require 2+ approvals
2. **Testing**: Comprehensive test suite
3. **Security Scan**: No critical vulnerabilities
4. **Performance Test**: Validate under load

### During Deployment

1. **Monitor Actively**: Watch dashboards
2. **Communicate**: Update team channels
3. **Document**: Record deployment details
4. **Be Ready**: Prepared to rollback

### Post-Deployment

1. **Validate**: Run smoke tests
2. **Monitor**: Watch metrics for 30+ minutes
3. **Communicate**: Confirm success
4. **Learn**: Document lessons learned

### Emergency Procedures

1. **Stop Deployment**: If issues detected
2. **Rollback Immediately**: Don't wait
3. **Investigate**: Find root cause
4. **Fix Forward**: Plan proper fix
5. **Post-Mortem**: Learn and improve

## Troubleshooting

### Common Issues

1. **Deployment Stuck**
   ```bash
   # Check deployment status
   gcloud run services describe <service> --region=us-central1
   
   # Cancel if needed
   gcloud run services update-traffic <service> --to-revisions=LATEST=100
   ```

2. **Health Checks Failing**
   ```bash
   # Check logs
   gcloud logging read "resource.type=cloud_run_revision" --limit=50
   
   # Test health endpoint
   curl -f https://<service-url>/health
   ```

3. **Traffic Not Switching**
   ```bash
   # Verify traffic allocation
   gcloud run services describe <service> --format="value(status.traffic)"
   
   # Force traffic switch
   gcloud run services update-traffic <service> --to-tags=<version>=100
   ```

For more information, see:
- [Monitoring Guide](./monitoring.md)
- [Rollback Procedures](./rollback-procedures.md)
- [Emergency Response](./emergency-response.md)
