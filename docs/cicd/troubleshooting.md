# CI/CD Pipeline Troubleshooting Guide

## Overview

This guide provides solutions to common issues encountered in the CCL CI/CD pipeline. Issues are organized by category with step-by-step resolution procedures.

## Quick Diagnosis

### Pipeline Status Check
```bash
# Check overall pipeline health
make ci-validate

# Check specific service
make health-check SERVICE=analysis-engine ENV=staging

# View recent deployments
gcloud logging read "jsonPayload.event_type=\"deployment\"" --limit=10
```

### Common Symptoms and Quick Fixes

| Symptom | Likely Cause | Quick Fix |
|---------|--------------|-----------|
| Build fails immediately | Dependency issue | `make clean && make deps` |
| Tests timeout | Resource constraints | Check runner capacity |
| Deployment hangs | Health check failure | Check service logs |
| Security scan fails | New vulnerability | Review and fix CVEs |
| Quality gate fails | Code quality drop | Run `make lint && make test` |

## Build Issues

### 1. Dependency Installation Failures

#### Rust Dependencies
```bash
# Symptoms
error: failed to download `serde v1.0.136`
error: unable to get packages from source

# Diagnosis
cargo tree --duplicates
cargo audit

# Solutions
# Clear cargo cache
rm -rf ~/.cargo/registry
rm -rf ~/.cargo/git

# Update Cargo.lock
cargo update

# Check for conflicting versions
cargo tree --duplicates
```

#### Python Dependencies
```bash
# Symptoms
ERROR: Could not find a version that satisfies the requirement
ERROR: No matching distribution found

# Diagnosis
pip check
pip list --outdated

# Solutions
# Clear pip cache
pip cache purge

# Update requirements
pip-compile requirements.in

# Use specific index
pip install --index-url https://pypi.org/simple/
```

#### Go Dependencies
```bash
# Symptoms
go: module example.com/module: reading at revision v1.0.0: unknown revision
go: downloading module: module lookup disabled

# Diagnosis
go mod verify
go mod graph

# Solutions
# Clean module cache
go clean -modcache

# Update dependencies
go get -u ./...
go mod tidy

# Use specific version
go get example.com/module@v1.2.3
```

#### TypeScript Dependencies
```bash
# Symptoms
npm ERR! peer dep missing
npm ERR! 404 Not Found

# Diagnosis
npm ls
npm audit

# Solutions
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Fix peer dependencies
npm install --legacy-peer-deps
```

### 2. Build Tool Issues

#### Out of Memory Errors
```bash
# Symptoms
fatal error: runtime: out of memory
Error: JavaScript heap out of memory

# Solutions
# Increase memory for Node.js
export NODE_OPTIONS="--max-old-space-size=4096"

# Increase memory for Java/Gradle
export GRADLE_OPTS="-Xmx4g"

# Use swap file on runners
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

#### Disk Space Issues
```bash
# Symptoms
No space left on device
Error: ENOSPC: no space left on device

# Diagnosis
df -h
du -sh * | sort -hr

# Solutions
# Clean Docker images
docker system prune -af

# Clean build artifacts
make clean
rm -rf target/ dist/ node_modules/

# Clean package caches
npm cache clean --force
pip cache purge
cargo clean
```

### 3. Compilation Errors

#### Rust Compilation Issues
```bash
# Common errors and solutions

# Error: linking with `cc` failed
# Solution: Install build tools
sudo apt-get update
sudo apt-get install build-essential

# Error: could not find system library 'openssl'
# Solution: Install OpenSSL development headers
sudo apt-get install libssl-dev pkg-config

# Error: failed to run custom build command for `ring`
# Solution: Install additional dependencies
sudo apt-get install clang llvm-dev libclang-dev
```

#### Python Build Issues
```bash
# Error: Microsoft Visual C++ 14.0 is required
# Solution: Install build tools
pip install --upgrade setuptools wheel

# Error: Failed building wheel for package
# Solution: Install system dependencies
sudo apt-get install python3-dev build-essential

# Error: No module named '_ctypes'
# Solution: Install libffi
sudo apt-get install libffi-dev
```

## Test Issues

### 1. Test Failures

#### Flaky Tests
```bash
# Identify flaky tests
# Run tests multiple times
for i in {1..10}; do make test || echo "Failed on run $i"; done

# Solutions
# Add retry logic
pytest --maxfail=1 --tb=short -x tests/

# Increase timeouts
pytest --timeout=300 tests/

# Use test isolation
pytest --forked tests/
```

#### Integration Test Failures
```bash
# Common issues
# Database connection failures
# External service unavailable
# Network timeouts

# Solutions
# Check service dependencies
docker-compose ps
kubectl get pods

# Verify network connectivity
curl -f http://test-database:5432
nc -zv external-service.com 443

# Use test doubles
export USE_MOCK_SERVICES=true
```

### 2. Test Environment Issues

#### Database Setup Problems
```bash
# Symptoms
Connection refused
Authentication failed
Database does not exist

# Solutions
# Reset test database
make test-db-reset

# Check database logs
docker logs test-postgres

# Verify credentials
psql -h localhost -U testuser -d testdb
```

#### Service Discovery Issues
```bash
# Symptoms
Service not found
Connection timeout
DNS resolution failed

# Solutions
# Check service registration
consul members
kubectl get services

# Verify DNS
nslookup service-name
dig service-name.default.svc.cluster.local
```

## Deployment Issues

### 1. Cloud Run Deployment Failures

#### Image Pull Errors
```bash
# Symptoms
Failed to pull image
Image not found
Authentication required

# Diagnosis
gcloud container images list --repository=gcr.io/ccl-platform

# Solutions
# Verify image exists
gcloud container images describe gcr.io/ccl-platform/service:tag

# Check authentication
gcloud auth configure-docker

# Verify permissions
gcloud projects get-iam-policy ccl-platform
```

#### Resource Allocation Issues
```bash
# Symptoms
Insufficient CPU
Insufficient memory
Request timeout

# Solutions
# Increase resource limits
gcloud run deploy service \
  --memory=4Gi \
  --cpu=2 \
  --timeout=900

# Check resource usage
gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/container/memory/utilizations"'
```

### 2. Health Check Failures

#### Service Not Ready
```bash
# Symptoms
Health check failed
Service unavailable
Connection refused

# Diagnosis
# Check service logs
gcloud logging read "resource.type=cloud_run_revision" --limit=50

# Test health endpoint locally
curl -f http://localhost:8080/health

# Solutions
# Increase startup time
gcloud run deploy service --timeout=900

# Fix health check endpoint
# Ensure /health returns 200 OK

# Check dependencies
# Verify database connectivity
# Verify external service availability
```

#### Database Connection Issues
```bash
# Symptoms
Connection pool exhausted
Database timeout
Authentication failed

# Solutions
# Check connection string
echo $DATABASE_URL

# Verify database accessibility
gcloud sql connect instance-name --user=username

# Increase connection pool
export DB_POOL_SIZE=20
export DB_TIMEOUT=30
```

### 3. Traffic Routing Issues

#### Load Balancer Problems
```bash
# Symptoms
502 Bad Gateway
504 Gateway Timeout
SSL certificate errors

# Diagnosis
# Check load balancer status
gcloud compute backend-services list
gcloud compute health-checks list

# Solutions
# Update backend service
gcloud compute backend-services update service-backend \
  --health-checks=service-health-check

# Check SSL certificate
gcloud compute ssl-certificates describe cert-name
```

#### DNS Issues
```bash
# Symptoms
Domain not found
DNS resolution timeout
Wrong IP address

# Solutions
# Check DNS records
dig service.ccl-platform.com
nslookup service.ccl-platform.com

# Update DNS
gcloud dns record-sets transaction start --zone=ccl-zone
gcloud dns record-sets transaction add --zone=ccl-zone \
  --name=service.ccl-platform.com. \
  --type=A \
  --ttl=300 \
  "*******"
gcloud dns record-sets transaction execute --zone=ccl-zone
```

## Security Scan Issues

### 1. Vulnerability Scan Failures

#### False Positives
```bash
# Common false positives
# Development dependencies in production
# Test files included in scan
# Outdated vulnerability database

# Solutions
# Update scan configuration
# Exclude test files
sonar.exclusions=**/*test*,**/node_modules/**

# Update vulnerability database
trivy --download-db-only
npm audit fix

# Whitelist known false positives
# Add to .trivyignore
CVE-2021-12345
```

#### Scan Tool Issues
```bash
# Symptoms
Scanner timeout
Database update failed
Authentication error

# Solutions
# Increase timeout
export TRIVY_TIMEOUT=10m

# Manual database update
trivy --download-db-only --cache-dir /tmp/trivy

# Check authentication
echo $GITHUB_TOKEN | docker login ghcr.io -u username --password-stdin
```

### 2. Secret Detection Issues

#### False Secret Detection
```bash
# Common false positives
# Test API keys
# Example configurations
# Generated UUIDs

# Solutions
# Update .gitleaks.toml
[allowlist]
paths = [
  "**/*test*",
  "**/*example*",
  "**/docs/**"
]

regexes = [
  "test-api-key-.*",
  "example-.*"
]
```

## Performance Issues

### 1. Slow Pipeline Execution

#### Build Performance
```bash
# Diagnosis
# Measure build times
time make build

# Profile build process
# For Rust
cargo build --timings

# For Node.js
npm run build --timing

# Solutions
# Enable caching
# Use build cache
export CARGO_TARGET_DIR=/cache/target

# Parallel builds
make -j$(nproc) build

# Optimize Docker builds
# Use multi-stage builds
# Cache dependencies separately
```

#### Test Performance
```bash
# Diagnosis
# Identify slow tests
pytest --durations=10

# Profile test execution
python -m cProfile -o profile.stats -m pytest

# Solutions
# Parallel test execution
pytest -n auto

# Skip slow tests in CI
pytest -m "not slow"

# Use test fixtures
# Cache test data
# Mock external services
```

### 2. Resource Constraints

#### Runner Capacity
```bash
# Symptoms
Jobs queued for long time
Runner out of memory
Disk space full

# Solutions
# Scale runners
# Add more GitHub runners
# Use larger instance types

# Optimize resource usage
# Clean up between jobs
# Use efficient algorithms
# Reduce memory footprint
```

## Monitoring and Alerting Issues

### 1. Missing Metrics

#### Metric Collection Failures
```bash
# Symptoms
No data in dashboards
Metrics not appearing
Stale data

# Solutions
# Check metric collection
gcloud logging read "jsonPayload.metric_name" --limit=10

# Verify metric definitions
gcloud monitoring metrics list --filter="metric.type:custom.googleapis.com/ccl"

# Restart metric collection
kubectl rollout restart deployment/metrics-collector
```

### 2. Alert Fatigue

#### Too Many Alerts
```bash
# Solutions
# Tune alert thresholds
# Increase error rate threshold from 1% to 5%
# Increase duration from 1m to 5m

# Group related alerts
# Use alert dependencies
# Implement alert suppression

# Improve signal-to-noise ratio
# Focus on actionable alerts
# Remove redundant alerts
```

## Emergency Procedures

### 1. Pipeline Completely Broken

```bash
# Immediate actions
# 1. Stop all deployments
gh workflow disable

# 2. Assess impact
# Check production services
make health-check-all ENV=production

# 3. Communicate
# Update team in #ccl-incidents
# Post status page update

# 4. Rollback if needed
./scripts/deploy/rollback.sh <service> production

# 5. Fix pipeline
# Identify root cause
# Apply fix
# Test in development first
```

### 2. Security Incident

```bash
# Immediate actions
# 1. Isolate affected systems
# Revoke compromised credentials
# Block suspicious IP addresses

# 2. Assess scope
# Check audit logs
# Identify affected data

# 3. Contain threat
# Patch vulnerabilities
# Update security rules

# 4. Communicate
# Notify security team
# Update stakeholders
# Document incident
```

## Getting Help

### 1. Internal Resources
- **Slack**: `#ccl-dev-support` for general help
- **Slack**: `#ccl-incidents` for urgent issues
- **Documentation**: Check service-specific README files
- **Runbooks**: See `/docs/runbooks/` directory

### 2. External Resources
- **GitHub Actions**: [GitHub Actions Documentation](https://docs.github.com/en/actions)
- **Google Cloud**: [Cloud Run Documentation](https://cloud.google.com/run/docs)
- **Security Tools**: Check tool-specific documentation

### 3. Escalation Process
1. **Level 1**: Development team member
2. **Level 2**: Team lead or senior developer
3. **Level 3**: Platform team or DevOps
4. **Level 4**: On-call engineer (production only)

For more information, see:
- [Emergency Response Procedures](./emergency-response.md)
- [Monitoring Guide](./monitoring.md)
- [Security Procedures](./security.md)
