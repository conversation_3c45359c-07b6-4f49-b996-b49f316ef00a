# CCL CI/CD Pipeline Documentation

## Overview

The CCL (Codebase Context Layer) platform uses a comprehensive CI/CD pipeline that supports multiple programming languages and provides automated quality gates, progressive deployments, and comprehensive monitoring.

## Architecture

### Pipeline Components

```mermaid
graph TD
    A[Code Commit] --> B[GitHub Actions Trigger]
    B --> C[Language-Specific Setup]
    C --> D[Dependencies Installation]
    D --> E[Code Linting]
    E --> F[Security Scanning]
    F --> G[Unit Tests]
    G --> H[Integration Tests]
    H --> I[Build Artifacts]
    I --> J[Container Build]
    J --> K[Vulnerability Scanning]
    K --> L[Quality Gates Check]
    L --> M{Environment}
    M -->|Development| N[Rolling Deployment]
    M -->|Staging| O[Canary Deployment]
    M -->|Production| P[Blue-Green Deployment]
    N --> Q[Health Checks]
    O --> Q
    P --> Q
    Q --> R[Monitoring & Alerting]
```

### Supported Languages

- **Rust** (Analysis Engine)
- **Python** (Query Intelligence, Pattern Mining)
- **Go** (Marketplace)
- **TypeScript** (Web, Collaboration, SDK)

## Pipeline Stages

### 1. Code Quality Checks

#### Linting
- **Rust**: `clippy`, `rustfmt`
- **Python**: `ruff`, `black`, `mypy`
- **Go**: `golangci-lint`, `gofmt`
- **TypeScript**: `eslint`, `prettier`

#### Security Scanning
- **Dependency Scanning**: Language-specific tools
- **Container Scanning**: Trivy
- **Secret Scanning**: GitLeaks
- **SAST**: SonarQube, language-specific tools

#### Testing
- **Unit Tests**: >90% coverage required
- **Integration Tests**: All API endpoints
- **E2E Tests**: Critical user journeys (staging/prod only)

### 2. Build Process

#### Artifact Generation
- **Rust**: Optimized binaries
- **Python**: Wheels and source distributions
- **Go**: Static binaries
- **TypeScript**: Bundled applications

#### Container Images
- Multi-stage Docker builds
- Security scanning with Trivy
- Image signing with Cosign
- Push to Google Artifact Registry

### 3. Deployment Strategies

#### Development Environment
- **Strategy**: Rolling deployment
- **Auto-deploy**: On merge to `develop` branch
- **Approval**: Not required

#### Staging Environment
- **Strategy**: Canary deployment (25% initial traffic)
- **Auto-deploy**: On merge to `main` branch
- **Approval**: Required from QA team

#### Production Environment
- **Strategy**: Blue-green deployment
- **Auto-deploy**: Disabled
- **Approval**: Required from platform team, security team, and CTO

## Getting Started

### Prerequisites

1. **GitHub Repository Access**: Ensure you have appropriate permissions
2. **GCP Access**: Service account keys configured in GitHub Secrets
3. **Local Development**: Follow the setup guide in each service directory

### Setting Up a New Service

1. **Create Service Directory**:
   ```bash
   mkdir my-service
   cd my-service
   ```

2. **Add Makefile**:
   ```makefile
   # Include common makefile
   include ../build/makefiles/common.mk
   
   # Set language
   LANGUAGE := python  # or rust, go, typescript
   ```

3. **Create GitHub Workflow**:
   ```yaml
   name: My Service CI
   
   on:
     push:
       branches: [main, develop]
       paths: ['my-service/**']
   
   jobs:
     ci:
       uses: ./.github/workflows/ci-common.yml
       with:
         service_name: my-service
         language: python
         working_directory: my-service
       secrets:
         GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
         SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
   ```

### Running Locally

```bash
# Install dependencies
make deps

# Run linting
make lint

# Run tests
make test

# Build service
make build

# Run in development mode
make dev-run
```

## Deployment Process

### Automatic Deployments

1. **Development**: Triggered on merge to `develop` branch
2. **Staging**: Triggered on merge to `main` branch
3. **Production**: Manual trigger with approvals

### Manual Deployments

```bash
# Deploy specific version to staging
gh workflow run cd-deploy.yml \
  -f service_name=analysis-engine \
  -f environment=staging \
  -f version=v1.2.3

# Deploy to production (requires approvals)
gh workflow run cd-deploy.yml \
  -f service_name=analysis-engine \
  -f environment=production \
  -f version=v1.2.3
```

### Progressive Rollout

Production deployments use progressive rollout:

1. **Deploy**: New version deployed without traffic
2. **Health Check**: Verify new version is healthy
3. **Traffic Shift**: Gradually increase traffic (5% → 10% → 25% → 50% → 100%)
4. **Monitoring**: Monitor metrics at each stage
5. **Rollback**: Automatic rollback if issues detected

## Monitoring and Alerting

### Key Metrics

- **Deployment Frequency**: How often we deploy
- **Lead Time**: Time from commit to production
- **Mean Time to Recovery (MTTR)**: Time to recover from incidents
- **Change Failure Rate**: Percentage of deployments causing issues

### Dashboards

- **CI/CD Overview**: [Cloud Monitoring Dashboard](https://console.cloud.google.com/monitoring/dashboards/custom/ccl-cicd)
- **Security Dashboard**: Security scan results and vulnerabilities
- **Quality Dashboard**: Test coverage and code quality metrics

### Alerts

Critical alerts are sent to:
- **Slack**: `#ccl-critical-alerts`
- **PagerDuty**: On-call engineer
- **Email**: `<EMAIL>`

## Troubleshooting

### Common Issues

#### Build Failures

1. **Check Dependencies**:
   ```bash
   make deps
   make clean
   make build
   ```

2. **Review Logs**: Check GitHub Actions logs for specific errors

3. **Local Reproduction**:
   ```bash
   make ci-validate  # Run full CI pipeline locally
   ```

#### Deployment Failures

1. **Check Service Health**:
   ```bash
   make health-check ENV=staging
   ```

2. **Review Deployment Logs**:
   ```bash
   gcloud logging read "resource.type=cloud_run_service" --limit=50
   ```

3. **Manual Rollback**:
   ```bash
   ./scripts/deploy/rollback.sh my-service production
   ```

#### Quality Gate Failures

1. **Security Issues**:
   - Review security scan results in GitHub Security tab
   - Fix critical vulnerabilities before deployment
   - Update dependencies if needed

2. **Test Coverage**:
   ```bash
   make coverage
   # Review coverage report in coverage/ directory
   ```

3. **Code Quality**:
   - Check SonarQube results
   - Fix code smells and bugs
   - Ensure complexity is within limits

### Emergency Procedures

#### Production Incident Response

1. **Immediate Actions**:
   - Check service health dashboards
   - Review recent deployments
   - Consider immediate rollback if deployment-related

2. **Rollback Process**:
   ```bash
   # Emergency rollback
   ./scripts/deploy/rollback.sh <service> production
   
   # Verify rollback
   make health-check ENV=production
   ```

3. **Communication**:
   - Update incident channel: `#ccl-incidents`
   - Notify stakeholders via status page
   - Page on-call engineer if critical

#### Break Glass Access

For emergency access to production:

1. **Request Access**: Contact platform team lead
2. **Justification**: Provide incident details
3. **Time-Limited**: Access automatically revoked after 4 hours
4. **Audit Trail**: All actions logged and reviewed

## Best Practices

### Code Quality

1. **Write Tests First**: Follow TDD practices
2. **Small Commits**: Keep changes focused and reviewable
3. **Descriptive Messages**: Use conventional commit format
4. **Code Reviews**: Require 2 approvals for production changes

### Security

1. **Never Commit Secrets**: Use GitHub Secrets or GCP Secret Manager
2. **Regular Updates**: Keep dependencies up to date
3. **Scan Early**: Run security scans in development
4. **Principle of Least Privilege**: Minimal required permissions

### Performance

1. **Optimize Builds**: Use caching and parallel execution
2. **Monitor Metrics**: Track build and deployment times
3. **Resource Limits**: Set appropriate CPU/memory limits
4. **Clean Up**: Remove unused artifacts and images

## Configuration Reference

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `PROJECT_ID` | GCP Project ID | Yes |
| `REGION` | GCP Region | Yes |
| `SERVICE_NAME` | Service name | Yes |
| `VERSION` | Deployment version | Yes |
| `ENVIRONMENT` | Target environment | Yes |

### Secrets

| Secret | Description | Scope |
|--------|-------------|-------|
| `GCP_SA_KEY` | GCP Service Account Key | Repository |
| `SONAR_TOKEN` | SonarQube authentication | Repository |
| `SLACK_WEBHOOK_URL` | Slack notifications | Organization |

## Support

### Getting Help

1. **Documentation**: Check service-specific README files
2. **Slack**: Ask in `#ccl-dev-support`
3. **Issues**: Create GitHub issue with `ci-cd` label
4. **On-call**: Page for production emergencies only

### Contributing

1. **Fork Repository**: Create feature branch
2. **Make Changes**: Follow coding standards
3. **Test Locally**: Ensure all checks pass
4. **Submit PR**: Include description and tests
5. **Review Process**: Address feedback promptly

---

For more detailed information, see:
- [Service-Specific Guides](./services/)
- [Deployment Strategies](./deployment-strategies.md)
- [Monitoring Guide](./monitoring.md)
- [Security Procedures](./security.md)
