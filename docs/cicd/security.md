# Security in CCL CI/CD Pipeline

## Overview

Security is integrated throughout the CCL CI/CD pipeline, from code commit to production deployment. This document outlines security measures, scanning tools, and best practices.

## Security Architecture

### Defense in Depth

```mermaid
graph TD
    A[Code Repository] --> B[Secret Scanning]
    B --> C[Dependency Scanning]
    C --> D[SAST Scanning]
    D --> E[Container Scanning]
    E --> F[Infrastructure Scanning]
    F --> G[Runtime Security]
    G --> H[Monitoring & Alerting]
```

### Security Gates

1. **Pre-commit**: Local security checks
2. **CI Pipeline**: Automated security scanning
3. **Pre-deployment**: Security validation
4. **Runtime**: Continuous monitoring

## Scanning Tools and Configuration

### 1. Secret Scanning

**Tool**: GitLeaks
**Purpose**: Detect hardcoded secrets in code

#### Configuration
```yaml
# .gitleaks.toml
[allowlist]
paths = [
  "**/*test*",
  "**/*example*",
  "**/*mock*"
]

commits = [
  "initial commit"
]

[[rules]]
id = "aws-access-key"
description = "AWS Access Key"
regex = '''AKIA[0-9A-Z]{16}'''

[[rules]]
id = "generic-api-key"
description = "Generic API Key"
regex = '''(?i)(api_key|apikey|api-key).*[=:]\s*['\"]?[0-9a-f]{32,}['\"]?'''
```

#### Usage in Pipeline
```yaml
- name: Run GitLeaks
  uses: gitleaks/gitleaks-action@v2
  env:
    GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    GITLEAKS_LICENSE: ${{ secrets.GITLEAKS_LICENSE }}
```

### 2. Dependency Scanning

#### Rust (Cargo Audit)
```bash
# Install and run
cargo install cargo-audit
cargo audit

# Configuration in .cargo/audit.toml
[advisories]
ignore = []
informational_warnings = ["unmaintained"]
```

#### Python (Safety + pip-audit)
```bash
# Safety for known vulnerabilities
safety check --json --output safety-report.json

# pip-audit for comprehensive scanning
pip-audit --format=json --output=pip-audit-report.json
```

#### Go (govulncheck)
```bash
# Official Go vulnerability scanner
govulncheck ./...

# Configuration
export GOVULNDB=https://vuln.go.dev
```

#### TypeScript (npm audit + retire.js)
```bash
# npm audit for npm packages
npm audit --audit-level=moderate --json

# retire.js for JavaScript libraries
retire --path . --outputformat json
```

### 3. Static Application Security Testing (SAST)

#### SonarQube Configuration
```properties
# sonar-project.properties
sonar.security.hotspots.inheritFromParent=true
sonar.security.review.rating=A

# Security rules
sonar.issue.ignore.multicriteria=e1,e2
sonar.issue.ignore.multicriteria.e1.ruleKey=*:S2068
sonar.issue.ignore.multicriteria.e1.resourceKey=**/*test*
```

#### Language-Specific SAST

**Rust (Clippy)**
```toml
# clippy.toml
avoid-breaking-exported-api = true
msrv = "1.70.0"

# Cargo.toml
[lints.clippy]
all = "warn"
pedantic = "warn"
nursery = "warn"
cargo = "warn"
```

**Python (Bandit)**
```yaml
# .bandit
skips: ['B101', 'B601']
exclude_dirs: ['tests', 'test_*']
```

**Go (gosec)**
```json
{
  "global": {
    "nosec": false,
    "audit": true
  },
  "rules": {
    "G101": {
      "pattern": "(?i)passwd|pass|password|pwd|secret|token",
      "ignore_entropy": false,
      "entropy_threshold": "80.0",
      "per_char_threshold": "3.0",
      "truncate": "32"
    }
  }
}
```

### 4. Container Scanning

#### Trivy Configuration
```yaml
# trivy.yaml
format: sarif
output: trivy-results.sarif
severity: HIGH,CRITICAL
vuln-type: os,library
ignore-unfixed: false
```

#### Usage in Pipeline
```yaml
- name: Run Trivy vulnerability scanner
  uses: aquasecurity/trivy-action@master
  with:
    image-ref: 'gcr.io/ccl-platform/${{ inputs.service_name }}:${{ github.sha }}'
    format: 'sarif'
    output: 'trivy-results.sarif'

- name: Upload Trivy scan results
  uses: github/codeql-action/upload-sarif@v2
  with:
    sarif_file: 'trivy-results.sarif'
```

### 5. Infrastructure Security

#### Terraform Security (tfsec)
```yaml
# .tfsec/config.yml
severity_overrides:
  HIGH: ERROR
  MEDIUM: WARNING
  LOW: INFO

exclude_paths:
  - "**/*test*"
  - "examples/"
```

#### Kubernetes Security (kube-score)
```bash
# Scan Kubernetes manifests
kube-score score k8s-manifests/*.yaml
```

## Security Policies

### 1. Vulnerability Management

#### Severity Levels
- **CRITICAL**: Immediate fix required, block deployment
- **HIGH**: Fix within 7 days, review deployment
- **MEDIUM**: Fix within 30 days, monitor
- **LOW**: Fix in next release cycle

#### Response Times
```yaml
vulnerability_response:
  critical:
    response_time: "2 hours"
    fix_time: "24 hours"
    escalation: "CISO, CTO"
  
  high:
    response_time: "24 hours"
    fix_time: "7 days"
    escalation: "Security Team Lead"
  
  medium:
    response_time: "72 hours"
    fix_time: "30 days"
    escalation: "Development Team"
```

### 2. Secret Management

#### Approved Secret Stores
1. **GitHub Secrets**: For CI/CD pipeline secrets
2. **Google Secret Manager**: For application secrets
3. **HashiCorp Vault**: For dynamic secrets (if needed)

#### Secret Rotation Policy
```yaml
rotation_schedule:
  api_keys: "30 days"
  database_credentials: "90 days"
  certificates: "365 days"
  jwt_signing_keys: "30 days"
```

#### Secret Naming Convention
```
Environment: dev/staging/prod
Service: analysis-engine, marketplace, etc.
Type: api-key, db-creds, cert, etc.

Format: {env}-{service}-{type}
Example: prod-marketplace-stripe-api-key
```

### 3. Access Control

#### GitHub Repository Permissions
```yaml
permissions:
  admin:
    - platform-team-leads
    - security-team
  
  maintain:
    - senior-developers
    - devops-team
  
  write:
    - developers
    - qa-team
  
  read:
    - contractors
    - interns
```

#### Branch Protection Rules
```yaml
main_branch:
  required_reviews: 2
  dismiss_stale_reviews: true
  require_code_owner_reviews: true
  required_status_checks:
    - "ci/security-scan"
    - "ci/tests"
    - "ci/build"
  
develop_branch:
  required_reviews: 1
  required_status_checks:
    - "ci/tests"
    - "ci/build"
```

### 4. Container Security

#### Base Image Policy
```yaml
approved_base_images:
  - "gcr.io/distroless/static-debian11"
  - "gcr.io/distroless/base-debian11"
  - "alpine:3.18"
  - "ubuntu:22.04"

prohibited_images:
  - "*:latest"
  - "node:*"  # Use specific versions
  - "python:*"  # Use specific versions
```

#### Container Hardening
```dockerfile
# Example secure Dockerfile
FROM gcr.io/distroless/static-debian11

# Create non-root user
USER 65534:65534

# Copy only necessary files
COPY --from=builder /app/binary /app/binary

# Set security labels
LABEL security.scan="trivy"
LABEL security.policy="ccl-container-policy"

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD ["/app/binary", "health"]

ENTRYPOINT ["/app/binary"]
```

## Compliance and Auditing

### 1. SOC2 Type II Compliance

#### Required Controls
- **Access Control**: Multi-factor authentication
- **Change Management**: Code review and approval
- **Data Protection**: Encryption at rest and in transit
- **Monitoring**: Comprehensive logging and alerting
- **Incident Response**: Documented procedures

#### Evidence Collection
```yaml
soc2_evidence:
  access_logs:
    retention: "7 years"
    location: "Cloud Logging"
  
  change_logs:
    retention: "7 years"
    location: "GitHub audit logs"
  
  security_scans:
    retention: "3 years"
    location: "Security dashboard"
```

### 2. GDPR Compliance

#### Data Classification
```yaml
data_types:
  personal_data:
    - user_emails
    - user_names
    - ip_addresses
  
  sensitive_data:
    - payment_information
    - authentication_tokens
  
  public_data:
    - documentation
    - open_source_code
```

#### Data Handling
- **Encryption**: AES-256 for data at rest, TLS 1.3 for data in transit
- **Access Logging**: All access to personal data logged
- **Retention**: Automatic deletion after retention period
- **Right to Deletion**: Automated data deletion on request

### 3. Audit Logging

#### Security Events
```yaml
logged_events:
  authentication:
    - login_attempts
    - mfa_challenges
    - session_creation
  
  authorization:
    - permission_grants
    - access_denials
    - privilege_escalation
  
  data_access:
    - database_queries
    - file_access
    - api_calls
  
  system_changes:
    - configuration_changes
    - deployment_events
    - security_policy_updates
```

## Incident Response

### 1. Security Incident Classification

#### Severity Levels
```yaml
severity_levels:
  critical:
    description: "Active breach or imminent threat"
    response_time: "15 minutes"
    escalation: "CISO, CEO"
  
  high:
    description: "Potential breach or significant vulnerability"
    response_time: "1 hour"
    escalation: "Security Team, CTO"
  
  medium:
    description: "Security policy violation"
    response_time: "4 hours"
    escalation: "Security Team Lead"
  
  low:
    description: "Minor security concern"
    response_time: "24 hours"
    escalation: "Development Team"
```

### 2. Response Procedures

#### Immediate Response (0-15 minutes)
1. **Assess**: Determine severity and scope
2. **Contain**: Isolate affected systems
3. **Communicate**: Notify incident commander
4. **Document**: Start incident log

#### Short-term Response (15 minutes - 4 hours)
1. **Investigate**: Gather evidence
2. **Mitigate**: Implement temporary fixes
3. **Communicate**: Update stakeholders
4. **Monitor**: Watch for further activity

#### Long-term Response (4+ hours)
1. **Remediate**: Implement permanent fixes
2. **Recover**: Restore normal operations
3. **Review**: Conduct post-incident analysis
4. **Improve**: Update security measures

### 3. Communication Plan

#### Internal Communication
```yaml
notification_matrix:
  critical:
    immediate: ["CISO", "CTO", "CEO"]
    within_1h: ["Security Team", "Platform Team"]
    within_4h: ["All Engineering"]
  
  high:
    immediate: ["Security Team Lead", "Platform Team Lead"]
    within_1h: ["Security Team", "Platform Team"]
    within_4h: ["Development Teams"]
```

#### External Communication
- **Customers**: Via status page and email
- **Regulators**: As required by law
- **Partners**: Based on contractual obligations
- **Public**: Through security advisories

## Security Metrics and KPIs

### 1. Vulnerability Metrics
- **Mean Time to Detection (MTTD)**: < 24 hours
- **Mean Time to Response (MTTR)**: < 4 hours for critical
- **Vulnerability Density**: < 1 critical per 10K LOC
- **Patch Coverage**: > 95% within SLA

### 2. Compliance Metrics
- **Security Scan Coverage**: 100% of deployments
- **Policy Compliance**: > 98%
- **Audit Findings**: < 5 per quarter
- **Training Completion**: 100% of team

### 3. Incident Metrics
- **Security Incidents**: < 2 per quarter
- **False Positive Rate**: < 10%
- **Response Time Compliance**: > 95%
- **Recovery Time**: < 4 hours average

## Best Practices

### 1. Secure Development
- **Security by Design**: Consider security from the start
- **Threat Modeling**: Identify potential threats
- **Secure Coding**: Follow OWASP guidelines
- **Regular Training**: Keep team updated on threats

### 2. Pipeline Security
- **Fail Secure**: Block deployment on security issues
- **Least Privilege**: Minimal required permissions
- **Audit Everything**: Log all security-relevant events
- **Regular Updates**: Keep tools and dependencies current

### 3. Monitoring and Response
- **Continuous Monitoring**: 24/7 security monitoring
- **Automated Response**: Automate common responses
- **Regular Testing**: Test incident response procedures
- **Continuous Improvement**: Learn from each incident

For more information, see:
- [Security Runbooks](./security-runbooks.md)
- [Incident Response Procedures](./incident-response.md)
- [Compliance Documentation](./compliance.md)
