# 🔧 Analysis Engine Operations Runbook

## Quick Reference

### Service Information
- **Service Name**: analysis-engine
- **Environment**: Production (vibe-match-463114)
- **Port**: 8001
- **Health Check**: `/health`
- **Metrics**: `/metrics`
- **Documentation**: `/docs`

### Emergency Contacts
- **Primary On-Call**: Development Team
- **Secondary**: Platform Engineering
- **Escalation**: Engineering Leadership

## 🚨 Emergency Procedures

### Service Down (Severity 1)
1. **Immediate Response** (0-5 minutes)
   ```bash
   # Check service status
   gcloud run services describe analysis-engine --region=us-central1
   
   # Check recent logs
   gcloud logs read "resource.type=cloud_run_revision" --limit=50
   
   # Check health endpoint
   curl https://analysis-engine-url/health
   ```

2. **Diagnosis** (5-15 minutes)
   - Check Cloud Run metrics in GCP Console
   - Review error logs for patterns
   - Verify external dependencies (Spanner, Redis, Vertex AI)
   - Check resource utilization (CPU, Memory)

3. **Recovery Actions**
   ```bash
   # Restart service
   gcloud run services update analysis-engine --region=us-central1
   
   # Scale up if needed
   gcloud run services update analysis-engine \
     --min-instances=5 --max-instances=1000
   
   # Rollback if necessary
   gcloud run services replace-traffic analysis-engine \
     --to-revisions=PREVIOUS_REVISION=100
   ```

### Performance Degradation (Severity 2)
1. **Check Key Metrics**
   - API response times (target: <100ms p95)
   - Analysis duration (target: <5min for 1M LOC)
   - Memory usage (target: <4GB per instance)
   - Error rates (target: <1%)

2. **Common Causes & Solutions**
   - **High Memory Usage**: Check for memory leaks, validate file sizes
   - **Slow Responses**: Check database connections, cache hit rates
   - **Circuit Breaker Open**: Check external service health

## 📊 Monitoring & Alerting

### Key Metrics Dashboard
```
Analysis Engine Production Dashboard
├── Service Health
│   ├── Uptime (target: 99.9%)
│   ├── Response Time p95 (target: <100ms)
│   └── Error Rate (target: <1%)
├── Performance
│   ├── Analysis Duration (target: <5min for 1M LOC)
│   ├── Memory Usage (target: <4GB)
│   └── CPU Utilization (target: <80%)
├── Business Metrics
│   ├── Analyses per Hour
│   ├── Success Rate (target: >99%)
│   └── Concurrent Analyses
└── Dependencies
    ├── Spanner Connection Pool
    ├── Redis Cache Hit Rate
    └── Vertex AI Circuit Breaker Status
```

### Alert Thresholds
```yaml
Critical Alerts:
  - Service Down: 0% availability for >1 minute
  - High Error Rate: >5% errors for >5 minutes
  - Memory Exhaustion: >3.8GB usage for >2 minutes

Warning Alerts:
  - Slow Responses: p95 >150ms for >10 minutes
  - Low Cache Hit Rate: <70% for >15 minutes
  - Circuit Breaker Open: >5 minutes
```

## 🔍 Troubleshooting Guide

### Common Issues

#### 1. High Memory Usage
**Symptoms**: Memory alerts, OOM kills, slow performance
**Diagnosis**:
```bash
# Check memory metrics
gcloud monitoring metrics list --filter="metric.type:run.googleapis.com/container/memory"

# Review large file processing
grep "large file" /var/log/analysis-engine.log
```
**Solutions**:
- Validate file size limits (max 50MB per file)
- Check for memory leaks in analysis pipeline
- Reduce concurrent analysis count
- Restart service if memory leak detected

#### 2. Slow API Responses
**Symptoms**: High p95 response times, timeout errors
**Diagnosis**:
```bash
# Check database connection pool
grep "connection pool" /var/log/analysis-engine.log

# Verify cache performance
redis-cli info stats
```
**Solutions**:
- Check Spanner connection pool health
- Validate Redis cache connectivity
- Review query performance
- Scale up instances if CPU bound

#### 3. Analysis Failures
**Symptoms**: High error rates, failed analyses
**Diagnosis**:
```bash
# Check parser errors
grep "parse error" /var/log/analysis-engine.log

# Verify language support
curl -X GET https://analysis-engine-url/api/v1/languages
```
**Solutions**:
- Validate repository format and accessibility
- Check language parser status
- Verify external service dependencies
- Review input validation errors

#### 4. Circuit Breaker Activation
**Symptoms**: Embeddings service unavailable, degraded functionality
**Diagnosis**:
```bash
# Check circuit breaker status
grep "circuit breaker" /var/log/analysis-engine.log

# Verify Vertex AI connectivity
curl -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  https://us-central1-aiplatform.googleapis.com/v1/projects/vibe-match-463114/locations/us-central1
```
**Solutions**:
- Wait for automatic recovery (5-minute timeout)
- Check Vertex AI service status
- Verify API quotas and limits
- Manual circuit breaker reset if needed

## 🚀 Deployment Procedures

### Standard Deployment
```bash
# 1. Build and test
cd services/analysis-engine
cargo test --all-features
cargo build --release

# 2. Build container
docker build -t gcr.io/vibe-match-463114/analysis-engine:$(git rev-parse --short HEAD) .

# 3. Push to registry
docker push gcr.io/vibe-match-463114/analysis-engine:$(git rev-parse --short HEAD)

# 4. Deploy to Cloud Run
gcloud run deploy analysis-engine \
  --image gcr.io/vibe-match-463114/analysis-engine:$(git rev-parse --short HEAD) \
  --platform managed \
  --region us-central1 \
  --memory 4Gi \
  --cpu 4 \
  --max-instances 1000 \
  --min-instances 0 \
  --set-env-vars GCP_PROJECT_ID=vibe-match-463114

# 5. Verify deployment
curl https://analysis-engine-url/health
```

### Rollback Procedure
```bash
# 1. List recent revisions
gcloud run revisions list --service=analysis-engine --region=us-central1

# 2. Rollback to previous revision
gcloud run services update-traffic analysis-engine \
  --to-revisions=PREVIOUS_REVISION=100 \
  --region=us-central1

# 3. Verify rollback
curl https://analysis-engine-url/health
```

## 🔧 Maintenance Tasks

### Daily Checks
- [ ] Review error logs for patterns
- [ ] Check key metrics dashboard
- [ ] Verify cache hit rates
- [ ] Monitor resource utilization

### Weekly Tasks
- [ ] Review performance trends
- [ ] Check dependency health
- [ ] Validate backup procedures
- [ ] Update documentation if needed

### Monthly Tasks
- [ ] Security patch review
- [ ] Capacity planning review
- [ ] Disaster recovery testing
- [ ] Performance optimization review

## 📈 Performance Tuning

### Memory Optimization
```bash
# Monitor memory usage patterns
gcloud logging read "resource.type=cloud_run_revision" \
  --filter="jsonPayload.message:memory" \
  --format="value(timestamp,jsonPayload.message)"

# Adjust memory limits if needed
gcloud run services update analysis-engine \
  --memory 6Gi --region=us-central1
```

### Cache Optimization
```bash
# Check Redis cache statistics
redis-cli info memory
redis-cli info stats

# Monitor cache hit rates
grep "cache hit" /var/log/analysis-engine.log | tail -100
```

### Database Performance
```bash
# Monitor Spanner metrics
gcloud spanner operations list --instance=ccl-production

# Check connection pool health
grep "connection pool" /var/log/analysis-engine.log
```

## 🔒 Security Procedures

### Access Management
- Service accounts use least privilege principle
- JWT tokens have appropriate expiration
- API keys are rotated regularly
- Network access is restricted via VPC

### Incident Response
1. **Security Alert**: Immediate isolation if needed
2. **Investigation**: Review access logs and audit trails
3. **Containment**: Disable compromised credentials
4. **Recovery**: Restore from known good state
5. **Post-Incident**: Update security measures

## 📞 Escalation Matrix

### Severity Levels
- **Severity 1**: Service completely down, data loss risk
- **Severity 2**: Significant performance degradation
- **Severity 3**: Minor issues, workarounds available
- **Severity 4**: Enhancement requests, documentation

### Response Times
- **Severity 1**: Immediate (0-15 minutes)
- **Severity 2**: 15 minutes
- **Severity 3**: 4 hours
- **Severity 4**: Next business day

### Contact Information
```
Primary On-Call: Development Team
├── Slack: #analysis-engine-alerts
├── PagerDuty: analysis-engine-primary
└── Phone: Emergency escalation only

Secondary: Platform Engineering
├── Slack: #platform-engineering
├── PagerDuty: platform-engineering
└── Email: <EMAIL>

Management Escalation:
├── Engineering Manager: <EMAIL>
├── Director of Engineering: <EMAIL>
└── CTO: <EMAIL>
```

---

**Document Version**: 1.0  
**Last Updated**: 2025-01-08  
**Next Review**: 2025-02-08
