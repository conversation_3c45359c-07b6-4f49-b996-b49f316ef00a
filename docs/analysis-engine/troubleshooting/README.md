# 🔧 Analysis Engine Troubleshooting Guide

## Table of Contents
- [Quick Diagnostics](#quick-diagnostics)
- [Common Issues](#common-issues)
  - [Service Won't Start](#service-wont-start)
  - [Authentication Failures](#authentication-failures)
  - [Performance Issues](#performance-issues)
  - [Memory Issues](#memory-issues)
  - [Parser <PERSON>rrors](#parser-errors)
  - [GCP Integration Issues](#gcp-integration-issues)
  - [WebSocket Connection Issues](#websocket-connection-issues)
  - [Rate Limiting Issues](#rate-limiting-issues)
- [Error Messages](#error-messages)
- [Debugging Procedures](#debugging-procedures)
- [Recovery Procedures](#recovery-procedures)
- [Contact Support](#contact-support)

## Quick Diagnostics

### Health Check Script

```bash
#!/bin/bash
# scripts/diagnose.sh

SERVICE_URL=${1:-http://localhost:8001}

echo "🔍 Running Analysis Engine Diagnostics..."
echo "====================================="

# Check basic health
echo -n "Basic Health: "
if curl -s ${SERVICE_URL}/health | grep -q "healthy"; then
    echo "✅ OK"
else
    echo "❌ FAILED"
fi

# Check readiness
echo -n "Service Ready: "
READY=$(curl -s ${SERVICE_URL}/health/ready)
if echo $READY | grep -q '"status":"ready"'; then
    echo "✅ OK"
    echo $READY | jq -r '.checks | to_entries[] | "  - \(.key): \(.value)"'
else
    echo "❌ NOT READY"
    echo $READY | jq .
fi

# Check recent errors
echo -e "\n📋 Recent Errors (last 10):"
gcloud logging read "resource.type=cloud_run_revision \
    AND resource.labels.service_name=analysis-engine \
    AND severity>=ERROR" \
    --limit 10 \
    --format="table(timestamp,jsonPayload.error)"

# Check metrics
echo -e "\n📊 Current Metrics:"
gcloud monitoring time-series list \
    --filter='metric.type="run.googleapis.com/request_count" \
        AND resource.labels.service_name="analysis-engine"' \
    --format="table(metric.labels.response_code_class,points[0].value.int64_value)"
```

## Common Issues

### Service Won't Start

#### Symptom
Service fails to start or crashes immediately after starting.

#### Diagnosis
```bash
# Check logs
gcloud logging read "resource.type=cloud_run_revision \
    AND resource.labels.service_name=analysis-engine" \
    --limit 50

# Check container status
gcloud run services describe analysis-engine \
    --region us-central1 \
    --format="value(status.conditions[0])"

# Local debugging
RUST_LOG=debug cargo run
```

#### Common Causes & Solutions

1. **Missing Environment Variables**
   ```bash
   # Required variables
   export GCP_PROJECT_ID=your-project
   export SPANNER_INSTANCE_ID=your-instance
   export SPANNER_DATABASE_ID=your-database
   export STORAGE_BUCKET=your-bucket
   export JWT_SECRET=your-secret
   ```

2. **Port Conflict**
   ```bash
   # Check if port 8001 is in use
   lsof -i :8001
   
   # Use different port
   PORT=8002 cargo run
   ```

3. **Dependency Issues**
   ```bash
   # Clean and rebuild
   cargo clean
   cargo update
   cargo build --release
   ```

### Authentication Failures

#### Symptom
401 Unauthorized or 403 Forbidden errors.

#### Diagnosis
```bash
# Test JWT token
curl -H "Authorization: Bearer $JWT_TOKEN" \
    ${SERVICE_URL}/api/v1/analyses

# Test API key
curl -H "X-API-Key: $API_KEY" \
    ${SERVICE_URL}/api/v1/analyses

# Check token expiry
jwt decode $JWT_TOKEN
```

#### Solutions

1. **Invalid JWT Token**
   ```typescript
   // Refresh Firebase token
   const user = firebase.auth().currentUser;
   const token = await user.getIdToken(true); // Force refresh
   ```

2. **GCP Authentication Issues**
   ```bash
   # Re-authenticate
   gcloud auth application-default login
   
   # Set correct project
   gcloud config set project vibe-match-463114
   
   # Verify credentials
   gcloud auth application-default print-access-token
   ```

3. **Service Account Issues**
   ```bash
   # Check service account
   gcloud iam service-accounts describe \
       analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com
   
   # Grant necessary roles
   gcloud projects add-iam-policy-binding ${PROJECT_ID} \
       --member="serviceAccount:analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com" \
       --role="roles/spanner.databaseUser"
   ```

### Performance Issues

#### Symptom
Slow response times, timeouts, or degraded performance.

#### Diagnosis
```bash
# Check response times
curl -w "@curl-format.txt" -o /dev/null -s \
    ${SERVICE_URL}/health

# Monitor CPU and memory
gcloud run services describe analysis-engine \
    --region us-central1 \
    --format="table(spec.template.spec.containers[0].resources)"

# Check concurrent requests
gcloud monitoring read \
    'fetch cloud_run_revision | metric run.googleapis.com/container/instance_count'
```

#### Solutions

1. **High Latency**
   ```bash
   # Increase instances
   gcloud run services update analysis-engine \
       --min-instances 5 \
       --max-instances 200
   
   # Enable CPU boost
   gcloud run services update analysis-engine \
       --cpu-boost
   ```

2. **Memory Pressure**
   ```bash
   # Increase memory allocation
   gcloud run services update analysis-engine \
       --memory 8Gi
   
   # Reduce batch size
   gcloud run services update analysis-engine \
       --set-env-vars ANALYSIS_BATCH_SIZE=50
   ```

3. **Database Bottleneck**
   ```sql
   -- Check Spanner CPU utilization
   SELECT 
     interval_end,
     SUM(cpu_utilization) as total_cpu
   FROM SPANNER_SYS.INSTANCE_OPERATIONS_1MINUTE
   WHERE interval_end > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
   GROUP BY interval_end
   ORDER BY interval_end DESC;
   ```

### Memory Issues

#### Symptom
Out of memory errors, service restarts, or memory leaks.

#### Diagnosis
```bash
# Check memory usage in logs
gcloud logging read "jsonPayload.memory_usage_mb>3000" \
    --limit 20

# Profile memory locally
RUST_LOG=debug cargo run &
PID=$!
while true; do
    ps -p $PID -o pid,vsz,rss,comm
    sleep 5
done
```

#### Solutions

1. **Memory Leak**
   ```rust
   // Use memory profiling
   #[global_allocator]
   static ALLOC: jemallocator::Jemalloc = jemallocator::Jemalloc;
   
   // Add periodic cleanup
   tokio::spawn(async {
       let mut interval = tokio::time::interval(Duration::from_secs(300));
       loop {
           interval.tick().await;
           // Force cleanup
           drop(old_cache_entries);
           // Trigger GC if using jemalloc
           unsafe { libc::malloc_trim(0); }
       }
   });
   ```

2. **Large File Processing**
   ```rust
   // Implement streaming for large files
   const MAX_FILE_SIZE: u64 = 100 * 1024 * 1024; // 100MB
   
   if metadata.len() > MAX_FILE_SIZE {
       return Err(AnalysisError::FileTooLarge);
   }
   
   // Use memory-mapped files
   let mmap = unsafe { MmapOptions::new().map(&file)? };
   ```

### Parser Errors

#### Symptom
Failed to parse files, unsupported language errors, or AST generation failures.

#### Diagnosis
```bash
# Test parser directly
echo 'fn main() { println!("test"); }' > test.rs
cargo run -- parse test.rs

# Check supported languages
cargo run -- list-languages

# Debug tree-sitter
RUST_LOG=trace cargo run -- parse problematic_file.ext
```

#### Solutions

1. **Unsupported Language**
   ```toml
   # Add language support in Cargo.toml
   [dependencies]
   tree-sitter-ruby = "0.20"
   
   # Update parser map
   m.insert("rb", tree_sitter_ruby::language());
   ```

2. **Parse Timeout**
   ```rust
   // Add timeout to parser
   let parse_timeout = Duration::from_secs(30);
   let tree = tokio::time::timeout(
       parse_timeout,
       tokio::task::spawn_blocking(move || {
           parser.parse(&content, None)
       })
   ).await??;
   ```

3. **Invalid Syntax**
   ```rust
   // Handle parse errors gracefully
   match parser.parse(&content, None) {
       Some(tree) if tree.root_node().has_error() => {
           warn!("Parse tree has errors, continuing with partial AST");
           Ok(ParsedFile::partial(tree, content))
       }
       Some(tree) => Ok(ParsedFile::complete(tree, content)),
       None => Err(ParseError::CompleteFail),
   }
   ```

### GCP Integration Issues

#### Symptom
Cannot connect to Spanner, Cloud Storage, or Vertex AI.

#### Diagnosis
```bash
# Test Spanner connection
gcloud spanner databases execute-sql ccl-main \
    --instance=ccl-production \
    --sql="SELECT 1"

# Test Cloud Storage
gsutil ls gs://${STORAGE_BUCKET}

# Test Vertex AI
curl -X POST \
    -H "Authorization: Bearer $(gcloud auth print-access-token)" \
    -H "Content-Type: application/json" \
    https://${REGION}-aiplatform.googleapis.com/v1/projects/${PROJECT_ID}/locations/${REGION}/endpoints/${ENDPOINT_ID}:predict
```

#### Solutions

1. **Spanner Connection Issues**
   ```bash
   # Check firewall rules
   gcloud compute firewall-rules list
   
   # Verify VPC connector
   gcloud compute networks vpc-access connectors describe ccl-vpc-connector \
       --region us-central1
   
   # Test from Cloud Run
   gcloud run services update analysis-engine \
       --vpc-connector projects/${PROJECT_ID}/locations/${REGION}/connectors/ccl-vpc-connector
   ```

2. **Vertex AI Rate Limits**
   ```rust
   // Implement exponential backoff
   use backoff::{ExponentialBackoff, retry};
   
   let result = retry(ExponentialBackoff::default(), || {
       vertex_client.predict(request.clone())
           .map_err(|e| {
               if e.is_rate_limit() {
                   backoff::Error::transient(e)
               } else {
                   backoff::Error::permanent(e)
               }
           })
   }).await?;
   ```

### WebSocket Connection Issues

#### Symptom
WebSocket connections fail or disconnect frequently.

#### Diagnosis
```javascript
// Test WebSocket connection
const ws = new WebSocket('wss://analysis-engine.ccl.dev/ws/progress/test');
ws.onopen = () => console.log('Connected');
ws.onerror = (e) => console.error('Error:', e);
ws.onclose = (e) => console.log('Closed:', e.code, e.reason);
```

#### Solutions

1. **Connection Timeout**
   ```rust
   // Configure WebSocket settings
   use actix_web_actors::ws;
   
   pub struct WebSocketSession {
       hb: Instant,
   }
   
   impl WebSocketSession {
       fn hb(&self, ctx: &mut ws::WebsocketContext<Self>) {
           ctx.run_interval(Duration::from_secs(5), |act, ctx| {
               if Instant::now().duration_since(act.hb) > Duration::from_secs(30) {
                   ctx.stop();
                   return;
               }
               ctx.ping(b"");
           });
       }
   }
   ```

2. **Load Balancer Issues**
   ```yaml
   # Cloud Run annotation for WebSocket support
   metadata:
     annotations:
       run.googleapis.com/cpu-throttling: "false"
       # Increase timeout for WebSocket
       run.googleapis.com/timeout: "3600s"
   ```

### Rate Limiting Issues

#### Symptom
429 Too Many Requests errors.

#### Diagnosis
```bash
# Check rate limit headers
curl -i ${SERVICE_URL}/api/v1/analyses | grep -i rate

# Check Redis rate limit data
redis-cli
> KEYS rate_limit:*
> TTL rate_limit:user:123
```

#### Solutions

1. **Rate Limit Exceeded**
   ```rust
   // Implement token bucket algorithm
   pub struct TokenBucket {
       capacity: u32,
       tokens: AtomicU32,
       refill_rate: u32,
       last_refill: AtomicU64,
   }
   
   impl TokenBucket {
       pub fn try_consume(&self, tokens: u32) -> bool {
           self.refill();
           let current = self.tokens.load(Ordering::Relaxed);
           if current >= tokens {
               self.tokens.fetch_sub(tokens, Ordering::Relaxed);
               true
           } else {
               false
           }
       }
   }
   ```

2. **Redis Connection Issues**
   ```rust
   // Fallback to in-memory rate limiting
   lazy_static! {
       static ref MEMORY_LIMITER: Mutex<HashMap<String, TokenBucket>> = 
           Mutex::new(HashMap::new());
   }
   
   pub async fn check_rate_limit(key: &str, limit: u32) -> bool {
       // Try Redis first
       if let Ok(allowed) = redis_check(key, limit).await {
           return allowed;
       }
       
       // Fallback to memory
       let mut limiters = MEMORY_LIMITER.lock().unwrap();
       let bucket = limiters.entry(key.to_string())
           .or_insert_with(|| TokenBucket::new(limit));
       bucket.try_consume(1)
   }
   ```

## Error Messages

### Common Error Codes

| Error Code | Message | Solution |
|------------|---------|----------|
| `PARSE_001` | "Unsupported file type" | Check supported languages, add parser if needed |
| `PARSE_002` | "File too large" | Split file or increase size limit |
| `AUTH_001` | "Invalid token" | Refresh authentication token |
| `AUTH_002` | "Token expired" | Get new token |
| `STORAGE_001` | "Failed to upload" | Check Storage permissions |
| `SPANNER_001` | "Database unavailable" | Check Spanner status |
| `VERTEX_001` | "Embedding failed" | Check Vertex AI quota |
| `RATE_001` | "Rate limit exceeded" | Wait or upgrade plan |

### Error Response Format

```json
{
  "error": {
    "code": "PARSE_002",
    "message": "File too large: 105MB (max: 100MB)",
    "details": {
      "file_path": "src/generated/large_file.rs",
      "file_size": 110100480,
      "max_size": 104857600
    },
    "suggestions": [
      "Split the file into smaller modules",
      "Exclude generated files from analysis",
      "Contact support to increase limit"
    ]
  },
  "request_id": "req_abc123",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## Debugging Procedures

### Enable Debug Logging

```bash
# Maximum verbosity
export RUST_LOG=trace
export RUST_BACKTRACE=full

# Specific module debugging
export RUST_LOG=warn,analysis_engine::parser=debug,analysis_engine::services=trace

# With structured output
export RUST_LOG_FORMAT=json
```

### Trace Request Flow

```rust
// Add request ID to all operations
use tracing::{info_span, Instrument};

let request_id = Uuid::new_v4();
let span = info_span!("request", request_id = %request_id);

async move {
    info!("Starting analysis");
    
    // Track each phase
    let download_span = info_span!("download");
    let files = download_repository(url)
        .instrument(download_span)
        .await?;
    
    let parse_span = info_span!("parse", file_count = files.len());
    let parsed = parse_files(files)
        .instrument(parse_span)
        .await?;
    
    info!("Analysis complete");
    Ok(result)
}
.instrument(span)
.await
```

### Remote Debugging

```bash
# SSH into Cloud Run instance (if enabled)
gcloud run services describe analysis-engine \
    --region us-central1 \
    --format="value(status.url)"

# Attach debugger
gcloud run services proxy analysis-engine \
    --port 9229:9229

# Use Chrome DevTools or VS Code
```

## Recovery Procedures

### Service Recovery

```bash
#!/bin/bash
# scripts/recover.sh

echo "🚑 Starting Analysis Engine Recovery..."

# 1. Check current status
STATUS=$(gcloud run services describe analysis-engine \
    --region us-central1 \
    --format="value(status.conditions[0].status)")

if [ "$STATUS" != "True" ]; then
    echo "Service is unhealthy, attempting recovery..."
    
    # 2. Rollback to last known good version
    LAST_GOOD=$(gcloud run revisions list \
        --service analysis-engine \
        --region us-central1 \
        --format="value(metadata.name)" \
        --filter="status.conditions[0].status=True" \
        --limit 1)
    
    gcloud run services update-traffic analysis-engine \
        --to-revisions=${LAST_GOOD}=100 \
        --region us-central1
    
    echo "Rolled back to ${LAST_GOOD}"
fi

# 3. Clear caches
redis-cli FLUSHDB

# 4. Restart dependent services
kubectl rollout restart deployment/pattern-detector

# 5. Verify recovery
sleep 30
curl -s ${SERVICE_URL}/health/ready | jq .
```

### Data Recovery

```bash
# Restore from Spanner backup
gcloud spanner databases restore \
    --source-backup=daily-backup-$(date -d yesterday +%Y%m%d) \
    --destination-database=ccl-main \
    --destination-instance=ccl-production

# Restore Cloud Storage data
gsutil -m rsync -r \
    gs://ccl-backups/$(date -d yesterday +%Y%m%d)/ \
    gs://ccl-analysis-artifacts/
```

## Contact Support

### Escalation Path

1. **Level 1**: Check this guide and logs
2. **Level 2**: Platform team (#platform-support)
3. **Level 3**: On-call engineer (PagerDuty)

### Information to Provide

```bash
# Gather diagnostic information
./scripts/gather-diagnostics.sh > diagnostics.txt

# Include:
# - Request ID
# - Error messages
# - Steps to reproduce
# - Time of occurrence
# - User ID (if applicable)
```

### Support Channels

- **Slack**: #analysis-engine-support
- **Email**: <EMAIL>
- **On-Call**: Use PagerDuty for P1 issues
- **Documentation**: https://docs.ccl.dev/analysis-engine

---

Remember: Most issues can be resolved by checking logs, verifying configuration, and ensuring all dependencies are healthy. When in doubt, gather diagnostics and reach out to the support team.