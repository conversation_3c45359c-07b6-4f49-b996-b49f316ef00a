# ⚡ Analysis Engine Performance Tuning Guide

## Table of Contents
- [Performance Goals](#performance-goals)
- [Profiling & Benchmarking](#profiling--benchmarking)
- [Code-Level Optimizations](#code-level-optimizations)
- [Concurrency & Parallelism](#concurrency--parallelism)
- [Memory Optimization](#memory-optimization)
- [I/O Performance](#io-performance)
- [Caching Strategies](#caching-strategies)
- [Database Optimization](#database-optimization)
- [Network Optimization](#network-optimization)
- [Compilation Optimizations](#compilation-optimizations)
- [Production Tuning](#production-tuning)

## Performance Goals

### SLOs (Service Level Objectives)

| Metric | Target | Current | Status |
|--------|--------|---------|--------|
| Parse 1M LOC | <5 minutes | ~4.5 minutes | ✅ |
| API Response (p95) | <100ms | ~85ms | ✅ |
| API Response (p99) | <200ms | ~150ms | ✅ |
| Memory per Analysis | <4GB | ~3.2GB | ✅ |
| Concurrent Analyses | 100+ | 120 tested | ✅ |
| File Processing Rate | >1000 files/sec | ~1200 files/sec | ✅ |

### Performance Budget

```yaml
# Per-operation time budgets
operation_budgets:
  api_request_overhead: 10ms
  authentication: 5ms
  rate_limiting: 3ms
  file_download: 50ms/MB
  parsing: 10ms/file
  pattern_detection: 5ms/file
  embedding_generation: 100ms/chunk
  database_write: 20ms
  response_serialization: 5ms
```

## Profiling & Benchmarking

### CPU Profiling

#### Using perf

```bash
# Build with debug symbols
cargo build --release

# Profile CPU usage
sudo perf record -F 99 -g target/release/analysis-engine

# Generate report
sudo perf report

# Generate flame graph
sudo perf script | stackcollapse-perf.pl | flamegraph.pl > flamegraph.svg
```

#### Using cargo-flamegraph

```bash
# Install flamegraph
cargo install flamegraph

# Generate flame graph
cargo flamegraph --bin analysis-engine

# Profile specific test
cargo flamegraph --test parser_bench --bench bench_large_file
```

### Memory Profiling

#### Using Valgrind

```bash
# Install valgrind
sudo apt-get install valgrind

# Profile memory usage
valgrind --tool=massif --massif-out-file=massif.out \
  target/release/analysis-engine

# Visualize results
ms_print massif.out > memory_profile.txt
```

#### Using heaptrack

```bash
# Install heaptrack
sudo apt-get install heaptrack

# Profile heap allocations
heaptrack target/release/analysis-engine

# Analyze results
heaptrack --analyze heaptrack.analysis-engine.*.gz
```

### Benchmarking

#### Micro-benchmarks

```rust
// benches/parser_bench.rs
use criterion::{black_box, criterion_group, criterion_main, Criterion};

fn benchmark_parse_file(c: &mut Criterion) {
    let parser = Parser::new();
    let content = include_str!("../fixtures/large_file.rs");
    
    c.bench_function("parse_large_rust_file", |b| {
        b.iter(|| {
            parser.parse_content(black_box(content), "rust")
        })
    });
}

fn benchmark_parallel_parsing(c: &mut Criterion) {
    let parser = Arc::new(Parser::new());
    let files: Vec<PathBuf> = generate_test_files(100);
    
    c.bench_function("parse_100_files_parallel", |b| {
        b.iter(|| {
            parser.parse_files_parallel(black_box(files.clone()))
        })
    });
}

criterion_group!(benches, benchmark_parse_file, benchmark_parallel_parsing);
criterion_main!(benches);
```

Run benchmarks:
```bash
cargo bench

# Compare with baseline
cargo bench -- --save-baseline before
# Make changes
cargo bench -- --baseline before
```

## Code-Level Optimizations

### 1. Avoid Allocations

```rust
// ❌ Bad: Allocates new String
fn process_line(line: &str) -> String {
    line.trim().to_string()
}

// ✅ Good: Returns borrowed str
fn process_line(line: &str) -> &str {
    line.trim()
}

// ❌ Bad: Allocates Vec for temporary use
fn count_tokens(text: &str) -> usize {
    text.split_whitespace().collect::<Vec<_>>().len()
}

// ✅ Good: Uses iterator directly
fn count_tokens(text: &str) -> usize {
    text.split_whitespace().count()
}
```

### 2. Use Efficient Data Structures

```rust
// For small collections (<100 items), use arrays
// ❌ Bad: HashMap for small, fixed set
let mut languages = HashMap::new();
languages.insert("rs", "rust");
languages.insert("js", "javascript");

// ✅ Good: Array with binary search
const LANGUAGES: &[(&str, &str)] = &[
    ("js", "javascript"),
    ("rs", "rust"),
    // ... sorted
];

fn get_language(ext: &str) -> Option<&'static str> {
    LANGUAGES.binary_search_by_key(&ext, |&(e, _)| e)
        .ok()
        .map(|i| LANGUAGES[i].1)
}
```

### 3. Optimize Hot Paths

```rust
// Identify hot paths with profiling, then optimize
#[inline(always)]
fn is_valid_identifier_char(c: char) -> bool {
    c.is_alphanumeric() || c == '_'
}

// Use lookup tables for frequently checked values
static VALID_CHARS: [bool; 128] = {
    let mut table = [false; 128];
    // Pre-compute at compile time
    // ... initialization
    table
};

#[inline(always)]
fn is_valid_identifier_char_fast(c: u8) -> bool {
    c < 128 && VALID_CHARS[c as usize]
}
```

### 4. Minimize Cloning

```rust
// ❌ Bad: Unnecessary clones
fn process_files(files: Vec<String>) -> Vec<ParsedFile> {
    files.iter()
        .map(|f| parse_file(f.clone())) // Unnecessary clone
        .collect()
}

// ✅ Good: Borrow when possible
fn process_files(files: &[String]) -> Vec<ParsedFile> {
    files.iter()
        .map(|f| parse_file(f)) // Just borrow
        .collect()
}

// Use Cow for conditional cloning
use std::borrow::Cow;

fn normalize_path(path: &str) -> Cow<str> {
    if path.starts_with("./") {
        Cow::Owned(path[2..].to_string())
    } else {
        Cow::Borrowed(path)
    }
}
```

## Concurrency & Parallelism

### 1. Parallel Processing with Rayon

```rust
use rayon::prelude::*;

// Configure thread pool
lazy_static! {
    static ref PARSER_POOL: ThreadPool = rayon::ThreadPoolBuilder::new()
        .num_threads(num_cpus::get())
        .thread_name(|i| format!("parser-{}", i))
        .build()
        .unwrap();
}

// Parallel file processing
pub fn parse_files_parallel(&self, files: Vec<PathBuf>) -> Vec<ParsedFile> {
    PARSER_POOL.install(|| {
        files
            .par_iter()
            .filter_map(|path| {
                match self.parse_file(path) {
                    Ok(parsed) => Some(parsed),
                    Err(e) => {
                        warn!("Failed to parse {:?}: {}", path, e);
                        None
                    }
                }
            })
            .collect()
    })
}

// Chunked processing for better cache locality
pub fn analyze_chunks_parallel(&self, files: Vec<PathBuf>) -> Vec<Analysis> {
    const CHUNK_SIZE: usize = 32; // Tune based on cache size
    
    files
        .par_chunks(CHUNK_SIZE)
        .flat_map(|chunk| {
            chunk.iter()
                .map(|file| self.analyze_file(file))
                .collect::<Vec<_>>()
        })
        .collect()
}
```

### 2. Async I/O Optimization

```rust
use tokio::io::{AsyncReadExt, BufReader};
use futures::stream::{self, StreamExt};

// Concurrent file reading
pub async fn read_files_concurrent(
    paths: Vec<PathBuf>,
    max_concurrent: usize,
) -> Vec<Result<String, io::Error>> {
    stream::iter(paths)
        .map(|path| async move {
            let file = tokio::fs::File::open(&path).await?;
            let mut reader = BufReader::with_capacity(64 * 1024, file);
            let mut content = String::new();
            reader.read_to_string(&mut content).await?;
            Ok(content)
        })
        .buffer_unordered(max_concurrent)
        .collect()
        .await
}

// Streaming processing
pub async fn process_large_file_streaming(
    path: &Path,
    chunk_processor: impl Fn(&[u8]) -> Result<()>,
) -> Result<()> {
    let file = tokio::fs::File::open(path).await?;
    let mut reader = BufReader::with_capacity(1024 * 1024, file); // 1MB buffer
    
    let mut buffer = vec![0u8; 64 * 1024]; // 64KB chunks
    loop {
        let n = reader.read(&mut buffer).await?;
        if n == 0 {
            break;
        }
        chunk_processor(&buffer[..n])?;
    }
    
    Ok(())
}
```

### 3. Lock-Free Data Structures

```rust
use crossbeam::queue::ArrayQueue;
use std::sync::Arc;

// Lock-free work queue
pub struct WorkQueue<T> {
    queue: Arc<ArrayQueue<T>>,
    capacity: usize,
}

impl<T> WorkQueue<T> {
    pub fn new(capacity: usize) -> Self {
        Self {
            queue: Arc::new(ArrayQueue::new(capacity)),
            capacity,
        }
    }
    
    pub fn try_push(&self, item: T) -> Result<(), T> {
        self.queue.push(item)
    }
    
    pub fn try_pop(&self) -> Option<T> {
        self.queue.pop()
    }
}
```

## Memory Optimization

### 1. Memory Pool

```rust
use typed_arena::Arena;

pub struct AstNodePool {
    arena: Arena<AstNode>,
}

impl AstNodePool {
    pub fn new() -> Self {
        Self {
            arena: Arena::with_capacity(10000),
        }
    }
    
    pub fn alloc(&self, node: AstNode) -> &AstNode {
        self.arena.alloc(node)
    }
    
    // Arena automatically deallocates all nodes when dropped
}
```

### 2. String Interning

```rust
use string_interner::{StringInterner, Sym};

pub struct SymbolTable {
    interner: StringInterner,
}

impl SymbolTable {
    pub fn intern(&mut self, string: &str) -> Sym {
        self.interner.get_or_intern(string)
    }
    
    pub fn resolve(&self, symbol: Sym) -> Option<&str> {
        self.interner.resolve(symbol)
    }
}

// Use symbols instead of strings in AST
pub struct Identifier {
    symbol: Sym, // 8 bytes instead of 24+ for String
}
```

### 3. Memory-Mapped Files

```rust
use memmap2::MmapOptions;
use std::fs::File;

pub async fn analyze_large_file_mmap(path: &Path) -> Result<Analysis> {
    let file = File::open(path)?;
    let mmap = unsafe { MmapOptions::new().map(&file)? };
    
    // Process file without loading into memory
    let content = std::str::from_utf8(&mmap)?;
    let analysis = process_content(content);
    
    Ok(analysis)
}
```

## I/O Performance

### 1. Buffered I/O

```rust
use std::io::{BufReader, BufWriter};

// Reading
pub fn read_file_buffered(path: &Path) -> io::Result<String> {
    let file = File::open(path)?;
    let mut reader = BufReader::with_capacity(128 * 1024, file); // 128KB buffer
    let mut content = String::new();
    reader.read_to_string(&mut content)?;
    Ok(content)
}

// Writing
pub fn write_results_buffered(path: &Path, results: &[Result]) -> io::Result<()> {
    let file = File::create(path)?;
    let mut writer = BufWriter::with_capacity(128 * 1024, file);
    
    for result in results {
        serde_json::to_writer(&mut writer, result)?;
        writer.write_all(b"\n")?;
    }
    
    writer.flush()?;
    Ok(())
}
```

### 2. Parallel I/O

```rust
use tokio::fs;
use futures::future::join_all;

pub async fn download_files_parallel(
    urls: Vec<String>,
    max_concurrent: usize,
) -> Vec<Result<Vec<u8>, Error>> {
    use futures::stream::{self, StreamExt};
    
    stream::iter(urls)
        .map(|url| async move {
            let response = reqwest::get(&url).await?;
            let bytes = response.bytes().await?;
            Ok(bytes.to_vec())
        })
        .buffer_unordered(max_concurrent)
        .collect()
        .await
}
```

## Caching Strategies

### 1. Multi-Level Cache

```rust
use lru::LruCache;
use std::sync::Mutex;

pub struct MultiLevelCache {
    l1_cache: Mutex<LruCache<String, Arc<Analysis>>>, // In-memory
    l2_cache: Arc<RedisCache>,                        // Redis
    l3_cache: Arc<StorageCache>,                      // Cloud Storage
}

impl MultiLevelCache {
    pub async fn get(&self, key: &str) -> Option<Arc<Analysis>> {
        // Check L1
        if let Some(value) = self.l1_cache.lock().unwrap().get(key) {
            return Some(Arc::clone(value));
        }
        
        // Check L2
        if let Some(value) = self.l2_cache.get(key).await {
            let arc_value = Arc::new(value);
            self.l1_cache.lock().unwrap().put(key.to_string(), Arc::clone(&arc_value));
            return Some(arc_value);
        }
        
        // Check L3
        if let Some(value) = self.l3_cache.get(key).await {
            let arc_value = Arc::new(value);
            self.l2_cache.set(key, &value, 3600).await;
            self.l1_cache.lock().unwrap().put(key.to_string(), Arc::clone(&arc_value));
            return Some(arc_value);
        }
        
        None
    }
}
```

### 2. Cache Warming

```rust
pub async fn warm_cache_on_startup(cache: &Cache) -> Result<()> {
    // Pre-load frequently accessed patterns
    let patterns = db.get_popular_patterns(100).await?;
    for pattern in patterns {
        cache.set(&pattern.id, &pattern, 86400).await?;
    }
    
    // Pre-load recent analyses
    let recent = db.get_recent_analyses(50).await?;
    for analysis in recent {
        cache.set(&analysis.id, &analysis, 3600).await?;
    }
    
    info!("Cache warmed with {} entries", patterns.len() + recent.len());
    Ok(())
}
```

## Database Optimization

### 1. Connection Pooling

```rust
use deadpool::managed::{Pool, PoolConfig};

pub fn create_spanner_pool() -> Pool<SpannerManager> {
    let config = PoolConfig {
        max_size: 100,
        min_idle: Some(10),
        max_lifetime: Some(Duration::from_secs(300)),
        idle_timeout: Some(Duration::from_secs(60)),
        ..Default::default()
    };
    
    Pool::builder(SpannerManager::new())
        .config(config)
        .build()
        .unwrap()
}
```

### 2. Batch Operations

```rust
pub async fn insert_patterns_batch(
    patterns: Vec<Pattern>,
    batch_size: usize,
) -> Result<()> {
    for chunk in patterns.chunks(batch_size) {
        let mut transaction = spanner.transaction().await?;
        
        for pattern in chunk {
            transaction.insert("patterns", pattern)?;
        }
        
        transaction.commit().await?;
    }
    
    Ok(())
}
```

### 3. Query Optimization

```sql
-- Use composite indexes
CREATE INDEX idx_analyses_user_status_created 
ON analyses(user_id, status, created_at DESC);

-- Avoid N+1 queries
-- Bad: Separate queries for each analysis
SELECT * FROM analyses WHERE user_id = @user_id;
-- Then for each analysis:
SELECT * FROM patterns WHERE analysis_id = @analysis_id;

-- Good: Single query with join
SELECT 
  a.*,
  ARRAY_AGG(STRUCT(p.* AS pattern)) AS patterns
FROM analyses a
LEFT JOIN patterns p ON p.analysis_id = a.id
WHERE a.user_id = @user_id
GROUP BY a.id;
```

## Network Optimization

### 1. HTTP/2 Connection Reuse

```rust
use reqwest::Client;

lazy_static! {
    static ref HTTP_CLIENT: Client = Client::builder()
        .http2_prior_knowledge()
        .pool_max_idle_per_host(10)
        .pool_idle_timeout(Duration::from_secs(90))
        .timeout(Duration::from_secs(30))
        .build()
        .unwrap();
}

pub async fn fetch_with_reuse(url: &str) -> Result<String> {
    HTTP_CLIENT
        .get(url)
        .send()
        .await?
        .text()
        .await
        .map_err(Into::into)
}
```

### 2. Compression

```rust
use flate2::Compression;
use flate2::write::GzEncoder;

pub fn compress_response(data: &[u8]) -> Vec<u8> {
    let mut encoder = GzEncoder::new(Vec::new(), Compression::fast());
    encoder.write_all(data).unwrap();
    encoder.finish().unwrap()
}

// In Actix-web
use actix_web::middleware::Compress;

HttpServer::new(|| {
    App::new()
        .wrap(Compress::default())
        .service(api_routes)
})
```

## Compilation Optimizations

### 1. Release Profile

```toml
# Cargo.toml
[profile.release]
opt-level = 3          # Maximum optimizations
lto = true            # Link-time optimization
codegen-units = 1     # Single codegen unit
panic = 'abort'       # Smaller binary
strip = true          # Strip symbols

[profile.release-with-debug]
inherits = "release"
debug = true          # Keep debug info

# For benchmarks
[profile.bench]
opt-level = 3
lto = true
```

### 2. Target-Specific Optimizations

```toml
# .cargo/config.toml
[target.x86_64-unknown-linux-gnu]
rustflags = [
    "-C", "target-cpu=native",
    "-C", "link-arg=-fuse-ld=lld",  # Faster linker
]

[build]
target = "x86_64-unknown-linux-gnu"
```

### 3. Conditional Compilation

```rust
#[cfg(feature = "production")]
const BUFFER_SIZE: usize = 1024 * 1024; // 1MB for production

#[cfg(not(feature = "production"))]
const BUFFER_SIZE: usize = 64 * 1024; // 64KB for development

// Remove debug code in release
#[cfg(debug_assertions)]
macro_rules! debug_trace {
    ($($arg:tt)*) => { eprintln!($($arg)*); }
}

#[cfg(not(debug_assertions))]
macro_rules! debug_trace {
    ($($arg:tt)*) => {};
}
```

## Production Tuning

### 1. Cloud Run Configuration

```yaml
# Optimal configuration for Analysis Engine
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: analysis-engine
  annotations:
    run.googleapis.com/cpu-throttling: "false"
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # CPU allocation
        run.googleapis.com/cpu-boost: "true"
        # Startup boost
        run.googleapis.com/startup-cpu-boost: "true"
    spec:
      containers:
      - image: gcr.io/project/analysis-engine
        resources:
          limits:
            cpu: "4"
            memory: "4Gi"
        env:
        - name: RUST_LOG
          value: "warn,analysis_engine=info"
        - name: TOKIO_WORKER_THREADS
          value: "8"
        # Optimize for throughput
        - name: MALLOC_CONF
          value: "background_thread:true,max_background_threads:4"
```

### 2. Load Testing & Tuning

```bash
# Run load test
artillery run tests/load/analysis-api.yaml

# Monitor during load test
watch -n 1 'gcloud run services describe analysis-engine \
  --region us-central1 \
  --format "value(status.traffic[0].percent,status.conditions[0].message)"'

# Analyze results
artillery report analysis-api.json --output report.html
```

### 3. Performance Monitoring

```sql
-- Create performance monitoring view
CREATE OR REPLACE VIEW performance_metrics AS
SELECT 
  TIMESTAMP_TRUNC(timestamp, MINUTE) as minute,
  COUNT(*) as requests,
  AVG(duration_ms) as avg_duration,
  APPROX_QUANTILES(duration_ms, 100)[OFFSET(50)] as p50,
  APPROX_QUANTILES(duration_ms, 100)[OFFSET(95)] as p95,
  APPROX_QUANTILES(duration_ms, 100)[OFFSET(99)] as p99,
  SUM(CASE WHEN status_code >= 500 THEN 1 ELSE 0 END) as errors
FROM request_logs
WHERE timestamp > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
GROUP BY minute
ORDER BY minute DESC;
```

### 4. Auto-Tuning Script

```python
#!/usr/bin/env python3
# scripts/auto_tune.py

import subprocess
import json
import time

def run_benchmark():
    """Run performance benchmark and return metrics"""
    result = subprocess.run(
        ["cargo", "bench", "--", "--output-format=json"],
        capture_output=True,
        text=True
    )
    return json.loads(result.stdout)

def adjust_parameters(metrics):
    """Adjust runtime parameters based on metrics"""
    if metrics['p99_latency'] > 200:
        # Increase worker threads
        subprocess.run([
            "gcloud", "run", "services", "update", "analysis-engine",
            "--set-env-vars", "TOKIO_WORKER_THREADS=16"
        ])
    
    if metrics['memory_usage'] > 0.8:
        # Reduce batch size
        subprocess.run([
            "gcloud", "run", "services", "update", "analysis-engine",
            "--set-env-vars", "ANALYSIS_BATCH_SIZE=50"
        ])

def main():
    while True:
        metrics = run_benchmark()
        adjust_parameters(metrics)
        time.sleep(3600)  # Check hourly

if __name__ == "__main__":
    main()
```

---

This performance tuning guide provides comprehensive strategies for optimizing the Analysis Engine. Regular profiling and monitoring are essential to maintain optimal performance as the system scales. For specific performance issues, consult the [Troubleshooting Guide](../troubleshooting/README.md) or contact the performance team.