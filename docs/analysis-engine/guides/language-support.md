# 🌍 Analysis Engine Language Support Guide

## Table of Contents
- [Overview](#overview)
- [Currently Supported Languages](#currently-supported-languages)
- [Planned Language Support](#planned-language-support)
- [Adding a New Language](#adding-a-new-language)
- [Language Detection](#language-detection)
- [Parser Configuration](#parser-configuration)
- [Pattern Queries](#pattern-queries)
- [Testing Language Support](#testing-language-support)
- [Performance Considerations](#performance-considerations)
- [Troubleshooting](#troubleshooting)

## Overview

The Analysis Engine uses Tree-sitter parsers to generate accurate Abstract Syntax Trees (ASTs) for supported programming languages. This enables precise pattern detection and code analysis without false positives from comments or strings.

### Why Tree-sitter?

- **Accuracy**: Real parsing, not regex-based
- **Performance**: Incremental parsing and error recovery
- **Consistency**: Uniform API across all languages
- **Extensibility**: Easy to add new languages
- **Robustness**: Handles syntax errors gracefully

## Currently Supported Languages

### Production Ready (8 languages)

| Language | Extensions | Parser Version | Features | Status |
|----------|------------|----------------|----------|---------|
| **Rust** | `.rs` | 0.20.4 | Full AST, all patterns | ✅ Stable |
| **JavaScript** | `.js`, `.mjs` | 0.20.4 | ES2022, JSX | ✅ Stable |
| **TypeScript** | `.ts`, `.tsx` | 0.20.4 | TypeScript 5.0 | ✅ Stable |
| **Python** | `.py` | 0.20.4 | Python 3.11 | ✅ Stable |
| **Go** | `.go` | 0.20.4 | Go 1.21 | ✅ Stable |
| **Java** | `.java` | 0.20.2 | Java 17 | ✅ Stable |
| **C** | `.c`, `.h` | 0.20.6 | C11 | ✅ Stable |
| **C++** | `.cpp`, `.cc`, `.hpp` | 0.20.3 | C++20 | ✅ Stable |

### Language Features

```rust
// Current language configuration
lazy_static! {
    static ref LANGUAGE_CONFIG: HashMap<&'static str, LanguageConfig> = {
        let mut m = HashMap::new();
        
        m.insert("rust", LanguageConfig {
            name: "rust",
            parser: tree_sitter_rust::language(),
            file_extensions: vec![".rs"],
            line_comment: "//",
            block_comment: Some(("/*", "*/")),
            string_delimiters: vec!["\"", "'"],
            features: LanguageFeatures {
                has_generics: true,
                has_async: true,
                has_macros: true,
                has_modules: true,
            },
        });
        
        // ... other languages
        m
    };
}
```

## Planned Language Support

### Phase 1: Web Languages (Q1 2024)
| Language | Extensions | Priority | Use Cases |
|----------|------------|----------|-----------|
| HTML | `.html`, `.htm` | High | Web templates, components |
| CSS | `.css`, `.scss`, `.sass` | High | Styling patterns |
| PHP | `.php` | High | WordPress, Laravel |
| Ruby | `.rb` | High | Rails applications |

### Phase 2: Mobile Languages (Q2 2024)
| Language | Extensions | Priority | Use Cases |
|----------|------------|----------|-----------|
| Swift | `.swift` | High | iOS development |
| Kotlin | `.kt`, `.kts` | High | Android development |
| Objective-C | `.m`, `.mm` | Medium | Legacy iOS |
| Dart | `.dart` | Medium | Flutter apps |

### Phase 3: Data & Scripting (Q3 2024)
| Language | Extensions | Priority | Use Cases |
|----------|------------|----------|-----------|
| SQL | `.sql` | High | Database queries |
| R | `.r`, `.R` | Medium | Data analysis |
| Julia | `.jl` | Medium | Scientific computing |
| Shell | `.sh`, `.bash` | High | DevOps scripts |

### Phase 4: Functional Languages (Q4 2024)
| Language | Extensions | Priority | Use Cases |
|----------|------------|----------|-----------|
| Haskell | `.hs` | Low | Functional patterns |
| Scala | `.scala` | Medium | Big data (Spark) |
| Clojure | `.clj`, `.cljs` | Low | JVM functional |
| Erlang/Elixir | `.erl`, `.ex` | Low | Distributed systems |

### Phase 5: Configuration & Markup
| Language | Extensions | Priority | Use Cases |
|----------|------------|----------|-----------|
| YAML | `.yml`, `.yaml` | High | Configuration files |
| JSON | `.json` | High | Data files |
| XML | `.xml` | Medium | Configuration, data |
| Markdown | `.md` | Low | Documentation |

## Adding a New Language

### Step 1: Add Tree-sitter Dependency

```toml
# Cargo.toml
[dependencies]
tree-sitter-ruby = "0.20.0"  # Example for Ruby
```

### Step 2: Update Language Map

```rust
// src/parser/languages.rs
use tree_sitter_ruby;

lazy_static! {
    static ref PARSERS: HashMap<&'static str, Language> = {
        let mut m = HashMap::new();
        
        // Existing languages...
        
        // Add new language
        m.insert("ruby", tree_sitter_ruby::language());
        m
    };
}

// Update file extension mapping
lazy_static! {
    static ref EXTENSIONS: HashMap<&'static str, &'static str> = {
        let mut m = HashMap::new();
        
        // Existing extensions...
        
        // Add Ruby extensions
        m.insert("rb", "ruby");
        m.insert("rake", "ruby");
        m.insert("gemspec", "ruby");
        m
    };
}
```

### Step 3: Add Language Configuration

```rust
// src/parser/config.rs
impl LanguageConfig {
    pub fn ruby() -> Self {
        Self {
            name: "ruby",
            parser: tree_sitter_ruby::language(),
            file_extensions: vec![".rb", ".rake", ".gemspec"],
            line_comment: "#",
            block_comment: Some(("=begin", "=end")),
            string_delimiters: vec!["\"", "'", "`"],
            features: LanguageFeatures {
                has_generics: false,
                has_async: false,
                has_macros: false,
                has_modules: true,
            },
            node_types: NodeTypes {
                function: vec!["method", "singleton_method"],
                class: vec!["class", "module"],
                variable: vec!["identifier", "instance_variable", "class_variable"],
                import: vec!["require", "require_relative", "load"],
            },
        }
    }
}
```

### Step 4: Add Pattern Queries

```rust
// src/patterns/queries/ruby.rs
pub const RUBY_AUTH_PATTERN: &str = r#"
(method
  name: (identifier) @method_name
  (#match? @method_name "authenticate|authorize|current_user")
  body: (
    (_)*
  )
)
"#;

pub const RUBY_DATABASE_PATTERN: &str = r#"
(call
  receiver: (constant) @model
  (#match? @model "[A-Z][a-zA-Z]*")
  method: (identifier) @method
  (#match? @method "find|where|create|update|destroy")
)
"#;

pub const RUBY_API_PATTERN: &str = r#"
(call
  receiver: (identifier) @framework
  (#match? @framework "get|post|put|patch|delete")
  arguments: (argument_list
    (string) @route
  )
)
"#;
```

### Step 5: Add Tests

```rust
// tests/language_tests/ruby_test.rs
#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_parse_ruby_file() {
        let parser = Parser::new();
        let content = r#"
            class User < ApplicationRecord
              def authenticate(password)
                BCrypt::Password.new(password_digest) == password
              end
            end
        "#;
        
        let result = parser.parse_content(content, "ruby").unwrap();
        assert_eq!(result.language, "ruby");
        assert!(result.tree.root_node().child_count() > 0);
    }
    
    #[test]
    fn test_ruby_pattern_detection() {
        let detector = PatternDetector::new();
        let content = r#"
            def current_user
              @current_user ||= User.find(session[:user_id])
            end
        "#;
        
        let patterns = detector.detect(content, "ruby");
        assert!(patterns.iter().any(|p| p.category == "authentication"));
    }
    
    #[test]
    fn test_ruby_syntax_error_handling() {
        let parser = Parser::new();
        let content = "def incomplete_method"; // Missing 'end'
        
        let result = parser.parse_content(content, "ruby");
        assert!(result.is_ok());
        assert!(result.unwrap().has_errors);
    }
}
```

### Step 6: Update Documentation

```markdown
// docs/languages/ruby.md
# Ruby Language Support

## Supported Features
- Classes and modules
- Methods (instance and class)
- Variables (local, instance, class, global)
- Blocks and lambdas
- Pattern matching (Ruby 3.0+)

## Pattern Detection
- Authentication patterns (Devise, has_secure_password)
- Database patterns (ActiveRecord)
- API patterns (Rails routes, Grape)
- Security patterns (parameter filtering)

## Known Limitations
- Complex metaprogramming may not be fully analyzed
- Dynamic method definitions using define_method
- Some DSL-heavy code (RSpec, Rails routes)
```

## Language Detection

### Detection Strategy

```rust
pub fn detect_language(&self, path: &Path) -> Option<&'static str> {
    // 1. Try by file extension
    if let Some(ext) = path.extension().and_then(|e| e.to_str()) {
        if let Some(lang) = EXTENSIONS.get(ext) {
            return Some(lang);
        }
    }
    
    // 2. Try by filename
    let filename = path.file_name().and_then(|f| f.to_str())?;
    match filename {
        "Dockerfile" => return Some("dockerfile"),
        "Makefile" | "GNUmakefile" => return Some("make"),
        "CMakeLists.txt" => return Some("cmake"),
        "package.json" => return Some("json"),
        _ => {}
    }
    
    // 3. Try by shebang (first line)
    if let Ok(content) = std::fs::read_to_string(path) {
        if let Some(first_line) = content.lines().next() {
            if first_line.starts_with("#!") {
                return detect_language_from_shebang(first_line);
            }
        }
    }
    
    None
}

fn detect_language_from_shebang(shebang: &str) -> Option<&'static str> {
    if shebang.contains("python") { Some("python") }
    else if shebang.contains("ruby") { Some("ruby") }
    else if shebang.contains("node") { Some("javascript") }
    else if shebang.contains("bash") || shebang.contains("sh") { Some("bash") }
    else { None }
}
```

## Parser Configuration

### Performance Tuning

```rust
// Per-language parser configuration
pub struct ParserConfig {
    pub timeout_ms: u64,
    pub max_file_size: usize,
    pub cancellation_flag: Option<Arc<AtomicBool>>,
}

impl Default for ParserConfig {
    fn default() -> Self {
        Self {
            timeout_ms: 5000,      // 5 second timeout
            max_file_size: 10_485_760, // 10MB
            cancellation_flag: None,
        }
    }
}

// Language-specific overrides
pub fn get_parser_config(language: &str) -> ParserConfig {
    match language {
        "c" | "cpp" => ParserConfig {
            timeout_ms: 10000,  // Longer timeout for C++ templates
            max_file_size: 20_971_520, // 20MB for generated code
            ..Default::default()
        },
        "javascript" | "typescript" => ParserConfig {
            timeout_ms: 3000,   // Faster for JS
            max_file_size: 5_242_880, // 5MB
            ..Default::default()
        },
        _ => ParserConfig::default(),
    }
}
```

### Error Recovery

```rust
pub fn parse_with_error_recovery(
    &self,
    content: &str,
    language: &str,
) -> Result<ParsedFile> {
    let mut parser = tree_sitter::Parser::new();
    parser.set_language(self.get_language(language)?)?;
    
    // Enable error recovery
    parser.set_included_ranges(&[]).unwrap();
    
    match parser.parse(content, None) {
        Some(tree) => {
            let has_errors = tree.root_node().has_error();
            if has_errors {
                warn!("Parse tree contains errors for {}", language);
            }
            
            Ok(ParsedFile {
                tree,
                content: content.to_string(),
                language: language.to_string(),
                has_errors,
                error_nodes: if has_errors {
                    collect_error_nodes(&tree)
                } else {
                    vec![]
                },
            })
        }
        None => Err(ParseError::ParseFailed),
    }
}
```

## Pattern Queries

### Query Syntax

Tree-sitter uses S-expression queries with predicates:

```scheme
; Function definition pattern
(function_declaration
  name: (identifier) @function_name
  parameters: (parameters
    (parameter
      name: (identifier) @param_name
    )*
  )
  body: (block) @function_body
)

; With predicates
((identifier) @constant
 (#match? @constant "^[A-Z_]+$"))

; Negation
((string) @non_empty_string
 (#not-match? @non_empty_string "^\"\"$"))
```

### Language-Specific Patterns

```rust
pub struct LanguagePatterns {
    pub authentication: Vec<&'static str>,
    pub database: Vec<&'static str>,
    pub api: Vec<&'static str>,
    pub security: Vec<&'static str>,
}

lazy_static! {
    static ref PATTERNS: HashMap<&'static str, LanguagePatterns> = {
        let mut m = HashMap::new();
        
        // Python patterns
        m.insert("python", LanguagePatterns {
            authentication: vec![
                include_str!("queries/python/auth.scm"),
                include_str!("queries/python/django_auth.scm"),
            ],
            database: vec![
                include_str!("queries/python/sqlalchemy.scm"),
                include_str!("queries/python/django_orm.scm"),
            ],
            api: vec![
                include_str!("queries/python/flask_routes.scm"),
                include_str!("queries/python/fastapi.scm"),
            ],
            security: vec![
                include_str!("queries/python/crypto.scm"),
                include_str!("queries/python/validation.scm"),
            ],
        });
        
        m
    };
}
```

## Testing Language Support

### Language Test Suite

```rust
// tests/language_support_test.rs
#[cfg(test)]
mod language_tests {
    use super::*;
    
    #[test]
    fn test_all_languages_parse() {
        let parser = Parser::new();
        let test_files = get_language_test_files();
        
        for (language, test_file) in test_files {
            let content = std::fs::read_to_string(&test_file)
                .expect(&format!("Failed to read test file for {}", language));
            
            let result = parser.parse_content(&content, language);
            assert!(
                result.is_ok(),
                "Failed to parse {} test file: {:?}",
                language,
                result.err()
            );
            
            let parsed = result.unwrap();
            assert!(!parsed.has_errors, "{} parser produced errors", language);
            assert!(parsed.tree.root_node().child_count() > 0);
        }
    }
    
    #[test]
    fn test_pattern_detection_across_languages() {
        let detector = PatternDetector::new();
        
        let test_cases = vec![
            ("rust", include_str!("fixtures/auth.rs"), "authentication"),
            ("python", include_str!("fixtures/auth.py"), "authentication"),
            ("javascript", include_str!("fixtures/auth.js"), "authentication"),
            ("go", include_str!("fixtures/auth.go"), "authentication"),
        ];
        
        for (language, content, expected_pattern) in test_cases {
            let patterns = detector.detect(content, language);
            assert!(
                patterns.iter().any(|p| p.category == expected_pattern),
                "Failed to detect {} pattern in {}",
                expected_pattern,
                language
            );
        }
    }
}
```

### Benchmarking Languages

```rust
// benches/language_bench.rs
use criterion::{black_box, criterion_group, criterion_main, Criterion};

fn benchmark_language_parsing(c: &mut Criterion) {
    let parser = Parser::new();
    let mut group = c.benchmark_group("language_parsing");
    
    for (language, content) in get_benchmark_files() {
        group.bench_function(
            &format!("parse_{}", language),
            |b| b.iter(|| {
                parser.parse_content(black_box(&content), black_box(language))
            })
        );
    }
    
    group.finish();
}

criterion_group!(benches, benchmark_language_parsing);
criterion_main!(benches);
```

## Performance Considerations

### Language-Specific Optimizations

```rust
// Optimize based on language characteristics
pub fn optimize_parser_for_language(parser: &mut Parser, language: &str) {
    match language {
        "c" | "cpp" => {
            // C++ can have very deep nesting
            parser.set_max_depth(500);
        }
        "python" => {
            // Python relies on indentation
            parser.set_included_ranges(&get_non_comment_ranges());
        }
        "javascript" | "typescript" => {
            // JS can have large minified files
            parser.set_cancellation_flag(Some(cancellation_flag));
        }
        _ => {}
    }
}
```

### Memory Usage by Language

| Language | Avg Memory/File | Parse Time | AST Size |
|----------|----------------|------------|----------|
| C | 2.5 MB | 15ms | Large |
| C++ | 4.2 MB | 25ms | Very Large |
| Rust | 2.8 MB | 18ms | Large |
| Python | 1.2 MB | 8ms | Medium |
| JavaScript | 1.8 MB | 12ms | Medium |
| Go | 1.5 MB | 10ms | Medium |
| Java | 2.0 MB | 14ms | Large |
| TypeScript | 2.2 MB | 16ms | Large |

## Troubleshooting

### Common Issues

1. **Parser Version Conflicts**
   ```bash
   error: failed to select a version for `tree-sitter`
   ```
   Solution: Pin all tree-sitter parsers to the same minor version
   ```toml
   [dependencies]
   tree-sitter = "=0.20.10"
   tree-sitter-rust = "=0.20.4"
   tree-sitter-python = "=0.20.4"
   ```

2. **Missing Language Parser**
   ```
   Error: Unsupported language: ruby
   ```
   Solution: Check if parser is added to PARSERS map and dependency is included

3. **Parse Errors**
   ```
   Warning: Parse tree contains errors for javascript
   ```
   Solution: May be due to newer syntax not supported by parser version

### Adding Custom Languages

For proprietary or domain-specific languages:

```rust
// 1. Implement tree-sitter grammar
// 2. Generate parser
// 3. Create Rust bindings

// Example for custom DSL
extern "C" {
    fn tree_sitter_mydsl() -> Language;
}

pub fn language() -> Language {
    unsafe { tree_sitter_mydsl() }
}
```

---

For the latest language support status and roadmap, see the [Analysis Engine Roadmap](https://github.com/ccl/analysis-engine/blob/main/ROADMAP.md).