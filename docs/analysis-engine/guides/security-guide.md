# 🔒 Analysis Engine Security Guide

## Table of Contents
- [Security Overview](#security-overview)
- [Authentication & Authorization](#authentication--authorization)
- [Data Security](#data-security)
- [API Security](#api-security)
- [Infrastructure Security](#infrastructure-security)
- [Code Security](#code-security)
- [Compliance](#compliance)
- [Security Operations](#security-operations)
- [Incident Response](#incident-response)
- [Security Checklist](#security-checklist)

## Security Overview

The Analysis Engine implements defense-in-depth security with multiple layers of protection:

```
┌─────────────────────────────────────────────────────┐
│                   Public Internet                    │
└──────────────────────┬──────────────────────────────┘
                       │ HTTPS/TLS 1.3
┌──────────────────────▼──────────────────────────────┐
│              Cloud Armor (DDoS/WAF)                 │
└──────────────────────┬──────────────────────────────┘
                       │
┌──────────────────────▼──────────────────────────────┐
│          API Gateway (Rate Limiting)                │
└──────────────────────┬──────────────────────────────┘
                       │
┌──────────────────────▼──────────────────────────────┐
│     Authentication Layer (JWT/API Keys)             │
└──────────────────────┬──────────────────────────────┘
                       │
┌──────────────────────▼──────────────────────────────┐
│      Authorization Layer (RBAC/Policies)            │
└──────────────────────┬──────────────────────────────┘
                       │
┌──────────────────────▼──────────────────────────────┐
│         Application Security Controls               │
│  (Input Validation, Output Encoding, CSRF)          │
└──────────────────────┬──────────────────────────────┘
                       │
┌──────────────────────▼──────────────────────────────┐
│           Encrypted Data Storage                    │
│     (Spanner, Cloud Storage, Redis)                │
└─────────────────────────────────────────────────────┘
```

### Security Principles

1. **Zero Trust**: Never trust, always verify
2. **Least Privilege**: Minimal permissions required
3. **Defense in Depth**: Multiple security layers
4. **Secure by Default**: Safe defaults, opt-in for risk
5. **Audit Everything**: Comprehensive logging

## Authentication & Authorization

### JWT Authentication

```rust
use jsonwebtoken::{decode, encode, Algorithm, DecodingKey, EncodingKey, Header, Validation};

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,        // User ID
    pub email: String,
    pub email_verified: bool,
    pub roles: Vec<String>,
    pub exp: i64,          // Expiration time
    pub iat: i64,          // Issued at
    pub iss: String,       // Issuer
}

impl Claims {
    pub fn validate(&self) -> Result<(), AuthError> {
        let now = Utc::now().timestamp();
        
        if self.exp < now {
            return Err(AuthError::TokenExpired);
        }
        
        if !self.email_verified {
            return Err(AuthError::EmailNotVerified);
        }
        
        if self.iss != EXPECTED_ISSUER {
            return Err(AuthError::InvalidIssuer);
        }
        
        Ok(())
    }
}

pub fn verify_jwt(token: &str) -> Result<Claims, AuthError> {
    let validation = Validation {
        algorithms: vec![Algorithm::RS256],
        validate_exp: true,
        validate_nbf: true,
        iss: Some(EXPECTED_ISSUER.to_string()),
        ..Default::default()
    };
    
    let token_data = decode::<Claims>(
        token,
        &DecodingKey::from_rsa_pem(PUBLIC_KEY.as_bytes())?,
        &validation
    )?;
    
    token_data.claims.validate()?;
    Ok(token_data.claims)
}
```

### API Key Authentication

```rust
use argon2::{Argon2, PasswordHash, PasswordVerifier};

pub struct ApiKeyValidator {
    spanner_client: Arc<SpannerClient>,
    cache: Arc<Cache>,
}

impl ApiKeyValidator {
    pub async fn validate(&self, api_key: &str) -> Result<ApiKeyInfo, AuthError> {
        // Check cache first
        if let Some(info) = self.cache.get(api_key).await {
            return Ok(info);
        }
        
        // Hash the API key for database lookup
        let key_hash = self.hash_api_key(api_key);
        
        // Query database
        let info = self.spanner_client
            .query_one::<ApiKeyInfo>(
                "SELECT user_id, subscription_tier, rate_limit, is_active 
                 FROM api_keys 
                 WHERE key_hash = @key_hash",
                &[("key_hash", &key_hash)]
            )
            .await?
            .ok_or(AuthError::InvalidApiKey)?;
        
        if !info.is_active {
            return Err(AuthError::ApiKeyInactive);
        }
        
        // Cache for 5 minutes
        self.cache.set(api_key, &info, 300).await?;
        
        Ok(info)
    }
    
    fn hash_api_key(&self, api_key: &str) -> String {
        use sha2::{Sha256, Digest};
        let mut hasher = Sha256::new();
        hasher.update(api_key.as_bytes());
        hex::encode(hasher.finalize())
    }
}
```

### Role-Based Access Control (RBAC)

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Role {
    Admin,
    User,
    ReadOnly,
    Service,
}

#[derive(Debug, Clone)]
pub struct Permission {
    pub resource: String,
    pub action: String,
}

lazy_static! {
    static ref ROLE_PERMISSIONS: HashMap<Role, Vec<Permission>> = {
        let mut m = HashMap::new();
        
        m.insert(Role::Admin, vec![
            Permission { resource: "*".to_string(), action: "*".to_string() },
        ]);
        
        m.insert(Role::User, vec![
            Permission { resource: "analyses".to_string(), action: "create".to_string() },
            Permission { resource: "analyses".to_string(), action: "read".to_string() },
            Permission { resource: "analyses".to_string(), action: "delete".to_string() },
            Permission { resource: "patterns".to_string(), action: "read".to_string() },
        ]);
        
        m.insert(Role::ReadOnly, vec![
            Permission { resource: "analyses".to_string(), action: "read".to_string() },
            Permission { resource: "patterns".to_string(), action: "read".to_string() },
        ]);
        
        m
    };
}

pub fn check_permission(roles: &[Role], resource: &str, action: &str) -> bool {
    roles.iter().any(|role| {
        ROLE_PERMISSIONS.get(role)
            .map(|perms| {
                perms.iter().any(|p| {
                    (p.resource == "*" || p.resource == resource) &&
                    (p.action == "*" || p.action == action)
                })
            })
            .unwrap_or(false)
    })
}
```

## Data Security

### Encryption at Rest

```yaml
# Spanner encryption
resource "google_spanner_database" "main" {
  encryption_config {
    kms_key_name = google_kms_crypto_key.database_key.id
  }
}

# Cloud Storage encryption
resource "google_storage_bucket" "artifacts" {
  encryption {
    default_kms_key_name = google_kms_crypto_key.storage_key.id
  }
}
```

### Encryption in Transit

```rust
// Force TLS for all connections
use rustls::{ClientConfig, OwnedTrustAnchor};

pub fn create_tls_config() -> ClientConfig {
    let mut config = ClientConfig::builder()
        .with_safe_defaults()
        .with_root_certificates(load_root_certs())
        .with_no_client_auth();
    
    // Force TLS 1.3
    config.alpn_protocols = vec![b"h2".to_vec()];
    config.min_protocol_version = Some(rustls::ProtocolVersion::TLSv1_3);
    
    config
}

// Redis TLS connection
pub async fn create_redis_client(url: &str) -> Result<Client> {
    let client = redis::Client::open(url)?;
    
    // Verify TLS certificate
    let connection_info = client.get_connection_info();
    if !connection_info.addr.is_secure() {
        return Err(SecurityError::InsecureConnection);
    }
    
    Ok(client)
}
```

### Data Sanitization

```rust
use ammonia::clean;
use regex::Regex;

pub struct DataSanitizer {
    email_regex: Regex,
    secret_regex: Regex,
}

impl DataSanitizer {
    pub fn new() -> Self {
        Self {
            email_regex: Regex::new(r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}").unwrap(),
            secret_regex: Regex::new(r"(?i)(api[_-]?key|secret|password|token)[\s]*[:=][\s]*[^\s]+").unwrap(),
        }
    }
    
    pub fn sanitize_for_storage(&self, content: &str) -> String {
        let mut sanitized = content.to_string();
        
        // Remove email addresses
        sanitized = self.email_regex.replace_all(&sanitized, "[EMAIL]").to_string();
        
        // Remove potential secrets
        sanitized = self.secret_regex.replace_all(&sanitized, "[REDACTED]").to_string();
        
        // Remove HTML/script tags
        sanitized = clean(&sanitized);
        
        sanitized
    }
    
    pub fn validate_repository_url(&self, url: &str) -> Result<String, ValidationError> {
        let parsed = Url::parse(url)?;
        
        // Only allow HTTPS
        if parsed.scheme() != "https" {
            return Err(ValidationError::InsecureProtocol);
        }
        
        // Whitelist allowed hosts
        let allowed_hosts = ["github.com", "gitlab.com", "bitbucket.org"];
        if !allowed_hosts.contains(&parsed.host_str().unwrap_or("")) {
            return Err(ValidationError::UnauthorizedHost);
        }
        
        Ok(url.to_string())
    }
}
```

## API Security

### Input Validation

```rust
use validator::{Validate, ValidationError};

#[derive(Debug, Deserialize, Validate)]
pub struct AnalysisRequest {
    #[validate(url(message = "Invalid repository URL"))]
    #[validate(custom = "validate_safe_url")]
    pub repository_url: String,
    
    #[validate(length(max = 100, message = "Branch name too long"))]
    #[validate(regex(path = "SAFE_BRANCH_REGEX", message = "Invalid branch name"))]
    pub branch: Option<String>,
    
    #[validate(length(max = 20, message = "Too many patterns"))]
    pub patterns: Vec<String>,
    
    #[validate(length(max = 10, message = "Too many languages"))]
    pub languages: Vec<String>,
}

lazy_static! {
    static ref SAFE_BRANCH_REGEX: Regex = Regex::new(r"^[a-zA-Z0-9/_.-]+$").unwrap();
}

fn validate_safe_url(url: &str) -> Result<(), ValidationError> {
    // Prevent SSRF attacks
    let parsed = Url::parse(url).map_err(|_| ValidationError::new("invalid_url"))?;
    
    // Block internal IPs
    if let Some(host) = parsed.host() {
        match host {
            url::Host::Ipv4(ip) => {
                if ip.is_private() || ip.is_loopback() {
                    return Err(ValidationError::new("internal_ip_blocked"));
                }
            }
            url::Host::Domain(domain) => {
                // Block internal domains
                if domain == "localhost" || domain.ends_with(".local") {
                    return Err(ValidationError::new("internal_domain_blocked"));
                }
            }
            _ => {}
        }
    }
    
    Ok(())
}
```

### Rate Limiting

```rust
use governor::{Quota, RateLimiter, state::keyed::DefaultKeyedStateStore};

pub struct RateLimitManager {
    limiters: HashMap<SubscriptionTier, Arc<RateLimiter<String, DefaultKeyedStateStore<String>>>>,
}

impl RateLimitManager {
    pub fn new() -> Self {
        let mut limiters = HashMap::new();
        
        // Define rate limits per tier
        limiters.insert(
            SubscriptionTier::Free,
            Arc::new(RateLimiter::keyed(Quota::per_hour(nonzero!(100u32))))
        );
        
        limiters.insert(
            SubscriptionTier::Pro,
            Arc::new(RateLimiter::keyed(Quota::per_hour(nonzero!(1000u32))))
        );
        
        limiters.insert(
            SubscriptionTier::Team,
            Arc::new(RateLimiter::keyed(Quota::per_hour(nonzero!(10000u32))))
        );
        
        Self { limiters }
    }
    
    pub async fn check_rate_limit(
        &self,
        user_id: &str,
        tier: SubscriptionTier,
    ) -> Result<(), RateLimitError> {
        let limiter = self.limiters.get(&tier)
            .ok_or(RateLimitError::UnknownTier)?;
        
        match limiter.check_key(&user_id.to_string()) {
            Ok(_) => Ok(()),
            Err(not_until) => {
                let retry_after = not_until.wait_time_from(clock::now());
                Err(RateLimitError::LimitExceeded { retry_after })
            }
        }
    }
}
```

### CORS Configuration

```rust
use actix_cors::Cors;

pub fn configure_cors() -> Cors {
    Cors::default()
        .allowed_origin_fn(|origin, _req_head| {
            // Allow specific origins
            let allowed_origins = [
                "https://app.ccl.dev",
                "https://staging.ccl.dev",
            ];
            
            allowed_origins.iter().any(|allowed| {
                origin.as_bytes() == allowed.as_bytes()
            })
        })
        .allowed_methods(vec!["GET", "POST", "PUT", "DELETE", "OPTIONS"])
        .allowed_headers(vec![
            header::AUTHORIZATION,
            header::CONTENT_TYPE,
            header::HeaderName::from_static("x-api-key"),
        ])
        .expose_headers(vec![
            header::HeaderName::from_static("x-ratelimit-limit"),
            header::HeaderName::from_static("x-ratelimit-remaining"),
            header::HeaderName::from_static("x-ratelimit-reset"),
        ])
        .max_age(3600)
        .supports_credentials()
}
```

## Infrastructure Security

### Network Security

```yaml
# VPC configuration
resource "google_compute_network" "vpc" {
  name                    = "ccl-vpc"
  auto_create_subnetworks = false
}

resource "google_compute_subnetwork" "private" {
  name          = "ccl-private-subnet"
  network       = google_compute_network.vpc.id
  ip_cidr_range = "10.0.0.0/24"
  region        = "us-central1"
  
  private_ip_google_access = true
  
  log_config {
    aggregation_interval = "INTERVAL_5_SEC"
    flow_sampling       = 0.5
    metadata           = "INCLUDE_ALL_METADATA"
  }
}

# Firewall rules
resource "google_compute_firewall" "deny_all_ingress" {
  name    = "deny-all-ingress"
  network = google_compute_network.vpc.name
  
  priority = 1000
  
  deny {
    protocol = "all"
  }
  
  source_ranges = ["0.0.0.0/0"]
}

resource "google_compute_firewall" "allow_cloud_run" {
  name    = "allow-cloud-run"
  network = google_compute_network.vpc.name
  
  priority = 900
  
  allow {
    protocol = "tcp"
    ports    = ["443", "8001"]
  }
  
  source_ranges = ["************/19"] # Cloud Run IPs
  target_tags   = ["analysis-engine"]
}
```

### Secret Management

```rust
use google_cloud_secretmanager::client::{Client, ClientConfig};

pub struct SecretManager {
    client: Client,
    project_id: String,
}

impl SecretManager {
    pub async fn new(project_id: String) -> Result<Self> {
        let config = ClientConfig::default().with_auth().await?;
        let client = Client::new(config).await?;
        
        Ok(Self { client, project_id })
    }
    
    pub async fn get_secret(&self, secret_name: &str) -> Result<String> {
        let secret_path = format!(
            "projects/{}/secrets/{}/versions/latest",
            self.project_id,
            secret_name
        );
        
        let response = self.client
            .access_secret_version(secret_path, None)
            .await?;
        
        let secret_data = response.payload
            .ok_or_else(|| SecurityError::SecretNotFound)?
            .data;
        
        String::from_utf8(secret_data)
            .map_err(|_| SecurityError::InvalidSecretFormat)
    }
}

// Use in application
lazy_static! {
    static ref JWT_SECRET: String = {
        tokio::runtime::Runtime::new()
            .unwrap()
            .block_on(async {
                let manager = SecretManager::new(PROJECT_ID.to_string())
                    .await
                    .expect("Failed to initialize secret manager");
                
                manager.get_secret("jwt-signing-key")
                    .await
                    .expect("Failed to load JWT secret")
            })
    };
}
```

### Container Security

```dockerfile
# Use distroless base image
FROM gcr.io/distroless/cc-debian11

# Run as non-root user
USER nonroot:nonroot

# Copy only necessary files
COPY --from=builder --chown=nonroot:nonroot /app/target/release/analysis-engine /app/

# No shell or package manager in production image
ENTRYPOINT ["/app/analysis-engine"]
```

## Code Security

### Dependency Scanning

```toml
# Cargo.toml
[dependencies]
# Pin all dependencies to specific versions
tokio = "=1.35.0"
actix-web = "=4.4.0"
serde = "=1.0.193"

# Security-focused dependencies
secrecy = "0.8"  # Secure secret handling
zeroize = "1.7"  # Secure memory wiping
```

```bash
# Regular security audits
cargo audit
cargo outdated
cargo tree --duplicate

# License compliance
cargo license
```

### Static Analysis

```rust
// Enforce security lints
#![deny(
    unsafe_code,
    missing_docs,
    trivial_casts,
    trivial_numeric_casts,
    unused_extern_crates,
    unused_import_braces,
    unused_qualifications,
    clippy::unwrap_used,
    clippy::expect_used,
    clippy::panic,
    clippy::unimplemented,
    clippy::todo,
)]

// Use secure random
use rand::rngs::OsRng;
use rand::RngCore;

pub fn generate_secure_token() -> String {
    let mut bytes = [0u8; 32];
    OsRng.fill_bytes(&mut bytes);
    base64::encode_config(bytes, base64::URL_SAFE_NO_PAD)
}
```

## Compliance

### Data Privacy (GDPR/CCPA)

```rust
pub struct PrivacyManager {
    encryption_key: Key<Aes256Gcm>,
}

impl PrivacyManager {
    pub async fn handle_data_request(&self, user_id: &str, request_type: DataRequest) -> Result<()> {
        match request_type {
            DataRequest::Export => self.export_user_data(user_id).await,
            DataRequest::Delete => self.delete_user_data(user_id).await,
            DataRequest::Rectification(data) => self.update_user_data(user_id, data).await,
        }
    }
    
    async fn delete_user_data(&self, user_id: &str) -> Result<()> {
        // Start transaction
        let mut txn = self.spanner.transaction().await?;
        
        // Delete from all tables
        txn.execute_update(
            "DELETE FROM analyses WHERE user_id = @user_id",
            &[("user_id", &user_id)]
        ).await?;
        
        txn.execute_update(
            "DELETE FROM api_keys WHERE user_id = @user_id",
            &[("user_id", &user_id)]
        ).await?;
        
        // Delete from Cloud Storage
        self.delete_user_artifacts(user_id).await?;
        
        // Commit transaction
        txn.commit().await?;
        
        // Audit log
        self.audit_log(AuditEvent::DataDeleted { user_id }).await?;
        
        Ok(())
    }
}
```

### Audit Logging

```rust
#[derive(Serialize)]
pub struct AuditLog {
    pub timestamp: DateTime<Utc>,
    pub event_type: String,
    pub user_id: Option<String>,
    pub resource: String,
    pub action: String,
    pub outcome: String,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub request_id: String,
    pub details: serde_json::Value,
}

pub async fn audit_log(event: AuditEvent, request: &HttpRequest) -> Result<()> {
    let log = AuditLog {
        timestamp: Utc::now(),
        event_type: event.event_type(),
        user_id: event.user_id(),
        resource: event.resource(),
        action: event.action(),
        outcome: event.outcome(),
        ip_address: get_client_ip(request),
        user_agent: get_user_agent(request),
        request_id: get_request_id(request),
        details: serde_json::to_value(&event)?,
    };
    
    // Write to audit log
    AUDIT_LOGGER.log(&log).await?;
    
    // Alert on suspicious activity
    if event.is_suspicious() {
        alert_security_team(&log).await?;
    }
    
    Ok(())
}
```

## Security Operations

### Monitoring & Alerting

```yaml
# Security alerts
resource "google_monitoring_alert_policy" "suspicious_activity" {
  display_name = "Suspicious Activity Detection"
  
  conditions {
    display_name = "Multiple failed auth attempts"
    
    condition_threshold {
      filter = <<-EOT
        resource.type = "cloud_run_revision"
        resource.labels.service_name = "analysis-engine"
        jsonPayload.event_type = "auth_failed"
      EOT
      
      aggregations {
        alignment_period     = "300s"
        per_series_aligner   = "ALIGN_COUNT"
        cross_series_reducer = "REDUCE_SUM"
        group_by_fields      = ["jsonPayload.ip_address"]
      }
      
      comparison      = "COMPARISON_GT"
      threshold_value = 10
      duration        = "300s"
    }
  }
  
  notification_channels = [
    google_monitoring_notification_channel.security_team.name
  ]
}
```

### Security Scanning

```bash
#!/bin/bash
# security-scan.sh

echo "🔒 Running security scan..."

# SAST scan
echo "Running static analysis..."
semgrep --config=auto --json -o sast-report.json .

# Dependency scan
echo "Checking dependencies..."
cargo audit --json > dependency-report.json

# Container scan
echo "Scanning container..."
trivy image --format json -o container-report.json gcr.io/project/analysis-engine:latest

# Secret scan
echo "Scanning for secrets..."
gitleaks detect --report-format json --report-path secrets-report.json

# Compile results
python3 compile_security_report.py
```

## Incident Response

### Incident Response Plan

```rust
pub enum SecurityIncident {
    DataBreach { affected_users: Vec<String> },
    UnauthorizedAccess { user_id: String, resource: String },
    DosAttack { source_ips: Vec<String> },
    MaliciousInput { payload: String },
}

pub async fn handle_security_incident(incident: SecurityIncident) -> Result<()> {
    // 1. Contain
    match &incident {
        SecurityIncident::UnauthorizedAccess { user_id, .. } => {
            block_user(user_id).await?;
        }
        SecurityIncident::DosAttack { source_ips } => {
            for ip in source_ips {
                block_ip(ip).await?;
            }
        }
        _ => {}
    }
    
    // 2. Assess
    let severity = assess_severity(&incident);
    let impact = assess_impact(&incident).await?;
    
    // 3. Notify
    if severity >= Severity::High {
        notify_security_team(&incident, severity, &impact).await?;
        
        if matches!(incident, SecurityIncident::DataBreach { .. }) {
            // Legal requirement to notify within 72 hours
            schedule_user_notification(&incident, Duration::hours(48)).await?;
        }
    }
    
    // 4. Eradicate
    match incident {
        SecurityIncident::MaliciousInput { payload } => {
            add_to_blocklist(&payload).await?;
        }
        _ => {}
    }
    
    // 5. Recover
    restore_normal_operations().await?;
    
    // 6. Lessons learned
    create_incident_report(&incident, &actions_taken).await?;
    
    Ok(())
}
```

## Security Checklist

### Development Phase
- [ ] All dependencies pinned to specific versions
- [ ] Security lints enabled in Cargo.toml
- [ ] No hardcoded secrets or credentials
- [ ] Input validation on all endpoints
- [ ] Output encoding for all responses
- [ ] Error messages don't leak sensitive info
- [ ] All SQL queries use parameterization
- [ ] Authentication required on all endpoints
- [ ] Authorization checks for all resources
- [ ] Rate limiting implemented
- [ ] CORS properly configured
- [ ] Security headers set

### Deployment Phase
- [ ] Container runs as non-root user
- [ ] Minimal base image (distroless)
- [ ] Network policies configured
- [ ] Secrets stored in Secret Manager
- [ ] TLS 1.3 enforced
- [ ] Firewall rules reviewed
- [ ] Service account permissions minimal
- [ ] Audit logging enabled
- [ ] Security monitoring configured
- [ ] Incident response plan tested

### Operational Phase
- [ ] Regular security scans scheduled
- [ ] Dependency updates reviewed
- [ ] Security patches applied promptly
- [ ] Access logs reviewed regularly
- [ ] Anomaly detection configured
- [ ] Backup encryption verified
- [ ] Disaster recovery tested
- [ ] Compliance audits scheduled
- [ ] Security training completed

---

This security guide is a living document. For security concerns or to report vulnerabilities, contact <EMAIL>.