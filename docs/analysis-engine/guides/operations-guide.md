# 🚀 Analysis Engine Operations Guide

## Table of Contents
- [Deployment](#deployment)
  - [Cloud Run Deployment](#cloud-run-deployment)
  - [Kubernetes Deployment](#kubernetes-deployment)
  - [Multi-Region Setup](#multi-region-setup)
- [Configuration Management](#configuration-management)
- [Monitoring & Observability](#monitoring--observability)
  - [Metrics](#metrics)
  - [Logging](#logging)
  - [Distributed Tracing](#distributed-tracing)
  - [Alerting](#alerting)
- [Operational Procedures](#operational-procedures)
  - [Health Checks](#health-checks)
  - [Scaling](#scaling)
  - [Backup & Recovery](#backup--recovery)
  - [Incident Response](#incident-response)
- [Performance Monitoring](#performance-monitoring)
- [Security Operations](#security-operations)
- [Maintenance Tasks](#maintenance-tasks)

## Deployment

### Cloud Run Deployment

#### Prerequisites
- Google Cloud SDK installed and configured
- Docker installed
- Appropriate IAM permissions

#### Step 1: Build and Push Container

```bash
# Set environment variables
export PROJECT_ID=vibe-match-463114
export REGION=us-central1
export SERVICE_NAME=analysis-engine
export IMAGE_TAG=latest

# Build the container
docker build -t gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG} \
  -f services/analysis-engine/Dockerfile \
  services/analysis-engine

# Configure Docker for GCR
gcloud auth configure-docker

# Push to Container Registry
docker push gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG}
```

#### Step 2: Deploy to Cloud Run

```bash
# Deploy the service
gcloud run deploy ${SERVICE_NAME} \
  --image gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG} \
  --platform managed \
  --region ${REGION} \
  --port 8001 \
  --memory 4Gi \
  --cpu 4 \
  --timeout 300s \
  --concurrency 100 \
  --min-instances 1 \
  --max-instances 100 \
  --set-env-vars "RUST_LOG=info" \
  --set-env-vars "GCP_PROJECT_ID=${PROJECT_ID}" \
  --set-env-vars "SPANNER_INSTANCE_ID=ccl-production" \
  --set-env-vars "SPANNER_DATABASE_ID=ccl-main" \
  --set-env-vars "STORAGE_BUCKET=ccl-analysis-artifacts" \
  --set-env-vars "PUBSUB_TOPIC=analysis-events" \
  --set-env-vars "VERTEX_AI_LOCATION=${REGION}" \
  --set-env-vars "REDIS_URL=redis://10.0.0.3:6379" \
  --service-account analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com \
  --vpc-connector ccl-vpc-connector \
  --allow-unauthenticated
```

#### Step 3: Verify Deployment

```bash
# Get service URL
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} \
  --platform managed \
  --region ${REGION} \
  --format 'value(status.url)')

# Test health endpoint
curl ${SERVICE_URL}/health

# Check logs
gcloud logging read "resource.type=cloud_run_revision \
  AND resource.labels.service_name=${SERVICE_NAME}" \
  --limit 50 --format json
```

### Kubernetes Deployment

For GKE or other Kubernetes deployments:

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: analysis-engine
  labels:
    app: analysis-engine
spec:
  replicas: 3
  selector:
    matchLabels:
      app: analysis-engine
  template:
    metadata:
      labels:
        app: analysis-engine
    spec:
      serviceAccountName: analysis-engine-sa
      containers:
      - name: analysis-engine
        image: gcr.io/vibe-match-463114/analysis-engine:latest
        ports:
        - containerPort: 8001
          protocol: TCP
        env:
        - name: RUST_LOG
          value: "info"
        - name: GCP_PROJECT_ID
          value: "vibe-match-463114"
        - name: SPANNER_INSTANCE_ID
          valueFrom:
            secretKeyRef:
              name: analysis-engine-secrets
              key: spanner-instance-id
        resources:
          requests:
            memory: "2Gi"
            cpu: "1"
          limits:
            memory: "4Gi"
            cpu: "4"
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8001
          initialDelaySeconds: 10
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: analysis-engine
spec:
  type: LoadBalancer
  selector:
    app: analysis-engine
  ports:
  - port: 80
    targetPort: 8001
    protocol: TCP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: analysis-engine-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: analysis-engine
  minReplicas: 3
  maxReplicas: 100
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

Deploy to GKE:
```bash
kubectl apply -f k8s/deployment.yaml
kubectl rollout status deployment/analysis-engine
```

### Multi-Region Setup

For global availability and disaster recovery:

#### 1. Deploy to Multiple Regions

```bash
# Deploy to US
gcloud run deploy analysis-engine \
  --image gcr.io/${PROJECT_ID}/analysis-engine:latest \
  --region us-central1 \
  --tag us

# Deploy to Europe
gcloud run deploy analysis-engine \
  --image gcr.io/${PROJECT_ID}/analysis-engine:latest \
  --region europe-west1 \
  --tag eu

# Deploy to Asia
gcloud run deploy analysis-engine \
  --image gcr.io/${PROJECT_ID}/analysis-engine:latest \
  --region asia-northeast1 \
  --tag asia
```

#### 2. Configure Global Load Balancer

```bash
# Create NEG for each region
gcloud compute network-endpoint-groups create analysis-engine-us-neg \
  --region=us-central1 \
  --network-endpoint-type=serverless \
  --cloud-run-service=analysis-engine

# Create backend service
gcloud compute backend-services create analysis-engine-backend \
  --global \
  --load-balancing-scheme=EXTERNAL \
  --protocol=HTTPS

# Add backends
gcloud compute backend-services add-backend analysis-engine-backend \
  --global \
  --network-endpoint-group=analysis-engine-us-neg \
  --network-endpoint-group-region=us-central1
```

## Configuration Management

### Environment-Specific Configs

```bash
# environments/production.yaml
name: production
config:
  log_level: info
  max_instances: 100
  min_instances: 5
  memory: 4Gi
  cpu: 4
  timeout: 300s
  
# environments/staging.yaml
name: staging
config:
  log_level: debug
  max_instances: 10
  min_instances: 1
  memory: 2Gi
  cpu: 2
  timeout: 600s
```

### Secret Management

```bash
# Create secrets in Secret Manager
gcloud secrets create analysis-engine-jwt-secret \
  --data-file=jwt-secret.txt

gcloud secrets create analysis-engine-redis-url \
  --data-file=redis-url.txt

# Grant access to service account
gcloud secrets add-iam-policy-binding analysis-engine-jwt-secret \
  --member="serviceAccount:analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com" \
  --role="roles/secretmanager.secretAccessor"

# Use in Cloud Run
gcloud run services update analysis-engine \
  --update-secrets JWT_SECRET=analysis-engine-jwt-secret:latest
```

### Feature Flags

```yaml
# config/features.yaml
features:
  new_language_parser:
    enabled: true
    rollout_percentage: 10
    allowed_users:
      - <EMAIL>
  
  enhanced_pattern_detection:
    enabled: false
    
  streaming_analysis:
    enabled: true
    max_file_size_mb: 50
```

## Monitoring & Observability

### Metrics

#### Key Metrics to Monitor

1. **Service Health**
   - Request rate (req/s)
   - Error rate (4xx, 5xx)
   - Latency (p50, p95, p99)
   - Uptime percentage

2. **Analysis Performance**
   - Analysis duration by repository size
   - Files processed per second
   - Pattern detection accuracy
   - Memory usage per analysis

3. **Resource Utilization**
   - CPU usage
   - Memory usage
   - Disk I/O
   - Network throughput

#### Cloud Monitoring Setup

```yaml
# monitoring/dashboard.yaml
displayName: "Analysis Engine Dashboard"
mosaicLayout:
  columns: 12
  tiles:
  - width: 4
    height: 4
    widget:
      title: "Request Rate"
      xyChart:
        dataSets:
        - timeSeriesQuery:
            timeSeriesFilter:
              filter: |
                resource.type="cloud_run_revision"
                resource.labels.service_name="analysis-engine"
                metric.type="run.googleapis.com/request_count"
              aggregation:
                alignmentPeriod: 60s
                perSeriesAligner: ALIGN_RATE
  
  - width: 4
    height: 4
    widget:
      title: "Error Rate"
      xyChart:
        dataSets:
        - timeSeriesQuery:
            timeSeriesFilter:
              filter: |
                resource.type="cloud_run_revision"
                resource.labels.service_name="analysis-engine"
                metric.type="run.googleapis.com/request_count"
                metric.labels.response_code_class="5xx"
```

Create dashboard:
```bash
gcloud monitoring dashboards create --config-from-file=monitoring/dashboard.yaml
```

### Logging

#### Structured Logging Configuration

```rust
// src/utils/telemetry.rs
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

pub fn init_telemetry() {
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new("info"));
    
    let formatting_layer = tracing_stackdriver::layer()
        .with_cloud_trace(true)
        .with_source_location(true);
    
    tracing_subscriber::registry()
        .with(env_filter)
        .with(formatting_layer)
        .init();
}
```

#### Log Queries

```bash
# View recent errors
gcloud logging read "resource.type=cloud_run_revision \
  AND resource.labels.service_name=analysis-engine \
  AND severity>=ERROR" \
  --limit 50 \
  --format json

# Analysis performance logs
gcloud logging read "resource.type=cloud_run_revision \
  AND resource.labels.service_name=analysis-engine \
  AND jsonPayload.event=analysis_completed" \
  --limit 100 \
  --format="csv(timestamp,jsonPayload.duration_seconds,jsonPayload.files_count)"

# Create log-based metric
gcloud logging metrics create analysis_duration \
  --description="Duration of repository analyses" \
  --value-extractor="EXTRACT(jsonPayload.duration_seconds)" \
  --log-filter='resource.type="cloud_run_revision"
    resource.labels.service_name="analysis-engine"
    jsonPayload.event="analysis_completed"'
```

### Distributed Tracing

#### Setup Cloud Trace

```rust
// Cargo.toml
[dependencies]
opentelemetry = "0.21"
opentelemetry-gcp = "0.21"
tracing-opentelemetry = "0.22"

// src/main.rs
use opentelemetry_gcp::CloudTraceExporter;

fn init_tracer() -> Result<()> {
    let exporter = CloudTraceExporter::builder()
        .with_project_id(env::var("GCP_PROJECT_ID")?)
        .build()?;
    
    let tracer = opentelemetry::sdk::trace::TracerProvider::builder()
        .with_batch_exporter(exporter, opentelemetry::runtime::Tokio)
        .build();
    
    opentelemetry::global::set_tracer_provider(tracer);
    Ok(())
}
```

#### Trace Analysis Workflow

```rust
#[instrument(skip(self))]
pub async fn analyze_repository(&self, request: AnalysisRequest) -> Result<Analysis> {
    let span = tracing::info_span!("analyze_repository", 
        repository_url = %request.repository_url,
        request_id = %request.id
    );
    
    async move {
        // Download phase
        let download_span = info_span!("download_repository");
        let files = download_span.in_scope(|| {
            self.download_repository(&request.repository_url)
        }).await?;
        
        // Parse phase
        let parse_span = info_span!("parse_files", file_count = files.len());
        let parsed = parse_span.in_scope(|| {
            self.parser.parse_files_parallel(files)
        }).await?;
        
        // Pattern detection phase
        let pattern_span = info_span!("detect_patterns");
        let patterns = pattern_span.in_scope(|| {
            self.pattern_detector.detect_all(&parsed)
        }).await?;
        
        Ok(analysis)
    }
    .instrument(span)
    .await
}
```

### Alerting

#### Alert Policies

```yaml
# monitoring/alerts.yaml
displayName: "Analysis Engine High Error Rate"
conditions:
  - displayName: "Error rate > 5%"
    conditionThreshold:
      filter: |
        resource.type="cloud_run_revision"
        resource.labels.service_name="analysis-engine"
        metric.type="run.googleapis.com/request_count"
      aggregations:
        - alignmentPeriod: 300s
          perSeriesAligner: ALIGN_RATE
          crossSeriesReducer: REDUCE_SUM
          groupByFields:
            - resource.label.service_name
      comparison: COMPARISON_GT
      thresholdValue: 0.05
      duration: 300s

notificationChannels:
  - projects/vibe-match-463114/notificationChannels/12345

documentation:
  content: |
    ## Analysis Engine High Error Rate
    
    The error rate has exceeded 5% for the past 5 minutes.
    
    ### Runbook
    1. Check recent deployments
    2. Review error logs: `gcloud logging read "severity>=ERROR"`
    3. Check downstream dependencies (Spanner, Vertex AI)
    4. Scale up if necessary
    5. Rollback if deployment-related
```

Create alerts:
```bash
gcloud alpha monitoring policies create --policy-from-file=monitoring/alerts.yaml
```

## Operational Procedures

### Health Checks

#### Manual Health Verification

```bash
# Check all health endpoints
SERVICE_URL=https://analysis-engine.ccl.dev

# Basic health
curl -s ${SERVICE_URL}/health | jq .

# Readiness (checks dependencies)
curl -s ${SERVICE_URL}/health/ready | jq .

# Liveness
curl -s ${SERVICE_URL}/health/live | jq .

# Full system check
./scripts/health-check.sh production
```

#### Automated Health Monitoring

```yaml
# monitoring/uptime-check.yaml
displayName: "Analysis Engine Uptime"
monitoredResource:
  type: uptime_url
  labels:
    host: analysis-engine.ccl.dev
    project_id: vibe-match-463114

httpCheck:
  path: /health/ready
  port: 443
  useSsl: true
  validateSsl: true
  requestMethod: GET
  acceptedResponseStatusCodes:
    - statusValue: 200

period: 60s
timeout: 10s

selectedRegions:
  - USA
  - EUROPE
  - ASIA_PACIFIC
```

### Scaling

#### Auto-scaling Configuration

```bash
# Update auto-scaling parameters
gcloud run services update analysis-engine \
  --min-instances 5 \
  --max-instances 200 \
  --concurrency 80 \
  --cpu-throttling \
  --region us-central1

# Monitor scaling events
gcloud logging read "resource.type=cloud_run_revision \
  AND protoPayload.methodName=google.cloud.run.v1.Services.ReplaceService" \
  --limit 10
```

#### Manual Scaling

```bash
# Scale up for expected load
gcloud run services update analysis-engine \
  --min-instances 50 \
  --region us-central1

# Scale down after peak
gcloud run services update analysis-engine \
  --min-instances 5 \
  --region us-central1
```

### Backup & Recovery

#### Data Backup

```bash
# Backup Spanner database
gcloud spanner backups create analysis-backup-$(date +%Y%m%d) \
  --instance=ccl-production \
  --database=ccl-main \
  --retention-period=30d \
  --async

# Export Cloud Storage artifacts
gsutil -m rsync -r \
  gs://ccl-analysis-artifacts \
  gs://ccl-analysis-artifacts-backup/$(date +%Y%m%d)

# Backup Redis state (if critical)
kubectl exec -it redis-master-0 -- redis-cli BGSAVE
kubectl cp redis-master-0:/data/dump.rdb ./backups/redis-$(date +%Y%m%d).rdb
```

#### Disaster Recovery

```bash
# Restore from Spanner backup
gcloud spanner databases restore \
  --source-backup=analysis-backup-20240101 \
  --destination-database=ccl-main-restored \
  --destination-instance=ccl-production

# Restore Cloud Storage
gsutil -m rsync -r \
  gs://ccl-analysis-artifacts-backup/20240101 \
  gs://ccl-analysis-artifacts-restored

# Update service to use restored resources
gcloud run services update analysis-engine \
  --set-env-vars SPANNER_DATABASE_ID=ccl-main-restored \
  --set-env-vars STORAGE_BUCKET=ccl-analysis-artifacts-restored
```

### Incident Response

#### Incident Response Runbook

1. **Detection**
   ```bash
   # Check service status
   curl https://analysis-engine.ccl.dev/health/ready
   
   # Check recent errors
   gcloud logging read "severity>=ERROR" --limit 50
   
   # Check metrics
   gcloud monitoring time-series list \
     --filter='metric.type="run.googleapis.com/request_count"'
   ```

2. **Triage**
   - Severity: P1 (service down), P2 (degraded), P3 (minor issue)
   - Impact: Number of affected users
   - Root cause: Deployment, dependency, or infrastructure

3. **Mitigation**
   ```bash
   # Rollback if deployment-related
   gcloud run services update-traffic analysis-engine \
     --to-revisions=analysis-engine-00042-abc=100 \
     --region us-central1
   
   # Scale up if load-related
   gcloud run services update analysis-engine \
     --min-instances 50 \
     --max-instances 500
   
   # Disable problematic features
   kubectl set env deployment/analysis-engine \
     FEATURE_NEW_PARSER=false
   ```

4. **Communication**
   - Update status page
   - Notify affected customers
   - Post in #incidents channel

5. **Resolution**
   - Fix root cause
   - Deploy patch
   - Verify fix

6. **Post-mortem**
   - Timeline of events
   - Root cause analysis
   - Action items
   - Lessons learned

## Performance Monitoring

### Key Performance Indicators

```sql
-- Analysis performance by repository size
SELECT 
  DATE(timestamp) as date,
  APPROX_QUANTILES(duration_seconds, 100)[OFFSET(50)] as p50_duration,
  APPROX_QUANTILES(duration_seconds, 100)[OFFSET(95)] as p95_duration,
  APPROX_QUANTILES(duration_seconds, 100)[OFFSET(99)] as p99_duration,
  AVG(files_count) as avg_files,
  COUNT(*) as total_analyses
FROM `vibe-match-463114.analysis_engine.analyses`
WHERE timestamp > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
GROUP BY date
ORDER BY date DESC;

-- Pattern detection accuracy
SELECT 
  pattern_name,
  COUNT(*) as detections,
  AVG(confidence_score) as avg_confidence,
  SUM(CASE WHEN user_verified = true THEN 1 ELSE 0 END) / COUNT(*) as accuracy
FROM `vibe-match-463114.analysis_engine.pattern_detections`
WHERE timestamp > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
GROUP BY pattern_name
ORDER BY detections DESC;
```

### Performance Dashboard

Create a comprehensive dashboard:

```python
# scripts/create_dashboard.py
from google.cloud import monitoring_dashboard_v1

client = monitoring_dashboard_v1.DashboardsServiceClient()
project_name = f"projects/{project_id}"

dashboard = monitoring_dashboard_v1.Dashboard(
    display_name="Analysis Engine Performance",
    grid_layout=monitoring_dashboard_v1.GridLayout(
        widgets=[
            # Request latency histogram
            monitoring_dashboard_v1.Widget(
                title="Request Latency Distribution",
                xy_chart=monitoring_dashboard_v1.XyChart(
                    timeseries_query=monitoring_dashboard_v1.TimeSeriesQuery(
                        time_series_filter=monitoring_dashboard_v1.TimeSeriesFilter(
                            filter='resource.type="cloud_run_revision" '
                                  'resource.labels.service_name="analysis-engine" '
                                  'metric.type="run.googleapis.com/request_latencies"'
                        )
                    )
                )
            ),
            # Add more widgets...
        ]
    )
)

client.create_dashboard(parent=project_name, dashboard=dashboard)
```

## Security Operations

### Security Monitoring

```bash
# Monitor authentication failures
gcloud logging read "jsonPayload.event=auth_failed" \
  --limit 100 \
  --format="table(timestamp,jsonPayload.user_id,jsonPayload.reason)"

# Check for suspicious patterns
gcloud logging read "jsonPayload.files_count>10000 OR \
  jsonPayload.duration_seconds>1800" \
  --limit 50

# Monitor rate limit violations
gcloud logging read "jsonPayload.event=rate_limit_exceeded" \
  --limit 100 \
  --format="table(timestamp,jsonPayload.api_key,jsonPayload.limit)"
```

### Security Scans

```bash
# Container vulnerability scanning
gcloud container images scan ${IMAGE_URL}
gcloud container images list-vulnerabilities ${IMAGE_URL}

# Dependency audit
cd services/analysis-engine
cargo audit
cargo outdated

# SAST scanning
semgrep --config=auto .

# Secret scanning
trufflehog filesystem . --json
```

## Maintenance Tasks

### Regular Maintenance

#### Daily
- Review error logs and alerts
- Check service health metrics
- Monitor resource utilization
- Verify backup completion

#### Weekly
- Review performance trends
- Update dependencies
- Run security scans
- Clean up old artifacts

#### Monthly
- Review and update documentation
- Performance optimization review
- Capacity planning
- Disaster recovery drill

### Maintenance Scripts

```bash
#!/bin/bash
# scripts/maintenance.sh

# Clean up old artifacts (>30 days)
gsutil -m rm -r gs://ccl-analysis-artifacts/analyses/*/
  $(gsutil ls -l gs://ccl-analysis-artifacts/analyses/ | \
    grep -E '[0-9]{4}-[0-9]{2}-[0-9]{2}' | \
    awk '$2 < "'$(date -d '30 days ago' '+%Y-%m-%d')'" {print $3}')

# Vacuum Redis
kubectl exec -it redis-master-0 -- redis-cli \
  --eval /scripts/vacuum.lua

# Archive old analyses
bq query --use_legacy_sql=false '
  CREATE OR REPLACE TABLE analysis_engine.analyses_archive AS
  SELECT * FROM analysis_engine.analyses
  WHERE timestamp < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 90 DAY)'

# Update monitoring baselines
python scripts/update_slo_baselines.py
```

---

This operations guide provides comprehensive procedures for running the Analysis Engine in production. For specific operational scenarios not covered here, consult the [Troubleshooting Guide](../troubleshooting/README.md) or contact the platform team.