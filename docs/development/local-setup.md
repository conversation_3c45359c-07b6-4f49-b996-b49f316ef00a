# CCL Local Development Environment Setup Guide

## Overview

This guide will walk you through setting up the complete CCL (Codebase Context Layer) platform on your local development machine. The setup includes all microservices, databases, emulators, and observability tools needed for full-stack development.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Quick Start](#quick-start)
3. [Detailed Setup](#detailed-setup)
4. [Service Architecture](#service-architecture)
5. [Development Workflow](#development-workflow)
6. [Troubleshooting](#troubleshooting)
7. [Advanced Configuration](#advanced-configuration)

## Prerequisites

### Required Software

- **Docker Desktop** (v24.0+): [Download](https://www.docker.com/products/docker-desktop)
- **Git** (v2.40+): [Download](https://git-scm.com/downloads)

### Optional but Recommended

- **VS Code**: [Download](https://code.visualstudio.com/)
- **Node.js** (v20+): For local frontend development
- **Python** (v3.11+): For local Python service development
- **Rust** (v1.75+): For local Rust service development
- **Go** (v1.21+): For local Go service development

### System Requirements

- **RAM**: Minimum 8GB, recommended 16GB
- **Storage**: 20GB free space
- **OS**: macOS, Linux, or Windows with WSL2

## Quick Start

```bash
# Clone the repository
git clone https://github.com/ccl-platform/ccl.git
cd ccl

# Run the setup script
./scripts/dev/setup.sh

# Start all services
./scripts/dev/start.sh

# Verify everything is running
./scripts/dev/health-check.sh
```

After successful startup, you can access:
- **Web Application**: http://localhost:3001
- **API Gateway**: http://localhost:8000
- **Grafana**: http://localhost:3000 (admin/admin)
- **Jaeger**: http://localhost:16686

## Detailed Setup

### Step 1: Initial Setup

The setup script will:
1. Check prerequisites
2. Create necessary directories
3. Generate development certificates
4. Configure local DNS (requires sudo)
5. Create environment configuration
6. Pull Docker images

```bash
./scripts/dev/setup.sh
```

### Step 2: Environment Configuration

The setup creates `.env.development` with all necessary configuration. Key variables:

```bash
# Database
DATABASE_URL=postgresql://ccl_dev:dev_password@localhost:5432/ccl_local

# Services
API_GATEWAY_URL=http://localhost:8000
ANALYSIS_SERVICE_URL=http://localhost:8001
QUERY_SERVICE_URL=http://localhost:8002
PATTERN_SERVICE_URL=http://localhost:8003
MARKETPLACE_SERVICE_URL=http://localhost:8004

# Observability
GRAFANA_URL=http://localhost:3000
JAEGER_UI_URL=http://localhost:16686
```

### Step 3: Starting Services

```bash
# Start all services
./scripts/dev/start.sh

# Start specific services
docker-compose -f docker/docker-compose.yml up -d postgres redis
docker-compose -f docker/docker-compose.yml up -d analysis-engine

# View logs
docker-compose -f docker/docker-compose.yml logs -f [service-name]
```

### Step 4: Seed Development Data

```bash
# Load sample data
./scripts/dev/seed-data.sh

# This creates:
# - Sample repositories
# - Test patterns
# - Example queries
# - Demo marketplace items
```

## Service Architecture

### Core Services

| Service | Port | Language | Description |
|---------|------|----------|-------------|
| Analysis Engine | 8001 | Rust | Code parsing and AST analysis |
| Query Intelligence | 8002 | Python | Natural language query processing |
| Pattern Mining | 8003 | Python | ML-powered pattern detection |
| Marketplace | 8004 | Go | Pattern sharing and commerce |
| Collaboration | 8005 | TypeScript | Real-time collaboration |
| Web | 3001 | TypeScript | Frontend application |

### Infrastructure Services

| Service | Port | Purpose |
|---------|------|---------|
| PostgreSQL | 5432 | Primary database |
| Redis | 6379 | Caching and sessions |
| Spanner Emulator | 9010 | Google Spanner emulation |
| Firestore Emulator | 8080 | Real-time database |
| Pub/Sub Emulator | 8085 | Message queue |
| Storage Emulator | 4443 | File storage |

### Observability Stack

| Service | Port | Purpose |
|---------|------|---------|
| Prometheus | 9090 | Metrics collection |
| Grafana | 3000 | Metrics visualization |
| Jaeger | 16686 | Distributed tracing |
| OpenTelemetry | 4317 | Telemetry collection |

## Development Workflow

### 1. Making Code Changes

All services support hot reloading:
- **Rust**: Uses `cargo-watch`
- **Python**: Uses `uvicorn --reload`
- **Go**: Uses `air`
- **TypeScript**: Uses Next.js dev server

### 2. Running Tests

```bash
# All tests
make test

# Service-specific tests
cd services/analysis-engine && cargo test
cd services/query-intelligence && pytest
cd services/marketplace && go test ./...
cd services/web && npm test
```

### 3. Code Quality

```bash
# Linting
make lint

# Formatting
make format

# Security scan
make security-scan
```

### 4. Database Access

```bash
# PostgreSQL
PGPASSWORD=dev_password psql -h localhost -U ccl_dev -d ccl_local

# Redis CLI
redis-cli

# View database in Grafana
open http://localhost:3000
```

### 5. API Testing

```bash
# Health check
curl http://localhost:8000/health

# Analysis endpoint
curl -X POST http://localhost:8001/api/v1/analysis \
  -H "Content-Type: application/json" \
  -d '{"repository_url": "https://github.com/user/repo"}'

# Use the REST client in VS Code
# Install REST Client extension and use .http files
```

## VS Code Integration

### 1. Open Project

```bash
code .
```

### 2. Install Recommended Extensions

When prompted, install all recommended extensions for:
- Rust development
- Python development
- Go development
- TypeScript/React development
- Docker support
- Database tools

### 3. Use Integrated Features

- **Tasks**: `Cmd+Shift+P` → "Tasks: Run Task"
  - Start/Stop environment
  - Run tests
  - Open monitoring tools
  
- **Debugging**: F5 to start debugging
  - Compound configurations for multiple services
  - Attach to running containers
  
- **Terminal**: Integrated terminal with proper environment

## Common Development Tasks

### Adding a New Endpoint

1. Define the endpoint in the service
2. Update API documentation
3. Add tests
4. Update the API gateway configuration if needed

### Modifying Database Schema

1. Update schema in `docker/init-scripts/postgres/`
2. Create migration scripts
3. Restart PostgreSQL container
4. Run migrations

### Adding a New Service

1. Create service directory structure
2. Add Dockerfile.dev
3. Update docker-compose.yml
4. Add to nginx configuration
5. Update documentation

## Performance Optimization

### Resource Management

```yaml
# Limit container resources in docker-compose.yml
services:
  analysis-engine:
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
```

### Faster Startup

```bash
# Start only essential services
docker-compose -f docker/docker-compose.yml up -d \
  postgres redis api-gateway analysis-engine web
```

### Caching

- Docker layer caching for faster builds
- Dependency caching volumes
- Hot reload without rebuilding

## Stopping and Cleanup

```bash
# Stop all services
./scripts/dev/stop.sh

# Stop and remove volumes (data loss!)
./scripts/dev/stop.sh --volumes

# Complete reset
./scripts/dev/reset.sh
```

## Advanced Configuration

### Custom Domain Setup

Edit `/etc/hosts`:
```
127.0.0.1 api.ccl.local
127.0.0.1 app.ccl.local
```

### SSL/TLS Configuration

Development certificates are auto-generated in `credentials/`

### Environment Overrides

Create `.env.local` for personal overrides:
```bash
# Override specific variables
API_GATEWAY_PORT=8080
ENABLE_DEBUG_LOGGING=true
```

### Multi-Environment Setup

```bash
# Use different environments
export CCL_ENV=staging
./scripts/dev/start.sh
```

## Next Steps

1. Explore the [API Documentation](../api/)
2. Read the [Architecture Guide](../architecture/)
3. Check [Service Documentation](../services/)
4. Join the development community

## Getting Help

- Check [Troubleshooting Guide](./troubleshooting.md)
- Search existing [GitHub Issues](https://github.com/ccl-platform/ccl/issues)
- Join our [Discord Community](https://discord.gg/ccl-platform)
- Read the [FAQ](../faq.md)

---

Happy coding! 🚀