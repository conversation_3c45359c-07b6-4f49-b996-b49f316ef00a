# CCL Development Environment Troubleshooting Guide

## Table of Contents

1. [Common Issues](#common-issues)
2. [Service-Specific Issues](#service-specific-issues)
3. [Docker Issues](#docker-issues)
4. [Database Issues](#database-issues)
5. [Performance Issues](#performance-issues)
6. [Network Issues](#network-issues)
7. [IDE Issues](#ide-issues)
8. [Platform-Specific Issues](#platform-specific-issues)

## Common Issues

### Services Won't Start

**Symptom**: Running `./scripts/dev/start.sh` fails or services don't come up.

**Solutions**:

1. Check Docker is running:
```bash
docker info
```

2. Check port availability:
```bash
# macOS/Linux
lsof -i :8000 -i :8001 -i :8002 -i :8003 -i :8004 -i :8005 -i :3000 -i :3001

# Kill processes using ports
kill -9 $(lsof -t -i:8000)
```

3. Clean Docker environment:
```bash
docker-compose -f docker/docker-compose.yml down -v
docker system prune -f
./scripts/dev/start.sh
```

4. Check Docker resources:
   - Open Docker Desktop
   - Settings → Resources
   - Ensure at least 8GB RAM allocated

### Health Check Failures

**Symptom**: `./scripts/dev/health-check.sh` shows services as unhealthy.

**Solutions**:

1. Check individual service logs:
```bash
docker-compose -f docker/docker-compose.yml logs [service-name]
```

2. Restart specific service:
```bash
docker-compose -f docker/docker-compose.yml restart [service-name]
```

3. Check service dependencies:
```bash
# Ensure infrastructure is up first
docker-compose -f docker/docker-compose.yml up -d postgres redis
sleep 10
docker-compose -f docker/docker-compose.yml up -d
```

### Permission Denied Errors

**Symptom**: Scripts fail with permission errors.

**Solutions**:

```bash
# Make scripts executable
chmod +x scripts/dev/*.sh

# Fix Docker socket permissions (Linux)
sudo usermod -aG docker $USER
newgrp docker
```

## Service-Specific Issues

### Analysis Engine (Rust)

**Issue**: Cargo build failures

```bash
# Clear Rust cache
cd services/analysis-engine
cargo clean
rm -rf target/

# Update dependencies
cargo update

# Rebuild
docker-compose -f docker/docker-compose.yml build analysis-engine
```

**Issue**: Memory issues during compilation

```bash
# Increase Docker memory or use release mode
RUSTFLAGS="-C opt-level=1" cargo build
```

### Query Intelligence (Python)

**Issue**: Import errors or missing dependencies

```bash
# Rebuild with no cache
docker-compose -f docker/docker-compose.yml build --no-cache query-intelligence

# Or fix locally
cd services/query-intelligence
pip install -r requirements.txt
```

**Issue**: Vertex AI connection errors

```bash
# For local development, mock mode is enabled
# Check environment variable
echo $ENABLE_MOCK_SERVICES  # Should be "true"
```

### Marketplace (Go)

**Issue**: Module download failures

```bash
# Clear Go module cache
docker-compose -f docker/docker-compose.yml exec marketplace go clean -modcache

# Rebuild
docker-compose -f docker/docker-compose.yml build marketplace
```

### Web (Next.js)

**Issue**: Build errors or blank page

```bash
# Clear Next.js cache
cd services/web
rm -rf .next/
rm -rf node_modules/
npm install
npm run dev
```

**Issue**: API connection errors

```bash
# Verify API gateway is running
curl http://localhost:8000/health

# Check CORS headers
curl -I http://localhost:8000/api/v1/analysis/health
```

## Docker Issues

### Container Crashes

**Symptom**: Containers repeatedly restart.

**Debug steps**:

```bash
# Check container status
docker-compose -f docker/docker-compose.yml ps

# View detailed logs
docker-compose -f docker/docker-compose.yml logs --tail=100 [service-name]

# Inspect container
docker inspect ccl-dev-network_[service-name]_1
```

### Disk Space Issues

**Symptom**: "No space left on device" errors.

**Solutions**:

```bash
# Check Docker disk usage
docker system df

# Clean up unused resources
docker system prune -a --volumes

# Remove old images
docker image prune -a

# Clear build cache
docker builder prune
```

### Network Issues

**Symptom**: Services can't communicate.

**Solutions**:

```bash
# Verify network exists
docker network ls | grep ccl-dev-network

# Recreate network
docker-compose -f docker/docker-compose.yml down
docker network rm ccl-dev-network || true
docker-compose -f docker/docker-compose.yml up -d
```

## Database Issues

### PostgreSQL Connection Failed

**Solutions**:

1. Check PostgreSQL is running:
```bash
docker-compose -f docker/docker-compose.yml ps postgres
```

2. Test connection:
```bash
PGPASSWORD=dev_password psql -h localhost -U ccl_dev -d ccl_local -c "SELECT 1"
```

3. Recreate database:
```bash
docker-compose -f docker/docker-compose.yml down -v postgres
docker-compose -f docker/docker-compose.yml up -d postgres
```

### Redis Connection Issues

**Solutions**:

```bash
# Test Redis connection
redis-cli -h localhost -p 6379 ping

# Flush Redis if needed
redis-cli FLUSHALL
```

### Emulator Issues

**Spanner Emulator**:
```bash
# Check emulator logs
docker-compose -f docker/docker-compose.yml logs spanner-emulator

# Test connection
curl http://localhost:9010/v1/projects/test-project/instances
```

**Firestore Emulator**:
```bash
# Check status
curl http://localhost:8080

# View UI (if available)
open http://localhost:4000
```

## Performance Issues

### Slow Service Startup

**Solutions**:

1. Increase Docker resources
2. Start services in stages:
```bash
# Start infrastructure first
docker-compose -f docker/docker-compose.yml up -d postgres redis
sleep 10

# Then services
docker-compose -f docker/docker-compose.yml up -d
```

3. Use production builds for better performance:
```bash
NODE_ENV=production docker-compose -f docker/docker-compose.yml up -d
```

### High Memory Usage

**Monitor usage**:
```bash
docker stats
```

**Limit memory per service**:
```yaml
# In docker-compose.yml
services:
  analysis-engine:
    deploy:
      resources:
        limits:
          memory: 2G
```

### Slow Hot Reload

**Solutions**:

1. Exclude node_modules from file watching
2. Use polling for file systems that don't support inotify:
```bash
# For React/Next.js
CHOKIDAR_USEPOLLING=true npm run dev
```

## Network Issues

### Cannot Access Services

**Check localhost vs 0.0.0.0**:
- Services inside Docker should bind to `0.0.0.0`
- Access from host using `localhost`

**Firewall issues**:
```bash
# macOS
sudo pfctl -d  # Disable firewall temporarily

# Linux
sudo ufw status
sudo ufw allow 8000:8005/tcp
```

### DNS Resolution Issues

**Fix local DNS**:
```bash
# Add to /etc/hosts
127.0.0.1 api.ccl.local
127.0.0.1 app.ccl.local
```

**Clear DNS cache**:
```bash
# macOS
sudo dscacheutil -flushcache

# Linux
sudo systemctl restart systemd-resolved
```

## IDE Issues

### VS Code Can't Connect to Services

**Solutions**:

1. Ensure services are running
2. Check launch.json ports match docker-compose.yml
3. Use "Remote - Containers" extension for debugging inside containers

### Language Server Issues

**Rust Analyzer**:
```bash
# Clear cache
rm -rf ~/.cargo/registry/cache
rm -rf target/
```

**Python Language Server**:
```bash
# Reinstall
pip install --upgrade pylsp-mypy python-lsp-server
```

## Platform-Specific Issues

### macOS

**Issue**: "Cannot connect to Docker daemon"

```bash
# Start Docker Desktop
open -a Docker

# Wait for Docker to start
while ! docker info > /dev/null 2>&1; do
    echo "Waiting for Docker to start..."
    sleep 1
done
```

**Issue**: Port already in use

```bash
# Find and kill process
sudo lsof -i :8000
sudo kill -9 [PID]
```

### Linux

**Issue**: Permission denied on Docker socket

```bash
sudo usermod -aG docker $USER
newgrp docker
```

**Issue**: Firewall blocking ports

```bash
# Ubuntu/Debian
sudo ufw allow 8000:8005/tcp
sudo ufw allow 3000:3001/tcp

# CentOS/RHEL
sudo firewall-cmd --add-port=8000-8005/tcp --permanent
sudo firewall-cmd --reload
```

### Windows (WSL2)

**Issue**: Slow file system performance

Solutions:
1. Keep code inside WSL2 filesystem
2. Use WSL2 backend for Docker Desktop
3. Exclude project from Windows Defender

**Issue**: Line ending issues

```bash
# Configure Git
git config --global core.autocrlf false

# Fix existing files
dos2unix scripts/dev/*.sh
```

## Emergency Recovery

### Complete Reset

If nothing else works:

```bash
# Stop everything
docker-compose -f docker/docker-compose.yml down -v

# Remove all CCL containers and images
docker ps -a | grep ccl | awk '{print $1}' | xargs docker rm -f
docker images | grep ccl | awk '{print $3}' | xargs docker rmi -f

# Clean Docker system
docker system prune -a --volumes

# Remove local data
rm -rf data/ logs/ credentials/dev-*
rm -f .env.development

# Start fresh
./scripts/dev/setup.sh
./scripts/dev/start.sh
```

## Getting More Help

### Collect Diagnostic Information

```bash
# Generate diagnostic report
cat > diagnostic-report.txt << EOF
Date: $(date)
OS: $(uname -a)
Docker: $(docker --version)
Docker Compose: $(docker-compose --version)

Services Status:
$(docker-compose -f docker/docker-compose.yml ps)

Recent Logs:
$(docker-compose -f docker/docker-compose.yml logs --tail=50)
EOF
```

### Resources

1. Check service logs: `docker-compose -f docker/docker-compose.yml logs [service]`
2. Search [GitHub Issues](https://github.com/ccl-platform/ccl/issues)
3. Ask in [Discord Community](https://discord.gg/ccl-platform)
4. Review [Documentation](https://docs.ccl.dev)

### Debug Mode

Enable debug logging:
```bash
# In .env.development
ENABLE_DEBUG_LOGGING=true
RUST_LOG=debug
LOG_LEVEL=debug
```

---

Remember: Most issues can be resolved by:
1. Checking logs
2. Restarting services
3. Clearing caches
4. Ensuring dependencies are running

When in doubt, `./scripts/dev/reset.sh` provides a clean slate! 🔧