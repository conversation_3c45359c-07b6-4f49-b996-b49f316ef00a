# Architecture Patterns

name: "Architecture Patterns"
description: |
  Comprehensive architecture patterns and infrastructure specifications for the CCL platform.
  
  Core Principles:
  - **Cloud-Native First**: Serverless by default with Google Cloud Platform
  - **AI-Powered Intelligence**: Native Vertex AI integration with Gemini 2.5
  - **Zero Trust Security**: VPC Service Controls and encryption everywhere
  - **Global Scale**: Multi-region deployment with sub-100ms response times
  - **Developer Experience**: Natural language interfaces across all platforms

## Goal

Define the complete architecture patterns for a cloud-native, AI-powered platform that transforms how developers understand and interact with codebases through advanced pattern recognition, real-time analysis, and predictive insights.

## Why

Architecture patterns enable:
- Consistent development practices across all services
- Scalable and resilient system design
- Security and compliance built into every layer
- Optimal performance and cost efficiency
- Future-proof technology choices

This provides:
- Clear implementation guidance for development teams
- Standardized infrastructure patterns
- Security and compliance by design
- Predictable scaling characteristics
- Reduced technical debt and maintenance overhead

## What

### User-Visible Behavior
- Sub-100ms query response times globally
- Real-time collaboration features
- Multi-platform access (Web, CLI, IDE, Mobile, Voice, AR/VR)
- Natural language code understanding
- Predictive code insights

### Technical Requirements
- [ ] Microservices architecture on Cloud Run
- [ ] Zero Trust network security
- [ ] AI/ML integration with Vertex AI
- [ ] Global data replication and caching
- [ ] Real-time collaboration infrastructure
- [ ] Multi-modal interface support
- [ ] Comprehensive observability

### Success Criteria
- [ ] 99.99% uptime SLA
- [ ] Sub-100ms API response times (p95)
- [ ] Auto-scaling 0-5000 instances per service
- [ ] Global deployment across 3+ regions
- [ ] SOC2/HIPAA compliance ready

## All Needed Context

### Documentation & References
- url: https://cloud.google.com/architecture/microservices-architecture-on-google-cloud
  why: Google Cloud microservices best practices
- url: https://cloud.google.com/security/zero-trust
  why: Zero trust security implementation
- url: https://cloud.google.com/vertex-ai/docs/start/introduction-unified-platform
  why: Vertex AI platform integration
- file: prp_docs/ccl-master-architecture.md
  why: Complete architecture specification

### Architecture Principles

```yaml
Core Principles:
  cloud_native_first:
    - Serverless by default (Cloud Run)
    - Managed services over self-hosted
    - Global scale from day one
    - Zero infrastructure management
    
  ai_powered_intelligence:
    - Gemini 2.5 integration
    - Real-time learning
    - Multi-modal analysis
    - Predictive capabilities
    
  security_compliance:
    - Zero Trust architecture
    - Data sovereignty
    - Encryption everywhere
    - Compliance ready (SOC2, HIPAA, FedRAMP)
    
  developer_experience:
    - Natural language first
    - Progressive disclosure
    - Multi-platform support
    - Real-time collaboration
```

### Known Gotchas & Library Quirks
- **CRITICAL**: VPC Service Controls can block legitimate cross-service calls
- **CRITICAL**: Cloud Run cold starts can impact initial response times
- **GOTCHA**: BigQuery streaming inserts have eventual consistency
- **GOTCHA**: Firestore has limited query capabilities compared to SQL
- **WARNING**: Vertex AI quotas vary by region and model type
- **TIP**: Use Cloud CDN for static assets and API caching
- **TIP**: Implement circuit breakers for external API dependencies
- **TIP**: Use Pub/Sub for async processing to improve response times

## Implementation Blueprint

### High-Level System Architecture

```
┌─────────────────────────────────────────────────────────────────────┐
│                        Developer Interfaces                         │
│  Web App │ CLI │ VS Code │ JetBrains │ Mobile │ Voice │ AR/VR      │
└────────────────────────┬────────────────────────────────────────────┘
                         │
┌────────────────────────┴────────────────────────────────────────────┐
│                    API Gateway (Apigee X)                           │
│         Rate Limiting │ Authentication │ API Management             │
└────────────────────────┬────────────────────────────────────────────┘
                         │
┌────────────────────────┴────────────────────────────────────────────┐
│                  Core Services Layer (Cloud Run)                    │
├─────────────────────────────────────────────────────────────────────┤
│ Query Service │ Analysis Service │ Pattern Service │ Market Service │
│ Auth Service  │ Billing Service  │ Admin Service   │ Search Service │
└────────────────────────┬────────────────────────────────────────────┘
                         │
┌────────────────────────┴────────────────────────────────────────────┐
│                    Intelligence Layer                               │
├─────────────────────────────────────────────────────────────────────┤
│           Vertex AI Platform          │      ML Processing          │
│  • Gemini 2.5 Integration            │  • Pattern Mining Engine    │
│  • Custom Model Training             │  • Similarity Search        │
│  • Embeddings Generation             │  • Anomaly Detection        │
│  • Multi-modal Processing            │  • Predictive Analytics     │
└────────────────────────┬────────────────────────────────────────────┘
                         │
┌────────────────────────┴────────────────────────────────────────────┐
│                    Data Layer                                       │
├─────────────────────────────────────────────────────────────────────┤
│   Spanner          │   BigQuery        │   Firestore              │
│ • User Data        │ • Analytics       │ • Real-time Sync         │
│ • Transactions     │ • Data Warehouse  │ • Session State          │
│ • Global Scale     │ • ML Training     │ • Collaboration          │
├────────────────────┼───────────────────┼───────────────────────────┤
│   Cloud Storage    │   Bigtable        │   Memorystore            │
│ • Code Artifacts   │ • Time Series     │ • Redis Cache            │
│ • Analysis Cache   │ • Metrics         │ • Session Cache          │
│ • Pattern Library  │ • Logs            │ • Query Cache            │
└─────────────────────────────────────────────────────────────────────┘
```

## Microservices Architecture

The CCL platform is composed of the following microservices:

### Analysis Engine Service

```yaml
Service: ccl-analysis-engine
Runtime: Cloud Run (CPU optimized)
Language: Rust + WebAssembly
Scaling: 0-1000 instances
Memory: 4GB per instance
CPU: 2 vCPU

Responsibilities:
  - Parse source code (25+ languages)
  - Build AST representations
  - Extract dependencies
  - Identify patterns
  - Generate embeddings
  - Code complexity analysis
  - Dependency graph creation
  - Language detection

API Endpoints:
  - POST /analyze - Start codebase analysis
  - GET /analysis/{id} - Get analysis status
  - GET /analysis/{id}/results - Get detailed results
  - POST /parse - Parse single file
  - GET /languages - List supported languages

Dependencies:
  - Cloud Storage (code artifacts)
  - Spanner (metadata storage)
  - Vertex AI (embeddings)
  - Pub/Sub (event streaming)
  - BigQuery (analytics logging)

Performance Requirements:
  - Parse 10MB of code in <30 seconds
  - Support concurrent analysis of 100+ repositories
  - Generate embeddings for 1M+ code snippets/hour
```

### Query Intelligence Service

```yaml
Service: ccl-query-intelligence
Runtime: Cloud Run (CPU optimized)
Language: Python 3.11
Scaling: 0-5000 instances
Memory: 8GB per instance
CPU: 4 vCPU

Responsibilities:
  - Natural language understanding
  - Context management
  - Response generation
  - Multi-turn conversations
  - Query optimization
  - Intent classification
  - Code explanation generation
  - Similarity search

API Endpoints:
  - POST /query - Process natural language query
  - GET /conversations/{id} - Get conversation history
  - POST /conversations/{id}/messages - Add message to conversation
  - POST /explain - Explain code snippet
  - POST /similarity - Find similar code patterns

Dependencies:
  - Vertex AI (Gemini 2.5)
  - Spanner (context storage)
  - Memorystore (cache)
  - BigQuery (analytics)
  - Cloud Storage (embeddings)

Performance Requirements:
  - Respond to queries in <100ms (p95)
  - Process 10,000+ concurrent conversations
  - Maintain context for 1M+ active sessions
```

### Pattern Mining Service

```yaml
Service: ccl-pattern-mining
Runtime: Cloud Run (GPU accelerated)
Language: Python + TensorFlow
Scaling: 0-100 instances
Memory: 16GB per instance
GPU: NVIDIA T4 (optional)

Responsibilities:
  - Detect coding patterns
  - Cluster similar implementations
  - Train custom models
  - Generate pattern templates
  - Quality scoring
  - Anti-pattern detection
  - Code refactoring suggestions
  - Pattern evolution tracking

API Endpoints:
  - POST /patterns/detect - Detect patterns in code
  - GET /patterns/{id} - Get pattern details
  - POST /patterns/train - Train custom pattern model
  - POST /patterns/validate - Validate pattern implementation
  - GET /patterns/recommendations - Get pattern recommendations

Dependencies:
  - Vertex AI Training
  - BigQuery ML
  - Cloud Storage
  - Pub/Sub
  - Spanner (pattern metadata)

Performance Requirements:
  - Detect patterns in 1MB codebase in <5 minutes
  - Train custom models on 100GB+ datasets
  - Process pattern detection for 1000+ repositories/day
```

### Marketplace Service

```yaml
Service: ccl-marketplace
Runtime: Cloud Run
Language: Go 1.21
Scaling: 0-500 instances
Memory: 2GB per instance
CPU: 1 vCPU

Responsibilities:
  - Pattern publishing
  - Licensing management
  - Revenue distribution
  - Quality control
  - Search & discovery
  - Review system
  - Payment processing
  - Analytics tracking

API Endpoints:
  - GET /marketplace/patterns - List available patterns
  - POST /marketplace/patterns - Publish new pattern
  - GET /marketplace/patterns/{id} - Get pattern details
  - POST /marketplace/purchase - Purchase pattern
  - GET /marketplace/analytics - Usage analytics

Dependencies:
  - Spanner (transactions)
  - Cloud Storage (artifacts)
  - Stripe API (payments)
  - Pub/Sub (events)
  - BigQuery (analytics)

Performance Requirements:
  - Handle 10,000+ pattern searches/minute
  - Process payments within 5 seconds
  - Support 1M+ pattern downloads/month
```

### Real-time Collaboration Service

```yaml
Service: ccl-collaboration
Runtime: Cloud Run + WebSockets
Language: Node.js 20
Scaling: 0-1000 instances
Memory: 4GB per instance
CPU: 2 vCPU

Responsibilities:
  - Live code analysis
  - Shared sessions
  - Real-time updates
  - Presence tracking
  - Conflict resolution
  - Screen sharing
  - Voice communication
  - Session recording

API Endpoints:
  - POST /sessions - Create collaboration session
  - GET /sessions/{id} - Get session details
  - WebSocket /sessions/{id}/ws - Real-time connection
  - POST /sessions/{id}/invite - Invite participants
  - GET /sessions/{id}/recordings - Get session recordings

Dependencies:
  - Firestore (real-time sync)
  - Memorystore (presence)
  - Pub/Sub (broadcasting)
  - Cloud Tasks (async)
  - Cloud Storage (recordings)

Performance Requirements:
  - Support 10,000+ concurrent sessions
  - Maintain <50ms latency for real-time updates
  - Handle 100+ participants per session
```

## Infrastructure Architecture

### Network Security (Zero Trust)

```yaml
VPC Configuration:
  name: ccl-secure-vpc
  subnets:
    - name: services-subnet
      region: us-central1
      ip_range: ********/24
      private_google_access: true
      
    - name: data-subnet  
      region: us-central1
      ip_range: ********/24
      private_google_access: true
      
    - name: management-subnet
      region: us-central1
      ip_range: ********/24
      private_google_access: true
      
  firewall_rules:
    - name: deny-all-ingress
      direction: INGRESS
      priority: 1000
      action: DENY
      source_ranges: ["0.0.0.0/0"]
      
    - name: allow-health-checks
      direction: INGRESS
      priority: 900
      action: ALLOW
      source_ranges: ["**********/16", "***********/22"]
      target_tags: ["cloud-run-service"]
      
    - name: allow-internal
      direction: INGRESS
      priority: 800
      action: ALLOW
      source_ranges: ["10.0.0.0/16"]
      target_tags: ["internal-service"]

VPC Service Controls:
  perimeter: ccl-security-perimeter
  restricted_services:
    - storage.googleapis.com
    - spanner.googleapis.com
    - bigquery.googleapis.com
    - aiplatform.googleapis.com
    - firestore.googleapis.com
    - redis.googleapis.com
  access_levels:
    - name: ccl-internal-access
      ip_subnetworks: ["10.0.0.0/16"]
      members: ["serviceAccount:*@ccl-platform-prod.iam.gserviceaccount.com"]
    - name: ccl-developer-access
      device_policy:
        require_screen_lock: true
        require_corp_owned: true
      regions: ["US", "EU"]
```

### API Gateway Configuration

```yaml
Apigee X Configuration:
  organization: ccl-platform
  environment: production
  
  api_proxies:
    - name: ccl-public-api
      base_path: /v1
      target_servers:
        - ccl-analysis-engine
        - ccl-query-intelligence
        - ccl-marketplace
        
  policies:
    - quota:
        free_tier: 1000/hour
        pro_tier: 10000/hour
        enterprise_tier: unlimited
        
    - oauth:
        authorization_server: https://auth.ccl.dev
        scopes: ["read:analysis", "write:analysis", "read:patterns"]
        
    - rate_limiting:
        requests_per_minute: 1000
        burst_limit: 100
        
    - cors:
        allow_origins: ["https://app.ccl.dev", "https://dashboard.ccl.dev"]
        allow_methods: ["GET", "POST", "PUT", "DELETE"]
        allow_headers: ["Authorization", "Content-Type"]
        
    - response_cache:
        cache_key: "{request.header.authorization}:{request.uri}"
        ttl: 300 # 5 minutes
```

### Data Architecture Patterns

#### Spanner (Global Transactional Data)

```sql
-- Multi-region Spanner configuration
CREATE DATABASE ccl_platform
OPTIONS (
  default_leader = 'us-central1',
  version_retention_period = '7d'
);

-- Users and Authentication
CREATE TABLE users (
    user_id STRING(36) NOT NULL,
    email STRING(255) NOT NULL,
    created_at TIMESTAMP NOT NULL,
    subscription_tier STRING(50),
    organization_id STRING(36),
    settings JSON,
    last_active TIMESTAMP,
) PRIMARY KEY (user_id);

-- Repositories with interleaved patterns
CREATE TABLE repositories (
    repo_id STRING(36) NOT NULL,
    user_id STRING(36) NOT NULL,
    name STRING(255) NOT NULL,
    url STRING(1024),
    last_analysis TIMESTAMP,
    total_lines INT64,
    primary_language STRING(50),
    metadata JSON,
    FOREIGN KEY (user_id) REFERENCES users (user_id)
) PRIMARY KEY (repo_id);

-- Patterns interleaved in repositories for locality
CREATE TABLE patterns (
    repo_id STRING(36) NOT NULL,
    pattern_id STRING(36) NOT NULL,
    pattern_type STRING(100),
    confidence FLOAT64,
    occurrences INT64,
    template JSON,
    examples JSON,
    created_at TIMESTAMP,
    quality_score FLOAT64,
    usage_count INT64,
) PRIMARY KEY (repo_id, pattern_id),
INTERLEAVE IN PARENT repositories ON DELETE CASCADE;

-- Analysis results interleaved for performance
CREATE TABLE analysis_results (
    repo_id STRING(36) NOT NULL,
    analysis_id STRING(36) NOT NULL,
    status STRING(50) NOT NULL,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    results JSON,
    error_message STRING(MAX),
    metrics JSON,
) PRIMARY KEY (repo_id, analysis_id),
INTERLEAVE IN PARENT repositories ON DELETE CASCADE;

-- Performance indexes
CREATE INDEX idx_patterns_by_type ON patterns(pattern_type) STORING (confidence, quality_score);
CREATE INDEX idx_repos_by_language ON repositories(primary_language) STORING (total_lines, last_analysis);
CREATE INDEX idx_users_by_org ON users(organization_id) STORING (subscription_tier, last_active);
CREATE INDEX idx_analysis_by_status ON analysis_results(status, started_at) STORING (completed_at);
```

#### BigQuery (Analytics & ML)

```sql
-- Analytics dataset with partitioning and clustering
CREATE SCHEMA ccl_analytics
OPTIONS (
  location = 'US',
  default_table_expiration_days = 365
);

-- Analysis events with optimal partitioning
CREATE OR REPLACE TABLE ccl_analytics.analysis_events (
    event_id STRING,
    timestamp TIMESTAMP,
    user_id STRING,
    repo_id STRING,
    event_type STRING,
    duration_ms INT64,
    files_analyzed INT64,
    patterns_detected INT64,
    error_count INT64,
    language_breakdown ARRAY<STRUCT<language STRING, percentage FLOAT64>>,
    complexity_metrics STRUCT<
        cyclomatic_complexity FLOAT64,
        cognitive_complexity FLOAT64,
        maintainability_index FLOAT64
    >,
    performance_metrics STRUCT<
        memory_usage_mb FLOAT64,
        cpu_time_seconds FLOAT64,
        io_operations INT64
    >
) PARTITION BY DATE(timestamp)
CLUSTER BY user_id, event_type, repo_id;

-- Query analytics for performance optimization
CREATE OR REPLACE TABLE ccl_analytics.query_logs (
    query_id STRING,
    timestamp TIMESTAMP,
    user_id STRING,
    conversation_id STRING,
    query_text STRING,
    query_type STRING, -- explain, search, generate, etc.
    response_time_ms INT64,
    tokens_used INT64,
    model_version STRING,
    satisfaction_score FLOAT64,
    context_size_kb FLOAT64,
    cache_hit BOOLEAN,
    error_code STRING
) PARTITION BY DATE(timestamp)
CLUSTER BY user_id, conversation_id, query_type;

-- Pattern usage for marketplace analytics
CREATE OR REPLACE TABLE ccl_analytics.pattern_usage (
    pattern_id STRING,
    timestamp TIMESTAMP,
    action STRING, -- viewed, copied, implemented, purchased, rated
    user_id STRING,
    repo_id STRING,
    revenue_cents INT64,
    rating FLOAT64,
    implementation_success BOOLEAN,
    usage_context STRUCT<
        file_type STRING,
        project_size STRING,
        team_size INT64
    >
) PARTITION BY DATE(timestamp)
CLUSTER BY pattern_id, action;

-- ML training datasets
CREATE OR REPLACE TABLE ccl_analytics.ml_training_data (
    training_id STRING,
    timestamp TIMESTAMP,
    model_type STRING,
    dataset_size_gb FLOAT64,
    training_duration_hours FLOAT64,
    accuracy_metrics STRUCT<
        precision FLOAT64,
        recall FLOAT64,
        f1_score FLOAT64
    >,
    hyperparameters JSON
) PARTITION BY DATE(timestamp);
```

## Service Communication Patterns

### Event-Driven Architecture

```yaml
Pub/Sub Topics:
  - name: analysis-events
    schema: analysis_event_schema
    retention: 7d
    subscribers:
      - pattern-mining-service
      - analytics-service
      - notification-service
      
  - name: pattern-events
    schema: pattern_event_schema
    retention: 7d
    subscribers:
      - marketplace-service
      - recommendation-service
      - quality-service
      
  - name: user-events
    schema: user_event_schema
    retention: 30d
    subscribers:
      - billing-service
      - analytics-service
      - personalization-service

Event Schemas:
  analysis_event_schema:
    type: object
    properties:
      event_type: {type: string, enum: ["started", "completed", "failed"]}
      analysis_id: {type: string}
      repository_id: {type: string}
      user_id: {type: string}
      timestamp: {type: string, format: date-time}
      metadata: {type: object}
    required: ["event_type", "analysis_id", "repository_id", "user_id"]
```

### Circuit Breaker Pattern

```yaml
Circuit Breakers:
  - service: vertex-ai
    failure_threshold: 5
    timeout: 30s
    recovery_timeout: 60s
    fallback: cached_response
    
  - service: external-git-apis
    failure_threshold: 3
    timeout: 10s
    recovery_timeout: 30s
    fallback: error_response
    
  - service: payment-processor
    failure_threshold: 2
    timeout: 15s
    recovery_timeout: 120s
    fallback: queue_for_retry
```

## Observability & Monitoring

### Monitoring Stack

```yaml
Google Cloud Monitoring:
  dashboards:
    - name: Service Health
      metrics:
        - cloud_run_request_count
        - cloud_run_request_latencies
        - cloud_run_billable_instance_time
        - cloud_run_memory_utilization
        
    - name: AI/ML Performance
      metrics:
        - vertex_ai_prediction_count
        - vertex_ai_prediction_latency
        - vertex_ai_error_count
        - custom_model_accuracy
        
    - name: Database Performance
      metrics:
        - spanner_cpu_utilization
        - spanner_query_count
        - bigquery_slot_usage
        - firestore_read_write_ops

  alerts:
    - name: High Error Rate
      condition: error_rate > 5%
      duration: 5m
      notification: pagerduty
      
    - name: High Latency
      condition: p95_latency > 1000ms
      duration: 3m
      notification: slack
      
    - name: Resource Exhaustion
      condition: cpu_utilization > 80%
      duration: 10m
      notification: email

Custom Metrics:
  - name: pattern_detection_accuracy
    type: gauge
    labels: ["model_version", "language"]
    
  - name: query_satisfaction_score
    type: histogram
    labels: ["query_type", "user_tier"]
    
  - name: collaboration_session_duration
    type: histogram
    labels: ["session_type", "participant_count"]
```

### Distributed Tracing

```yaml
Cloud Trace Configuration:
  sampling_rate: 0.1 # 10% of requests
  
  custom_spans:
    - name: code_analysis
      attributes:
        - repository_id
        - language
        - file_count
        - analysis_type
        
    - name: pattern_detection
      attributes:
        - pattern_type
        - confidence_threshold
        - model_version
        
    - name: query_processing
      attributes:
        - query_type
        - context_size
        - model_used
        - cache_hit
```

## Deployment & Scaling Patterns

### Auto-scaling Configuration

```yaml
Cloud Run Scaling:
  analysis_engine:
    min_instances: 1
    max_instances: 1000
    concurrency: 100
    cpu_throttling: false
    scaling_triggers:
      - cpu_utilization: 70%
      - memory_utilization: 80%
      - request_rate: 1000/min
      
  query_intelligence:
    min_instances: 10
    max_instances: 5000
    concurrency: 1000
    cpu_throttling: false
    scaling_triggers:
      - cpu_utilization: 60%
      - request_rate: 10000/min
      - response_time: 100ms
      
  pattern_mining:
    min_instances: 0
    max_instances: 100
    concurrency: 10
    cpu_throttling: false
    scaling_triggers:
      - queue_depth: 10
      - processing_time: 300s
```

### Multi-Region Deployment

```yaml
Regional Configuration:
  primary_region: us-central1
  secondary_regions:
    - us-east1
    - europe-west1
    - asia-southeast1
    
  traffic_distribution:
    us-central1: 50%
    us-east1: 20%
    europe-west1: 20%
    asia-southeast1: 10%
    
  failover_strategy:
    automatic: true
    health_check_interval: 30s
    failure_threshold: 3
    recovery_threshold: 2
    
  data_replication:
    spanner: multi-region (us-central1, us-east1)
    bigquery: US multi-region
    storage: dual-region (us-central1, us-east1)
```

## Success Criteria

### Performance Metrics
- [ ] 99.99% uptime SLA across all services
- [ ] Sub-100ms API response times (p95)
- [ ] Auto-scaling 0-5000 instances per service within 30 seconds
- [ ] Global deployment with <50ms latency between regions
- [ ] Support 1M+ concurrent users

### Security & Compliance
- [ ] SOC2 Type II compliance
- [ ] HIPAA compliance for healthcare customers
- [ ] Zero Trust network architecture implemented
- [ ] All data encrypted at rest and in transit
- [ ] Regular security audits and penetration testing

### Operational Excellence
- [ ] Comprehensive monitoring and alerting
- [ ] Automated incident response
- [ ] Blue-green deployments with zero downtime
- [ ] Disaster recovery with <1 hour RTO
- [ ] Cost optimization with 20% year-over-year reduction

### Business Metrics
- [ ] Support 10,000+ repositories analyzed per day
- [ ] Process 1M+ natural language queries per day
- [ ] Enable 100,000+ pattern downloads per month
- [ ] Facilitate 10,000+ collaboration sessions per day
- [ ] Achieve 95%+ customer satisfaction score
