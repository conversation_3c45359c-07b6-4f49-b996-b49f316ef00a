# Gemini AI Integration

name: "Gemini AI Integration"
description: |
  Complete integration with Google's Gemini AI models for natural language understanding, code analysis, and intelligent suggestions in the CCL platform.
  
  Core Principles:
  - **Multi-Modal Understanding**: Process code, text, and diagrams
  - **Context-Aware**: Maintain conversation and codebase context
  - **Safety-First**: Content filtering and responsible AI practices
  - **Performance Optimized**: Caching and batch processing
  - **Cost Efficient**: Smart model selection and token management

## Goal

Implement comprehensive Gemini AI integration for CCL platform, enabling natural language code queries, intelligent documentation generation, architecture explanations, and code improvement suggestions.

## Why

Gemini integration powers CCL's core AI capabilities:
- Natural language code understanding
- Intelligent documentation generation
- Architecture pattern recognition
- Code quality suggestions
- Real-time coding assistance

This integration enables:
- Human-like code conversations
- Contextual code explanations
- Automated documentation
- Pattern discovery insights
- Proactive improvement suggestions

## What

### User-Visible Behavior
- Natural language queries about code
- AI-generated documentation
- Real-time code suggestions
- Architecture visualizations with explanations
- Pattern detection insights

### Technical Requirements
- [ ] Vertex AI client configuration
- [ ] Multiple model support (Flash, Pro, Ultra)
- [ ] Safety settings implementation
- [ ] Response streaming capability
- [ ] Context window management
- [ ] Token usage tracking
- [ ] Error handling and fallbacks
- [ ] Response caching layer

### Success Criteria
- [ ] <2s response time for queries
- [ ] 95%+ helpful response rate
- [ ] Safety filters prevent harmful content
- [ ] Token usage within budget limits
- [ ] Graceful handling of rate limits

## All Needed Context

### Documentation & References
- url: https://cloud.google.com/vertex-ai/generative-ai/docs/start/quickstarts/quickstart-multimodal
  why: Vertex AI Gemini quickstart and setup
- url: https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/gemini
  why: Gemini model specifications and limits
- url: https://cloud.google.com/vertex-ai/generative-ai/docs/multimodal/ground-with-google-search
  why: Grounding responses with search
- file: packages/ccl-core/src/ai/gemini_integration.py
  why: Current implementation reference

### Model Selection Strategy

```yaml
Model Tiers:
  gemini-2.5-flash:
    use_cases: [quick_queries, code_completion, simple_explanations]
    context_window: 1048576  # 1M tokens
    cost_per_million_input: 0.075
    cost_per_million_output: 0.30
    
  gemini-1.5-pro:
    use_cases: [complex_analysis, architecture_review, detailed_documentation]
    context_window: 2097152  # 2M tokens
    cost_per_million_input: 1.25
    cost_per_million_output: 5.00
    
  gemini-1.5-flash-8b:
    use_cases: [real_time_suggestions, syntax_checks, quick_validations]
    context_window: 1048576  # 1M tokens
    cost_per_million_input: 0.0375
    cost_per_million_output: 0.15
```

### Known Gotchas & Library Quirks
- **CRITICAL**: Initialize client with vertex AI not generative AI SDK
- **CRITICAL**: Response streaming requires special handling
- **GOTCHA**: Context window includes both input and output
- **GOTCHA**: Safety filters can block legitimate code discussions
- **WARNING**: Rate limits vary by project quota
- **TIP**: Use response_mime_type for JSON responses
- **TIP**: Batch similar requests for efficiency

## Implementation Blueprint

### Core Integration Class

```python
# packages/ccl-core/src/ai/gemini_integration.py
from google import genai
from google.genai.types import (
    GenerateContentConfig,
    SafetySetting,
    HarmCategory,
    HarmBlockThreshold,
    FunctionDeclaration,
    Tool
)
import asyncio
from typing import List, Dict, Optional, AsyncGenerator
import json
from dataclasses import dataclass
from enum import Enum
import logging
from tenacity import retry, stop_after_attempt, wait_exponential

logger = logging.getLogger(__name__)

class ModelType(Enum):
    FLASH = "gemini-2.5-flash"
    PRO = "gemini-1.5-pro"
    FLASH_8B = "gemini-1.5-flash-8b"

@dataclass
class GeminiConfig:
    project_id: str
    location: str = "us-central1"
    default_model: ModelType = ModelType.FLASH
    max_retries: int = 3
    timeout_seconds: int = 30
    enable_grounding: bool = True
    enable_caching: bool = True
    
class GeminiIntegration:
    def __init__(self, config: GeminiConfig):
        self.config = config
        self.client = genai.Client(
            vertexai=True,
            project=config.project_id,
            location=config.location
        )
        
        # Configure safety settings - balanced for code discussions
        self.safety_settings = [
            SafetySetting(
                category=HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                threshold=HarmBlockThreshold.BLOCK_ONLY_HIGH
            ),
            SafetySetting(
                category=HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                threshold=HarmBlockThreshold.BLOCK_ONLY_HIGH
            ),
            SafetySetting(
                category=HarmCategory.HARM_CATEGORY_HARASSMENT,
                threshold=HarmBlockThreshold.BLOCK_ONLY_HIGH
            ),
            SafetySetting(
                category=HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                threshold=HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
            )
        ]
        
        # Define function calling tools
        self.code_analysis_tools = self._create_code_analysis_tools()
        
    def _create_code_analysis_tools(self) -> List[Tool]:
        """Create function calling tools for code analysis"""
        
        analyze_complexity = FunctionDeclaration(
            name="analyze_code_complexity",
            description="Analyze the complexity of provided code",
            parameters={
                "type": "object",
                "properties": {
                    "code": {"type": "string", "description": "Code to analyze"},
                    "language": {"type": "string", "description": "Programming language"},
                },
                "required": ["code", "language"]
            }
        )
        
        find_patterns = FunctionDeclaration(
            name="find_code_patterns",
            description="Find common patterns in code",
            parameters={
                "type": "object",
                "properties": {
                    "code": {"type": "string", "description": "Code to search"},
                    "pattern_type": {"type": "string", "description": "Type of pattern to find"},
                },
                "required": ["code", "pattern_type"]
            }
        )
        
        return [Tool(function_declarations=[analyze_complexity, find_patterns])]
    
    def _select_model(self, use_case: str) -> ModelType:
        """Select appropriate model based on use case"""
        model_mapping = {
            "quick_query": ModelType.FLASH_8B,
            "code_completion": ModelType.FLASH,
            "detailed_analysis": ModelType.PRO,
            "architecture_review": ModelType.PRO,
            "documentation": ModelType.FLASH,
            "real_time": ModelType.FLASH_8B
        }
        return model_mapping.get(use_case, self.config.default_model)
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def analyze_code_intent(
        self, 
        code: str, 
        context: Optional[Dict] = None,
        use_case: str = "detailed_analysis"
    ) -> Dict:
        """Analyze code intent and purpose using Gemini"""
        
        model = self._select_model(use_case)
        prompt = self._build_code_analysis_prompt(code, context)
        
        config = GenerateContentConfig(
            temperature=0.1,  # Low temperature for factual analysis
            top_p=0.95,
            top_k=40,
            max_output_tokens=2048,
            response_modalities=["TEXT"],
            response_mime_type="application/json"
        )
        
        response = await self.client.models.generate_content_async(
            model=model.value,
            contents=[genai.Part.from_text(prompt)],
            generation_config=config,
            safety_settings=self.safety_settings,
            tools=self.code_analysis_tools if use_case == "detailed_analysis" else None
        )
        
        # Parse JSON response
        try:
            return json.loads(response.text)
        except json.JSONDecodeError:
            logger.error(f"Failed to parse JSON response: {response.text}")
            return {"error": "Invalid response format", "raw_response": response.text}
    
    def _build_code_analysis_prompt(self, code: str, context: Optional[Dict]) -> str:
        """Build comprehensive prompt for code analysis"""
        
        context_str = ""
        if context:
            if "file_path" in context:
                context_str += f"File: {context['file_path']}\n"
            if "repository" in context:
                context_str += f"Repository: {context['repository']}\n"
            if "dependencies" in context:
                context_str += f"Dependencies: {', '.join(context['dependencies'])}\n"
        
        prompt = f"""Analyze this code and provide a comprehensive understanding.

{context_str}

Code:
```
{code}
```

Provide analysis in JSON format with these fields:
{{
  "purpose": "High-level purpose of this code",
  "functionality": ["List of key functions/features"],
  "patterns": ["Design patterns used"],
  "dependencies": ["External dependencies"],
  "complexity": {{
    "cognitive": "low|medium|high",
    "cyclomatic": <number>,
    "explanation": "Why this complexity rating"
  }},
  "quality": {{
    "score": <1-10>,
    "strengths": ["List of strengths"],
    "weaknesses": ["List of weaknesses"]
  }},
  "security": {{
    "issues": ["Potential security concerns"],
    "recommendations": ["Security improvements"]
  }},
  "performance": {{
    "bottlenecks": ["Potential performance issues"],
    "optimizations": ["Suggested optimizations"]
  }},
  "maintainability": {{
    "score": <1-10>,
    "suggestions": ["Improvement suggestions"]
  }}
}}"""
        
        return prompt
    
    async def generate_documentation(
        self,
        code: str,
        analysis: Dict,
        style: str = "technical"
    ) -> str:
        """Generate natural language documentation"""
        
        style_prompts = {
            "technical": "Generate detailed technical documentation for experienced developers",
            "beginner": "Generate simple, beginner-friendly documentation with examples",
            "api": "Generate API documentation with clear parameter and return descriptions",
            "tutorial": "Generate tutorial-style documentation with step-by-step explanations"
        }
        
        prompt = f"""{style_prompts.get(style, style_prompts['technical'])}

Code:
```
{code}
```

Analysis:
{json.dumps(analysis, indent=2)}

Generate documentation that includes:
1. Overview and purpose
2. Prerequisites and setup
3. Detailed functionality explanation
4. Parameters and return values
5. Usage examples with explanations
6. Error handling
7. Best practices and tips
8. Related patterns or alternatives

Format as clean, readable markdown."""

        config = GenerateContentConfig(
            temperature=0.3,  # Some creativity for documentation
            top_p=0.95,
            max_output_tokens=4096,
            response_modalities=["TEXT"]
        )
        
        response = await self.client.models.generate_content_async(
            model=ModelType.FLASH.value,
            contents=[genai.Part.from_text(prompt)],
            generation_config=config,
            safety_settings=self.safety_settings
        )
        
        return response.text
    
    async def stream_query_response(
        self,
        query: str,
        code_context: List[Dict[str, str]],
        conversation_history: Optional[List[Dict]] = None
    ) -> AsyncGenerator[str, None]:
        """Stream responses for natural language queries"""
        
        # Build context from code files
        context_parts = []
        for file_context in code_context[:10]:  # Limit to 10 most relevant files
            context_parts.append(f"""
File: {file_context.get('path', 'unknown')}
```{file_context.get('language', '')}
{file_context.get('content', '')}
```
""")
        
        # Build conversation history
        history_parts = []
        if conversation_history:
            for msg in conversation_history[-5:]:  # Last 5 messages
                role = "User" if msg['role'] == 'user' else "Assistant"
                history_parts.append(f"{role}: {msg['content']}")
        
        prompt = f"""You are a helpful AI assistant specializing in code analysis and explanation.

Previous conversation:
{chr(10).join(history_parts)}

Relevant code context:
{chr(10).join(context_parts)}

User query: {query}

Provide a helpful, accurate response. If referencing code, mention the specific file and line numbers.
Be concise but thorough. Use code examples where helpful."""

        config = GenerateContentConfig(
            temperature=0.4,
            top_p=0.95,
            max_output_tokens=2048,
            response_modalities=["TEXT"]
        )
        
        # Use streaming for real-time response
        stream = await self.client.models.generate_content_stream_async(
            model=ModelType.FLASH.value,
            contents=[genai.Part.from_text(prompt)],
            generation_config=config,
            safety_settings=self.safety_settings
        )
        
        async for chunk in stream:
            if chunk.text:
                yield chunk.text
    
    async def explain_architecture(
        self,
        components: List[Dict],
        relationships: List[Dict],
        focus_area: Optional[str] = None
    ) -> str:
        """Explain system architecture in natural language"""
        
        # Prepare component descriptions
        component_desc = []
        for comp in components:
            desc = f"- **{comp['name']}**: {comp['type']} - {comp.get('description', 'No description')}"
            if 'technologies' in comp:
                desc += f" (Technologies: {', '.join(comp['technologies'])})"
            component_desc.append(desc)
        
        # Prepare relationship descriptions
        rel_desc = []
        for rel in relationships:
            rel_desc.append(
                f"- {rel['from']} → {rel['to']}: {rel['type']} "
                f"({rel.get('description', 'No description')})"
            )
        
        focus_prompt = ""
        if focus_area:
            focus_prompt = f"\nFocus particularly on: {focus_area}"
        
        prompt = f"""Explain this software architecture in clear, accessible language.

Components:
{chr(10).join(component_desc)}

Relationships:
{chr(10).join(rel_desc)}
{focus_prompt}

Provide:
1. **Overview**: High-level summary of the system
2. **Architecture Pattern**: Identify the architectural pattern(s) used
3. **Key Components**: Explain the role of each major component
4. **Data Flow**: How data moves through the system
5. **Communication**: How components interact
6. **Scalability**: How the architecture supports scaling
7. **Reliability**: Fault tolerance and reliability features
8. **Security**: Security considerations in the architecture
9. **Trade-offs**: Architectural trade-offs made
10. **Improvements**: Potential architectural improvements

Target audience: Senior developers new to this codebase."""

        config = GenerateContentConfig(
            temperature=0.4,
            max_output_tokens=4096,
            response_modalities=["TEXT"]
        )
        
        response = await self.client.models.generate_content_async(
            model=ModelType.PRO.value,  # Use Pro for complex analysis
            contents=[genai.Part.from_text(prompt)],
            generation_config=config,
            safety_settings=self.safety_settings
        )
        
        return response.text
    
    async def suggest_improvements(
        self,
        code: str,
        patterns: List[Dict],
        metrics: Dict,
        improvement_focus: List[str] = None
    ) -> List[Dict]:
        """Suggest code improvements based on analysis"""
        
        if not improvement_focus:
            improvement_focus = ["performance", "security", "maintainability", "patterns"]
        
        prompt = f"""Analyze this code and suggest specific improvements.

Code:
```
{code}
```

Detected Patterns:
{json.dumps(patterns, indent=2)}

Code Metrics:
{json.dumps(metrics, indent=2)}

Focus on these improvement areas: {', '.join(improvement_focus)}

Provide specific, actionable improvements in JSON format:
{{
  "improvements": [
    {{
      "type": "performance|security|maintainability|pattern|style",
      "severity": "critical|high|medium|low",
      "title": "Short descriptive title",
      "description": "Detailed explanation of the issue and why it matters",
      "current_code": "The problematic code snippet",
      "suggested_code": "The improved code snippet",
      "impact": {{
        "performance": "Description of performance impact if applicable",
        "security": "Description of security impact if applicable",
        "maintainability": "Description of maintainability impact"
      }},
      "effort": "low|medium|high",
      "references": ["Links to documentation or best practices"]
    }}
  ],
  "summary": {{
    "total_issues": <number>,
    "critical_issues": <number>,
    "estimated_effort_hours": <number>,
    "priority_order": ["List of improvement IDs in priority order"]
  }}
}}"""

        config = GenerateContentConfig(
            temperature=0.2,  # Low temperature for consistent analysis
            max_output_tokens=4096,
            response_mime_type="application/json"
        )
        
        response = await self.client.models.generate_content_async(
            model=ModelType.PRO.value,  # Use Pro for detailed analysis
            contents=[genai.Part.from_text(prompt)],
            generation_config=config,
            safety_settings=self.safety_settings
        )
        
        result = json.loads(response.text)
        return result["improvements"]
    
    async def generate_test_cases(
        self,
        code: str,
        framework: str = "pytest"
    ) -> str:
        """Generate test cases for code"""
        
        framework_templates = {
            "pytest": "Generate pytest test cases with fixtures and parametrization",
            "jest": "Generate Jest test cases with describe/it blocks",
            "junit": "Generate JUnit test cases with @Test annotations",
            "go": "Generate Go test cases with testing.T"
        }
        
        prompt = f"""{framework_templates.get(framework, 'Generate unit test cases')}

Code to test:
```
{code}
```

Generate comprehensive test cases that include:
1. Happy path tests
2. Edge cases
3. Error cases
4. Boundary conditions
5. Performance tests (if applicable)
6. Integration tests (if applicable)

Include:
- Clear test names that describe what is being tested
- Setup and teardown as needed
- Assertions with meaningful messages
- Comments explaining complex test logic
- Test data fixtures

Format as ready-to-run test code."""

        config = GenerateContentConfig(
            temperature=0.2,
            max_output_tokens=4096,
            response_modalities=["TEXT"]
        )
        
        response = await self.client.models.generate_content_async(
            model=ModelType.FLASH.value,
            contents=[genai.Part.from_text(prompt)],
            generation_config=config,
            safety_settings=self.safety_settings
        )
        
        return response.text
```

### Context Management

```python
# packages/ccl-core/src/ai/context_manager.py
from typing import List, Dict, Optional
import tiktoken
from dataclasses import dataclass

@dataclass
class ContextWindow:
    max_tokens: int
    reserved_output_tokens: int = 2048
    
    def __post_init__(self):
        self.available_input_tokens = self.max_tokens - self.reserved_output_tokens
        self.encoder = tiktoken.encoding_for_model("gpt-4")  # Close approximation

class ContextManager:
    def __init__(self, model_type: ModelType):
        self.window = self._get_context_window(model_type)
    
    def _get_context_window(self, model_type: ModelType) -> ContextWindow:
        windows = {
            ModelType.FLASH: ContextWindow(max_tokens=1048576),
            ModelType.PRO: ContextWindow(max_tokens=2097152),
            ModelType.FLASH_8B: ContextWindow(max_tokens=1048576)
        }
        return windows[model_type]
    
    def prioritize_context(
        self,
        query: str,
        code_files: List[Dict[str, str]],
        conversation_history: List[Dict[str, str]] = None
    ) -> Dict:
        """Prioritize context to fit within token limits"""
        
        # Calculate token usage
        query_tokens = len(self.window.encoder.encode(query))
        history_tokens = 0
        
        if conversation_history:
            history_text = "\n".join([
                f"{msg['role']}: {msg['content']}" 
                for msg in conversation_history
            ])
            history_tokens = len(self.window.encoder.encode(history_text))
        
        # Available tokens for code context
        available_for_code = (
            self.window.available_input_tokens - 
            query_tokens - 
            history_tokens - 
            1000  # Buffer for prompt template
        )
        
        # Score and rank code files
        scored_files = []
        for file in code_files:
            score = self._calculate_relevance_score(query, file)
            tokens = len(self.window.encoder.encode(file.get('content', '')))
            scored_files.append({
                **file,
                'relevance_score': score,
                'token_count': tokens
            })
        
        # Sort by relevance
        scored_files.sort(key=lambda x: x['relevance_score'], reverse=True)
        
        # Select files that fit within token limit
        selected_files = []
        used_tokens = 0
        
        for file in scored_files:
            if used_tokens + file['token_count'] <= available_for_code:
                selected_files.append(file)
                used_tokens += file['token_count']
            else:
                # Try to include partial file
                remaining_tokens = available_for_code - used_tokens
                if remaining_tokens > 500:  # Minimum useful context
                    truncated_content = self._truncate_to_tokens(
                        file['content'],
                        remaining_tokens
                    )
                    file['content'] = truncated_content
                    file['truncated'] = True
                    selected_files.append(file)
                break
        
        return {
            'query': query,
            'code_context': selected_files,
            'conversation_history': conversation_history[-5:] if conversation_history else [],
            'token_usage': {
                'query': query_tokens,
                'history': history_tokens,
                'code': used_tokens,
                'total': query_tokens + history_tokens + used_tokens
            }
        }
    
    def _calculate_relevance_score(self, query: str, file: Dict) -> float:
        """Calculate relevance score for a file"""
        score = 0.0
        
        # File path relevance
        if any(term in file.get('path', '').lower() for term in query.lower().split()):
            score += 2.0
        
        # Content relevance (simple keyword matching)
        content = file.get('content', '').lower()
        query_terms = query.lower().split()
        for term in query_terms:
            score += content.count(term) * 0.1
        
        # Recent modification bonus
        if file.get('recently_modified', False):
            score += 1.0
        
        # File type relevance
        if file.get('language') in ['python', 'javascript', 'typescript', 'go']:
            score += 0.5
        
        return score
    
    def _truncate_to_tokens(self, text: str, max_tokens: int) -> str:
        """Truncate text to fit within token limit"""
        tokens = self.window.encoder.encode(text)
        if len(tokens) <= max_tokens:
            return text
        
        # Truncate and add ellipsis
        truncated_tokens = tokens[:max_tokens - 10]  # Leave room for ellipsis
        truncated_text = self.window.encoder.decode(truncated_tokens)
        return truncated_text + "\n... (truncated)"
```

### Response Caching

```python
# packages/ccl-core/src/ai/response_cache.py
import hashlib
import json
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import redis.asyncio as redis

class ResponseCache:
    def __init__(self, redis_url: str, ttl_seconds: int = 3600):
        self.redis = redis.from_url(redis_url)
        self.ttl = ttl_seconds
    
    def _generate_cache_key(self, prompt: str, model: str, config: Dict) -> str:
        """Generate deterministic cache key"""
        cache_data = {
            'prompt': prompt,
            'model': model,
            'config': config
        }
        cache_str = json.dumps(cache_data, sort_keys=True)
        return f"gemini:response:{hashlib.sha256(cache_str.encode()).hexdigest()}"
    
    async def get(self, prompt: str, model: str, config: Dict) -> Optional[str]:
        """Get cached response if available"""
        key = self._generate_cache_key(prompt, model, config)
        
        try:
            cached = await self.redis.get(key)
            if cached:
                data = json.loads(cached)
                return data['response']
        except Exception as e:
            logger.warning(f"Cache get error: {e}")
        
        return None
    
    async def set(self, prompt: str, model: str, config: Dict, response: str):
        """Cache response with TTL"""
        key = self._generate_cache_key(prompt, model, config)
        
        try:
            cache_data = {
                'response': response,
                'timestamp': datetime.utcnow().isoformat(),
                'model': model
            }
            await self.redis.setex(
                key,
                self.ttl,
                json.dumps(cache_data)
            )
        except Exception as e:
            logger.warning(f"Cache set error: {e}")
    
    async def invalidate_pattern(self, pattern: str):
        """Invalidate all cache keys matching pattern"""
        try:
            async for key in self.redis.scan_iter(match=f"gemini:response:{pattern}*"):
                await self.redis.delete(key)
        except Exception as e:
            logger.warning(f"Cache invalidation error: {e}")
```

## Validation Loop

### Level 1: API Connection Testing
```python
# Test Gemini API connection
async def test_gemini_connection():
    config = GeminiConfig(
        project_id="ccl-platform-prod",
        location="us-central1"
    )
    
    gemini = GeminiIntegration(config)
    
    # Test simple query
    response = await gemini.analyze_code_intent(
        code="def hello(): return 'world'",
        use_case="quick_query"
    )
    
    assert "purpose" in response
    assert response["purpose"] is not None
```

### Level 2: Performance Testing
```python
# Test response time and streaming
async def test_performance():
    start_time = time.time()
    
    async for chunk in gemini.stream_query_response(
        query="Explain this Python function",
        code_context=[{
            "path": "test.py",
            "content": "def factorial(n): return 1 if n <= 1 else n * factorial(n-1)"
        }]
    ):
        first_chunk_time = time.time() - start_time
        assert first_chunk_time < 2.0  # First chunk within 2 seconds
        break
```

### Level 3: Safety and Error Handling
```python
# Test safety filters and error handling
async def test_safety_and_errors():
    # Test rate limit handling
    tasks = []
    for i in range(100):
        tasks.append(
            gemini.analyze_code_intent(f"code {i}", use_case="quick_query")
        )
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Should handle rate limits gracefully
    errors = [r for r in results if isinstance(r, Exception)]
    assert len(errors) < 10  # Less than 10% failure rate
```

## Final Validation Checklist

- [ ] Vertex AI client initializes successfully
- [ ] All model types are accessible
- [ ] Safety settings prevent harmful content
- [ ] Response streaming works smoothly
- [ ] Context window management prevents overflows
- [ ] Token tracking accurate within 5%
- [ ] Cache improves response time >50%
- [ ] Rate limit handling prevents errors
- [ ] JSON responses parse correctly
- [ ] Function calling tools work properly

## Anti-Patterns to Avoid

1. **DON'T hardcode API keys** - Use service accounts
2. **DON'T ignore safety settings** - Can expose harmful content
3. **DON'T exceed context windows** - Causes request failures
4. **DON'T cache sensitive data** - Privacy concerns
5. **DON'T skip error handling** - API calls can fail
6. **DON'T use one model for all** - Wastes money/performance
7. **DON'T ignore streaming** - Poor user experience
8. **DON'T forget token tracking** - Budget overruns
9. **DON'T skip response validation** - Can return malformed data
10. **DON'T neglect monitoring** - Miss degraded performance