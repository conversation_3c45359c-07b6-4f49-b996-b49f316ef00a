# PRP Updates for Analysis Engine

This document outlines the proposed changes to the `analysis-engine` to bring its dependencies up to date and ensure compatibility with the latest versions.

## 1. Dependency Updates

The following dependencies will be updated in `PRPs/services/analysis-engine.md`:

*   `google-cloud-spanner`: `0.8+` -> `0.33.0+` (Note: package renamed to `gcloud-spanner`)
*   `tokio`: `1.35+` -> `1.46.1+`
*   `tree-sitter`: `0.20+` -> `0.25.6+`
*   `tungstenite`: `0.21+` -> `0.27.0+`
*   `tower-http`: `0.4+` -> `0.6.6+`
*   `uuid`: `1.6+` -> `1.17.0+`
*   `dashmap`: `5.5+` -> `6.1.0+`

## 2. Code Changes

The following code changes are proposed to address the breaking changes in the updated dependencies:

### 2.1. `google-cloud-spanner`

*   **Action:** Rename the `google-cloud-spanner` crate to `gcloud-spanner` in `Cargo.toml`.
*   **Reason:** The package has been renamed in the `google-cloud-rust` repository.

### 2.2. `tree-sitter`

*   **Action:**
    *   Ensure that a `tree-sitter.json` file is present in the grammar repositories.
    *   Replace all uses of `ts_node_child_containing_descendant` with `ts_node_child_with_descendant`.
    *   Update the `TSInput` struct to include the mandatory `DecodeFunction` field.
*   **Reason:** The `tree-sitter` ABI was bumped to 15 in v0.25.0, which introduced these breaking changes.

### 2.3. `tungstenite`

*   **Action:**
    *   Replace all uses of `read_message`, `write_message`, and `write_pending` with `read`, `write`, `send`, and `flush`.
    *   Remove all uses of the `send_queue` and replace it with a write buffer.
*   **Reason:** The `tungstenite` API was significantly changed in v0.20.0.

### 2.4. `tower-http`

*   **Action:**
    *   Enable the `body` feature flag in `Cargo.toml`.
    *   Update the code to be compatible with `tower` 0.5, `http-body` 1.0, and `http` 1.0.
*   **Reason:** The `body` module is now behind a feature flag, and the dependencies have been updated.

## 3. Next Steps

I will now proceed with implementing these changes. I will start by updating the `Cargo.toml` file for the `analysis-engine` service.
