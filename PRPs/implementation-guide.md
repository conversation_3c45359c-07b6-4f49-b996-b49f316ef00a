# CCL Implementation Guide for Claude Code

name: "CCL Implementation Guide"
description: |
  Complete implementation guide for developing features in the CCL platform with context engineering best practices.
  
  Core Principles:
  - **Context is King**: Include ALL necessary documentation, examples, and caveats
  - **Validation Loops**: Provide executable tests/lints the AI can run and fix
  - **Information Dense**: Use keywords and patterns from the codebase
  - **Progressive Success**: Start simple, validate, then enhance

## Goal

Provide step-by-step implementation instructions for developing features in the CCL platform with comprehensive context engineering support, enabling AI assistants to build production-ready code with minimal iterations.

## Why

This implementation guide enables:
- Consistent development practices across all services
- AI-assisted development with high success rates
- Comprehensive validation at every step
- Reduced debugging and iteration cycles
- Production-ready code from first implementation

## What

### User-Visible Behavior
- Clear implementation patterns for all CCL services
- Executable validation commands for quality assurance
- Progressive implementation phases for manageable development
- Comprehensive testing strategies

### Technical Requirements
- [ ] Service implementation patterns for Rust, Python, Go, TypeScript
- [ ] Database implementation with Spanner and BigQuery
- [ ] API design patterns (REST, GraphQL, gRPC)
- [ ] Testing strategies (unit, integration, E2E)
- [ ] Security implementation patterns
- [ ] Performance optimization techniques
- [ ] Deployment and monitoring patterns

### Success Criteria
- [ ] All validation commands execute successfully
- [ ] Code follows established patterns and conventions
- [ ] Comprehensive test coverage (>90%)
- [ ] Security compliance verified
- [ ] Performance benchmarks met
- [ ] Production deployment successful

## Overview

This guide provides step-by-step implementation instructions for developing features in the CCL platform. Follow these patterns to ensure consistency and maintainability.

## Development Environment Setup

### Prerequisites
```bash
# Required tools
- Go 1.21+
- Rust 1.75+
- Python 3.11+
- Node.js 20+
- gcloud CLI
- Docker Desktop
- Terraform 1.5+
```

### Current vs Desired Codebase Structure

```bash
# Current Codebase Structure:
episteme/
├── PRPs/                           # ✓ Product Requirements Prompts
│   ├── api/                       # API specifications  
│   ├── architecture-patterns.md  # System architecture
│   ├── feature-specifications.md # Feature details
│   ├── implementation-guide.md   # This file
│   └── services/                 # Service specifications
├── context-engineering-intro/     # ✓ Context engineering template
└── README.md                     # ✓ Project overview

# Desired Implementation Structure (to be created):
ccl-platform/
├── analysis-engine/              # Rust - Code parsing and AST analysis
│   ├── src/
│   │   ├── analyzers/           # Language-specific analyzers
│   │   ├── ast/                 # AST representations
│   │   ├── parsers/             # Parser implementations
│   │   └── main.rs              # Service entry point
│   ├── tests/                   # Unit and integration tests
│   ├── Cargo.toml              # Dependencies
│   └── Dockerfile              # Container build
├── query-intelligence/           # Python - AI-powered query processing
│   ├── handlers/               # Query handler implementations
│   ├── models/                 # Pydantic data models
│   ├── services/               # Business logic
│   ├── tests/                  # Test suite
│   ├── requirements.txt        # Dependencies
│   └── Dockerfile              # Container build
├── pattern-mining/              # Python - ML pattern detection
│   ├── detectors/              # Pattern detection algorithms
│   ├── ml/                     # Machine learning models
│   ├── training/               # Model training scripts
│   └── tests/                  # Test suite
├── marketplace/                 # Go - Pattern marketplace and commerce
│   ├── cmd/                    # Service entry points
│   ├── internal/               # Private packages
│   ├── pkg/                    # Public packages
│   ├── api/                    # API definitions
│   └── tests/                  # Test suite
├── web/                        # TypeScript/React - Frontend application
│   ├── src/
│   │   ├── components/         # React components
│   │   ├── hooks/              # Custom hooks
│   │   ├── pages/              # Page components
│   │   └── api/                # API client
│   ├── tests/                  # Frontend tests
│   └── package.json            # Dependencies
├── infrastructure/             # Terraform - Cloud infrastructure
│   ├── environments/           # Environment-specific configs
│   ├── modules/                # Reusable modules
│   └── scripts/                # Deployment scripts
├── shared/                     # Shared libraries and schemas
│   ├── proto/                  # gRPC protocol definitions
│   ├── schemas/                # Data schemas
│   └── utils/                  # Common utilities
├── docs/                       # Documentation
│   ├── api/                    # API documentation
│   ├── architecture/           # Architecture docs
│   └── deployment/             # Deployment guides
├── scripts/                    # Development and deployment scripts
│   ├── setup-local-secrets.sh  # Local development setup
│   ├── build-all.sh           # Build all services
│   └── deploy.sh              # Deployment script
├── Makefile                    # Build and development commands
├── docker-compose.yml          # Local development environment
└── README.md                   # Project documentation
```

### Initial Setup
```bash
# Clone repository
git clone https://github.com/your-org/ccl.git
cd ccl

# Install dependencies
make install-deps

# Configure gcloud
gcloud auth login
gcloud config set project ccl-production

# Set up local secrets
./scripts/setup-local-secrets.sh

# Start local development environment
make dev-up
```

## Progressive Implementation Phases

### Phase 1: Foundation (Weeks 1-2) - MVP
**Goal**: Basic functionality to validate core concepts

```yaml
Sprint 1.1 - Core Infrastructure:
  Tasks:
    - Set up GCP project and basic infrastructure
    - Create Analysis Engine skeleton (Rust)
    - Implement basic code parsing for 3 languages (Python, JavaScript, Go)
    - Set up Spanner database with core tables
    
  Validation Commands:
    - make test-analysis-engine
    - make verify-gcp-setup
    - curl -f http://localhost:8080/health
    
  Success Criteria:
    - Analysis Engine can parse basic code files
    - Database connection established
    - Health checks passing

Sprint 1.2 - Basic API Layer:
  Tasks:
    - Create REST API for analysis requests
    - Implement basic authentication with Firebase
    - Add repository analysis endpoint
    - Create simple web interface for testing
    
  Validation Commands:
    - make test-api
    - make test-auth
    - newman run api-tests.postman_collection.json
    
  Success Criteria:
    - API responds to analysis requests
    - Authentication works end-to-end
    - Can analyze small repositories (<1000 files)
```

### Phase 2: Intelligence (Weeks 3-4) - AI Core
**Goal**: Add AI-powered query and pattern detection

```yaml
Sprint 2.1 - Query Intelligence:
  Tasks:
    - Implement Query Intelligence service (Python)
    - Integrate with Vertex AI (Gemini 2.5)
    - Add natural language query processing
    - Create embeddings for code chunks
    
  Validation Commands:
    - make test-query-intelligence
    - make test-gemini-integration
    - python -m pytest tests/integration/test_query_flow.py
    
  Success Criteria:
    - Natural language queries return relevant results
    - Confidence scores >80% for common queries
    - Response times <500ms for cached queries

Sprint 2.2 - Pattern Mining:
  Tasks:
    - Implement Pattern Mining service (Python)
    - Add basic pattern detection algorithms
    - Create pattern templates and validation
    - Integrate with BigQuery for analytics
    
  Validation Commands:
    - make test-pattern-mining
    - make test-ml-pipeline
    - python -m pytest tests/ml/test_pattern_detection.py
    
  Success Criteria:
    - Detects common patterns (Factory, Repository, etc.)
    - Pattern confidence scores >70%
    - Can process 10MB codebases in <5 minutes
```

### Phase 3: Platform (Weeks 5-6) - Full Platform
**Goal**: Complete platform with marketplace and collaboration

```yaml
Sprint 3.1 - Marketplace & Commerce:
  Tasks:
    - Implement Marketplace service (Go)
    - Add pattern publishing and purchasing
    - Integrate Stripe for payments
    - Create pattern discovery and rating system
    
  Validation Commands:
    - make test-marketplace
    - make test-payments
    - go test ./marketplace/...
    
  Success Criteria:
    - Can publish and purchase patterns
    - Payment processing works end-to-end
    - Pattern search and filtering functional

Sprint 3.2 - Real-time Collaboration:
  Tasks:
    - Implement Collaboration service (Node.js)
    - Add WebSocket support for real-time features
    - Create shared sessions and presence tracking
    - Integrate with Firestore for real-time sync
    
  Validation Commands:
    - make test-collaboration
    - make test-websockets
    - npm test -- --testPathPattern=collaboration
    
  Success Criteria:
    - Multiple users can join sessions
    - Real-time updates work reliably
    - Session state persists correctly
```

### Known Gotchas & Critical Implementation Details

```yaml
CRITICAL Gotchas:
  spanner_gotchas:
    - Interleaved tables require parent key in all queries
    - Use FORCE_INDEX hints for complex queries to avoid full scans
    - Transaction limits: 20,000 mutations, 100MB data
    - Regional vs multi-regional affects latency (use us-central1 for lowest)
    
  vertex_ai_gotchas:
    - Gemini 2.5 quotas vary by region (us-central1 has highest limits)
    - Embeddings API has rate limits: 1000 requests/minute
    - Model versions change - pin to specific versions in production
    - Authentication requires service account with aiplatform.user role
    
  cloud_run_gotchas:
    - Cold starts affect first request (use min-instances: 1 for critical services)
    - Memory limits are hard caps - service killed at limit
    - Request timeout max is 3600s, but default is 300s
    - VPC connector required for private database access
    
  bigquery_gotchas:
    - Streaming inserts have eventual consistency (up to 90 minutes)
    - DML operations have quotas: 200 concurrent DML per table
    - Use clustering for better performance on large tables
    - INFORMATION_SCHEMA queries can be expensive
    
  firestore_gotchas:
    - Composite indexes required for complex queries
    - Document size limit is 1MB
    - Collection group queries need special indexes
    - Real-time listeners have connection limits per client
    
  networking_gotchas:
    - VPC Service Controls can block legitimate cross-service calls
    - Private Google Access required for services without external IPs
    - Load balancer timeout defaults to 30s (increase for long operations)
    - Cloud NAT required for outbound internet from private instances

WARNING Areas:
  - Multi-region Spanner replication can add 100-500ms latency
  - Pattern training on large datasets may hit Vertex AI compute quotas
  - WebSocket connections don't auto-scale like HTTP requests
  - File uploads >32MB require resumable upload for Cloud Storage

TIP - Performance Optimizations:
  - Use Cloud CDN for static assets and cacheable API responses
  - Implement circuit breakers for external API dependencies
  - Use Pub/Sub for async processing to improve response times
  - Enable connection pooling for database connections
  - Use Redis (Memorystore) for session caching and query results
```

## Service Implementation Patterns

### 1. Analysis Engine (Rust)

#### Creating a New Analyzer
```rust
// src/analyzers/mod.rs
pub mod my_analyzer;

// src/analyzers/my_analyzer.rs
use crate::ast::{AstNode, Visitor};
use crate::errors::AnalysisError;

pub struct MyAnalyzer {
    // State fields
}

impl MyAnalyzer {
    pub fn new() -> Self {
        Self {
            // Initialize
        }
    }

    pub async fn analyze(&self, ast: &AstNode) -> Result<AnalysisResult, AnalysisError> {
        // Implementation
        let visitor = MyVisitor::new();
        ast.accept(&visitor)?;
        
        Ok(visitor.into_result())
    }
}

// Implement AST visitor pattern
struct MyVisitor {
    // Visitor state
}

impl Visitor for MyVisitor {
    fn visit_function(&mut self, func: &FunctionNode) -> Result<(), AnalysisError> {
        // Process function
        Ok(())
    }
    
    // Other visit methods...
}
```

#### Testing Analyzers
```rust
#[cfg(test)]
mod tests {
    use super::*;
    use crate::test_utils::{parse_code, create_test_ast};

    #[tokio::test]
    async fn test_analyze_function() {
        let ast = create_test_ast("
            fn example() {
                println!(\"test\");
            }
        ");
        
        let analyzer = MyAnalyzer::new();
        let result = analyzer.analyze(&ast).await.unwrap();
        
        assert_eq!(result.functions.len(), 1);
    }
}
```

### 2. Query Intelligence (Python)

#### Implementing Query Handlers
```python
# query_intelligence/handlers/my_handler.py
from typing import List, Optional
from dataclasses import dataclass
from query_intelligence.base import QueryHandler, QueryContext
from query_intelligence.models import QueryResult, CodeReference

@dataclass
class MyQueryHandler(QueryHandler):
    """Handles specific query patterns."""
    
    async def can_handle(self, query: str, context: QueryContext) -> bool:
        """Determine if this handler can process the query."""
        # Pattern matching logic
        return "my pattern" in query.lower()
    
    async def handle(self, query: str, context: QueryContext) -> QueryResult:
        """Process the query and return results."""
        # Query Vertex AI for understanding
        understanding = await self._understand_query(query)
        
        # Search codebase
        references = await self._search_codebase(
            understanding.search_terms,
            context.repository_id
        )
        
        # Generate response
        response = await self._generate_response(
            query, understanding, references
        )
        
        return QueryResult(
            answer=response.text,
            confidence=response.confidence,
            references=references,
            reasoning=understanding.reasoning
        )
    
    async def _understand_query(self, query: str) -> QueryUnderstanding:
        """Use Gemini to understand query intent."""
        prompt = self._build_understanding_prompt(query)
        response = await self.vertex_client.generate(prompt)
        return QueryUnderstanding.from_response(response)
```

#### Testing Query Handlers
```python
# tests/handlers/test_my_handler.py
import pytest
from unittest.mock import AsyncMock, patch
from query_intelligence.handlers.my_handler import MyQueryHandler

@pytest.mark.asyncio
async def test_can_handle():
    handler = MyQueryHandler()
    context = create_test_context()
    
    assert await handler.can_handle("my pattern query", context)
    assert not await handler.can_handle("other query", context)

@pytest.mark.asyncio
async def test_handle_query():
    handler = MyQueryHandler()
    context = create_test_context()
    
    with patch.object(handler, '_understand_query') as mock_understand:
        mock_understand.return_value = create_test_understanding()
        
        result = await handler.handle("test query", context)
        
        assert result.confidence > 0.8
        assert len(result.references) > 0
```

### 3. Pattern Mining (Python)

#### Creating Pattern Detectors
```python
# pattern_mining/detectors/my_pattern.py
from pattern_mining.base import PatternDetector, Pattern
from pattern_mining.ast import AstAnalyzer
import numpy as np

class MyPatternDetector(PatternDetector):
    """Detects specific code patterns."""
    
    def __init__(self, threshold: float = 0.8):
        self.threshold = threshold
        self.analyzer = AstAnalyzer()
    
    async def detect(self, codebase_id: str) -> List[Pattern]:
        """Detect patterns in the codebase."""
        # Load AST data
        ast_data = await self._load_ast_data(codebase_id)
        
        # Extract features
        features = self._extract_features(ast_data)
        
        # Run pattern detection algorithm
        patterns = await self._detect_patterns(features)
        
        # Filter by confidence
        return [p for p in patterns if p.confidence >= self.threshold]
    
    def _extract_features(self, ast_data: List[AstNode]) -> np.ndarray:
        """Extract numerical features from AST."""
        features = []
        for node in ast_data:
            feature_vector = self.analyzer.node_to_vector(node)
            features.append(feature_vector)
        return np.array(features)
    
    async def _detect_patterns(self, features: np.ndarray) -> List[Pattern]:
        """Apply ML algorithm to detect patterns."""
        # Use clustering or other ML techniques
        from sklearn.cluster import DBSCAN
        
        clustering = DBSCAN(eps=0.3, min_samples=5)
        labels = clustering.fit_predict(features)
        
        # Convert clusters to patterns
        patterns = []
        for label in np.unique(labels):
            if label != -1:  # Skip noise
                pattern = self._cluster_to_pattern(features[labels == label])
                patterns.append(pattern)
        
        return patterns
```

### 4. Marketplace Service (Go)

#### Implementing Pattern APIs
```go
// marketplace/handlers/pattern_handler.go
package handlers

import (
    "context"
    "net/http"
    
    "github.com/gin-gonic/gin"
    "github.com/your-org/ccl/marketplace/models"
    "github.com/your-org/ccl/marketplace/services"
)

type PatternHandler struct {
    service *services.PatternService
}

func NewPatternHandler(service *services.PatternService) *PatternHandler {
    return &PatternHandler{service: service}
}

// CreatePattern handles pattern creation
func (h *PatternHandler) CreatePattern(c *gin.Context) {
    var req models.CreatePatternRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // Validate request
    if err := req.Validate(); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // Get user from context
    userID := c.GetString("user_id")
    
    // Create pattern
    pattern, err := h.service.CreatePattern(c.Request.Context(), userID, &req)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create pattern"})
        return
    }
    
    c.JSON(http.StatusCreated, pattern)
}

// Service implementation
package services

type PatternService struct {
    repo     repositories.PatternRepository
    storage  storage.Client
    pubsub   pubsub.Client
}

func (s *PatternService) CreatePattern(ctx context.Context, userID string, req *models.CreatePatternRequest) (*models.Pattern, error) {
    // Begin transaction
    tx, err := s.repo.BeginTx(ctx)
    if err != nil {
        return nil, err
    }
    defer tx.Rollback()
    
    // Create pattern record
    pattern := &models.Pattern{
        ID:          generateID(),
        UserID:      userID,
        Name:        req.Name,
        Description: req.Description,
        Price:       req.Price,
        Status:      models.PatternStatusPending,
    }
    
    if err := tx.CreatePattern(ctx, pattern); err != nil {
        return nil, err
    }
    
    // Store pattern artifact
    artifactPath := fmt.Sprintf("patterns/%s/artifact.wasm", pattern.ID)
    if err := s.storage.Upload(ctx, artifactPath, req.ArtifactData); err != nil {
        return nil, err
    }
    
    // Publish event
    event := &events.PatternCreated{
        PatternID: pattern.ID,
        UserID:    userID,
        Timestamp: time.Now(),
    }
    
    if err := s.pubsub.Publish(ctx, "pattern-events", event); err != nil {
        return nil, err
    }
    
    // Commit transaction
    if err := tx.Commit(); err != nil {
        return nil, err
    }
    
    return pattern, nil
}
```

### 5. Web Frontend (TypeScript/React)

#### Creating Components
```typescript
// web/src/components/PatternCard.tsx
import React from 'react';
import { Pattern } from '@/types/pattern';
import { usePattern } from '@/hooks/usePattern';
import { Card, Badge, Button } from '@/components/ui';

interface PatternCardProps {
  pattern: Pattern;
  onPurchase?: (pattern: Pattern) => void;
}

export const PatternCard: React.FC<PatternCardProps> = ({ 
  pattern, 
  onPurchase 
}) => {
  const { isPurchased, isLoading } = usePattern(pattern.id);
  
  const handlePurchase = async () => {
    if (onPurchase) {
      onPurchase(pattern);
    }
  };
  
  return (
    <Card className="pattern-card">
      <Card.Header>
        <h3>{pattern.name}</h3>
        <Badge variant={pattern.verified ? 'success' : 'default'}>
          {pattern.verified ? 'Verified' : 'Community'}
        </Badge>
      </Card.Header>
      
      <Card.Body>
        <p className="description">{pattern.description}</p>
        
        <div className="stats">
          <span>Used by {pattern.usageCount} projects</span>
          <span>{pattern.rating}/5 rating</span>
        </div>
        
        <div className="code-preview">
          <pre>{pattern.preview}</pre>
        </div>
      </Card.Body>
      
      <Card.Footer>
        {isPurchased ? (
          <Button variant="secondary" disabled>
            Already Purchased
          </Button>
        ) : (
          <Button 
            onClick={handlePurchase}
            loading={isLoading}
            variant="primary"
          >
            Purchase for ${pattern.price}
          </Button>
        )}
      </Card.Footer>
    </Card>
  );
};

// Custom hook for pattern data
// web/src/hooks/usePattern.ts
import { useQuery, useMutation } from '@tanstack/react-query';
import { patternApi } from '@/api/pattern';

export const usePattern = (patternId: string) => {
  const { data: pattern, isLoading } = useQuery({
    queryKey: ['pattern', patternId],
    queryFn: () => patternApi.getPattern(patternId),
  });
  
  const purchaseMutation = useMutation({
    mutationFn: () => patternApi.purchasePattern(patternId),
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries(['pattern', patternId]);
    },
  });
  
  return {
    pattern,
    isLoading,
    isPurchased: pattern?.purchased ?? false,
    purchase: purchaseMutation.mutate,
  };
};
```

## Database Implementation

### Spanner Schema
```sql
-- patterns table
CREATE TABLE patterns (
  id STRING(36) NOT NULL,
  user_id STRING(36) NOT NULL,
  name STRING(255) NOT NULL,
  description STRING(MAX),
  price NUMERIC NOT NULL,
  status STRING(50) NOT NULL,
  created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
  updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
  CONSTRAINT pk_patterns PRIMARY KEY (id),
  CONSTRAINT fk_patterns_users FOREIGN KEY (user_id) REFERENCES users (id)
) PRIMARY KEY (id);

-- Create indexes
CREATE INDEX idx_patterns_user_id ON patterns(user_id);
CREATE INDEX idx_patterns_status ON patterns(status);
CREATE INDEX idx_patterns_created_at ON patterns(created_at DESC);
```

### BigQuery Analytics
```sql
-- Create analysis results table
CREATE TABLE `ccl-production.analytics.pattern_usage` (
  pattern_id STRING NOT NULL,
  repository_id STRING NOT NULL,
  usage_count INT64 NOT NULL,
  last_used TIMESTAMP NOT NULL,
  performance_metrics STRUCT<
    avg_execution_time_ms FLOAT64,
    success_rate FLOAT64,
    error_count INT64
  >,
  analysis_date DATE NOT NULL
)
PARTITION BY analysis_date
CLUSTER BY pattern_id, repository_id;

-- Query pattern performance
WITH pattern_stats AS (
  SELECT 
    pattern_id,
    COUNT(DISTINCT repository_id) as unique_repos,
    SUM(usage_count) as total_usage,
    AVG(performance_metrics.avg_execution_time_ms) as avg_execution_time,
    AVG(performance_metrics.success_rate) as avg_success_rate
  FROM `ccl-production.analytics.pattern_usage`
  WHERE analysis_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
  GROUP BY pattern_id
)
SELECT 
  p.name,
  ps.*,
  p.price * ps.total_usage * 0.3 as estimated_revenue
FROM pattern_stats ps
JOIN `ccl-production.transactional.patterns` p ON ps.pattern_id = p.id
ORDER BY total_usage DESC
LIMIT 100;
```

## API Implementation

### REST API Endpoints
```yaml
# api/openapi/patterns.yaml
paths:
  /patterns:
    get:
      summary: List patterns
      parameters:
        - name: category
          in: query
          schema:
            type: string
        - name: sort
          in: query
          schema:
            type: string
            enum: [popularity, rating, price, recent]
      responses:
        200:
          description: List of patterns
          content:
            application/json:
              schema:
                type: object
                properties:
                  patterns:
                    type: array
                    items:
                      $ref: '#/components/schemas/Pattern'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
    
    post:
      summary: Create new pattern
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePatternRequest'
      responses:
        201:
          description: Pattern created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Pattern'
```

### GraphQL Schema
```graphql
# api/graphql/schema.graphql
type Pattern {
  id: ID!
  name: String!
  description: String!
  price: Float!
  author: User!
  category: PatternCategory!
  tags: [String!]!
  verified: Boolean!
  rating: Float!
  usageCount: Int!
  preview: String!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type Query {
  pattern(id: ID!): Pattern
  patterns(
    filter: PatternFilter
    sort: PatternSort
    pagination: PaginationInput
  ): PatternConnection!
  
  myPatterns: [Pattern!]!
  purchasedPatterns: [Pattern!]!
}

type Mutation {
  createPattern(input: CreatePatternInput!): Pattern!
  updatePattern(id: ID!, input: UpdatePatternInput!): Pattern!
  purchasePattern(id: ID!): PurchaseResult!
  ratePattern(id: ID!, rating: Int!): Pattern!
}

input PatternFilter {
  category: PatternCategory
  minPrice: Float
  maxPrice: Float
  verified: Boolean
  author: ID
}
```

## Testing Strategy

### Unit Tests
```bash
# Run all unit tests
make test-unit

# Run specific service tests
make test-unit-analysis
make test-unit-query
make test-unit-marketplace
```

### Integration Tests
```python
# tests/integration/test_query_flow.py
import pytest
from tests.fixtures import create_test_repository

@pytest.mark.integration
async def test_complete_query_flow(ccl_client, test_repository):
    """Test complete query processing flow."""
    # Index repository
    analysis = await ccl_client.analyze_repository(test_repository.url)
    assert analysis.status == "completed"
    
    # Query the repository
    result = await ccl_client.query(
        "Find all database connection patterns",
        repository_id=test_repository.id
    )
    
    assert result.confidence > 0.8
    assert len(result.references) > 0
    assert "database" in result.answer.lower()
    
    # Verify pattern detection
    patterns = await ccl_client.get_patterns(test_repository.id)
    db_patterns = [p for p in patterns if "database" in p.name.lower()]
    assert len(db_patterns) > 0
```

### End-to-End Tests
```typescript
// e2e/pattern-purchase.spec.ts
import { test, expect } from '@playwright/test';

test('complete pattern purchase flow', async ({ page }) => {
  // Login
  await page.goto('/login');
  await page.fill('[name="email"]', '<EMAIL>');
  await page.fill('[name="password"]', 'password');
  await page.click('button[type="submit"]');
  
  // Navigate to marketplace
  await page.goto('/marketplace');
  
  // Search for pattern
  await page.fill('[placeholder="Search patterns..."]', 'repository pattern');
  await page.keyboard.press('Enter');
  
  // Click first pattern
  await page.click('.pattern-card:first-child');
  
  // Purchase pattern
  await page.click('button:has-text("Purchase for $")');
  
  // Confirm purchase
  await page.click('button:has-text("Confirm Purchase")');
  
  // Verify success
  await expect(page.locator('.toast-success')).toContainText('Pattern purchased successfully');
  await expect(page.locator('button:has-text("Already Purchased")')).toBeVisible();
});
```

## Deployment

### Service Deployment
```bash
# Deploy a specific service
make deploy-service SERVICE=analysis-engine ENV=production

# Deploy all services
make deploy-all ENV=production

# Rollback deployment
make rollback SERVICE=analysis-engine VERSION=v1.2.3
```

### Infrastructure Updates
```bash
# Plan infrastructure changes
cd infrastructure/environments/production
terraform plan -out=tfplan

# Apply changes
terraform apply tfplan

# Update Cloud Run services
gcloud run deploy analysis-engine \
  --image gcr.io/ccl-production/analysis-engine:latest \
  --platform managed \
  --region us-central1 \
  --memory 2Gi \
  --cpu 2 \
  --max-instances 100 \
  --min-instances 2
```

## Monitoring and Debugging

### Distributed Tracing
```python
# Add tracing to Python services
from opentelemetry import trace
from opentelemetry.trace import Status, StatusCode

tracer = trace.get_tracer(__name__)

class QueryHandler:
    async def handle(self, query: str, context: QueryContext) -> QueryResult:
        with tracer.start_as_current_span(
            "handle_query",
            attributes={
                "query.text": query,
                "repository.id": context.repository_id,
            }
        ) as span:
            try:
                result = await self._process_query(query, context)
                span.set_attribute("query.confidence", result.confidence)
                return result
            except Exception as e:
                span.set_status(Status(StatusCode.ERROR, str(e)))
                span.record_exception(e)
                raise
```

### Structured Logging
```go
// Use structured logging in Go services
import (
    "github.com/rs/zerolog/log"
)

func (s *PatternService) CreatePattern(ctx context.Context, req *CreatePatternRequest) (*Pattern, error) {
    logger := log.With().
        Str("operation", "create_pattern").
        Str("user_id", req.UserID).
        Str("pattern_name", req.Name).
        Logger()
    
    logger.Info().Msg("Creating pattern")
    
    pattern, err := s.repo.Create(ctx, req)
    if err != nil {
        logger.Error().Err(err).Msg("Failed to create pattern")
        return nil, err
    }
    
    logger.Info().
        Str("pattern_id", pattern.ID).
        Msg("Pattern created successfully")
    
    return pattern, nil
}
```

## Performance Optimization

### Caching Strategy
```python
# Implement multi-level caching
from cachetools import TTLCache
import redis.asyncio as redis

class CachedQueryHandler:
    def __init__(self):
        self.memory_cache = TTLCache(maxsize=1000, ttl=300)
        self.redis_client = redis.from_url("redis://localhost")
    
    async def get_cached_result(self, query_hash: str) -> Optional[QueryResult]:
        # Check memory cache first
        if query_hash in self.memory_cache:
            return self.memory_cache[query_hash]
        
        # Check Redis cache
        cached = await self.redis_client.get(f"query:{query_hash}")
        if cached:
            result = QueryResult.from_json(cached)
            self.memory_cache[query_hash] = result
            return result
        
        return None
    
    async def cache_result(self, query_hash: str, result: QueryResult):
        # Cache in memory
        self.memory_cache[query_hash] = result
        
        # Cache in Redis with longer TTL
        await self.redis_client.setex(
            f"query:{query_hash}",
            3600,  # 1 hour
            result.to_json()
        )
```

### Query Optimization
```sql
-- Optimize Spanner queries with proper indexes
CREATE INDEX idx_patterns_composite 
ON patterns(status, created_at DESC)
STORING (name, price, user_id);

-- Use query hints for better performance
@{FORCE_INDEX=idx_patterns_composite}
SELECT p.id, p.name, p.price, u.username
FROM patterns p
JOIN users u ON p.user_id = u.id
WHERE p.status = 'ACTIVE'
  AND p.created_at > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
ORDER BY p.created_at DESC
LIMIT 100;
```

## Security Implementation

### Authentication Flow
```typescript
// Implement secure authentication
import { auth } from '@/lib/auth';

export async function authenticateRequest(req: Request): Promise<User> {
  const token = req.headers.get('Authorization')?.replace('Bearer ', '');
  
  if (!token) {
    throw new AuthError('No token provided');
  }
  
  try {
    // Verify JWT token
    const decoded = await auth.verifyToken(token);
    
    // Check token expiration
    if (decoded.exp < Date.now() / 1000) {
      throw new AuthError('Token expired');
    }
    
    // Verify user still exists and is active
    const user = await userService.getUser(decoded.sub);
    if (!user || !user.active) {
      throw new AuthError('Invalid user');
    }
    
    return user;
  } catch (error) {
    throw new AuthError('Invalid token');
  }
}
```

### Input Validation
```python
# Implement comprehensive input validation
from pydantic import BaseModel, validator, constr
from typing import Optional

class CreatePatternRequest(BaseModel):
    name: constr(min_length=3, max_length=100)
    description: constr(min_length=10, max_length=1000)
    category: PatternCategory
    price: float
    artifact_data: bytes
    
    @validator('price')
    def validate_price(cls, v):
        if v < 0 or v > 10000:
            raise ValueError('Price must be between 0 and 10000')
        return round(v, 2)
    
    @validator('artifact_data')
    def validate_artifact(cls, v):
        if len(v) > 10 * 1024 * 1024:  # 10MB limit
            raise ValueError('Artifact size exceeds 10MB limit')
        # Validate WASM format
        if not v.startswith(b'\x00asm'):
            raise ValueError('Invalid WASM artifact')
        return v
```

## Validation Loop

### Level 1: Syntax & Style Validation
```bash
# Run these FIRST - fix any errors before proceeding
make lint                           # Auto-fix style issues across all services
make fmt                           # Format code according to standards
make typecheck                     # Type checking for TypeScript/Python

# Service-specific linting
make lint-rust                     # Rust clippy + formatting
make lint-python                   # ruff + black + mypy  
make lint-go                       # golangci-lint + gofmt
make lint-typescript               # eslint + prettier

# Expected: No errors. If errors exist, READ and fix before continuing.
```

### Level 2: Unit & Integration Tests
```bash
# Run comprehensive test suite
make test                          # All tests across all services
make test-unit                     # Unit tests only
make test-integration              # Integration tests only
make test-e2e                      # End-to-end tests

# Service-specific testing
make test-analysis-engine          # Rust analyzer tests
make test-query-intelligence       # Python query handler tests  
make test-pattern-mining           # ML model and detection tests
make test-marketplace              # Go API and business logic tests
make test-web                      # React component and hook tests

# Coverage requirements
make coverage                      # Generate coverage report
# Expected: >90% coverage across all services
```

### Level 3: Security & Compliance Validation
```bash
# Security scanning
make security-scan                 # Vulnerability scanning
make secrets-scan                  # Check for hardcoded secrets
make dependency-audit              # Audit dependencies for vulnerabilities

# Compliance validation  
make compliance-check              # SOC2/HIPAA compliance verification
make privacy-audit                 # GDPR/CCPA compliance check
make penetration-test              # Basic security testing

# Expected: No critical vulnerabilities, compliance requirements met
```

### Level 4: Performance & Load Testing
```bash
# Performance benchmarks
make benchmark                     # Run performance benchmarks
make load-test DURATION=5m         # 5-minute load test
make stress-test                   # Stress testing with high load

# Service-specific performance tests
make perf-analysis-engine          # Code parsing performance
make perf-query-intelligence       # Query response time benchmarks
make perf-pattern-mining           # ML inference performance
make perf-marketplace              # API throughput testing

# Expected performance targets:
# - API response times <100ms (p95)
# - Analysis completion <30s for 1M LOC
# - Pattern detection <5min for 10MB codebase
# - Query processing <500ms with cache
```

### Level 5: Deployment Validation
```bash
# Infrastructure validation
make verify-infrastructure ENV=staging    # Verify infrastructure is ready
make verify-secrets ENV=staging          # Verify all secrets are configured
make verify-networking ENV=staging       # Test network connectivity

# Deployment validation
make deploy ENV=staging                   # Deploy to staging environment
make health-check ENV=staging            # Verify all services are healthy
make smoke-test ENV=staging              # Basic functionality verification

# Production readiness
make production-readiness-check          # Complete pre-production validation
make disaster-recovery-test              # Test backup and recovery procedures

# Expected: All services healthy, smoke tests passing, ready for production
```

### Continuous Validation During Development
```bash
# Use these commands during active development
make dev-validate                  # Quick validation for development
make pre-commit                    # Run before committing code
make pre-push                     # Run before pushing to remote

# CI/CD pipeline validation (automatically run)
make ci-validate                   # Full CI validation suite
make cd-validate                   # Deployment validation pipeline
```

## Confidence Score: 9/10

### High Confidence Areas (9-10/10):
- **Service Architecture**: Well-established microservices patterns with clear boundaries
- **Database Design**: Proven Spanner and BigQuery patterns for global scale
- **API Implementation**: Standard REST/GraphQL patterns with comprehensive examples
- **Testing Strategy**: Complete test pyramid with unit, integration, and E2E tests
- **Security Implementation**: Industry-standard authentication and authorization patterns
- **GCP Integration**: Established patterns for Cloud Run, Vertex AI, and data services

### Medium Confidence Areas (7-8/10):
- **ML Pipeline Implementation**: Pattern detection algorithms need fine-tuning based on data
- **Real-time Collaboration**: WebSocket scaling patterns may need adjustment under load
- **Multi-region Deployment**: Latency optimization may require iteration
- **Performance Optimization**: Cache strategies may need tuning based on usage patterns

### Areas Requiring Validation (6-7/10):
- **Vertex AI Integration**: Gemini 2.5 API stability and quota management
- **Pattern Marketplace Economics**: Revenue sharing and pricing models
- **Large-scale Analysis**: Performance with repositories >10M LOC
- **Collaboration Scaling**: Session management with >1000 concurrent users

### Risk Mitigation Strategies:
1. **Start with MVP** to validate core assumptions
2. **Implement comprehensive monitoring** for early issue detection
3. **Use staged rollouts** for new features and optimizations
4. **Maintain fallback strategies** for critical dependencies
5. **Regular performance testing** to catch regressions early

This confidence score reflects the comprehensive nature of the implementation guide, established technology patterns, and clear validation procedures while acknowledging areas that will require iterative refinement based on real-world usage.

## Final Implementation Checklist

### Pre-Implementation Validation
- [ ] All prerequisite tools installed and configured
- [ ] GCP project set up with required APIs enabled
- [ ] Local development environment functional
- [ ] Secret management system configured
- [ ] CI/CD pipeline ready for deployment

### Phase-by-Phase Validation
- [ ] Phase 1: MVP functionality validated and deployed to staging
- [ ] Phase 2: AI/ML features working with acceptable performance
- [ ] Phase 3: Full platform features complete and tested
- [ ] Production deployment successful with monitoring active
- [ ] User acceptance testing completed with >95% satisfaction

### Ongoing Operational Validation
- [ ] All monitoring dashboards configured and alerting properly
- [ ] Security scanning automated and reporting clean
- [ ] Performance benchmarks meeting targets consistently
- [ ] Backup and disaster recovery procedures tested
- [ ] Documentation complete and up-to-date

---

This implementation guide provides concrete patterns and examples for developing features in the CCL platform with comprehensive context engineering support. The validation loops ensure production-ready code, while the confidence scoring helps identify areas requiring additional attention. Always refer to the service-specific documentation for detailed API references and follow the established patterns to maintain consistency across the codebase.