# Query Intelligence Natural Language Interface PRP

## Purpose
Enable developers and non-technical stakeholders to interact with codebases using natural language queries, providing instant, accurate, and contextual responses about code structure, functionality, patterns, and architecture through an AI-powered interface that democratizes codebase knowledge.

## Goal
Deliver a production-ready natural language query system that processes developer questions in <100ms, achieves >85% accuracy on benchmark queries, supports multi-turn conversations, and serves as the primary interface for code understanding across the CCL platform.

## Business Value
- **Knowledge Democratization**: Enable non-technical stakeholders to understand codebases without developer assistance
- **Developer Productivity**: Reduce time to find code patterns from hours to seconds
- **Onboarding Acceleration**: New team members can understand codebases 10x faster
- **Documentation Automation**: Generate contextual explanations on-demand
- **Cross-team Collaboration**: Bridge communication gaps between technical and business teams
- **Cost Reduction**: Eliminate 60% of "how does this work?" meetings and documentation requests
- **Competitive Advantage**: First-to-market natural language codebase interface

## Success Criteria

### Performance Requirements
- [ ] Process queries in <100ms (p95) for cached responses
- [ ] Handle context up to 2M tokens efficiently
- [ ] Support 1000+ concurrent users without degradation
- [ ] Maintain <50ms first-token latency for streaming responses
- [ ] Memory usage <4GB per instance

### Accuracy & Quality
- [ ] Achieve >85% accuracy on benchmark queries
- [ ] Provide confidence scores >80% correlation with human evaluation
- [ ] Support multi-turn conversations with context retention
- [ ] Generate relevant follow-up questions for 90% of queries
- [ ] Maintain conversation history for 24 hours

### Language & Feature Support
- [ ] Support 25+ programming languages
- [ ] Handle complex architectural queries
- [ ] Process code explanation requests
- [ ] Answer "where is X used" queries
- [ ] Explain design patterns and anti-patterns

### Reliability & Monitoring
- [ ] Achieve 99.9% uptime with graceful degradation
- [ ] Handle Vertex AI rate limits transparently
- [ ] Provide detailed error messages for failed queries
- [ ] Monitor query quality with automated feedback loops

## Documentation

### Internal References
- Service specification: `PRPs/services/query-intelligence.md`
- Gemini integration: `PRPs/ai-ml/gemini-integration.md`
- Embeddings guide: `PRPs/ai-ml/embeddings.md`
- Example code: `examples/query-intelligence/query_processor.py`
- Implementation guide: `PRPs/implementation-guide.md`
- Current progress: Repository Analysis API (dependency)

### External Documentation
- **Vertex AI Python SDK**: https://cloud.google.com/vertex-ai/docs/python-sdk/use-vertex-ai-python-sdk
- **Gemini 2.5 Flash/Pro API Reference**: https://cloud.google.com/vertex-ai/generative-ai/docs/models/gemini/2-5-flash
- **Gemini API Rate Limits (2025)**: https://ai.google.dev/gemini-api/docs/rate-limits
- **LangChain RAG Tutorials**: https://python.langchain.com/docs/tutorials/rag/
- **FastAPI Streaming Response Guide**: https://apidog.com/blog/fastapi-streaming-response/
- **Redis Async Python (redis-py)**: https://redis-py.readthedocs.io/en/stable/
- **Advanced Redis Caching Techniques**: https://codedamn.com/news/backend/advanced-redis-caching-techniques
- **Pydantic V2 Documentation**: https://docs.pydantic.dev/latest/
- **Code Embeddings Models 2025**: https://github.com/codefuse-ai/Awesome-Code-LLM
- **Google Cloud Spanner Python**: https://cloud.google.com/spanner/docs/getting-started/python
- **Prometheus Python Client**: https://prometheus.github.io/client_python/
- **Structlog Documentation**: https://www.structlog.org/en/stable/
- **Pytest Async Testing**: https://pytest-asyncio.readthedocs.io/en/latest/
- **WebSocket FastAPI**: https://fastapi.tiangolo.com/advanced/websockets/
- **Contextual Retrieval (Anthropic 2024)**: https://www.anthropic.com/news/contextual-retrieval
- **Production RAG Systems 2025**: https://medium.com/@meeran03/building-production-ready-rag-systems-best-practices-and-latest-tools-581cae9518e7

### Code Examples from Research

```python
# Vertex AI client initialization with 2025 best practices
import vertexai
from vertexai.generative_models import GenerativeModel, GenerationConfig
from google.cloud import aiplatform
import asyncio
from typing import AsyncGenerator

# Initialize with proper region selection for highest quotas (July 2025)
vertexai.init(
    project="ccl-platform-prod",
    location="us-central1",  # Highest quotas as of 2025
    credentials=None  # Uses default service account
)

# Use Gemini 2.5 Flash for optimal cost/performance ratio
model = GenerativeModel(
    "gemini-2.5-flash",  # Most cost-efficient model in 2025
    generation_config=GenerationConfig(
        temperature=0.1,  # Low for factual code responses
        top_p=0.95,
        max_output_tokens=8192,
        candidate_count=1
    ),
    safety_settings={
        "HARM_CATEGORY_HARASSMENT": "BLOCK_NONE",
        "HARM_CATEGORY_HATE_SPEECH": "BLOCK_NONE",
        "HARM_CATEGORY_SEXUALLY_EXPLICIT": "BLOCK_NONE",
        "HARM_CATEGORY_DANGEROUS_CONTENT": "BLOCK_NONE"
    }
)

# Advanced async query processing with circuit breaker pattern (2025)
from circuitbreaker import circuit
import structlog

logger = structlog.get_logger()

@circuit(failure_threshold=5, recovery_timeout=30)
async def process_query_with_resilience(query: str, context: QueryContext) -> QueryResult:
    """Process query with circuit breaker and advanced retry logic"""

    retry_delays = [1, 2, 4, 8, 16]  # Exponential backoff

    for attempt, delay in enumerate(retry_delays):
        try:
            # Check rate limits before making request
            await rate_limiter.acquire()

            result = await process_query(query, context)

            # Log successful processing
            logger.info(
                "query_processed_successfully",
                attempt=attempt + 1,
                query_length=len(query),
                execution_time_ms=result.execution_time_ms
            )

            return result

        except ResourceExhausted as e:
            if attempt < len(retry_delays) - 1:
                logger.warning(
                    "rate_limit_hit_retrying",
                    attempt=attempt + 1,
                    delay=delay,
                    error=str(e)
                )
                await asyncio.sleep(delay)
            else:
                raise QueryProcessingError(f"Rate limit exceeded after {len(retry_delays)} attempts")

        except Exception as e:
            if attempt < len(retry_delays) - 1:
                logger.error(
                    "query_processing_error_retrying",
                    attempt=attempt + 1,
                    delay=delay,
                    error=str(e)
                )
                await asyncio.sleep(delay)
            else:
                raise QueryProcessingError(f"Failed after {len(retry_delays)} attempts: {e}")

# Advanced RAG pipeline with contextual retrieval (2025 technique)
from sentence_transformers import SentenceTransformer
import numpy as np

class AdvancedRAGPipeline:
    def __init__(self):
        # Use latest code-specific embedding model (2025)
        self.embedder = SentenceTransformer('mistralai/Codestral-Embed')
        self.contextual_embedder = SentenceTransformer('sentence-transformers/all-mpnet-base-v2')

    async def retrieve_and_generate_contextual(self, query: str, repo_id: str) -> str:
        """Advanced RAG with contextual retrieval technique"""

        # Step 1: Generate query embedding
        query_embedding = await self._embed_query(query)

        # Step 2: Retrieve initial chunks
        initial_chunks = await self.vector_search.search(
            embedding=query_embedding,
            filters={"repository_id": repo_id},
            limit=50  # Retrieve more for better context
        )

        # Step 3: Apply contextual retrieval (Anthropic 2024 technique)
        # This technique involves adding explanatory context to each chunk *before* embedding.
        # This helps the model to better understand the context of each chunk, leading to
        # more accurate and relevant results.
        contextualized_chunks = await self._add_contextual_information(
            initial_chunks, query
        )

        # Step 4: Re-rank chunks using cross-encoder
        reranked_chunks = await self._rerank_chunks(
            query, contextualized_chunks
        )

        # Step 5: Adaptive context window management
        context_manager = AdaptiveContextManager(
            model_type="gemini-2.5-flash",
            max_tokens=1_000_000  # 2M token context window
        )

        optimized_context = await context_manager.optimize_context(
            query=query,
            chunks=reranked_chunks[:20],
            conversation_history=[]
        )

        # Step 6: Generate response with streaming
        response_stream = await self._generate_streaming_response(
            query, optimized_context
        )

        return response_stream

    async def _add_contextual_information(self, chunks: List[CodeChunk], query: str) -> List[CodeChunk]:
        """Add contextual information to chunks for better retrieval"""

        contextualized_chunks = []

        for chunk in chunks:
            # Add surrounding context (function/class definitions)
            surrounding_context = await self._get_surrounding_context(chunk)

            # Add file-level context
            file_context = await self._get_file_context(chunk.file_path)

            # Create contextual summary
            contextual_summary = await self._generate_contextual_summary(
                chunk, surrounding_context, file_context, query
            )

            # Create enhanced chunk
            enhanced_chunk = CodeChunk(
                content=chunk.content,
                file_path=chunk.file_path,
                line_start=chunk.line_start,
                line_end=chunk.line_end,
                contextual_summary=contextual_summary,
                surrounding_context=surrounding_context,
                file_context=file_context
            )

            contextualized_chunks.append(enhanced_chunk)

        return contextualized_chunks

# High-performance Redis caching with advanced patterns (2025)
import redis.asyncio as redis
import json
import hashlib
import pickle
from typing import Optional, Any
import aiocache
from aiocache.serializers import PickleSerializer

class AdvancedQueryCache:
    """Advanced caching with multiple layers and intelligent TTL"""

    def __init__(self, redis_url: str):
        # Primary Redis cache
        self.redis = redis.from_url(redis_url, decode_responses=False)

        # In-memory cache for hot queries
        self.memory_cache = aiocache.Cache(
            aiocache.SimpleMemoryCache,
            serializer=PickleSerializer(),
            ttl=300  # 5 minutes
        )

        # Cache statistics
        self.stats = {
            "hits": 0,
            "misses": 0,
            "memory_hits": 0,
            "redis_hits": 0
        }

    async def get_cached_response(
        self,
        query: str,
        context_hash: str,
        use_memory_cache: bool = True
    ) -> Optional[QueryResult]:
        """Multi-layer cache retrieval with statistics"""

        cache_key = self._generate_cache_key(query, context_hash)

        # Layer 1: Memory cache (fastest)
        if use_memory_cache:
            memory_result = await self.memory_cache.get(cache_key)
            if memory_result:
                self.stats["hits"] += 1
                self.stats["memory_hits"] += 1
                logger.info("cache_hit_memory", key=cache_key[:8])
                return memory_result

        # Layer 2: Redis cache
        try:
            redis_result = await self.redis.get(cache_key)
            if redis_result:
                result = pickle.loads(redis_result)

                # Promote to memory cache
                if use_memory_cache:
                    await self.memory_cache.set(cache_key, result)

                self.stats["hits"] += 1
                self.stats["redis_hits"] += 1
                logger.info("cache_hit_redis", key=cache_key[:8])
                return result

        except Exception as e:
            logger.warning("redis_cache_error", error=str(e))

        # Cache miss
        self.stats["misses"] += 1
        logger.info("cache_miss", key=cache_key[:8])
        return None

    async def cache_response(
        self,
        query: str,
        context_hash: str,
        response: QueryResult,
        ttl: Optional[int] = None
    ) -> None:
        """Intelligent caching with adaptive TTL"""

        cache_key = self._generate_cache_key(query, context_hash)

        # Calculate adaptive TTL based on query characteristics
        adaptive_ttl = self._calculate_adaptive_ttl(query, response)
        final_ttl = ttl or adaptive_ttl

        try:
            # Cache in Redis
            serialized_response = pickle.dumps(response)
            await self.redis.setex(cache_key, final_ttl, serialized_response)

            # Cache in memory for hot queries
            if response.confidence > 0.8:  # High confidence responses
                await self.memory_cache.set(cache_key, response)

            logger.info(
                "response_cached",
                key=cache_key[:8],
                ttl=final_ttl,
                confidence=response.confidence
            )

        except Exception as e:
            logger.error("cache_storage_error", error=str(e))

    def _calculate_adaptive_ttl(self, query: str, response: QueryResult) -> int:
        """Calculate TTL based on query and response characteristics"""

        base_ttl = 3600  # 1 hour default

        # Longer TTL for high confidence responses
        if response.confidence > 0.9:
            base_ttl *= 2
        elif response.confidence < 0.7:
            base_ttl //= 2

        # Longer TTL for architectural queries (less likely to change)
        if response.intent in ["ARCHITECTURE", "EXPLAIN"]:
            base_ttl *= 1.5

        # Shorter TTL for implementation details (more likely to change)
        if response.intent in ["IMPLEMENT", "DEBUG"]:
            base_ttl //= 2

        return int(base_ttl)

    def _generate_cache_key(self, query: str, context_hash: str) -> str:
        """Generate consistent cache key"""
        combined = f"{query}:{context_hash}"
        return f"query:v2:{hashlib.sha256(combined.encode()).hexdigest()}"

    async def get_cache_stats(self) -> dict:
        """Get cache performance statistics"""
        total_requests = self.stats["hits"] + self.stats["misses"]
        hit_rate = (self.stats["hits"] / total_requests * 100) if total_requests > 0 else 0

        return {
            **self.stats,
            "hit_rate_percent": round(hit_rate, 2),
            "total_requests": total_requests
        }
```

## Implementation Blueprint

### Phase 1: Query Processing Pipeline

```python
from typing import List, Dict, Optional, AsyncGenerator
from dataclasses import dataclass
from datetime import datetime
import asyncio
import structlog
from fastapi import FastAPI, WebSocket, HTTPException
from pydantic import BaseModel, Field
import redis.asyncio as redis
from google.cloud import aiplatform
import numpy as np

logger = structlog.get_logger()

@dataclass
class QueryPipeline:
    """
    Main pipeline for processing natural language queries about code
    """
    
    def __init__(self, config: QueryConfig):
        self.config = config
        self.embedder = CodeEmbedder(config.embedding_model)
        self.retriever = SemanticRetriever(config.vector_db_config)
        self.generator = ResponseGenerator(config.llm_config)
        self.cache = QueryCache(config.redis_url)
        self.context_manager = ContextManager()
        
    async def process_query(
        self, 
        query: str, 
        context: QueryContext,
        stream: bool = False
    ) -> QueryResult:
        """
        Process a natural language query with full pipeline
        """
        start_time = datetime.now()
        
        # Step 1: Input validation and preprocessing
        validated_query = self._validate_and_clean_query(query)
        
        # Step 2: Check cache for exact matches
        cache_key = self._generate_cache_key(validated_query, context)
        cached_result = await self.cache.get_cached_response(cache_key)
        if cached_result:
            logger.info("cache_hit", query_hash=cache_key[:8])
            return QueryResult.from_cache(cached_result)
        
        # Step 3: Query understanding and intent classification
        intent = await self._classify_intent(validated_query, context)
        logger.info("intent_classified", intent=intent.primary_intent)
        
        # Step 4: Query expansion and reformulation
        expanded_query = await self._expand_query(validated_query, intent, context)
        
        # Step 5: Context retrieval (RAG)
        relevant_context = await self._retrieve_context(
            expanded_query, 
            context, 
            intent
        )
        
        # Step 6: Context prioritization and window management
        prioritized_context = self.context_manager.prioritize_context(
            query=expanded_query,
            code_files=relevant_context.code_chunks,
            conversation_history=context.conversation_history,
            intent=intent
        )
        
        # Step 7: Generate response (streaming or batch)
        if stream:
            response_stream = self._generate_streaming_response(
                validated_query, 
                prioritized_context, 
                intent
            )
            return QueryResult.streaming(response_stream)
        else:
            response = await self._generate_response(
                validated_query, 
                prioritized_context, 
                intent
            )
        
        # Step 8: Post-process and validate response
        validated_response = await self._validate_response(
            response, 
            relevant_context
        )
        
        # Step 9: Calculate confidence score
        confidence = await self._calculate_confidence(
            validated_query, 
            validated_response, 
            relevant_context
        )
        
        # Step 10: Generate follow-up questions
        follow_ups = await self._generate_follow_ups(
            validated_query, 
            validated_response, 
            context
        )
        
        # Step 11: Extract and format code references
        references = self._extract_code_references(relevant_context.code_chunks[:5])
        
        # Step 12: Build final result
        execution_time = (datetime.now() - start_time).total_seconds() * 1000
        
        result = QueryResult(
            answer=validated_response.text,
            confidence=confidence,
            references=references,
            follow_up_questions=follow_ups,
            execution_time_ms=execution_time,
            intent=intent.primary_intent,
            sources_used=len(relevant_context.code_chunks),
            model_used=self.generator.current_model
        )
        
        # Step 13: Cache result for future queries
        await self.cache.cache_response(cache_key, result, ttl=3600)
        
        # Step 14: Log metrics and analytics
        await self._log_query_metrics(validated_query, result, context)
        
        return result
    
    async def _classify_intent(
        self, 
        query: str, 
        context: QueryContext
    ) -> QueryIntent:
        """
        Classify the intent of the query using a lightweight model
        """
        classification_prompt = f"""
        Analyze this code-related query and classify its intent:
        
        Query: "{query}"
        Repository: {context.repository_id}
        
        Classify into one of these intents:
        - EXPLAIN: User wants explanation of how code works
        - FIND: User wants to locate specific code or functionality  
        - DEBUG: User needs help understanding errors or issues
        - IMPLEMENT: User wants guidance on implementing something
        - REFACTOR: User wants suggestions for code improvement
        - ARCHITECTURE: User wants high-level system understanding
        - USAGE: User wants to know how to use existing code
        
        Also extract:
        - Code elements mentioned (functions, classes, files)
        - Scope (specific file, module, entire codebase)
        - Complexity level (simple, moderate, complex)
        
        Return JSON format:
        {{
            "primary_intent": "EXPLAIN",
            "confidence": 0.95,
            "code_elements": ["function_name", "ClassName"],
            "scope": "module",
            "complexity": "moderate",
            "requires_context": true
        }}
        """
        
        response = await self.generator.generate_structured(
            classification_prompt,
            response_format="json",
            temperature=0.1
        )
        
        return QueryIntent.from_json(response)
    
    async def _expand_query(
        self, 
        query: str, 
        intent: QueryIntent, 
        context: QueryContext
    ) -> str:
        """
        Expand and reformulate query for better retrieval
        """
        if intent.primary_intent in ["FIND", "USAGE"]:
            # Add synonyms and related terms
            expansion_prompt = f"""
            Expand this search query with relevant synonyms and related terms:
            
            Original: "{query}"
            Intent: {intent.primary_intent}
            Code elements: {intent.code_elements}
            
            Generate 3-5 alternative phrasings that would help find the same information.
            Include technical synonyms and common variations.
            
            Return as a single expanded query string.
            """
            
            expanded = await self.generator.generate_text(
                expansion_prompt,
                temperature=0.3,
                max_tokens=200
            )
            
            return f"{query} {expanded}"
        
        return query
    
    async def _retrieve_context(
        self, 
        query: str, 
        context: QueryContext, 
        intent: QueryIntent
    ) -> RetrievalResult:
        """
        Retrieve relevant code context using hybrid search
        """
        # Generate query embedding
        query_embedding = await self.embedder.embed_text(query)
        
        # Determine search strategy based on intent
        search_params = self._get_search_params(intent)
        
        # Semantic search
        semantic_results = await self.retriever.semantic_search(
            embedding=query_embedding,
            repository_id=context.repository_id,
            filters=self._build_search_filters(intent, context),
            limit=search_params.semantic_limit
        )
        
        # Keyword search for specific terms
        keyword_results = await self.retriever.keyword_search(
            query=query,
            repository_id=context.repository_id,
            filters=self._build_search_filters(intent, context),
            limit=search_params.keyword_limit
        )
        
        # Hybrid ranking
        merged_results = await self._merge_search_results(
            semantic_results,
            keyword_results,
            query,
            intent
        )
        
        return RetrievalResult(
            code_chunks=merged_results,
            total_found=len(merged_results),
            search_strategy="hybrid",
            retrieval_time_ms=(datetime.now() - start_time).total_seconds() * 1000
        )

    async def _generate_streaming_response(
        self,
        query: str,
        context: PrioritizedContext,
        intent: QueryIntent
    ) -> AsyncGenerator[str, None]:
        """
        Generate streaming response for real-time user experience
        """
        prompt = self._build_generation_prompt(query, context, intent)

        async for chunk in self.generator.generate_stream(
            prompt,
            temperature=0.3,
            max_tokens=2048
        ):
            yield chunk

    def _build_generation_prompt(
        self,
        query: str,
        context: PrioritizedContext,
        intent: QueryIntent
    ) -> str:
        """
        Build comprehensive prompt for response generation
        """
        # Intent-specific prompt templates
        intent_templates = {
            "EXPLAIN": "Explain how this code works, focusing on the logic and purpose:",
            "FIND": "Help locate the requested code or functionality:",
            "DEBUG": "Help understand and debug this issue:",
            "IMPLEMENT": "Provide guidance on implementing this functionality:",
            "REFACTOR": "Suggest improvements and refactoring opportunities:",
            "ARCHITECTURE": "Explain the high-level architecture and design:",
            "USAGE": "Show how to use this code with examples:"
        }

        template = intent_templates.get(intent.primary_intent, "Answer this question about the code:")

        # Build context sections
        code_context = self._format_code_context(context.code_context)
        conversation_history = self._format_conversation_history(context.conversation_history)

        prompt = f"""You are an expert software engineer helping developers understand their codebase.

{template}

User Query: {query}

Relevant Code Context:
{code_context}

Previous Conversation:
{conversation_history}

Instructions:
1. Provide a clear, accurate answer that directly addresses the question
2. Reference specific code files and line numbers when relevant
3. Use technical terms appropriately but explain complex concepts
4. Include code examples when helpful
5. Suggest next steps or related areas to explore
6. If the context is insufficient, clearly state what additional information would be helpful

Keep the response comprehensive but concise. Focus on practical, actionable information."""

        return prompt

### Phase 2: Handler Chain Implementation

```python
from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Pattern
import re

class QueryHandler(ABC):
    """Base class for specialized query handlers"""

    def __init__(self, name: str, patterns: List[str], priority: int = 0):
        self.name = name
        self.patterns = [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
        self.priority = priority

    def can_handle(self, query: str, intent: QueryIntent) -> float:
        """Return confidence score (0-1) for handling this query"""
        # Check pattern matching
        pattern_score = 0.0
        for pattern in self.patterns:
            if pattern.search(query):
                pattern_score = 1.0
                break

        # Check intent compatibility
        intent_score = self._calculate_intent_compatibility(intent)

        return (pattern_score * 0.6) + (intent_score * 0.4)

    @abstractmethod
    async def handle(
        self,
        query: str,
        context: QueryContext,
        intent: QueryIntent
    ) -> QueryResult:
        """Handle the query and return result"""
        pass

    @abstractmethod
    def _calculate_intent_compatibility(self, intent: QueryIntent) -> float:
        """Calculate how well this handler matches the intent"""
        pass

class DefinitionHandler(QueryHandler):
    """Handles 'What is X?' or 'Show me the definition of Y' queries"""

    def __init__(self):
        patterns = [
            r"what is (\w+)",
            r"show me the definition of (\w+)",
            r"where is (\w+) defined",
            r"define (\w+)",
            r"(\w+) definition"
        ]
        super().__init__("DefinitionHandler", patterns, priority=8)

    def _calculate_intent_compatibility(self, intent: QueryIntent) -> float:
        if intent.primary_intent in ["FIND", "EXPLAIN"]:
            return 0.9
        return 0.3

    async def handle(
        self,
        query: str,
        context: QueryContext,
        intent: QueryIntent
    ) -> QueryResult:
        """Handle definition queries"""
        # Extract the entity being asked about
        entity = self._extract_entity(query)

        if not entity:
            return QueryResult.error("Could not identify what you're asking about")

        # Search for definitions
        definition_results = await self._search_definitions(
            entity,
            context.repository_id
        )

        if not definition_results:
            return QueryResult.not_found(f"No definition found for '{entity}'")

        # Generate response
        response = await self._generate_definition_response(
            entity,
            definition_results,
            query
        )

        return QueryResult(
            answer=response,
            confidence=0.9,
            references=self._extract_references(definition_results),
            intent="DEFINITION",
            sources_used=len(definition_results)
        )

    def _extract_entity(self, query: str) -> Optional[str]:
        """Extract the entity being asked about"""
        for pattern in self.patterns:
            match = pattern.search(query)
            if match and match.groups():
                return match.group(1)

        # Fallback: look for capitalized words (likely class/function names)
        words = query.split()
        for word in words:
            if word[0].isupper() and len(word) > 2:
                return word

        return None

class ImplementationHandler(QueryHandler):
    """Handles 'How does X work?' queries"""

    def __init__(self):
        patterns = [
            r"how does (\w+) work",
            r"explain (\w+)",
            r"how is (\w+) implemented",
            r"walk me through (\w+)",
            r"(\w+) implementation"
        ]
        super().__init__("ImplementationHandler", patterns, priority=7)

    def _calculate_intent_compatibility(self, intent: QueryIntent) -> float:
        if intent.primary_intent == "EXPLAIN":
            return 0.95
        elif intent.primary_intent in ["ARCHITECTURE", "USAGE"]:
            return 0.7
        return 0.2

    async def handle(
        self,
        query: str,
        context: QueryContext,
        intent: QueryIntent
    ) -> QueryResult:
        """Handle implementation explanation queries"""
        entity = self._extract_entity(query)

        # Search for implementation details
        impl_results = await self._search_implementations(
            entity,
            context.repository_id,
            include_dependencies=True
        )

        # Analyze code flow and dependencies
        flow_analysis = await self._analyze_code_flow(impl_results)

        # Generate comprehensive explanation
        response = await self._generate_implementation_explanation(
            entity,
            impl_results,
            flow_analysis,
            query
        )

        return QueryResult(
            answer=response,
            confidence=0.85,
            references=self._extract_references(impl_results),
            intent="IMPLEMENTATION",
            sources_used=len(impl_results)
        )

class UsageHandler(QueryHandler):
    """Handles 'Where is X used?' queries"""

    def __init__(self):
        patterns = [
            r"where is (\w+) used",
            r"who calls (\w+)",
            r"(\w+) usage",
            r"find usages of (\w+)",
            r"references to (\w+)"
        ]
        super().__init__("UsageHandler", patterns, priority=6)

    def _calculate_intent_compatibility(self, intent: QueryIntent) -> float:
        if intent.primary_intent in ["FIND", "USAGE"]:
            return 0.9
        return 0.3

    async def handle(
        self,
        query: str,
        context: QueryContext,
        intent: QueryIntent
    ) -> QueryResult:
        """Handle usage finding queries"""
        entity = self._extract_entity(query)

        # Search for all usages
        usage_results = await self._search_usages(
            entity,
            context.repository_id
        )

        # Group by usage type (calls, imports, inheritance, etc.)
        grouped_usages = self._group_usages_by_type(usage_results)

        # Generate usage summary
        response = await self._generate_usage_summary(
            entity,
            grouped_usages,
            query
        )

        return QueryResult(
            answer=response,
            confidence=0.8,
            references=self._extract_references(usage_results),
            intent="USAGE",
            sources_used=len(usage_results)
        )

class ArchitectureHandler(QueryHandler):
    """Handles high-level architecture questions"""

    def __init__(self):
        patterns = [
            r"architecture",
            r"system design",
            r"how is .* structured",
            r"overall design",
            r"high level",
            r"components",
            r"modules"
        ]
        super().__init__("ArchitectureHandler", patterns, priority=5)

    def _calculate_intent_compatibility(self, intent: QueryIntent) -> float:
        if intent.primary_intent == "ARCHITECTURE":
            return 0.95
        elif intent.scope in ["codebase", "system"]:
            return 0.8
        return 0.2

    async def handle(
        self,
        query: str,
        context: QueryContext,
        intent: QueryIntent
    ) -> QueryResult:
        """Handle architecture queries"""
        # Get high-level components
        components = await self._get_system_components(context.repository_id)

        # Analyze relationships
        relationships = await self._analyze_component_relationships(components)

        # Generate architecture explanation
        response = await self._generate_architecture_explanation(
            components,
            relationships,
            query
        )

        return QueryResult(
            answer=response,
            confidence=0.75,
            references=self._extract_component_references(components),
            intent="ARCHITECTURE",
            sources_used=len(components)
        )

class HandlerChain:
    """Manages the chain of query handlers"""

    def __init__(self):
        self.handlers = [
            DefinitionHandler(),
            ImplementationHandler(),
            UsageHandler(),
            ArchitectureHandler(),
            # Add more specialized handlers
        ]

        # Sort by priority (higher priority first)
        self.handlers.sort(key=lambda h: h.priority, reverse=True)

    async def process_query(
        self,
        query: str,
        context: QueryContext,
        intent: QueryIntent
    ) -> QueryResult:
        """Process query through the handler chain"""

        # Find the best handler
        best_handler = None
        best_score = 0.0

        for handler in self.handlers:
            score = handler.can_handle(query, intent)
            if score > best_score:
                best_score = score
                best_handler = handler

        # Use best handler if confidence is high enough
        if best_handler and best_score > 0.6:
            logger.info(
                "handler_selected",
                handler=best_handler.name,
                confidence=best_score
            )
            return await best_handler.handle(query, context, intent)

        # Fall back to general handler
        logger.info("using_general_handler", best_score=best_score)
        return await self._general_handler(query, context, intent)

    async def _general_handler(
        self,
        query: str,
        context: QueryContext,
        intent: QueryIntent
    ) -> QueryResult:
        """General handler for queries that don't match specific patterns"""
        # Use the main pipeline for general queries
        pipeline = QueryPipeline(self.config)
        return await pipeline.process_query(query, context)

### Phase 3: Advanced RAG System Design (2025 Techniques)

```python
from typing import List, Dict, Optional, Tuple
import numpy as np
from dataclasses import dataclass
from sentence_transformers import SentenceTransformer, CrossEncoder
import faiss
import asyncio

@dataclass
class RAGConfig:
    """Configuration for RAG system with 2025 best practices"""

    # Embedding models
    code_embedding_model: str = "mistralai/Codestral-Embed"
    text_embedding_model: str = "sentence-transformers/all-mpnet-base-v2"

    # Retrieval parameters
    initial_retrieval_limit: int = 100
    final_context_limit: int = 20
    similarity_threshold: float = 0.7

    # Chunking strategy
    chunk_size: int = 512
    chunk_overlap: int = 50
    adaptive_chunking: bool = True

    # Re-ranking
    use_cross_encoder: bool = True
    cross_encoder_model: str = "cross-encoder/ms-marco-MiniLM-L-6-v2"

    # Context enhancement
    use_contextual_retrieval: bool = True
    context_window_size: int = 1000

    # Performance optimization
    use_faiss_index: bool = True
    enable_query_expansion: bool = True
    enable_hypothetical_document_embeddings: bool = True

class AdvancedRAGSystem:
    """Production-ready RAG system with 2025 techniques"""

    def __init__(self, config: RAGConfig):
        self.config = config

        # Initialize embedding models
        self.code_embedder = SentenceTransformer(config.code_embedding_model)
        self.text_embedder = SentenceTransformer(config.text_embedding_model)

        # Initialize re-ranker
        if config.use_cross_encoder:
            self.cross_encoder = CrossEncoder(config.cross_encoder_model)

        # Initialize vector index
        if config.use_faiss_index:
            self.faiss_index = None  # Will be initialized with data
            self.chunk_metadata = {}

        # Query expansion cache
        self.expansion_cache = {}

    async def retrieve_and_rank(
        self,
        query: str,
        repository_id: str,
        conversation_history: Optional[List[str]] = None
    ) -> List[EnhancedCodeChunk]:
        """Advanced retrieval with multiple ranking stages"""

        # Stage 1: Query preprocessing and expansion
        processed_query = await self._preprocess_query(query, conversation_history)
        expanded_queries = await self._expand_query(processed_query)

        # Stage 2: Multi-query retrieval
        all_chunks = []
        for expanded_query in expanded_queries:
            chunks = await self._retrieve_chunks(expanded_query, repository_id)
            all_chunks.extend(chunks)

        # Stage 3: Deduplication and initial filtering
        unique_chunks = self._deduplicate_chunks(all_chunks)
        filtered_chunks = self._filter_by_similarity(unique_chunks, query)

        # Stage 4: Contextual enhancement
        if self.config.use_contextual_retrieval:
            enhanced_chunks = await self._enhance_with_context(filtered_chunks, query)
        else:
            enhanced_chunks = filtered_chunks

        # Stage 5: Cross-encoder re-ranking
        if self.config.use_cross_encoder:
            reranked_chunks = await self._rerank_with_cross_encoder(
                query, enhanced_chunks
            )
        else:
            reranked_chunks = enhanced_chunks

        # Stage 6: Final selection and optimization
        final_chunks = self._select_optimal_chunks(reranked_chunks)

        return final_chunks[:self.config.final_context_limit]

    async def _preprocess_query(
        self,
        query: str,
        conversation_history: Optional[List[str]] = None
    ) -> str:
        """Preprocess query with conversation context"""

        if not conversation_history:
            return query

        # Combine recent conversation for context
        recent_context = " ".join(conversation_history[-3:])  # Last 3 exchanges

        # Use LLM to reformulate query with context
        reformulation_prompt = f"""
        Given this conversation history and current query, reformulate the query to be more specific and contextual.

        Conversation History:
        {recent_context}

        Current Query: {query}

        Reformulated Query (be specific and include relevant context):
        """

        # This would call the LLM for reformulation
        reformulated = await self._call_llm_for_reformulation(reformulation_prompt)

        return reformulated or query

    async def _expand_query(self, query: str) -> List[str]:
        """Expand query using multiple techniques"""

        if query in self.expansion_cache:
            return self.expansion_cache[query]

        expanded_queries = [query]  # Original query

        if self.config.enable_query_expansion:
            # Technique 1: Synonym expansion
            synonyms = await self._get_code_synonyms(query)
            for synonym in synonyms:
                expanded_queries.append(f"{query} {synonym}")

            # Technique 2: Technical term expansion
            tech_terms = await self._extract_technical_terms(query)
            for term in tech_terms:
                expanded_queries.append(f"{query} {term}")

        if self.config.enable_hypothetical_document_embeddings:
            # Technique 3: HyDE (Hypothetical Document Embeddings)
            hypothetical_doc = await self._generate_hypothetical_document(query)
            expanded_queries.append(hypothetical_doc)

        # Cache results
        self.expansion_cache[query] = expanded_queries

        return expanded_queries

    async def _generate_hypothetical_document(self, query: str) -> str:
        """Generate hypothetical document that would answer the query"""

        hyde_prompt = f"""
        Generate a hypothetical code snippet or documentation that would perfectly answer this query:

        Query: {query}

        Generate realistic code or documentation (don't explain, just provide the content):
        """

        # This would call the LLM to generate hypothetical content
        hypothetical = await self._call_llm_for_hyde(hyde_prompt)

        return hypothetical or query

    async def _retrieve_chunks(
        self,
        query: str,
        repository_id: str
    ) -> List[CodeChunk]:
        """Retrieve chunks using hybrid search"""

        # Generate query embedding
        query_embedding = self.text_embedder.encode([query])[0]

        if self.config.use_faiss_index and self.faiss_index:
            # Use FAISS for fast similarity search
            similarities, indices = self.faiss_index.search(
                query_embedding.reshape(1, -1),
                self.config.initial_retrieval_limit
            )

            chunks = []
            for idx, similarity in zip(indices[0], similarities[0]):
                if similarity > self.config.similarity_threshold:
                    chunk_data = self.chunk_metadata[idx]
                    chunk = CodeChunk(
                        content=chunk_data['content'],
                        file_path=chunk_data['file_path'],
                        line_start=chunk_data['line_start'],
                        line_end=chunk_data['line_end'],
                        similarity_score=float(similarity)
                    )
                    chunks.append(chunk)

            return chunks
        else:
            # Fallback to database search
            return await self._database_search(query, repository_id)

    def _deduplicate_chunks(self, chunks: List[CodeChunk]) -> List[CodeChunk]:
        """Remove duplicate chunks while preserving best scores"""

        seen_content = {}
        unique_chunks = []

        for chunk in sorted(chunks, key=lambda x: x.similarity_score, reverse=True):
            content_hash = hashlib.sha256(chunk.content.encode()).hexdigest()

            if content_hash not in seen_content:
                seen_content[content_hash] = True
                unique_chunks.append(chunk)

        return unique_chunks

    async def _enhance_with_context(
        self,
        chunks: List[CodeChunk],
        query: str
    ) -> List[EnhancedCodeChunk]:
        """Enhance chunks with contextual information (Anthropic 2024 technique)"""

        enhanced_chunks = []

        for chunk in chunks:
            # Get surrounding code context
            surrounding_context = await self._get_surrounding_context(
                chunk.file_path,
                chunk.line_start,
                chunk.line_end
            )

            # Get file-level context
            file_context = await self._get_file_context(chunk.file_path)

            # Generate contextual summary
            contextual_summary = await self._generate_contextual_summary(
                chunk, surrounding_context, file_context, query
            )

            # Create enhanced chunk
            enhanced_chunk = EnhancedCodeChunk(
                content=chunk.content,
                file_path=chunk.file_path,
                line_start=chunk.line_start,
                line_end=chunk.line_end,
                similarity_score=chunk.similarity_score,
                surrounding_context=surrounding_context,
                file_context=file_context,
                contextual_summary=contextual_summary,
                relevance_explanation=await self._explain_relevance(chunk, query)
            )

            enhanced_chunks.append(enhanced_chunk)

        return enhanced_chunks

    async def _rerank_with_cross_encoder(
        self,
        query: str,
        chunks: List[EnhancedCodeChunk]
    ) -> List[EnhancedCodeChunk]:
        """Re-rank chunks using cross-encoder for better relevance"""

        if not self.cross_encoder:
            return chunks

        # Prepare query-chunk pairs for cross-encoder
        pairs = []
        for chunk in chunks:
            # Combine chunk content with contextual summary for better ranking
            chunk_text = f"{chunk.contextual_summary}\n\n{chunk.content}"
            pairs.append([query, chunk_text])

        # Get cross-encoder scores
        scores = self.cross_encoder.predict(pairs)

        # Update chunks with new scores
        for chunk, score in zip(chunks, scores):
            chunk.cross_encoder_score = float(score)
            # Combine similarity and cross-encoder scores
            chunk.final_score = (chunk.similarity_score * 0.4) + (score * 0.6)

        # Sort by final score
        return sorted(chunks, key=lambda x: x.final_score, reverse=True)

    def _select_optimal_chunks(
        self,
        chunks: List[EnhancedCodeChunk]
    ) -> List[EnhancedCodeChunk]:
        """Select optimal chunks considering diversity and relevance"""

        if len(chunks) <= self.config.final_context_limit:
            return chunks

        selected_chunks = []
        selected_files = set()

        # First pass: Select highest scoring chunks from different files
        for chunk in chunks:
            if len(selected_chunks) >= self.config.final_context_limit:
                break

            if chunk.file_path not in selected_files:
                selected_chunks.append(chunk)
                selected_files.add(chunk.file_path)

        # Second pass: Fill remaining slots with best remaining chunks
        remaining_slots = self.config.final_context_limit - len(selected_chunks)
        if remaining_slots > 0:
            remaining_chunks = [c for c in chunks if c not in selected_chunks]
            selected_chunks.extend(remaining_chunks[:remaining_slots])

        return selected_chunks

### Phase 4: Advanced Caching Layer (2025 Patterns)

```python
import asyncio
from typing import Dict, Any, Optional, List
import redis.asyncio as redis
import json
import pickle
import hashlib
from datetime import datetime, timedelta
import aiocache
from aiocache.serializers import PickleSerializer

class MultiLevelCache:
    """Advanced multi-level caching system for 2025"""

    def __init__(self, redis_url: str, config: CacheConfig):
        self.config = config

        # Level 1: In-memory cache (fastest)
        self.l1_cache = aiocache.Cache(
            aiocache.SimpleMemoryCache,
            serializer=PickleSerializer(),
            ttl=config.l1_ttl
        )

        # Level 2: Redis cache (fast, shared)
        self.l2_cache = redis.from_url(redis_url, decode_responses=False)

        # Level 3: Persistent cache (database)
        self.l3_cache = None  # Database connection

        # Cache analytics
        self.analytics = CacheAnalytics()

        # Background tasks
        self._cleanup_task = None
        self._prewarming_task = None

    async def get(self, key: str, cache_levels: List[int] = [1, 2, 3]) -> Optional[Any]:
        """Get value from cache with level fallback"""

        start_time = datetime.now()

        # Level 1: Memory cache
        if 1 in cache_levels:
            try:
                value = await self.l1_cache.get(key)
                if value is not None:
                    await self.analytics.record_hit("L1", key, start_time)
                    return value
            except Exception as e:
                logger.warning("l1_cache_error", error=str(e))

        # Level 2: Redis cache
        if 2 in cache_levels:
            try:
                value = await self.l2_cache.get(key)
                if value is not None:
                    result = pickle.loads(value)

                    # Promote to L1 cache
                    if 1 in cache_levels:
                        await self.l1_cache.set(key, result)

                    await self.analytics.record_hit("L2", key, start_time)
                    return result
            except Exception as e:
                logger.warning("l2_cache_error", error=str(e))

        # Level 3: Database cache
        if 3 in cache_levels and self.l3_cache:
            try:
                value = await self._get_from_database(key)
                if value is not None:
                    # Promote to higher levels
                    if 2 in cache_levels:
                        await self.l2_cache.setex(
                            key,
                            self.config.l2_ttl,
                            pickle.dumps(value)
                        )
                    if 1 in cache_levels:
                        await self.l1_cache.set(key, value)

                    await self.analytics.record_hit("L3", key, start_time)
                    return value
            except Exception as e:
                logger.warning("l3_cache_error", error=str(e))

        # Cache miss
        await self.analytics.record_miss(key, start_time)
        return None

    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        cache_levels: List[int] = [1, 2, 3]
    ) -> None:
        """Set value in specified cache levels"""

        # Calculate adaptive TTL
        adaptive_ttl = self._calculate_adaptive_ttl(key, value, ttl)

        # Set in Level 1 (memory)
        if 1 in cache_levels:
            try:
                await self.l1_cache.set(key, value)
            except Exception as e:
                logger.warning("l1_cache_set_error", error=str(e))

        # Set in Level 2 (Redis)
        if 2 in cache_levels:
            try:
                await self.l2_cache.setex(
                    key,
                    adaptive_ttl,
                    pickle.dumps(value)
                )
            except Exception as e:
                logger.warning("l2_cache_set_error", error=str(e))

        # Set in Level 3 (Database)
        if 3 in cache_levels and self.l3_cache:
            try:
                await self._set_in_database(key, value, adaptive_ttl)
            except Exception as e:
                logger.warning("l3_cache_set_error", error=str(e))

    def _calculate_adaptive_ttl(self, key: str, value: Any, base_ttl: Optional[int]) -> int:
        """Calculate TTL based on key patterns and value characteristics"""

        if base_ttl:
            return base_ttl

        # Default TTL
        ttl = self.config.default_ttl

        # Adjust based on key patterns
        if "query:" in key:
            if hasattr(value, 'confidence') and value.confidence > 0.9:
                ttl *= 2  # High confidence queries last longer
            elif hasattr(value, 'intent'):
                if value.intent in ["ARCHITECTURE", "EXPLAIN"]:
                    ttl *= 1.5  # Architectural info changes less frequently
                elif value.intent in ["DEBUG", "IMPLEMENT"]:
                    ttl //= 2  # Implementation details change more frequently

        elif "embedding:" in key:
            ttl *= 7  # Embeddings are expensive to compute

        elif "analysis:" in key:
            ttl *= 24  # Analysis results are very expensive

        return ttl

    async def invalidate_pattern(self, pattern: str) -> int:
        """Invalidate all keys matching pattern"""

        invalidated_count = 0

        # Invalidate from Redis
        try:
            cursor = 0
            while True:
                cursor, keys = await self.l2_cache.scan(
                    cursor=cursor,
                    match=pattern,
                    count=100
                )

                if keys:
                    await self.l2_cache.delete(*keys)
                    invalidated_count += len(keys)

                if cursor == 0:
                    break
        except Exception as e:
            logger.warning("redis_invalidation_error", error=str(e))

        # Note: L1 cache invalidation would require tracking keys
        # L3 cache invalidation would be database-specific

        return invalidated_count

    async def start_background_tasks(self):
        """Start background maintenance tasks"""

        self._cleanup_task = asyncio.create_task(self._cleanup_expired_keys())
        self._prewarming_task = asyncio.create_task(self._prewarm_cache())

    async def _cleanup_expired_keys(self):
        """Background task to clean up expired keys"""

        while True:
            try:
                # Clean up every hour
                await asyncio.sleep(3600)

                # Cleanup logic here
                await self._perform_cleanup()

            except Exception as e:
                logger.error("cleanup_task_error", error=str(e))

    async def _prewarm_cache(self):
        """Background task to pre-warm cache with popular queries"""

        while True:
            try:
                # Pre-warm every 6 hours
                await asyncio.sleep(21600)

                # Get popular queries from analytics
                popular_queries = await self.analytics.get_popular_queries(limit=100)

                # Pre-compute responses for popular queries
                for query_info in popular_queries:
                    if not await self.get(query_info.cache_key):
                        # Re-compute and cache
                        await self._recompute_and_cache(query_info)

            except Exception as e:
                logger.error("prewarming_task_error", error=str(e))

class CacheAnalytics:
    """Analytics for cache performance optimization"""

    def __init__(self):
        self.metrics = {
            "hits": {"L1": 0, "L2": 0, "L3": 0},
            "misses": 0,
            "response_times": [],
            "popular_keys": {},
            "error_count": 0
        }

    async def record_hit(self, level: str, key: str, start_time: datetime):
        """Record cache hit with timing"""

        response_time = (datetime.now() - start_time).total_seconds() * 1000

        self.metrics["hits"][level] += 1
        self.metrics["response_times"].append(response_time)

        # Track popular keys
        key_pattern = self._extract_key_pattern(key)
        self.metrics["popular_keys"][key_pattern] = \
            self.metrics["popular_keys"].get(key_pattern, 0) + 1

    async def record_miss(self, key: str, start_time: datetime):
        """Record cache miss"""

        self.metrics["misses"] += 1

    def get_hit_rate(self) -> float:
        """Calculate overall hit rate"""

        total_hits = sum(self.metrics["hits"].values())
        total_requests = total_hits + self.metrics["misses"]

        return (total_hits / total_requests * 100) if total_requests > 0 else 0

    def get_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""

        response_times = self.metrics["response_times"]

        return {
            "hit_rate_percent": self.get_hit_rate(),
            "total_requests": sum(self.metrics["hits"].values()) + self.metrics["misses"],
            "level_distribution": self.metrics["hits"],
            "avg_response_time_ms": np.mean(response_times) if response_times else 0,
            "p95_response_time_ms": np.percentile(response_times, 95) if response_times else 0,
            "popular_patterns": sorted(
                self.metrics["popular_keys"].items(),
                key=lambda x: x[1],
                reverse=True
            )[:10],
            "error_count": self.metrics["error_count"]
        }
```

## Data Models

### Input Models
```python
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class QueryIntent(str, Enum):
    EXPLAIN = "EXPLAIN"
    FIND = "FIND"
    DEBUG = "DEBUG"
    IMPLEMENT = "IMPLEMENT"
    REFACTOR = "REFACTOR"
    ARCHITECTURE = "ARCHITECTURE"
    USAGE = "USAGE"

class QueryFilters(BaseModel):
    """Filters for query processing"""
    file_patterns: Optional[List[str]] = Field(None, description="File patterns to include/exclude")
    languages: Optional[List[str]] = Field(None, description="Programming languages to focus on")
    date_range: Optional[Dict[str, datetime]] = Field(None, description="Date range for code changes")
    exclude_tests: bool = Field(False, description="Exclude test files from results")
    exclude_generated: bool = Field(True, description="Exclude generated code files")
    min_confidence: float = Field(0.0, description="Minimum confidence threshold")
    max_results: int = Field(20, description="Maximum number of results")

class ConversationMessage(BaseModel):
    """Single message in conversation history"""
    role: str = Field(..., description="Role: 'user' or 'assistant'")
    content: str = Field(..., description="Message content")
    timestamp: datetime = Field(default_factory=datetime.now)
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class QueryRequest(BaseModel):
    """Request model for natural language queries"""
    query: str = Field(..., min_length=1, max_length=10000, description="Natural language query")
    repository_id: str = Field(..., description="Repository identifier")
    session_id: Optional[str] = Field(None, description="Session identifier for conversation tracking")
    conversation_history: Optional[List[ConversationMessage]] = Field(None, description="Previous conversation")
    filters: Optional[QueryFilters] = Field(None, description="Query filters")
    stream: bool = Field(False, description="Enable streaming response")
    include_references: bool = Field(True, description="Include code references in response")
    include_follow_ups: bool = Field(True, description="Generate follow-up questions")

    @validator('query')
    def validate_query(cls, v):
        if not v.strip():
            raise ValueError('Query cannot be empty')
        return v.strip()

class QueryContext(BaseModel):
    """Context for query processing"""
    repository_id: str
    user_id: str
    session_id: Optional[str] = None
    conversation_history: List[ConversationMessage] = Field(default_factory=list)
    filters: QueryFilters = Field(default_factory=QueryFilters)
    metadata: Dict[str, Any] = Field(default_factory=dict)

### Output Models
```python
class CodeReference(BaseModel):
    """Reference to specific code location"""
    file_path: str = Field(..., description="Path to the file")
    start_line: int = Field(..., ge=1, description="Starting line number")
    end_line: int = Field(..., ge=1, description="Ending line number")
    snippet: str = Field(..., description="Code snippet")
    relevance_score: float = Field(..., ge=0.0, le=1.0, description="Relevance score")
    language: Optional[str] = Field(None, description="Programming language")
    context_summary: Optional[str] = Field(None, description="Summary of surrounding context")

    @validator('end_line')
    def validate_line_numbers(cls, v, values):
        if 'start_line' in values and v < values['start_line']:
            raise ValueError('end_line must be >= start_line')
        return v

class ConfidenceBreakdown(BaseModel):
    """Detailed confidence score breakdown"""
    overall: float = Field(..., ge=0.0, le=1.0, description="Overall confidence")
    query_understanding: float = Field(..., ge=0.0, le=1.0, description="Query understanding confidence")
    context_relevance: float = Field(..., ge=0.0, le=1.0, description="Context relevance confidence")
    response_coherence: float = Field(..., ge=0.0, le=1.0, description="Response coherence confidence")
    factual_accuracy: float = Field(..., ge=0.0, le=1.0, description="Factual accuracy confidence")

class QueryMetrics(BaseModel):
    """Performance and quality metrics"""
    execution_time_ms: float = Field(..., ge=0, description="Total execution time")
    retrieval_time_ms: float = Field(..., ge=0, description="Context retrieval time")
    generation_time_ms: float = Field(..., ge=0, description="Response generation time")
    tokens_used: int = Field(..., ge=0, description="Total tokens consumed")
    sources_retrieved: int = Field(..., ge=0, description="Number of code sources retrieved")
    cache_hit: bool = Field(False, description="Whether response was cached")

class QueryResult(BaseModel):
    """Complete query result"""
    answer: str = Field(..., description="Generated answer")
    confidence: ConfidenceBreakdown = Field(..., description="Confidence breakdown")
    references: List[CodeReference] = Field(default_factory=list, description="Code references")
    follow_up_questions: List[str] = Field(default_factory=list, description="Suggested follow-up questions")
    intent: QueryIntent = Field(..., description="Detected query intent")
    metrics: QueryMetrics = Field(..., description="Performance metrics")
    model_used: str = Field(..., description="AI model used for generation")
    timestamp: datetime = Field(default_factory=datetime.now)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class StreamingQueryResponse(BaseModel):
    """Streaming response chunk"""
    chunk_id: int = Field(..., description="Chunk sequence number")
    content: str = Field(..., description="Content chunk")
    is_final: bool = Field(False, description="Whether this is the final chunk")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Chunk metadata")

class QueryError(BaseModel):
    """Error response model"""
    error_code: str = Field(..., description="Error code")
    message: str = Field(..., description="Human-readable error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.now)
    request_id: Optional[str] = Field(None, description="Request identifier for debugging")
```

## Query Handler Types

### 1. Definition Queries
```python
class DefinitionHandler(QueryHandler):
    """Handles 'What is X?' or 'Show me the definition of Y' queries"""

    patterns = [
        r"what is (\w+)",
        r"show me the definition of (\w+)",
        r"where is (\w+) defined",
        r"define (\w+)",
        r"(\w+) definition"
    ]

    def __init__(self):
        super().__init__("DefinitionHandler", self.patterns, priority=8)

    async def handle(self, query: str, context: QueryContext, intent: QueryIntent) -> QueryResult:
        """Handle definition queries with enhanced search"""

        # Extract entity from query
        entity = self._extract_entity(query)
        if not entity:
            return self._create_error_result("Could not identify what you're asking about")

        # Multi-strategy search for definitions
        search_strategies = [
            self._search_class_definitions,
            self._search_function_definitions,
            self._search_variable_definitions,
            self._search_type_definitions,
            self._search_interface_definitions
        ]

        all_results = []
        for strategy in search_strategies:
            results = await strategy(entity, context.repository_id)
            all_results.extend(results)

        if not all_results:
            return self._create_not_found_result(f"No definition found for '{entity}'")

        # Rank and select best definitions
        ranked_results = self._rank_definition_results(all_results, entity, query)

        # Generate comprehensive definition response
        response = await self._generate_definition_response(
            entity, ranked_results[:5], query, context
        )

        return QueryResult(
            answer=response.text,
            confidence=ConfidenceBreakdown(
                overall=response.confidence,
                query_understanding=0.95,  # High for definition queries
                context_relevance=response.context_relevance,
                response_coherence=response.coherence,
                factual_accuracy=response.factual_accuracy
            ),
            references=self._create_references(ranked_results[:3]),
            follow_up_questions=self._generate_definition_follow_ups(entity, ranked_results),
            intent=QueryIntent.FIND,
            metrics=response.metrics,
            model_used=response.model_used
        )

    async def _search_class_definitions(self, entity: str, repo_id: str) -> List[CodeMatch]:
        """Search for class definitions"""
        patterns = [
            f"class {entity}",
            f"class {entity}(",
            f"class {entity}<",
            f"interface {entity}",
            f"struct {entity}",
            f"type {entity}"
        ]

        results = []
        for pattern in patterns:
            matches = await self.code_search.search_pattern(pattern, repo_id)
            results.extend(matches)

        return results

    async def _search_function_definitions(self, entity: str, repo_id: str) -> List[CodeMatch]:
        """Search for function definitions"""
        patterns = [
            f"def {entity}(",
            f"function {entity}(",
            f"func {entity}(",
            f"{entity} = function",
            f"const {entity} = ",
            f"let {entity} = ",
            f"var {entity} = "
        ]

        results = []
        for pattern in patterns:
            matches = await self.code_search.search_pattern(pattern, repo_id)
            results.extend(matches)

        return results

### 2. Implementation Queries
class ImplementationHandler(QueryHandler):
    """Handles 'How does X work?' queries"""

    patterns = [
        r"how does (\w+) work",
        r"explain (\w+)",
        r"how is (\w+) implemented",
        r"walk me through (\w+)",
        r"(\w+) implementation",
        r"show me how (\w+) works"
    ]

    async def handle(self, query: str, context: QueryContext, intent: QueryIntent) -> QueryResult:
        """Handle implementation explanation queries"""

        entity = self._extract_entity(query)

        # Get implementation details with dependencies
        impl_analysis = await self._analyze_implementation(entity, context.repository_id)

        if not impl_analysis.found:
            return self._create_not_found_result(f"Could not find implementation for '{entity}'")

        # Generate step-by-step explanation
        explanation = await self._generate_implementation_explanation(
            entity, impl_analysis, query, context
        )

        return QueryResult(
            answer=explanation.text,
            confidence=ConfidenceBreakdown(
                overall=explanation.confidence,
                query_understanding=0.9,
                context_relevance=impl_analysis.relevance_score,
                response_coherence=explanation.coherence,
                factual_accuracy=explanation.accuracy
            ),
            references=self._create_references(impl_analysis.code_chunks),
            follow_up_questions=self._generate_implementation_follow_ups(entity, impl_analysis),
            intent=QueryIntent.EXPLAIN,
            metrics=explanation.metrics,
            model_used=explanation.model_used
        )

    async def _analyze_implementation(self, entity: str, repo_id: str) -> ImplementationAnalysis:
        """Comprehensive implementation analysis"""

        # Find main implementation
        main_impl = await self._find_main_implementation(entity, repo_id)

        if not main_impl:
            return ImplementationAnalysis(found=False)

        # Analyze dependencies
        dependencies = await self._analyze_dependencies(main_impl, repo_id)

        # Analyze data flow
        data_flow = await self._analyze_data_flow(main_impl, dependencies)

        # Analyze control flow
        control_flow = await self._analyze_control_flow(main_impl)

        # Get usage examples
        usage_examples = await self._find_usage_examples(entity, repo_id)

        return ImplementationAnalysis(
            found=True,
            main_implementation=main_impl,
            dependencies=dependencies,
            data_flow=data_flow,
            control_flow=control_flow,
            usage_examples=usage_examples,
            complexity_score=self._calculate_complexity(main_impl),
            relevance_score=self._calculate_relevance(main_impl, entity)
        )

### 3. Usage Queries
class UsageHandler(QueryHandler):
    """Handles 'Where is X used?' queries"""

    patterns = [
        r"where is (\w+) used",
        r"who calls (\w+)",
        r"(\w+) usage",
        r"find usages of (\w+)",
        r"references to (\w+)",
        r"where do you use (\w+)"
    ]

    async def handle(self, query: str, context: QueryContext, intent: QueryIntent) -> QueryResult:
        """Handle usage finding queries with comprehensive analysis"""

        entity = self._extract_entity(query)

        # Comprehensive usage analysis
        usage_analysis = await self._analyze_all_usages(entity, context.repository_id)

        if not usage_analysis.usages:
            return self._create_not_found_result(f"No usages found for '{entity}'")

        # Generate usage summary with patterns
        summary = await self._generate_usage_summary(
            entity, usage_analysis, query, context
        )

        return QueryResult(
            answer=summary.text,
            confidence=ConfidenceBreakdown(
                overall=summary.confidence,
                query_understanding=0.95,
                context_relevance=usage_analysis.relevance_score,
                response_coherence=summary.coherence,
                factual_accuracy=summary.accuracy
            ),
            references=self._create_references(usage_analysis.top_usages),
            follow_up_questions=self._generate_usage_follow_ups(entity, usage_analysis),
            intent=QueryIntent.USAGE,
            metrics=summary.metrics,
            model_used=summary.model_used
        )

    async def _analyze_all_usages(self, entity: str, repo_id: str) -> UsageAnalysis:
        """Comprehensive usage analysis"""

        # Different types of usage patterns
        usage_types = {
            "function_calls": await self._find_function_calls(entity, repo_id),
            "imports": await self._find_imports(entity, repo_id),
            "inheritance": await self._find_inheritance_usage(entity, repo_id),
            "composition": await self._find_composition_usage(entity, repo_id),
            "variable_references": await self._find_variable_references(entity, repo_id),
            "type_annotations": await self._find_type_annotations(entity, repo_id)
        }

        # Analyze usage patterns
        patterns = self._analyze_usage_patterns(usage_types)

        # Calculate usage statistics
        stats = self._calculate_usage_statistics(usage_types)

        return UsageAnalysis(
            usages=usage_types,
            patterns=patterns,
            statistics=stats,
            top_usages=self._get_top_usages(usage_types, limit=10),
            relevance_score=self._calculate_usage_relevance(usage_types, entity)
        )

### 4. Architecture Queries
class ArchitectureHandler(QueryHandler):
    """Handles high-level architecture questions"""

    patterns = [
        r"architecture",
        r"system design",
        r"how is .* structured",
        r"overall design",
        r"high level",
        r"components",
        r"modules",
        r"system overview"
    ]

    async def handle(self, query: str, context: QueryContext, intent: QueryIntent) -> QueryResult:
        """Handle architecture queries with comprehensive analysis"""

        # Analyze system architecture
        arch_analysis = await self._analyze_system_architecture(context.repository_id)

        # Generate architecture explanation
        explanation = await self._generate_architecture_explanation(
            arch_analysis, query, context
        )

        return QueryResult(
            answer=explanation.text,
            confidence=ConfidenceBreakdown(
                overall=explanation.confidence,
                query_understanding=0.85,
                context_relevance=arch_analysis.relevance_score,
                response_coherence=explanation.coherence,
                factual_accuracy=explanation.accuracy
            ),
            references=self._create_architecture_references(arch_analysis),
            follow_up_questions=self._generate_architecture_follow_ups(arch_analysis),
            intent=QueryIntent.ARCHITECTURE,
            metrics=explanation.metrics,
            model_used=explanation.model_used
        )
```

## API Endpoints

### POST /api/v1/query
Submit a natural language query about code

```python
from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends
from fastapi.responses import StreamingResponse
import asyncio
import structlog

app = FastAPI(title="Query Intelligence API", version="1.0.0")
logger = structlog.get_logger()

@app.post("/api/v1/query", response_model=QueryResult)
async def process_query(
    request: QueryRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
) -> QueryResult:
    """
    Process a natural language query about a codebase

    Args:
        request: Query request with natural language query and context
        background_tasks: Background task queue for analytics
        current_user: Authenticated user information

    Returns:
        QueryResult with answer, confidence, and references

    Raises:
        HTTPException: For validation errors, rate limits, or processing failures
    """

    # Validate request
    if not request.query.strip():
        raise HTTPException(status_code=400, detail="Query cannot be empty")

    # Check rate limits
    await rate_limiter.check_rate_limit(current_user.id)

    # Validate repository access
    if not await has_repository_access(current_user.id, request.repository_id):
        raise HTTPException(status_code=403, detail="Access denied to repository")

    try:
        # Create query context
        context = QueryContext(
            repository_id=request.repository_id,
            user_id=current_user.id,
            session_id=request.session_id,
            conversation_history=request.conversation_history or [],
            filters=request.filters or QueryFilters()
        )

        # Process query
        query_processor = QueryProcessor()
        result = await query_processor.process_query(request.query, context)

        # Log analytics in background
        background_tasks.add_task(
            log_query_analytics,
            user_id=current_user.id,
            query=request.query,
            result=result
        )

        return result

    except RateLimitExceeded as e:
        raise HTTPException(status_code=429, detail="Rate limit exceeded")
    except RepositoryNotFound as e:
        raise HTTPException(status_code=404, detail="Repository not found")
    except QueryProcessingError as e:
        logger.error("query_processing_failed", error=str(e), user_id=current_user.id)
        raise HTTPException(status_code=500, detail="Query processing failed")

### GET /api/v1/query/{query_id}
Retrieve query results by ID

@app.get("/api/v1/query/{query_id}", response_model=QueryResult)
async def get_query_result(
    query_id: str,
    current_user: User = Depends(get_current_user)
) -> QueryResult:
    """
    Retrieve previously processed query results

    Args:
        query_id: Unique query identifier
        current_user: Authenticated user information

    Returns:
        QueryResult if found and accessible

    Raises:
        HTTPException: If query not found or access denied
    """

    # Retrieve from cache or database
    result = await query_cache.get_query_result(query_id)

    if not result:
        raise HTTPException(status_code=404, detail="Query result not found")

    # Verify user has access to this query result
    if result.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    return result

### POST /api/v1/query/stream
Stream query responses for real-time interaction

@app.post("/api/v1/query/stream")
async def stream_query_response(
    request: QueryRequest,
    current_user: User = Depends(get_current_user)
) -> StreamingResponse:
    """
    Process query with streaming response for real-time user experience

    Args:
        request: Query request with streaming enabled
        current_user: Authenticated user information

    Returns:
        StreamingResponse with real-time query processing updates
    """

    # Validate streaming request
    if not request.stream:
        raise HTTPException(status_code=400, detail="Streaming not enabled in request")

    # Check rate limits
    await rate_limiter.check_rate_limit(current_user.id)

    async def generate_stream():
        """Generate streaming response chunks"""

        try:
            context = QueryContext(
                repository_id=request.repository_id,
                user_id=current_user.id,
                session_id=request.session_id,
                conversation_history=request.conversation_history or [],
                filters=request.filters or QueryFilters()
            )

            # Initialize streaming processor
            streaming_processor = StreamingQueryProcessor()

            chunk_id = 0
            async for chunk in streaming_processor.process_query_stream(request.query, context):
                response_chunk = StreamingQueryResponse(
                    chunk_id=chunk_id,
                    content=chunk.content,
                    is_final=chunk.is_final,
                    metadata=chunk.metadata
                )

                yield f"data: {response_chunk.json()}\n\n"
                chunk_id += 1

                # Small delay to prevent overwhelming client
                await asyncio.sleep(0.01)

            # Send final chunk
            final_chunk = StreamingQueryResponse(
                chunk_id=chunk_id,
                content="",
                is_final=True,
                metadata={"status": "completed"}
            )
            yield f"data: {final_chunk.json()}\n\n"

        except Exception as e:
            error_chunk = StreamingQueryResponse(
                chunk_id=chunk_id,
                content="",
                is_final=True,
                metadata={"status": "error", "error": str(e)}
            )
            yield f"data: {error_chunk.json()}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )

### WebSocket /api/v1/query/ws
Real-time bidirectional query processing

@app.websocket("/api/v1/query/ws")
async def websocket_query_endpoint(
    websocket: WebSocket,
    token: str = Query(...),
    repository_id: str = Query(...)
):
    """
    WebSocket endpoint for real-time query processing

    Supports:
    - Real-time query streaming
    - Conversation context maintenance
    - Multi-turn interactions
    - Live code updates
    """

    # Authenticate WebSocket connection
    try:
        user = await authenticate_websocket_token(token)
        await websocket.accept()

        logger.info(
            "websocket_connected",
            user_id=user.id,
            repository_id=repository_id
        )

    except AuthenticationError:
        await websocket.close(code=1008, reason="Authentication failed")
        return

    # Initialize session
    session = WebSocketSession(
        websocket=websocket,
        user_id=user.id,
        repository_id=repository_id
    )

    try:
        while True:
            # Receive message from client
            data = await websocket.receive_json()

            if data.get("type") == "query":
                await handle_websocket_query(session, data)
            elif data.get("type") == "feedback":
                await handle_websocket_feedback(session, data)
            elif data.get("type") == "ping":
                await websocket.send_json({"type": "pong"})
            else:
                await websocket.send_json({
                    "type": "error",
                    "message": "Unknown message type"
                })

    except WebSocketDisconnect:
        logger.info("websocket_disconnected", user_id=user.id)
    except Exception as e:
        logger.error("websocket_error", error=str(e), user_id=user.id)
        await websocket.close(code=1011, reason="Internal error")

async def handle_websocket_query(session: WebSocketSession, data: dict):
    """Handle query message over WebSocket"""

    try:
        query = data.get("query", "").strip()
        if not query:
            await session.send_error("Query cannot be empty")
            return

        # Send acknowledgment
        await session.websocket.send_json({
            "type": "query_received",
            "query_id": data.get("query_id")
        })

        # Process query with streaming
        context = QueryContext(
            repository_id=session.repository_id,
            user_id=session.user_id,
            session_id=session.session_id,
            conversation_history=session.conversation_history
        )

        streaming_processor = StreamingQueryProcessor()

        async for chunk in streaming_processor.process_query_stream(query, context):
            await session.websocket.send_json({
                "type": "query_chunk",
                "query_id": data.get("query_id"),
                "content": chunk.content,
                "is_final": chunk.is_final,
                "metadata": chunk.metadata
            })

        # Update conversation history
        session.add_to_conversation("user", query)

    except Exception as e:
        await session.send_error(f"Query processing failed: {str(e)}")

### GET /api/v1/health
Health check endpoint

@app.get("/api/v1/health")
async def health_check():
    """
    Health check endpoint for monitoring and load balancing

    Returns:
        Health status with system metrics
    """

    health_status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "checks": {}
    }

    # Check database connectivity
    try:
        await database.execute("SELECT 1")
        health_status["checks"]["database"] = "healthy"
    except Exception as e:
        health_status["checks"]["database"] = f"unhealthy: {str(e)}"
        health_status["status"] = "degraded"

    # Check Redis connectivity
    try:
        await redis_client.ping()
        health_status["checks"]["redis"] = "healthy"
    except Exception as e:
        health_status["checks"]["redis"] = f"unhealthy: {str(e)}"
        health_status["status"] = "degraded"

    # Check Vertex AI connectivity
    try:
        await vertex_ai_client.health_check()
        health_status["checks"]["vertex_ai"] = "healthy"
    except Exception as e:
        health_status["checks"]["vertex_ai"] = f"unhealthy: {str(e)}"
        health_status["status"] = "degraded"

    # Add performance metrics
    health_status["metrics"] = {
        "active_connections": get_active_connection_count(),
        "cache_hit_rate": await get_cache_hit_rate(),
        "avg_response_time_ms": await get_avg_response_time(),
        "queries_per_minute": await get_queries_per_minute()
    }

    return health_status
```

## Vertex AI Integration Details (2025 Update)

### Client Configuration with Latest Best Practices
```python
import vertexai
from vertexai.generative_models import GenerativeModel, GenerationConfig
from google.cloud import aiplatform
import asyncio
from typing import AsyncGenerator, Optional
import structlog

logger = structlog.get_logger()

class VertexAIClient:
    """Production-ready Vertex AI client with 2025 optimizations"""

    def __init__(self, config: VertexAIConfig):
        self.config = config

        # Initialize Vertex AI with optimal region (July 2025)
        vertexai.init(
            project=config.project_id,
            location="us-central1",  # Highest quotas as of 2025
            credentials=config.credentials
        )

        # Initialize models with different configurations
        self.models = {
            "flash": GenerativeModel(
                "gemini-2.5-flash",  # Most cost-effective for 2025
                generation_config=GenerationConfig(
                    temperature=0.1,
                    top_p=0.95,
                    max_output_tokens=8192,
                    candidate_count=1
                )
            ),
            "pro": GenerativeModel(
                "gemini-2.5-pro",  # For complex analysis
                generation_config=GenerationConfig(
                    temperature=0.2,
                    top_p=0.95,
                    max_output_tokens=8192,
                    candidate_count=1
                )
            )
        }

        # Rate limiting and circuit breaker
        self.rate_limiter = VertexAIRateLimiter(config.rate_limits)
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=5,
            recovery_timeout=30,
            expected_exception=Exception
        )

        # Performance monitoring
        self.metrics = VertexAIMetrics()

    async def generate_response(
        self,
        prompt: str,
        model_type: str = "flash",
        stream: bool = False,
        **kwargs
    ) -> Union[str, AsyncGenerator[str, None]]:
        """Generate response with comprehensive error handling"""

        start_time = datetime.now()

        try:
            # Check rate limits
            await self.rate_limiter.acquire()

            # Select model
            model = self.models.get(model_type, self.models["flash"])

            # Apply circuit breaker
            if stream:
                return await self._generate_streaming_response(model, prompt, **kwargs)
            else:
                return await self._generate_single_response(model, prompt, **kwargs)

        except ResourceExhausted as e:
            # Handle rate limiting with exponential backoff
            await self._handle_rate_limit(e)
            raise QueryProcessingError("Rate limit exceeded, please try again later")

        except Exception as e:
            # Log error and update metrics
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            await self.metrics.record_error(str(e), execution_time)

            logger.error(
                "vertex_ai_error",
                error=str(e),
                model_type=model_type,
                execution_time_ms=execution_time
            )

            raise QueryProcessingError(f"AI processing failed: {str(e)}")

        finally:
            # Record metrics
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            await self.metrics.record_request(model_type, execution_time)

    async def _generate_single_response(
        self,
        model: GenerativeModel,
        prompt: str,
        **kwargs
    ) -> str:
        """Generate single response with retries"""

        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = await asyncio.to_thread(
                    model.generate_content,
                    prompt,
                    **kwargs
                )

                if response.text:
                    return response.text
                else:
                    raise ValueError("Empty response from model")

            except Exception as e:
                if attempt == max_retries - 1:
                    raise

                # Exponential backoff
                await asyncio.sleep(2 ** attempt)
                logger.warning(
                    "vertex_ai_retry",
                    attempt=attempt + 1,
                    error=str(e)
                )

    async def _generate_streaming_response(
        self,
        model: GenerativeModel,
        prompt: str,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate streaming response with error handling"""

        try:
            # Use asyncio.to_thread for streaming to avoid blocking
            stream = await asyncio.to_thread(
                model.generate_content,
                prompt,
                stream=True,
                **kwargs
            )

            for chunk in stream:
                if chunk.text:
                    yield chunk.text

        except Exception as e:
            logger.error("streaming_error", error=str(e))
            yield f"[Error: {str(e)}]"

### Rate Limiting Strategy (2025 Update)
class VertexAIRateLimiter:
    """Advanced rate limiter for Vertex AI with 2025 quotas"""

    def __init__(self, config: RateLimitConfig):
        self.config = config

        # Token bucket for requests per minute
        self.request_bucket = TokenBucket(
            capacity=config.requests_per_minute,
            refill_rate=config.requests_per_minute / 60
        )

        # Token bucket for tokens per minute
        self.token_bucket = TokenBucket(
            capacity=config.tokens_per_minute,
            refill_rate=config.tokens_per_minute / 60
        )

        # Adaptive rate limiting based on response times
        self.adaptive_limiter = AdaptiveRateLimiter()

    async def acquire(self, estimated_tokens: int = 1000) -> None:
        """Acquire rate limit tokens with adaptive adjustment"""

        # Check request rate limit
        if not await self.request_bucket.acquire(1):
            raise RateLimitExceeded("Request rate limit exceeded")

        # Check token rate limit
        if not await self.token_bucket.acquire(estimated_tokens):
            raise RateLimitExceeded("Token rate limit exceeded")

        # Apply adaptive limiting based on system performance
        await self.adaptive_limiter.acquire()

### Context Window Management (2025 Optimization)
class ContextWindowManager:
    """Advanced context window management for Gemini 2.5"""

    def __init__(self):
        # Gemini 2.5 has 2M token context window
        self.max_context_tokens = 2_000_000
        self.reserved_output_tokens = 8192
        self.available_input_tokens = self.max_context_tokens - self.reserved_output_tokens

        # Token counting (approximate)
        self.token_estimator = TokenEstimator()

    async def optimize_context(
        self,
        query: str,
        code_chunks: List[CodeChunk],
        conversation_history: List[ConversationMessage] = None
    ) -> OptimizedContext:
        """Optimize context to fit within token limits"""

        # Estimate token usage
        query_tokens = self.token_estimator.count_tokens(query)

        history_tokens = 0
        if conversation_history:
            history_text = "\n".join([
                f"{msg.role}: {msg.content}"
                for msg in conversation_history[-5:]  # Last 5 messages
            ])
            history_tokens = self.token_estimator.count_tokens(history_text)

        # Available tokens for code context
        available_for_code = (
            self.available_input_tokens -
            query_tokens -
            history_tokens -
            2000  # Buffer for prompt template
        )

        # Prioritize and select code chunks
        prioritized_chunks = await self._prioritize_chunks(query, code_chunks)
        selected_chunks = await self._select_chunks_within_limit(
            prioritized_chunks,
            available_for_code
        )

        return OptimizedContext(
            query=query,
            code_chunks=selected_chunks,
            conversation_history=conversation_history[-5:] if conversation_history else [],
            token_usage={
                "query_tokens": query_tokens,
                "history_tokens": history_tokens,
                "code_tokens": sum(chunk.token_count for chunk in selected_chunks),
                "total_tokens": query_tokens + history_tokens + sum(chunk.token_count for chunk in selected_chunks)
            }
        )

    async def _prioritize_chunks(
        self,
        query: str,
        chunks: List[CodeChunk]
    ) -> List[PrioritizedChunk]:
        """Prioritize chunks based on relevance and importance"""

        prioritized = []

        for chunk in chunks:
            # Calculate priority score
            relevance_score = await self._calculate_relevance(query, chunk)
            importance_score = await self._calculate_importance(chunk)
            recency_score = self._calculate_recency(chunk)

            priority_score = (
                relevance_score * 0.5 +
                importance_score * 0.3 +
                recency_score * 0.2
            )

            prioritized.append(PrioritizedChunk(
                chunk=chunk,
                priority_score=priority_score,
                relevance_score=relevance_score,
                importance_score=importance_score,
                recency_score=recency_score
            ))

        return sorted(prioritized, key=lambda x: x.priority_score, reverse=True)
```

## Task List

1. [ ] **Phase 1: Service Foundation (Week 1-2)**
   - [ ] Set up Python project with Poetry and FastAPI
   - [ ] Implement Vertex AI client wrapper with 2025 optimizations
   - [ ] Create basic query processing pipeline
   - [ ] Set up Redis caching with multi-level strategy
   - [ ] Implement health check and monitoring endpoints

2. [ ] **Phase 2: Query Processing Engine (Week 3-4)**
   - [ ] Create query classification system with intent detection
   - [ ] Build handler chain architecture with specialized handlers
   - [ ] Implement semantic search with code embeddings
   - [ ] Add contextual retrieval (Anthropic 2024 technique)
   - [ ] Create confidence scoring system

3. [ ] **Phase 3: RAG System Implementation (Week 5-6)**
   - [ ] Set up vector database with FAISS/Vertex AI Matching Engine
   - [ ] Implement advanced chunking strategies
   - [ ] Build cross-encoder re-ranking system
   - [ ] Add query expansion and HyDE techniques
   - [ ] Optimize context window management

4. [ ] **Phase 4: API and Streaming (Week 7-8)**
   - [ ] Create REST API endpoints with FastAPI
   - [ ] Implement WebSocket support for real-time queries
   - [ ] Add streaming response capabilities
   - [ ] Build conversation memory and session management
   - [ ] Implement rate limiting and authentication

5. [ ] **Phase 5: Performance Optimization (Week 9-10)**
   - [ ] Implement multi-level caching strategy
   - [ ] Add background pre-warming tasks
   - [ ] Optimize embedding generation and storage
   - [ ] Build cache invalidation system
   - [ ] Add performance monitoring and analytics

6. [ ] **Phase 6: Testing and Quality Assurance (Week 11-12)**
   - [ ] Create comprehensive test suite (unit, integration, E2E)
   - [ ] Build query quality benchmarks
   - [ ] Implement load testing with realistic scenarios
   - [ ] Add security testing and validation
   - [ ] Create performance regression tests

7. [ ] **Phase 7: Production Deployment (Week 13-14)**
   - [ ] Set up CI/CD pipeline with automated testing
   - [ ] Configure production monitoring and alerting
   - [ ] Implement blue-green deployment strategy
   - [ ] Add comprehensive logging and tracing
   - [ ] Create operational runbooks and documentation

## Validation Loops

### Level 1: Code Quality and Type Safety
```bash
# Run these FIRST - fix any errors before proceeding
make lint-python                    # ruff + black + mypy
make typecheck                      # Comprehensive type checking
make security-scan                  # bandit + safety checks
make format                         # Auto-format code

# Expected: No errors. If errors exist, READ and fix before continuing.
```

### Level 2: Unit and Integration Testing
```bash
# Core functionality testing
pytest tests/unit/ -v --cov=query_intelligence --cov-report=html
pytest tests/integration/ -v --maxfail=5

# AI/ML specific testing
python tests/quality/test_embedding_quality.py
python tests/quality/test_response_accuracy.py
python tests/quality/benchmark_queries.py

# Expected: >90% test coverage, all tests passing
```

### Level 3: Performance and Load Testing
```bash
# Response time validation
python scripts/test_response_times.py --target-p95=100ms

# Load testing with realistic scenarios
locust -f tests/load/query_load_test.py --host=http://localhost:8002

# Memory and resource usage
python scripts/monitor_resource_usage.py --duration=300s

# Expected: <100ms p95 response time, stable memory usage
```

### Level 4: AI Quality and Accuracy Testing
```python
# Test query understanding accuracy
async def test_query_classification_accuracy():
    test_queries = load_benchmark_queries()
    correct_classifications = 0

    for query, expected_intent in test_queries:
        result = await query_processor.classify_intent(query)
        if result.primary_intent == expected_intent:
            correct_classifications += 1

    accuracy = correct_classifications / len(test_queries)
    assert accuracy > 0.85, f"Classification accuracy {accuracy} below threshold"

# Test response quality
async def test_response_quality():
    quality_metrics = await run_quality_benchmark()

    assert quality_metrics.relevance_score > 0.8
    assert quality_metrics.factual_accuracy > 0.9
    assert quality_metrics.coherence_score > 0.85
    assert quality_metrics.completeness_score > 0.8

# Test confidence score correlation
async def test_confidence_correlation():
    human_ratings = load_human_quality_ratings()
    ai_confidence_scores = []

    for query, expected_quality in human_ratings:
        result = await query_processor.process_query(query, test_context)
        ai_confidence_scores.append(result.confidence.overall)

    correlation = calculate_correlation(human_ratings, ai_confidence_scores)
    assert correlation > 0.8, f"Confidence correlation {correlation} too low"
```

### Level 5: End-to-End Workflow Testing
```bash
# Complete workflow testing
python tests/e2e/test_complete_query_flow.py
python tests/e2e/test_conversation_flow.py
python tests/e2e/test_streaming_responses.py

# Multi-user concurrent testing
python tests/e2e/test_concurrent_users.py --users=100

# Expected: All workflows complete successfully under load
```

## Performance Optimization

### Embedding Cache Strategy
```python
class EmbeddingCacheOptimizer:
    """Optimize embedding cache for maximum performance"""

    def __init__(self):
        # L1: In-memory cache for hot embeddings
        self.hot_cache = LRUCache(maxsize=10000)

        # L2: Redis cache for warm embeddings
        self.warm_cache = redis.Redis(decode_responses=False)

        # L3: Database cache for cold embeddings
        self.cold_cache = DatabaseCache()

        # Pre-computed embeddings for common patterns
        self.pattern_embeddings = {}

    async def get_embedding(self, text: str, cache_key: str) -> np.ndarray:
        """Get embedding with multi-level cache optimization"""

        # Check hot cache first (fastest)
        if cache_key in self.hot_cache:
            return self.hot_cache[cache_key]

        # Check warm cache (Redis)
        cached = await self.warm_cache.get(cache_key)
        if cached:
            embedding = pickle.loads(cached)
            self.hot_cache[cache_key] = embedding  # Promote to hot cache
            return embedding

        # Check cold cache (database)
        embedding = await self.cold_cache.get(cache_key)
        if embedding:
            # Promote to warm cache
            await self.warm_cache.setex(
                cache_key,
                86400,  # 24 hours
                pickle.dumps(embedding)
            )
            self.hot_cache[cache_key] = embedding
            return embedding

        # Generate new embedding
        embedding = await self._generate_embedding(text)

        # Cache at all levels
        await self._cache_embedding(cache_key, embedding)

        return embedding

    async def pre_warm_cache(self):
        """Pre-warm cache with common queries and patterns"""

        common_queries = await self._get_common_queries()

        for query in common_queries:
            cache_key = self._generate_cache_key(query)
            if not await self._is_cached(cache_key):
                embedding = await self._generate_embedding(query)
                await self._cache_embedding(cache_key, embedding)

### Query Result Streaming Optimization
class StreamingOptimizer:
    """Optimize streaming responses for real-time experience"""

    def __init__(self):
        self.chunk_size = 50  # Characters per chunk
        self.chunk_delay = 0.01  # Seconds between chunks
        self.buffer_size = 1000  # Buffer size for smooth streaming

    async def optimize_streaming(
        self,
        response_generator: AsyncGenerator[str, None]
    ) -> AsyncGenerator[str, None]:
        """Optimize streaming for smooth user experience"""

        buffer = ""

        async for chunk in response_generator:
            buffer += chunk

            # Send chunks when buffer reaches optimal size
            while len(buffer) >= self.chunk_size:
                yield buffer[:self.chunk_size]
                buffer = buffer[self.chunk_size:]

                # Small delay for smooth streaming
                await asyncio.sleep(self.chunk_delay)

        # Send remaining buffer
        if buffer:
            yield buffer

### Memory Usage Optimization
class MemoryOptimizer:
    """Optimize memory usage for large-scale deployment"""

    def __init__(self):
        self.max_memory_mb = 4000  # 4GB limit
        self.cleanup_threshold = 0.8  # Cleanup at 80% usage

    async def monitor_and_optimize(self):
        """Continuous memory monitoring and optimization"""

        while True:
            memory_usage = self._get_memory_usage_mb()

            if memory_usage > (self.max_memory_mb * self.cleanup_threshold):
                await self._perform_memory_cleanup()

            await asyncio.sleep(60)  # Check every minute

    async def _perform_memory_cleanup(self):
        """Perform memory cleanup operations"""

        # Clear old cache entries
        await self._cleanup_old_cache_entries()

        # Clear conversation history older than 24 hours
        await self._cleanup_old_conversations()

        # Force garbage collection
        import gc
        gc.collect()

        logger.info("memory_cleanup_completed")
```

## Confidence Score Calculation

```python
class ConfidenceScoreCalculator:
    """Advanced confidence scoring with multiple factors"""

    def __init__(self):
        self.weights = {
            "query_clarity": 0.2,
            "context_relevance": 0.25,
            "response_coherence": 0.2,
            "factual_accuracy": 0.25,
            "model_certainty": 0.1
        }

    async def calculate_confidence(
        self,
        query: str,
        response: str,
        context: RetrievalResult,
        model_metadata: dict
    ) -> ConfidenceBreakdown:
        """Calculate comprehensive confidence score"""

        # Factor 1: Query clarity and specificity
        query_clarity = await self._assess_query_clarity(query)

        # Factor 2: Context relevance and quality
        context_relevance = await self._assess_context_relevance(query, context)

        # Factor 3: Response coherence and completeness
        response_coherence = await self._assess_response_coherence(response, query)

        # Factor 4: Factual accuracy based on code references
        factual_accuracy = await self._assess_factual_accuracy(response, context)

        # Factor 5: Model certainty from generation metadata
        model_certainty = self._extract_model_certainty(model_metadata)

        # Calculate weighted overall confidence
        overall_confidence = (
            query_clarity * self.weights["query_clarity"] +
            context_relevance * self.weights["context_relevance"] +
            response_coherence * self.weights["response_coherence"] +
            factual_accuracy * self.weights["factual_accuracy"] +
            model_certainty * self.weights["model_certainty"]
        )

        return ConfidenceBreakdown(
            overall=overall_confidence,
            query_understanding=query_clarity,
            context_relevance=context_relevance,
            response_coherence=response_coherence,
            factual_accuracy=factual_accuracy
        )

    async def _assess_query_clarity(self, query: str) -> float:
        """Assess how clear and specific the query is"""

        clarity_factors = {
            "length": min(len(query.split()) / 10, 1.0),  # Optimal 10 words
            "specificity": await self._measure_specificity(query),
            "technical_terms": self._count_technical_terms(query) / 5,  # Up to 5 terms
            "ambiguity": 1.0 - await self._measure_ambiguity(query)
        }

        return sum(clarity_factors.values()) / len(clarity_factors)

    async def _assess_context_relevance(
        self,
        query: str,
        context: RetrievalResult
    ) -> float:
        """Assess how relevant the retrieved context is"""

        if not context.code_chunks:
            return 0.0

        relevance_scores = []

        for chunk in context.code_chunks[:5]:  # Top 5 chunks
            # Semantic similarity score
            semantic_score = chunk.similarity_score

            # Keyword overlap score
            keyword_score = self._calculate_keyword_overlap(query, chunk.content)

            # Code quality score (well-documented, clear structure)
            quality_score = await self._assess_code_quality(chunk)

            chunk_relevance = (semantic_score * 0.5 + keyword_score * 0.3 + quality_score * 0.2)
            relevance_scores.append(chunk_relevance)

        return sum(relevance_scores) / len(relevance_scores)

    async def _assess_response_coherence(self, response: str, query: str) -> float:
        """Assess response coherence and completeness"""

        coherence_factors = {
            "structure": await self._assess_response_structure(response),
            "completeness": await self._assess_completeness(response, query),
            "clarity": await self._assess_response_clarity(response),
            "relevance": await self._assess_response_relevance(response, query)
        }

        return sum(coherence_factors.values()) / len(coherence_factors)

    async def _assess_factual_accuracy(
        self,
        response: str,
        context: RetrievalResult
    ) -> float:
        """Assess factual accuracy based on code references"""

        # Extract claims from response
        claims = await self._extract_factual_claims(response)

        if not claims:
            return 0.8  # Default for responses without specific claims

        verified_claims = 0

        for claim in claims:
            # Verify claim against code context
            if await self._verify_claim_against_code(claim, context):
                verified_claims += 1

        return verified_claims / len(claims) if claims else 0.8

## Anti-Patterns to Avoid

- ❌ **Sending entire codebase to LLM**: Use intelligent chunking and context selection
- ❌ **Not handling rate limits**: Implement proper rate limiting and circuit breakers
- ❌ **Ignoring confidence scores**: Always provide and use confidence metrics
- ❌ **No query validation**: Validate and sanitize all user inputs
- ❌ **Missing context in responses**: Always include relevant code references
- ❌ **Not caching embeddings**: Embeddings are expensive to compute
- ❌ **Sequential processing**: Use async/await for all I/O operations
- ❌ **No conversation memory**: Maintain context across multi-turn conversations
- ❌ **Hallucination without references**: Always ground responses in actual code
- ❌ **Poor error handling**: Provide meaningful error messages and graceful degradation

## Known Gotchas

- **Gemini 2.5 rate limits vary by region**: us-central1 has highest quotas as of July 2025
- **Context window is 2M tokens but optimal is <1M**: Performance degrades with very large contexts
- **Embedding generation can be expensive at scale**: Implement aggressive caching
- **Vector similarity doesn't always mean semantic similarity**: Use re-ranking with cross-encoders
- **Multi-language code requires special handling**: Different embedding strategies per language
- **Streaming responses need careful error handling**: Errors mid-stream are hard to recover from
- **Cache invalidation is complex with code changes**: Implement smart invalidation strategies
- **WebSocket connections can be unstable**: Implement reconnection logic
- **Memory usage grows with conversation history**: Implement cleanup strategies
- **Query expansion can introduce noise**: Balance expansion with precision

## Performance Optimization Summary

- **Embedding cache with 7-day TTL**: Reduce expensive embedding computations
- **Redis for sub-millisecond cache lookups**: Multi-level caching strategy
- **Batch embedding requests**: Process multiple embeddings together
- **Pre-compute embeddings for common queries**: Background pre-warming
- **Use Gemini 2.5 Flash for faster responses**: Cost-effective model selection
- **Implement query result streaming**: Real-time user experience
- **Parallelize context retrieval**: Concurrent search across multiple sources
- **Adaptive context window management**: Optimize token usage
- **Circuit breakers for external services**: Prevent cascade failures
- **Background cleanup tasks**: Maintain optimal memory usage

## Confidence Score: 9/10

**High confidence areas:**
- Query classification and intent detection (proven NLP techniques)
- API design and FastAPI implementation (well-established patterns)
- Caching strategies and performance optimization (battle-tested approaches)
- Vertex AI integration and rate limiting (documented best practices)

**Medium confidence areas:**
- RAG optimization and contextual retrieval (emerging techniques, need validation)
- Multi-turn conversation handling (complex state management)
- Cross-language code understanding (requires extensive testing)

**Lower confidence areas:**
- Accuracy benchmarks on real codebases (need domain-specific validation)
- Scale testing with 1000+ concurrent users (requires load testing)
- Production reliability at 99.9% uptime (needs operational validation)

**Mitigation strategies:**
- Comprehensive testing with real codebases before production
- Gradual rollout with feature flags and monitoring
- Extensive load testing and performance validation
- Continuous monitoring and feedback loops for quality improvement