<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CCL Orchestration Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0a0e27;
            color: #e0e6ed;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background: #1a1f3a;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        h1 {
            color: #4fc3f7;
            font-size: 28px;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4caf50;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: #1a1f3a;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #2a2f4a;
        }
        
        .card h2 {
            color: #4fc3f7;
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .agent-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .agent-item {
            background: #0a0e27;
            padding: 12px;
            border-radius: 6px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .agent-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .status-idle { background: #424242; }
        .status-working { background: #2196f3; }
        .status-completed { background: #4caf50; }
        .status-failed { background: #f44336; }
        .status-blocked { background: #ff9800; }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #0a0e27;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4fc3f7, #2196f3);
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
        }
        
        .task-queue {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .task-item {
            background: #0a0e27;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .task-priority {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .priority-1 { background: #f44336; }
        .priority-2 { background: #ff9800; }
        .priority-3 { background: #4caf50; }
        .priority-4 { background: #2196f3; }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
        }
        
        .metric-item {
            text-align: center;
            padding: 15px;
            background: #0a0e27;
            border-radius: 6px;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #4fc3f7;
        }
        
        .metric-label {
            font-size: 12px;
            color: #9ca3af;
            margin-top: 5px;
        }
        
        .log-viewer {
            background: #0a0e27;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin-bottom: 5px;
            opacity: 0.8;
        }
        
        .log-time {
            color: #9ca3af;
            margin-right: 10px;
        }
        
        .log-info { color: #4fc3f7; }
        .log-warning { color: #ff9800; }
        .log-error { color: #f44336; }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #1976d2;
        }
        
        button:disabled {
            background: #424242;
            cursor: not-allowed;
        }
        
        .sparc-phases {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
        }
        
        .phase {
            flex: 1;
            text-align: center;
            padding: 10px;
            background: #0a0e27;
            border-radius: 6px;
            margin: 0 5px;
            position: relative;
        }
        
        .phase.active {
            background: #1a2f4a;
            border: 2px solid #4fc3f7;
        }
        
        .phase.completed {
            background: #1a3a2a;
            border: 2px solid #4caf50;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🚀 CCL Orchestration Dashboard</h1>
            <div class="status-indicator">
                <span class="status-dot"></span>
                <span>System Active</span>
            </div>
        </header>
        
        <div class="sparc-phases">
            <div class="phase completed">
                <strong>Specification</strong>
                <div>✓ Complete</div>
            </div>
            <div class="phase active">
                <strong>Pseudocode</strong>
                <div>In Progress</div>
            </div>
            <div class="phase">
                <strong>Architecture</strong>
                <div>Pending</div>
            </div>
            <div class="phase">
                <strong>Refinement</strong>
                <div>Pending</div>
            </div>
            <div class="phase">
                <strong>Completion</strong>
                <div>Pending</div>
            </div>
        </div>
        
        <div class="grid">
            <div class="card">
                <h2>👥 Active Agents</h2>
                <div class="agent-list" id="agentList">
                    <div class="agent-item">
                        <span>🏗️ agent-architect-1</span>
                        <div class="agent-status">
                            <span class="status-badge status-working">Working</span>
                        </div>
                    </div>
                    <div class="agent-item">
                        <span>💻 agent-backend-1</span>
                        <div class="agent-status">
                            <span class="status-badge status-idle">Idle</span>
                        </div>
                    </div>
                    <div class="agent-item">
                        <span>💻 agent-backend-2</span>
                        <div class="agent-status">
                            <span class="status-badge status-idle">Idle</span>
                        </div>
                    </div>
                    <div class="agent-item">
                        <span>🎨 agent-frontend-1</span>
                        <div class="agent-status">
                            <span class="status-badge status-idle">Idle</span>
                        </div>
                    </div>
                    <div class="agent-item">
                        <span>🗄️ agent-database-1</span>
                        <div class="agent-status">
                            <span class="status-badge status-idle">Idle</span>
                        </div>
                    </div>
                    <div class="agent-item">
                        <span>🔧 agent-devops-1</span>
                        <div class="agent-status">
                            <span class="status-badge status-idle">Idle</span>
                        </div>
                    </div>
                    <div class="agent-item">
                        <span>🧪 agent-tester-1</span>
                        <div class="agent-status">
                            <span class="status-badge status-idle">Idle</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h2>📋 Task Queue</h2>
                <div class="task-queue" id="taskQueue">
                    <div class="task-item">
                        <span class="task-priority priority-1"></span>
                        arch-001: Design overall CCL platform architecture
                    </div>
                    <div class="task-item">
                        <span class="task-priority priority-2"></span>
                        arch-002: Design API contracts and interfaces
                    </div>
                    <div class="task-item">
                        <span class="task-priority priority-2"></span>
                        db-001: Design Spanner database schemas
                    </div>
                    <div class="task-item">
                        <span class="task-priority priority-3"></span>
                        backend-001: Implement Analysis Engine service
                    </div>
                    <div class="task-item">
                        <span class="task-priority priority-3"></span>
                        backend-002: Implement Query Intelligence service
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h2>📊 Overall Progress</h2>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 15%">15%</div>
                </div>
                <div class="metrics">
                    <div class="metric-item">
                        <div class="metric-value">8</div>
                        <div class="metric-label">Total Tasks</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">1</div>
                        <div class="metric-label">In Progress</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">0</div>
                        <div class="metric-label">Completed</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">7</div>
                        <div class="metric-label">Remaining</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2>📜 System Logs</h2>
            <div class="log-viewer" id="logViewer">
                <div class="log-entry">
                    <span class="log-time">10:23:45</span>
                    <span class="log-info">INFO: Orchestration system initialized</span>
                </div>
                <div class="log-entry">
                    <span class="log-time">10:23:46</span>
                    <span class="log-info">INFO: Loaded 7 agent personas</span>
                </div>
                <div class="log-entry">
                    <span class="log-time">10:23:47</span>
                    <span class="log-info">INFO: Loaded 8 initial tasks</span>
                </div>
                <div class="log-entry">
                    <span class="log-time">10:23:48</span>
                    <span class="log-info">INFO: Launching agent agent-architect-1 with persona architect</span>
                </div>
                <div class="log-entry">
                    <span class="log-time">10:23:50</span>
                    <span class="log-info">INFO: Starting orchestration</span>
                </div>
            </div>
        </div>
        
        <div class="controls">
            <button onclick="refreshDashboard()">🔄 Refresh</button>
            <button onclick="pauseOrchestration()">⏸️ Pause</button>
            <button onclick="resumeOrchestration()" disabled>▶️ Resume</button>
            <button onclick="viewKnowledgeBank()">🧠 Knowledge Bank</button>
            <button onclick="exportReport()">📊 Export Report</button>
        </div>
    </div>
    
    <script>
        // Dashboard functionality
        let isPaused = false;
        
        function refreshDashboard() {
            // In production, this would fetch real data from the orchestration system
            console.log('Refreshing dashboard...');
            addLogEntry('INFO', 'Dashboard refreshed');
        }
        
        function pauseOrchestration() {
            isPaused = true;
            document.querySelector('button[onclick="pauseOrchestration()"]').disabled = true;
            document.querySelector('button[onclick="resumeOrchestration()"]').disabled = false;
            addLogEntry('WARNING', 'Orchestration paused by user');
        }
        
        function resumeOrchestration() {
            isPaused = false;
            document.querySelector('button[onclick="pauseOrchestration()"]').disabled = false;
            document.querySelector('button[onclick="resumeOrchestration()"]').disabled = true;
            addLogEntry('INFO', 'Orchestration resumed');
        }
        
        function viewKnowledgeBank() {
            window.open('.claude/memory/knowledge-bank.json', '_blank');
        }
        
        function exportReport() {
            const report = {
                timestamp: new Date().toISOString(),
                progress: 15,
                tasksCompleted: 0,
                tasksTotal: 8,
                activeAgents: 7,
                systemStatus: 'Active'
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `ccl-orchestration-report-${Date.now()}.json`;
            a.click();
        }
        
        function addLogEntry(level, message) {
            const logViewer = document.getElementById('logViewer');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            
            const time = new Date().toLocaleTimeString();
            const levelClass = `log-${level.toLowerCase()}`;
            
            entry.innerHTML = `
                <span class="log-time">${time}</span>
                <span class="${levelClass}">${level}: ${message}</span>
            `;
            
            logViewer.appendChild(entry);
            logViewer.scrollTop = logViewer.scrollHeight;
        }
        
        // Simulate real-time updates
        setInterval(() => {
            if (!isPaused) {
                // Simulate progress
                const progressFill = document.querySelector('.progress-fill');
                const currentWidth = parseInt(progressFill.style.width);
                if (currentWidth < 100) {
                    const newWidth = Math.min(currentWidth + Math.random() * 2, 100);
                    progressFill.style.width = newWidth + '%';
                    progressFill.textContent = Math.floor(newWidth) + '%';
                }
            }
        }, 5000);
        
        // Auto-refresh every 30 seconds
        setInterval(() => {
            if (!isPaused) {
                refreshDashboard();
            }
        }, 30000);
    </script>
</body>
</html>