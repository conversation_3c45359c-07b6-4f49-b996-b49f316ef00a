{"description": "End-to-end pattern detection flow from Repository Analysis to Pattern Detection output", "flow_steps": [{"step": 1, "service": "Repository Analysis API", "action": "Analyze repository and output AST data", "output_schema": "ast-output-v1.json"}, {"step": 2, "service": "Pattern Detection", "action": "Transform AST data to pattern detection input", "input_schema": "pattern-input-v1.json"}, {"step": 3, "service": "Pattern Detection", "action": "Process patterns and generate output", "output_schema": "pattern-output-v1.json"}], "pattern_input_example": {"repository_id": "repo_a1b2c3d4e5f6g7h8", "analysis_id": "analysis_z9y8x7w6v5u4t3s2", "request_id": "req_p1q2r3s4t5u6v7w8", "ast_data": {"files": [{"file_path": "src/main.py", "language": "Python", "content_hash": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_bytes": 1024, "ast_nodes": [{"id": "node_001", "type": "ClassDef", "name": "Calculator", "range": {"start_line": 5, "end_line": 15, "start_column": 0, "end_column": 20}, "parent_id": null, "children_ids": ["node_002", "node_003", "node_004"], "properties": {"decorator_list": [], "bases": [], "keywords": []}, "text": "class Calculator:\n    def __init__(self):\n        if hasattr(Calculator, '_instance'):\n            return Calculator._instance\n        Calculator._instance = self\n    \n    def add(self, a, b):\n        return a + b", "annotations": []}, {"id": "node_002", "type": "FunctionDef", "name": "__init__", "range": {"start_line": 6, "end_line": 10, "start_column": 4, "end_column": 30}, "parent_id": "node_001", "children_ids": ["node_005"], "properties": {"decorator_list": [], "args": {"args": [{"arg": "self"}]}, "returns": null}, "text": "def __init__(self):\n    if hasattr(Calculator, '_instance'):\n        return Calculator._instance\n    Calculator._instance = self", "annotations": []}], "metrics": {"lines_of_code": 15, "total_lines": 25, "complexity": 3, "function_count": 3, "class_count": 1, "method_count": 2, "variable_count": 1, "comment_ratio": 0.2, "nesting_depth": 2, "coupling_score": 0.3, "cohesion_score": 0.8}, "symbols": [{"name": "Calculator", "type": "class", "range": {"start_line": 5, "end_line": 15}, "visibility": "public", "modifiers": [], "signature": "class Calculator", "parameters": [], "return_type": null, "complexity": 3, "references": [{"symbol_name": "_instance", "reference_type": "composition", "line_number": 8}], "documentation": null, "annotations": []}, {"name": "__init__", "type": "method", "range": {"start_line": 6, "end_line": 10}, "visibility": "public", "modifiers": [], "signature": "def __init__(self)", "parameters": [{"name": "self", "type": "Calculator", "default_value": null}], "return_type": null, "complexity": 2, "references": [{"symbol_name": "has<PERSON>r", "reference_type": "call", "line_number": 7}], "documentation": null, "annotations": []}], "imports": [], "code_features": {"token_frequency": {"class": 1, "def": 2, "if": 1, "hasattr": 1, "return": 2, "self": 3}, "structural_features": {"max_nesting_depth": 2, "avg_method_length": 3.5, "inheritance_depth": 0, "interface_count": 0}, "semantic_features": [{"feature_name": "singleton_pattern_indicators", "feature_value": 0.85, "confidence": 0.9}, {"feature_name": "class_instance_management", "feature_value": 0.92, "confidence": 0.88}]}}], "repository_metrics": {"total_files": 2, "total_lines": 40, "total_complexity": 5, "languages": {"Python": {"files": 1, "lines": 25, "percentage": 62.5}, "JavaScript": {"files": 1, "lines": 15, "percentage": 37.5}}, "architecture_hints": {"framework_detected": ["flask", "pytest"], "architecture_style": "layered", "design_patterns_hint": ["singleton", "factory"]}}}, "detection_config": {"enabled_detectors": ["design_patterns", "anti_patterns", "security_vulnerabilities", "performance_issues", "code_smells"], "confidence_threshold": 0.7, "language_specific": {"Python": {"enabled": true, "custom_patterns": ["django_patterns", "flask_patterns"], "exclusions": ["test_patterns"]}, "JavaScript": {"enabled": true, "custom_patterns": ["react_patterns", "node_patterns"], "exclusions": []}}, "performance_limits": {"max_processing_time_ms": 30000, "max_memory_mb": 1024, "max_patterns_per_file": 50}, "output_preferences": {"include_code_examples": true, "include_recommendations": true, "group_by_severity": false, "max_examples_per_pattern": 3}}, "context": {"user_id": "user_abc123def456", "organization_id": "org_xyz789uvw012", "priority": "normal", "callback_url": "https://api.example.com/webhooks/pattern-detection", "tags": ["code-review", "quality-check"]}}, "pattern_output_example": {"request_id": "req_p1q2r3s4t5u6v7w8", "repository_id": "repo_a1b2c3d4e5f6g7h8", "analysis_id": "analysis_z9y8x7w6v5u4t3s2", "patterns": [{"id": "pattern_s1i2n3g4l5e6t7o8", "pattern_type": "design_pattern", "pattern_name": "<PERSON><PERSON>", "confidence": 0.89, "severity": "medium", "locations": [{"file_path": "src/main.py", "range": {"start_line": 5, "end_line": 15, "start_column": 0, "end_column": 20}, "symbol_name": "Calculator", "context": {"function_name": null, "class_name": "Calculator", "namespace": null, "module_name": "main"}, "snippet": "class Calculator:\n    def __init__(self):\n        if hasattr(Calculator, '_instance'):\n            return Calculator._instance\n        Calculator._instance = self"}], "description": "Singleton design pattern implementation detected in Calculator class", "explanation": "The class implements singleton pattern by checking for existing instance in __init__ method and storing class-level instance reference", "recommendations": ["Consider using a proper singleton implementation with __new__ method", "Add thread safety if used in concurrent environment", "Document the singleton behavior clearly", "Consider if singleton is necessary or if dependency injection would be better"], "examples": [{"title": "Current Implementation", "description": "Basic singleton implementation using class attribute", "code": "class Calculator:\n    def __init__(self):\n        if hasattr(Calculator, '_instance'):\n            return Calculator._instance\n        Calculator._instance = self", "language": "Python", "file_path": "src/main.py", "line_range": {"start": 5, "end": 10}, "annotations": [{"line": 7, "message": "Instance check - singleton pattern indicator"}, {"line": 9, "message": "Instance storage - maintains single instance"}]}, {"title": "Recommended Implementation", "description": "Thread-safe singleton using __new__ method", "code": "class Calculator:\n    _instance = None\n    _lock = threading.Lock()\n    \n    def __new__(cls):\n        if cls._instance is None:\n            with cls._lock:\n                if cls._instance is None:\n                    cls._instance = super().__new__(cls)\n        return cls._instance", "language": "Python", "file_path": null, "line_range": null, "annotations": [{"line": 5, "message": "Double-checked locking pattern"}, {"line": 6, "message": "Thread synchronization"}]}], "related_patterns": ["factory_pattern_001"], "tags": ["creational", "singleton", "instance-management"], "impact": {"maintainability": "neutral", "performance": "positive", "security": "neutral", "readability": "negative", "testability": "negative"}, "metrics": {"complexity_increase": 2, "lines_affected": 6, "files_affected": 1, "estimated_fix_time_minutes": 15}, "detection_method": {"algorithm": "hybrid", "model_name": "pattern_classifier_v2.1", "rule_set": "singleton_detection_rules", "features_used": ["class_instance_management", "singleton_pattern_indicators"]}}], "summary": {"total_patterns": 1, "by_type": {"design_pattern": 1, "anti_pattern": 0, "security_vulnerability": 0, "performance_issue": 0, "code_smell": 0}, "by_severity": {"info": 0, "low": 0, "medium": 1, "high": 0, "critical": 0}, "by_language": {"Python": 1, "JavaScript": 0}, "top_patterns": [{"pattern_name": "<PERSON><PERSON>", "count": 1, "avg_confidence": 0.89}], "quality_scores": {"overall_score": 78.5, "maintainability_score": 82.0, "security_score": 95.0, "performance_score": 88.0, "readability_score": 75.0, "testability_score": 65.0}, "recommendations": [{"priority": "medium", "category": "Design Patterns", "description": "Review singleton implementation for thread safety and necessity", "affected_files": 1, "estimated_effort_hours": 0.5}], "trends": {"improving_areas": ["performance", "security"], "degrading_areas": ["testability"], "new_issues": 0, "resolved_issues": 0}}, "metadata": {"version": "2.1.0", "timestamp": "2025-01-15T10:35:00Z", "processing_time_ms": 12500, "model_versions": {"pattern_classifier": "v2.1.0", "code_analyzer": "v1.8.3", "feature_extractor": "v1.5.2"}, "performance_stats": {"files_processed": 2, "patterns_detected": 1, "avg_confidence": 0.89, "memory_peak_mb": 245.8, "cache_hit_ratio": 0.25}, "detection_config": {"enabled_detectors": ["design_patterns", "anti_patterns", "security_vulnerabilities", "performance_issues", "code_smells"], "confidence_threshold": 0.7, "language_filters": ["Python", "JavaScript"]}}}}