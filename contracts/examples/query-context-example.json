{"repository_id": "repo_a1b2c3d4e5f6g7h8", "analysis_id": "analysis_z9y8x7w6v5u4t3s2", "ast_summary": {"symbols": [{"name": "main", "type": "function", "file_path": "src/main.py", "signature": "def main() -> int", "visibility": "public", "documentation": "Main entry point of the application.", "usage_count": 1, "complexity": 2, "tags": ["entry-point", "main-function", "application-start"], "related_symbols": ["Calculator", "print"]}, {"name": "Calculator", "type": "class", "file_path": "src/main.py", "signature": "class Calculator", "visibility": "public", "documentation": "Simple calculator class.", "usage_count": 3, "complexity": 5, "tags": ["calculator", "math", "utility-class"], "related_symbols": ["add", "subtract", "multiply"]}, {"name": "add", "type": "method", "file_path": "src/main.py", "signature": "def add(self, a, b)", "visibility": "public", "documentation": null, "usage_count": 15, "complexity": 1, "tags": ["arithmetic", "addition", "calculator-method"], "related_symbols": ["Calculator", "subtract", "multiply"]}, {"name": "formatDate", "type": "function", "file_path": "src/utils.js", "signature": "function formatDate(date)", "visibility": "public", "documentation": null, "usage_count": 8, "complexity": 1, "tags": ["date-formatting", "utility", "string-manipulation"], "related_symbols": ["Date", "toISOString"]}], "structure": {"directories": [{"path": "src", "type": "directory", "children": [{"path": "src/main.py", "type": "file", "metadata": {"file_count": 1, "total_lines": 25, "primary_language": "Python", "purpose": "source"}}, {"path": "src/utils.js", "type": "file", "metadata": {"file_count": 1, "total_lines": 15, "primary_language": "JavaScript", "purpose": "source"}}], "metadata": {"file_count": 2, "total_lines": 40, "primary_language": "Python", "purpose": "source"}}, {"path": "tests", "type": "directory", "children": [], "metadata": {"file_count": 0, "total_lines": 0, "primary_language": null, "purpose": "test"}}], "modules": [{"name": "main", "path": "src/main.py", "type": "module", "exports": ["main", "Calculator"], "imports": [], "description": "Main application module", "version": null}, {"name": "utils", "path": "src/utils.js", "type": "module", "exports": ["formatDate", "parseDate"], "imports": [], "description": "Utility functions module", "version": null}], "dependencies": [{"name": "pytest", "type": "pip", "version": "^7.0.0", "scope": "test", "usage_locations": ["tests/test_main.py"]}, {"name": "lodash", "type": "npm", "version": "^4.17.21", "scope": "runtime", "usage_locations": ["src/utils.js"]}]}, "metrics": {"complexity_distribution": {"low": 3, "medium": 1, "high": 0, "very_high": 0}, "language_stats": {"Python": {"files": 1, "lines": 25, "functions": 3, "classes": 1}, "JavaScript": {"files": 1, "lines": 15, "functions": 2, "classes": 0}}, "quality_scores": {"maintainability": 88.9, "test_coverage": 65.0, "documentation_coverage": 45.0, "technical_debt_ratio": 0.08}}}, "embeddings": [{"chunk_id": "chunk_x1y2z3a4b5c6d7e8", "content": "def main():\n    \"\"\"Main entry point of the application.\"\"\"\n    print('Hello, World!')\n    return 0", "vector": [0.1, -0.2, 0.3, 0.05, -0.15, 0.25, 0.08, -0.12, 0.18, -0.08], "metadata": {"file_path": "src/main.py", "language": "Python", "chunk_type": "function", "symbols": ["main", "print"], "complexity": 2, "importance_score": 0.9, "keywords": ["main", "entry", "point", "application", "hello", "world"], "context": {"parent_symbol": "main", "related_chunks": ["chunk_m9n8o7p6q5r4s3t2"], "dependencies": ["print"]}}}, {"chunk_id": "chunk_m9n8o7p6q5r4s3t2", "content": "class Calculator:\n    \"\"\"Simple calculator class.\"\"\"\n    \n    def add(self, a, b):\n        return a + b", "vector": [0.2, -0.1, 0.4, 0.15, -0.05, 0.35, 0.18, -0.22, 0.12, -0.28], "metadata": {"file_path": "src/main.py", "language": "Python", "chunk_type": "class", "symbols": ["Calculator", "add"], "complexity": 3, "importance_score": 0.75, "keywords": ["calculator", "class", "add", "math", "arithmetic"], "context": {"parent_symbol": "Calculator", "related_chunks": ["chunk_x1y2z3a4b5c6d7e8"], "dependencies": []}}}, {"chunk_id": "chunk_j8k7l6m5n4o3p2q1", "content": "function formatDate(date) {\n  return date.toISOString().split('T')[0];\n}", "vector": [0.15, -0.25, 0.35, 0.1, -0.2, 0.3, 0.12, -0.18, 0.22, -0.15], "metadata": {"file_path": "src/utils.js", "language": "JavaScript", "chunk_type": "function", "symbols": ["formatDate"], "complexity": 1, "importance_score": 0.6, "keywords": ["format", "date", "iso", "string", "utility"], "context": {"parent_symbol": "formatDate", "related_chunks": [], "dependencies": ["Date", "toISOString", "split"]}}}], "patterns": [{"pattern_id": "singleton_detected_001", "pattern_type": "design_pattern", "pattern_name": "<PERSON><PERSON>", "confidence": 0.85, "description": "Potential Singleton pattern detected in Calculator class", "locations": [{"file_path": "src/main.py", "chunk_id": "chunk_m9n8o7p6q5r4s3t2", "line_range": {"start": 5, "end": 9}}], "impact": "medium", "recommendations": ["Consider if <PERSON><PERSON> is necessary for this use case", "Ensure thread safety if used in concurrent environment"], "related_patterns": ["factory_pattern_002"]}, {"pattern_id": "utility_function_001", "pattern_type": "design_pattern", "pattern_name": "Utility Function", "confidence": 0.92, "description": "Utility function pattern detected for date formatting", "locations": [{"file_path": "src/utils.js", "chunk_id": "chunk_j8k7l6m5n4o3p2q1", "line_range": {"start": 0, "end": 2}}], "impact": "low", "recommendations": ["Consider adding input validation", "Add JSDoc documentation"], "related_patterns": []}], "metadata": {"created_at": "2025-01-15T10:30:10Z", "version": "1.0.0", "source_commit": "a1b2c3d4e5f6789012345678901234567890abcd", "processing_stats": {"chunks_processed": 3, "embeddings_generated": 3, "symbols_indexed": 4, "processing_time_ms": 2500}, "cache_info": {"cache_key": "query_context_repo_a1b2c3d4e5f6g7h8_analysis_z9y8x7w6v5u4t3s2", "ttl_seconds": 3600, "last_updated": "2025-01-15T10:30:10Z"}}}