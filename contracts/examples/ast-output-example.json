{"repository": {"id": "repo_a1b2c3d4e5f6g7h8", "url": "https://github.com/example/sample-project", "commit": "a1b2c3d4e5f6789012345678901234567890abcd", "branch": "main", "size_bytes": 2048576, "clone_time_ms": 3500}, "analysis": {"files": [{"path": "src/main.py", "language": "Python", "content_hash": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_bytes": 1024, "ast": {"type": "<PERSON><PERSON><PERSON>", "range": {"start": {"line": 0, "column": 0, "byte": 0}, "end": {"line": 25, "column": 0, "byte": 1024}}, "children": [{"type": "FunctionDef", "name": "main", "range": {"start": {"line": 0, "column": 0, "byte": 0}, "end": {"line": 10, "column": 0, "byte": 256}}, "properties": {"decorator_list": [], "args": {"args": [], "defaults": []}, "returns": null}, "text": "def main():\n    print('Hello, World!')\n    return 0", "children": [{"type": "Expr", "range": {"start": {"line": 1, "column": 4, "byte": 15}, "end": {"line": 1, "column": 26, "byte": 37}}, "children": [{"type": "Call", "range": {"start": {"line": 1, "column": 4, "byte": 15}, "end": {"line": 1, "column": 26, "byte": 37}}, "properties": {"func": {"type": "Name", "id": "print"}, "args": [{"type": "Constant", "value": "Hello, <PERSON>!"}]}, "text": "print('Hello, <PERSON>!')"}]}]}]}, "metrics": {"lines_of_code": 15, "total_lines": 25, "complexity": 2, "maintainability_index": 85.5, "function_count": 3, "class_count": 1, "comment_ratio": 0.2}, "chunks": [{"chunk_id": "chunk_x1y2z3a4b5c6d7e8", "content": "def main():\n    \"\"\"Main entry point of the application.\"\"\"\n    print('Hello, World!')\n    return 0", "range": {"start": {"line": 0, "column": 0, "byte": 0}, "end": {"line": 3, "column": 12, "byte": 89}}, "type": "function", "language": "Python", "context": {"parent_symbol": "main", "imports": [], "dependencies": ["print"]}}, {"chunk_id": "chunk_m9n8o7p6q5r4s3t2", "content": "class Calculator:\n    \"\"\"Simple calculator class.\"\"\"\n    \n    def add(self, a, b):\n        return a + b", "range": {"start": {"line": 5, "column": 0, "byte": 100}, "end": {"line": 9, "column": 20, "byte": 180}}, "type": "class", "language": "Python", "context": {"parent_symbol": "Calculator", "imports": [], "dependencies": []}}], "symbols": [{"name": "main", "type": "function", "range": {"start": {"line": 0, "column": 0, "byte": 0}, "end": {"line": 3, "column": 12, "byte": 89}}, "visibility": "public", "signature": "def main() -> int", "documentation": "Main entry point of the application."}, {"name": "Calculator", "type": "class", "range": {"start": {"line": 5, "column": 0, "byte": 100}, "end": {"line": 9, "column": 20, "byte": 180}}, "visibility": "public", "signature": "class Calculator", "documentation": "Simple calculator class."}, {"name": "add", "type": "method", "range": {"start": {"line": 8, "column": 4, "byte": 150}, "end": {"line": 9, "column": 20, "byte": 180}}, "visibility": "public", "signature": "def add(self, a, b)", "documentation": null}]}, {"path": "src/utils.js", "language": "JavaScript", "content_hash": "f4a5b6c7d8e9f0123456789abcdef0123456789abcdef0123456789abcdef012", "size_bytes": 512, "ast": {"type": "Program", "range": {"start": {"line": 0, "column": 0, "byte": 0}, "end": {"line": 15, "column": 0, "byte": 512}}, "children": [{"type": "FunctionDeclaration", "name": "formatDate", "range": {"start": {"line": 0, "column": 0, "byte": 0}, "end": {"line": 5, "column": 1, "byte": 128}}, "properties": {"params": [{"type": "Identifier", "name": "date"}], "body": {"type": "BlockStatement"}}, "text": "function formatDate(date) {\n  return date.toISOString().split('T')[0];\n}"}]}, "metrics": {"lines_of_code": 8, "total_lines": 15, "complexity": 1, "maintainability_index": 92.3, "function_count": 2, "class_count": 0, "comment_ratio": 0.15}, "chunks": [{"chunk_id": "chunk_j8k7l6m5n4o3p2q1", "content": "function formatDate(date) {\n  return date.toISOString().split('T')[0];\n}", "range": {"start": {"line": 0, "column": 0, "byte": 0}, "end": {"line": 2, "column": 1, "byte": 65}}, "type": "function", "language": "JavaScript", "context": {"parent_symbol": "formatDate", "imports": [], "dependencies": ["Date"]}}], "symbols": [{"name": "formatDate", "type": "function", "range": {"start": {"line": 0, "column": 0, "byte": 0}, "end": {"line": 2, "column": 1, "byte": 65}}, "visibility": "public", "signature": "function formatDate(date)", "documentation": null}]}], "metrics": {"total_files": 2, "total_lines": 23, "total_complexity": 3, "average_complexity": 1.5, "maintainability_score": 88.9, "technical_debt_minutes": 15, "test_coverage_estimate": 0.65}, "languages": {"primary_language": "Python", "languages": {"Python": {"lines": 15, "files": 1, "percentage": 65.2, "bytes": 1024}, "JavaScript": {"lines": 8, "files": 1, "percentage": 34.8, "bytes": 512}}}, "embeddings": [{"chunk_id": "chunk_x1y2z3a4b5c6d7e8", "vector": [0.1, -0.2, 0.3, 0.05, -0.15, 0.25, 0.08, -0.12], "model": "text-embedding-ada-002", "metadata": {"tokens_used": 25, "created_at": "2025-01-15T10:30:00Z"}}, {"chunk_id": "chunk_m9n8o7p6q5r4s3t2", "vector": [0.2, -0.1, 0.4, 0.15, -0.05, 0.35, 0.18, -0.22], "model": "text-embedding-ada-002", "metadata": {"tokens_used": 32, "created_at": "2025-01-15T10:30:01Z"}}, {"chunk_id": "chunk_j8k7l6m5n4o3p2q1", "vector": [0.15, -0.25, 0.35, 0.1, -0.2, 0.3, 0.12, -0.18], "model": "text-embedding-ada-002", "metadata": {"tokens_used": 18, "created_at": "2025-01-15T10:30:02Z"}}], "patterns": [{"pattern_id": "singleton_detected_001", "pattern_type": "design_pattern", "confidence": 0.85, "location": {"file_path": "src/main.py", "range": {"start": {"line": 5, "column": 0, "byte": 100}, "end": {"line": 9, "column": 20, "byte": 180}}}, "description": "Potential Singleton pattern detected in Calculator class"}]}, "metadata": {"analysis_id": "analysis_z9y8x7w6v5u4t3s2", "version": "1.2.3", "timestamp": "2025-01-15T10:30:05Z", "duration_ms": 45000, "performance": {"parsing_time_ms": 35000, "embedding_time_ms": 8000, "total_memory_mb": 256.5, "files_per_second": 2.4, "cache_hit_ratio": 0.15}, "warnings": [{"code": "LARGE_FILE", "message": "File exceeds recommended size limit", "file_path": "src/large_file.py"}, {"code": "UNSUPPORTED_LANGUAGE", "message": "Language not fully supported for advanced analysis", "file_path": "config.toml"}]}}