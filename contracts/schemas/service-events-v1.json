{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://ccl.dev/schemas/service-events-v1.json", "title": "CCL Service Events", "description": "Event format for CCL service-to-service communication via event bus", "version": "1.0.0", "type": "object", "required": ["event_id", "event_type", "source_service", "timestamp", "data"], "properties": {"event_id": {"type": "string", "description": "Unique event identifier", "pattern": "^event_[a-zA-Z0-9]{16}$"}, "event_type": {"type": "string", "enum": ["repository.analysis.started", "repository.analysis.completed", "repository.analysis.failed", "pattern.detection.started", "pattern.detection.completed", "pattern.detection.failed", "query.processed", "query.failed", "pattern.published", "pattern.purchased", "user.registered", "organization.created"], "description": "Type of event"}, "source_service": {"type": "string", "enum": ["repository-analysis", "query-intelligence", "pattern-detection", "marketplace", "auth-service"], "description": "Service that generated the event"}, "timestamp": {"type": "string", "format": "date-time", "description": "Event timestamp"}, "correlation_id": {"type": "string", "description": "Request correlation ID for tracing", "pattern": "^[a-zA-Z0-9-]{8,64}$"}, "data": {"type": "object", "description": "Event-specific data", "oneOf": [{"$ref": "#/definitions/RepositoryAnalysisEvent"}, {"$ref": "#/definitions/PatternDetectionEvent"}, {"$ref": "#/definitions/QueryEvent"}, {"$ref": "#/definitions/MarketplaceEvent"}, {"$ref": "#/definitions/UserEvent"}]}, "metadata": {"type": "object", "description": "Event metadata", "properties": {"version": {"type": "string"}, "environment": {"type": "string", "enum": ["development", "staging", "production"]}, "region": {"type": "string"}, "trace_id": {"type": "string"}, "user_id": {"type": "string"}, "organization_id": {"type": "string"}}}}, "definitions": {"RepositoryAnalysisEvent": {"type": "object", "description": "Repository analysis events", "required": ["repository_id", "analysis_id"], "properties": {"repository_id": {"type": "string", "pattern": "^repo_[a-zA-Z0-9]{16}$"}, "analysis_id": {"type": "string", "pattern": "^analysis_[a-zA-Z0-9]{16}$"}, "repository_url": {"type": "string", "format": "uri"}, "branch": {"type": "string"}, "commit": {"type": "string", "pattern": "^[a-f0-9]{40}$"}, "status": {"type": "string", "enum": ["started", "in_progress", "completed", "failed", "cancelled"]}, "progress": {"type": "object", "properties": {"percentage": {"type": "number", "minimum": 0, "maximum": 100}, "current_step": {"type": "string"}, "files_processed": {"type": "integer", "minimum": 0}, "total_files": {"type": "integer", "minimum": 0}}}, "results": {"type": "object", "description": "Analysis results (for completed events)", "properties": {"total_files": {"type": "integer", "minimum": 0}, "total_lines": {"type": "integer", "minimum": 0}, "languages": {"type": "array", "items": {"type": "string"}}, "complexity_score": {"type": "number", "minimum": 0}, "quality_score": {"type": "number", "minimum": 0, "maximum": 100}, "patterns_detected": {"type": "integer", "minimum": 0}}}, "error": {"type": "object", "description": "Error information (for failed events)", "properties": {"error_code": {"type": "string"}, "message": {"type": "string"}, "retryable": {"type": "boolean"}}}, "processing_time_ms": {"type": "integer", "minimum": 0}}}, "PatternDetectionEvent": {"type": "object", "description": "Pattern detection events", "required": ["repository_id", "request_id"], "properties": {"repository_id": {"type": "string", "pattern": "^repo_[a-zA-Z0-9]{16}$"}, "analysis_id": {"type": "string", "pattern": "^analysis_[a-zA-Z0-9]{16}$"}, "request_id": {"type": "string", "pattern": "^req_[a-zA-Z0-9]{16}$"}, "status": {"type": "string", "enum": ["started", "in_progress", "completed", "failed", "cancelled"]}, "detection_config": {"type": "object", "properties": {"enabled_detectors": {"type": "array", "items": {"type": "string"}}, "confidence_threshold": {"type": "number", "minimum": 0, "maximum": 1}}}, "results": {"type": "object", "description": "Detection results (for completed events)", "properties": {"total_patterns": {"type": "integer", "minimum": 0}, "patterns_by_type": {"type": "object", "patternProperties": {"^[a-zA-Z_]+$": {"type": "integer", "minimum": 0}}}, "quality_score": {"type": "number", "minimum": 0, "maximum": 100}, "high_severity_count": {"type": "integer", "minimum": 0}, "recommendations_count": {"type": "integer", "minimum": 0}}}, "error": {"type": "object", "description": "Error information (for failed events)", "properties": {"error_code": {"type": "string"}, "message": {"type": "string"}, "retryable": {"type": "boolean"}}}, "processing_time_ms": {"type": "integer", "minimum": 0}}}, "QueryEvent": {"type": "object", "description": "Query processing events", "required": ["query_id", "repository_id"], "properties": {"query_id": {"type": "string", "pattern": "^query_[a-zA-Z0-9]{16}$"}, "repository_id": {"type": "string", "pattern": "^repo_[a-zA-Z0-9]{16}$"}, "session_id": {"type": "string"}, "query_text": {"type": "string", "maxLength": 500}, "query_intent": {"type": "string", "enum": ["EXPLAIN", "FIND", "DEBUG", "IMPLEMENT", "REFACTOR", "ARCHITECTURE", "USAGE"]}, "status": {"type": "string", "enum": ["started", "processing", "completed", "failed"]}, "results": {"type": "object", "description": "Query results (for completed events)", "properties": {"answer_length": {"type": "integer", "minimum": 0}, "confidence_score": {"type": "number", "minimum": 0, "maximum": 1}, "sources_used": {"type": "integer", "minimum": 0}, "cache_hit": {"type": "boolean"}, "tokens_used": {"type": "integer", "minimum": 0}}}, "error": {"type": "object", "description": "Error information (for failed events)", "properties": {"error_code": {"type": "string"}, "message": {"type": "string"}, "retryable": {"type": "boolean"}}}, "processing_time_ms": {"type": "integer", "minimum": 0}}}, "MarketplaceEvent": {"type": "object", "description": "Marketplace events", "required": ["pattern_id"], "properties": {"pattern_id": {"type": "string", "pattern": "^mp_pattern_[a-zA-Z0-9]{16}$"}, "pattern_name": {"type": "string"}, "author_id": {"type": "string"}, "category": {"type": "string"}, "price": {"type": "object", "properties": {"amount": {"type": "number", "minimum": 0}, "currency": {"type": "string", "pattern": "^[A-Z]{3}$"}}}, "purchase_info": {"type": "object", "description": "Purchase information (for purchase events)", "properties": {"purchase_id": {"type": "string"}, "buyer_id": {"type": "string"}, "license_type": {"type": "string"}, "seats": {"type": "integer", "minimum": 1}}}, "publication_info": {"type": "object", "description": "Publication information (for publish events)", "properties": {"version": {"type": "string"}, "quality_score": {"type": "number", "minimum": 0, "maximum": 100}, "review_status": {"type": "string", "enum": ["pending", "approved", "rejected"]}}}}}, "UserEvent": {"type": "object", "description": "User and organization events", "properties": {"user_id": {"type": "string"}, "organization_id": {"type": "string"}, "email": {"type": "string", "format": "email"}, "plan": {"type": "string", "enum": ["free", "pro", "enterprise"]}, "registration_info": {"type": "object", "properties": {"source": {"type": "string"}, "referrer": {"type": "string"}, "trial_started": {"type": "boolean"}}}, "organization_info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "string", "enum": ["1-10", "11-50", "51-200", "201-1000", "1000+"]}, "industry": {"type": "string"}}}}}}}