{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://ccl.dev/schemas/marketplace-pattern-v1.json", "title": "Marketplace Pattern", "description": "Pattern format for CCL Marketplace, derived from Pattern Detection output", "version": "1.0.0", "type": "object", "required": ["id", "name", "description", "category", "languages", "price", "author", "quality_metrics"], "properties": {"id": {"type": "string", "description": "Unique marketplace pattern identifier", "pattern": "^mp_pattern_[a-zA-Z0-9]{16}$"}, "name": {"type": "string", "description": "Pattern name", "minLength": 3, "maxLength": 100}, "description": {"type": "string", "description": "Pattern description", "minLength": 10, "maxLength": 1000}, "category": {"type": "string", "enum": ["architecture", "security", "performance", "testing", "data-access", "api", "ui", "devops", "concurrency", "design-patterns"], "description": "Pattern category"}, "languages": {"type": "array", "description": "Supported programming languages", "items": {"type": "string"}, "minItems": 1, "maxItems": 10}, "tags": {"type": "array", "description": "Searchable tags", "items": {"type": "string"}, "maxItems": 20}, "price": {"$ref": "#/definitions/PriceInfo"}, "author": {"$ref": "#/definitions/AuthorInfo"}, "quality_metrics": {"$ref": "#/definitions/QualityMetrics"}, "pattern_data": {"$ref": "#/definitions/PatternData"}, "marketplace_metadata": {"$ref": "#/definitions/MarketplaceMetadata"}, "licensing": {"$ref": "#/definitions/LicensingInfo"}, "support": {"$ref": "#/definitions/SupportInfo"}}, "definitions": {"PriceInfo": {"type": "object", "description": "Pattern pricing information", "required": ["amount", "currency", "license_type"], "properties": {"amount": {"type": "number", "minimum": 0, "maximum": 10000, "description": "Price amount"}, "currency": {"type": "string", "pattern": "^[A-Z]{3}$", "description": "ISO 4217 currency code"}, "license_type": {"type": "string", "enum": ["individual", "team", "enterprise", "open-source"], "description": "License type"}, "pricing_model": {"type": "string", "enum": ["one-time", "subscription", "usage-based", "freemium"], "description": "Pricing model"}, "discount_info": {"type": "object", "description": "Discount information", "properties": {"bulk_discount": {"type": "number", "minimum": 0, "maximum": 1}, "educational_discount": {"type": "number", "minimum": 0, "maximum": 1}, "promotional_price": {"type": "number", "minimum": 0}, "promotion_expires": {"type": "string", "format": "date-time"}}}}}, "AuthorInfo": {"type": "object", "description": "Pattern author information", "required": ["id", "name", "reputation_score"], "properties": {"id": {"type": "string", "description": "Author identifier"}, "name": {"type": "string", "description": "Author display name"}, "email": {"type": "string", "format": "email", "description": "Author contact email"}, "organization": {"type": "string", "description": "Author organization"}, "reputation_score": {"type": "number", "minimum": 0, "maximum": 100, "description": "Author reputation score"}, "verified": {"type": "boolean", "description": "Whether author is verified"}, "profile_url": {"type": "string", "format": "uri", "description": "Author profile URL"}, "social_links": {"type": "object", "description": "Social media links", "properties": {"github": {"type": "string", "format": "uri"}, "linkedin": {"type": "string", "format": "uri"}, "twitter": {"type": "string", "format": "uri"}, "website": {"type": "string", "format": "uri"}}}}}, "QualityMetrics": {"type": "object", "description": "Pattern quality and performance metrics", "required": ["overall_score", "detection_accuracy", "false_positive_rate"], "properties": {"overall_score": {"type": "number", "minimum": 0, "maximum": 100, "description": "Overall quality score"}, "detection_accuracy": {"type": "number", "minimum": 0, "maximum": 1, "description": "Pattern detection accuracy"}, "false_positive_rate": {"type": "number", "minimum": 0, "maximum": 1, "description": "False positive rate"}, "precision": {"type": "number", "minimum": 0, "maximum": 1, "description": "Detection precision"}, "recall": {"type": "number", "minimum": 0, "maximum": 1, "description": "Detection recall"}, "performance_impact": {"type": "object", "description": "Performance impact metrics", "properties": {"cpu_overhead_percent": {"type": "number", "minimum": 0}, "memory_overhead_mb": {"type": "number", "minimum": 0}, "detection_time_ms": {"type": "number", "minimum": 0}}}, "validation_stats": {"type": "object", "description": "Validation statistics", "properties": {"test_cases_passed": {"type": "integer", "minimum": 0}, "test_cases_total": {"type": "integer", "minimum": 0}, "code_coverage_percent": {"type": "number", "minimum": 0, "maximum": 100}, "peer_reviews": {"type": "integer", "minimum": 0}}}}}, "PatternData": {"type": "object", "description": "Core pattern implementation data", "required": ["pattern_type", "implementation", "examples"], "properties": {"pattern_type": {"type": "string", "enum": ["design_pattern", "anti_pattern", "security_vulnerability", "performance_issue", "code_smell", "architectural_pattern", "test_pattern", "concurrency_pattern"], "description": "Type of pattern"}, "implementation": {"type": "object", "description": "Pattern implementation details", "required": ["detection_rules", "confidence_threshold"], "properties": {"detection_rules": {"type": "array", "description": "Pattern detection rules", "items": {"type": "object", "required": ["rule_type", "rule_definition"], "properties": {"rule_type": {"type": "string", "enum": ["ast_pattern", "regex", "ml_model", "heuristic"]}, "rule_definition": {"type": "string"}, "weight": {"type": "number", "minimum": 0, "maximum": 1}, "language_specific": {"type": "boolean"}}}}, "confidence_threshold": {"type": "number", "minimum": 0, "maximum": 1, "description": "Minimum confidence for detection"}, "ml_model_info": {"type": "object", "description": "ML model information (if applicable)", "properties": {"model_type": {"type": "string"}, "model_version": {"type": "string"}, "training_data_size": {"type": "integer", "minimum": 0}, "feature_set": {"type": "array", "items": {"type": "string"}}}}}}, "examples": {"type": "array", "description": "Pattern examples", "items": {"type": "object", "required": ["title", "code", "language", "explanation"], "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "code": {"type": "string", "maxLength": 5000}, "language": {"type": "string"}, "explanation": {"type": "string"}, "before_after": {"type": "object", "description": "Before/after refactoring example", "properties": {"before": {"type": "string"}, "after": {"type": "string"}, "improvement_notes": {"type": "string"}}}}}, "minItems": 1, "maxItems": 10}, "documentation": {"type": "object", "description": "Pattern documentation", "properties": {"overview": {"type": "string"}, "when_to_use": {"type": "string"}, "when_not_to_use": {"type": "string"}, "benefits": {"type": "array", "items": {"type": "string"}}, "drawbacks": {"type": "array", "items": {"type": "string"}}, "related_patterns": {"type": "array", "items": {"type": "string"}}, "references": {"type": "array", "items": {"type": "string"}}}}, "customization": {"type": "object", "description": "Pattern customization options", "properties": {"configurable_parameters": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "default_value": {"type": "string"}, "description": {"type": "string"}}}}, "language_adaptations": {"type": "object", "patternProperties": {"^[a-zA-Z][a-zA-Z0-9+#-]*$": {"type": "object", "properties": {"specific_rules": {"type": "array", "items": {"type": "string"}}, "examples": {"type": "array", "items": {"type": "string"}}}}}}}}}}, "MarketplaceMetadata": {"type": "object", "description": "Marketplace-specific metadata", "required": ["status", "created_at", "updated_at"], "properties": {"status": {"type": "string", "enum": ["draft", "pending_review", "approved", "published", "deprecated", "removed"], "description": "Pattern status in marketplace"}, "created_at": {"type": "string", "format": "date-time", "description": "Pattern creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "description": "Last update timestamp"}, "published_at": {"type": "string", "format": "date-time", "description": "Publication timestamp"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$", "description": "Pattern version"}, "download_count": {"type": "integer", "minimum": 0, "description": "Total download count"}, "purchase_count": {"type": "integer", "minimum": 0, "description": "Total purchase count"}, "view_count": {"type": "integer", "minimum": 0, "description": "Total view count"}, "rating": {"type": "object", "description": "User ratings", "properties": {"average": {"type": "number", "minimum": 0, "maximum": 5}, "count": {"type": "integer", "minimum": 0}, "distribution": {"type": "object", "properties": {"5_star": {"type": "integer", "minimum": 0}, "4_star": {"type": "integer", "minimum": 0}, "3_star": {"type": "integer", "minimum": 0}, "2_star": {"type": "integer", "minimum": 0}, "1_star": {"type": "integer", "minimum": 0}}}}}, "featured": {"type": "boolean", "description": "Whether pattern is featured"}, "trending": {"type": "boolean", "description": "Whether pattern is trending"}, "categories_secondary": {"type": "array", "description": "Secondary categories", "items": {"type": "string"}}}}, "LicensingInfo": {"type": "object", "description": "Pattern licensing information", "required": ["license_type", "terms"], "properties": {"license_type": {"type": "string", "enum": ["individual", "team", "enterprise", "open-source"], "description": "License type"}, "terms": {"type": "string", "description": "License terms and conditions"}, "usage_restrictions": {"type": "array", "description": "Usage restrictions", "items": {"type": "string"}}, "commercial_use": {"type": "boolean", "description": "Whether commercial use is allowed"}, "redistribution": {"type": "boolean", "description": "Whether redistribution is allowed"}, "modification": {"type": "boolean", "description": "Whether modification is allowed"}, "attribution_required": {"type": "boolean", "description": "Whether attribution is required"}, "seat_limits": {"type": "object", "description": "Seat-based licensing limits", "properties": {"max_seats": {"type": "integer", "minimum": 1}, "current_seats": {"type": "integer", "minimum": 0}, "seat_price": {"type": "number", "minimum": 0}}}}}, "SupportInfo": {"type": "object", "description": "Pattern support information", "properties": {"support_level": {"type": "string", "enum": ["community", "basic", "premium", "enterprise"], "description": "Level of support provided"}, "documentation_url": {"type": "string", "format": "uri", "description": "Documentation URL"}, "support_email": {"type": "string", "format": "email", "description": "Support contact email"}, "issue_tracker_url": {"type": "string", "format": "uri", "description": "Issue tracker URL"}, "community_forum_url": {"type": "string", "format": "uri", "description": "Community forum URL"}, "response_time_sla": {"type": "object", "description": "Support response time SLA", "properties": {"critical": {"type": "string"}, "high": {"type": "string"}, "medium": {"type": "string"}, "low": {"type": "string"}}}, "maintenance_schedule": {"type": "object", "description": "Maintenance and update schedule", "properties": {"update_frequency": {"type": "string"}, "last_updated": {"type": "string", "format": "date-time"}, "next_update": {"type": "string", "format": "date-time"}, "end_of_life": {"type": "string", "format": "date-time"}}}}}}}