{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://ccl.dev/schemas/error-response-v1.json", "title": "Unified Error Response", "description": "Standardized error response format for all CCL services", "version": "1.0.0", "type": "object", "required": ["error_id", "service", "error_type", "message", "retryable", "timestamp"], "properties": {"error_id": {"type": "string", "description": "Unique error identifier", "pattern": "^error_[a-zA-Z0-9]{16}$"}, "service": {"type": "string", "enum": ["repository-analysis", "query-intelligence", "pattern-detection", "marketplace"], "description": "Service that generated the error"}, "error_type": {"type": "string", "enum": ["validation", "authentication", "authorization", "not_found", "conflict", "rate_limit", "timeout", "internal", "external_dependency", "quota_exceeded", "maintenance"], "description": "Type of error"}, "error_code": {"type": "string", "description": "Service-specific error code", "pattern": "^[A-Z][A-Z0-9_]{2,49}$"}, "message": {"type": "string", "description": "Technical error message for developers", "maxLength": 500}, "user_message": {"type": "string", "description": "User-friendly error message", "maxLength": 200}, "retryable": {"type": "boolean", "description": "Whether the operation can be retried"}, "retry_after_seconds": {"type": "integer", "minimum": 0, "maximum": 3600, "description": "Recommended retry delay in seconds"}, "correlation_id": {"type": "string", "description": "Request correlation ID for tracing", "pattern": "^[a-zA-Z0-9-]{8,64}$"}, "timestamp": {"type": "string", "format": "date-time", "description": "Error occurrence timestamp"}, "context": {"type": "object", "description": "Additional error context", "properties": {"request_id": {"type": "string"}, "user_id": {"type": "string"}, "organization_id": {"type": "string"}, "resource_id": {"type": "string"}, "operation": {"type": "string"}, "parameters": {"type": "object", "description": "Sanitized request parameters", "additionalProperties": true}}}, "details": {"type": "object", "description": "Detailed error information", "properties": {"field_errors": {"type": "array", "description": "Field-specific validation errors", "items": {"type": "object", "required": ["field", "message"], "properties": {"field": {"type": "string"}, "message": {"type": "string"}, "code": {"type": "string"}, "value": {"type": "string"}}}}, "constraint_violations": {"type": "array", "description": "Business rule violations", "items": {"type": "object", "required": ["constraint", "message"], "properties": {"constraint": {"type": "string"}, "message": {"type": "string"}, "current_value": {"type": "string"}, "expected_value": {"type": "string"}}}}, "dependency_errors": {"type": "array", "description": "External dependency errors", "items": {"type": "object", "required": ["service", "error"], "properties": {"service": {"type": "string"}, "error": {"type": "string"}, "status_code": {"type": "integer"}, "response_time_ms": {"type": "integer"}}}}}}, "suggestions": {"type": "array", "description": "Suggested actions to resolve the error", "items": {"type": "object", "required": ["action", "description"], "properties": {"action": {"type": "string"}, "description": {"type": "string"}, "url": {"type": "string", "format": "uri"}, "priority": {"type": "string", "enum": ["low", "medium", "high"]}}}}, "support": {"type": "object", "description": "Support information", "properties": {"contact_support": {"type": "boolean"}, "support_url": {"type": "string", "format": "uri"}, "documentation_url": {"type": "string", "format": "uri"}, "status_page_url": {"type": "string", "format": "uri"}}}, "metadata": {"type": "object", "description": "Additional metadata", "properties": {"service_version": {"type": "string"}, "api_version": {"type": "string"}, "environment": {"type": "string", "enum": ["development", "staging", "production"]}, "region": {"type": "string"}, "trace_id": {"type": "string"}, "span_id": {"type": "string"}}}}}