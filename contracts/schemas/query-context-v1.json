{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://ccl.dev/schemas/query-context-v1.json", "title": "Query Intelligence Context Input", "description": "Context format for Query Intelligence service, derived from Repository Analysis output", "version": "1.0.0", "type": "object", "required": ["repository_id", "analysis_id", "ast_summary", "embeddings", "metadata"], "properties": {"repository_id": {"type": "string", "description": "Repository identifier", "pattern": "^repo_[a-zA-Z0-9]{16}$"}, "analysis_id": {"type": "string", "description": "Analysis identifier from Repository Analysis", "pattern": "^analysis_[a-zA-Z0-9]{16}$"}, "ast_summary": {"type": "object", "description": "Processed AST data optimized for querying", "required": ["symbols", "structure", "metrics"], "properties": {"symbols": {"type": "array", "description": "Searchable symbol index", "items": {"$ref": "#/definitions/SearchableSymbol"}}, "structure": {"type": "object", "description": "Repository structure summary", "required": ["directories", "modules", "dependencies"], "properties": {"directories": {"type": "array", "description": "Directory structure", "items": {"$ref": "#/definitions/DirectoryNode"}}, "modules": {"type": "array", "description": "Module/package structure", "items": {"$ref": "#/definitions/ModuleInfo"}}, "dependencies": {"type": "array", "description": "External dependencies", "items": {"$ref": "#/definitions/Dependency"}}}}, "metrics": {"type": "object", "description": "Queryable metrics", "required": ["complexity_distribution", "language_stats", "quality_scores"], "properties": {"complexity_distribution": {"type": "object", "description": "Complexity distribution across files", "properties": {"low": {"type": "integer", "minimum": 0}, "medium": {"type": "integer", "minimum": 0}, "high": {"type": "integer", "minimum": 0}, "very_high": {"type": "integer", "minimum": 0}}}, "language_stats": {"type": "object", "description": "Language statistics for queries", "patternProperties": {"^[a-zA-Z][a-zA-Z0-9+#-]*$": {"type": "object", "properties": {"files": {"type": "integer", "minimum": 0}, "lines": {"type": "integer", "minimum": 0}, "functions": {"type": "integer", "minimum": 0}, "classes": {"type": "integer", "minimum": 0}}}}}, "quality_scores": {"type": "object", "description": "Code quality metrics", "properties": {"maintainability": {"type": "number", "minimum": 0, "maximum": 100}, "test_coverage": {"type": "number", "minimum": 0, "maximum": 100}, "documentation_coverage": {"type": "number", "minimum": 0, "maximum": 100}, "technical_debt_ratio": {"type": "number", "minimum": 0, "maximum": 1}}}}}}}, "embeddings": {"type": "array", "description": "Semantic embeddings for code chunks", "items": {"$ref": "#/definitions/QueryEmbedding"}, "maxItems": 10000}, "patterns": {"type": "array", "description": "Detected patterns for query enhancement", "items": {"$ref": "#/definitions/QueryPattern"}}, "metadata": {"type": "object", "description": "Context metadata", "required": ["created_at", "version", "source_commit"], "properties": {"created_at": {"type": "string", "format": "date-time", "description": "Context creation timestamp"}, "version": {"type": "string", "description": "Context schema version", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "source_commit": {"type": "string", "description": "Source repository commit", "pattern": "^[a-f0-9]{40}$"}, "processing_stats": {"type": "object", "description": "Processing statistics", "properties": {"chunks_processed": {"type": "integer", "minimum": 0}, "embeddings_generated": {"type": "integer", "minimum": 0}, "symbols_indexed": {"type": "integer", "minimum": 0}, "processing_time_ms": {"type": "integer", "minimum": 0}}}, "cache_info": {"type": "object", "description": "Cache information for optimization", "properties": {"cache_key": {"type": "string"}, "ttl_seconds": {"type": "integer", "minimum": 0}, "last_updated": {"type": "string", "format": "date-time"}}}}}}, "definitions": {"SearchableSymbol": {"type": "object", "description": "Symbol optimized for search and querying", "required": ["name", "type", "file_path", "signature"], "properties": {"name": {"type": "string", "description": "Symbol name"}, "type": {"type": "string", "enum": ["function", "method", "class", "interface", "variable", "constant", "type", "namespace"], "description": "Symbol type"}, "file_path": {"type": "string", "description": "File containing the symbol"}, "signature": {"type": "string", "description": "Full symbol signature"}, "visibility": {"type": "string", "enum": ["public", "private", "protected", "internal"], "description": "Symbol visibility"}, "documentation": {"type": "string", "description": "Symbol documentation"}, "usage_count": {"type": "integer", "minimum": 0, "description": "Number of times symbol is referenced"}, "complexity": {"type": "integer", "minimum": 0, "description": "Symbol complexity score"}, "tags": {"type": "array", "description": "Semantic tags for enhanced search", "items": {"type": "string"}}, "related_symbols": {"type": "array", "description": "Related symbols for context", "items": {"type": "string"}}}}, "DirectoryNode": {"type": "object", "description": "Directory structure node", "required": ["path", "type"], "properties": {"path": {"type": "string", "description": "Directory path"}, "type": {"type": "string", "enum": ["directory", "file"], "description": "Node type"}, "children": {"type": "array", "description": "Child nodes", "items": {"$ref": "#/definitions/DirectoryNode"}}, "metadata": {"type": "object", "description": "Directory metadata", "properties": {"file_count": {"type": "integer", "minimum": 0}, "total_lines": {"type": "integer", "minimum": 0}, "primary_language": {"type": "string"}, "purpose": {"type": "string", "enum": ["source", "test", "config", "docs", "build", "other"]}}}}}, "ModuleInfo": {"type": "object", "description": "Module or package information", "required": ["name", "path", "type"], "properties": {"name": {"type": "string", "description": "Module name"}, "path": {"type": "string", "description": "Module path"}, "type": {"type": "string", "enum": ["package", "module", "namespace", "library"], "description": "Module type"}, "exports": {"type": "array", "description": "Exported symbols", "items": {"type": "string"}}, "imports": {"type": "array", "description": "Imported modules", "items": {"type": "string"}}, "description": {"type": "string", "description": "Module description"}, "version": {"type": "string", "description": "Module version (if applicable)"}}}, "Dependency": {"type": "object", "description": "External dependency information", "required": ["name", "type"], "properties": {"name": {"type": "string", "description": "Dependency name"}, "type": {"type": "string", "enum": ["npm", "pip", "cargo", "go", "maven", "nuget", "gem", "other"], "description": "Dependency type/manager"}, "version": {"type": "string", "description": "Dependency version"}, "scope": {"type": "string", "enum": ["runtime", "development", "test", "build"], "description": "Dependency scope"}, "usage_locations": {"type": "array", "description": "Files where dependency is used", "items": {"type": "string"}}}}, "QueryEmbedding": {"type": "object", "description": "Embedding optimized for query processing", "required": ["chunk_id", "content", "vector", "metadata"], "properties": {"chunk_id": {"type": "string", "description": "Unique chunk identifier", "pattern": "^chunk_[a-zA-Z0-9]{16}$"}, "content": {"type": "string", "description": "Code content", "maxLength": 8192}, "vector": {"type": "array", "description": "Embedding vector", "items": {"type": "number"}, "minItems": 768, "maxItems": 1536}, "metadata": {"type": "object", "description": "Chunk metadata for query optimization", "required": ["file_path", "language", "chunk_type"], "properties": {"file_path": {"type": "string"}, "language": {"type": "string"}, "chunk_type": {"type": "string", "enum": ["function", "class", "method", "block", "comment", "import"]}, "symbols": {"type": "array", "description": "Symbols contained in chunk", "items": {"type": "string"}}, "complexity": {"type": "integer", "minimum": 0}, "importance_score": {"type": "number", "minimum": 0, "maximum": 1, "description": "Chunk importance for retrieval"}, "keywords": {"type": "array", "description": "Extracted keywords for search", "items": {"type": "string"}}, "context": {"type": "object", "description": "Additional context", "properties": {"parent_symbol": {"type": "string"}, "related_chunks": {"type": "array", "items": {"type": "string"}}, "dependencies": {"type": "array", "items": {"type": "string"}}}}}}}}, "QueryPattern": {"type": "object", "description": "Pattern information for query enhancement", "required": ["pattern_id", "pattern_type", "confidence", "description"], "properties": {"pattern_id": {"type": "string", "description": "Pattern identifier"}, "pattern_type": {"type": "string", "enum": ["design_pattern", "anti_pattern", "security_issue", "performance_issue", "code_smell"], "description": "Pattern type"}, "pattern_name": {"type": "string", "description": "Human-readable pattern name"}, "confidence": {"type": "number", "minimum": 0, "maximum": 1, "description": "Detection confidence"}, "description": {"type": "string", "description": "Pattern description"}, "locations": {"type": "array", "description": "Pattern locations in code", "items": {"type": "object", "required": ["file_path", "chunk_id"], "properties": {"file_path": {"type": "string"}, "chunk_id": {"type": "string"}, "line_range": {"type": "object", "properties": {"start": {"type": "integer", "minimum": 0}, "end": {"type": "integer", "minimum": 0}}}}}}, "impact": {"type": "string", "enum": ["low", "medium", "high", "critical"], "description": "Pattern impact level"}, "recommendations": {"type": "array", "description": "Improvement recommendations", "items": {"type": "string"}}, "related_patterns": {"type": "array", "description": "Related pattern IDs", "items": {"type": "string"}}}}}}