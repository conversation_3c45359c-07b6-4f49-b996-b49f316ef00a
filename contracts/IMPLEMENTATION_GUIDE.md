# CCL Integration Contracts Implementation Guide

## Overview
This guide provides step-by-step instructions for implementing CCL service integration contracts, ensuring seamless communication between all four core services.

## Quick Start

### 1. Validate Your Environment
```bash
# Clone the contracts repository
git clone https://github.com/ccl-platform/contracts.git
cd contracts

# Install validation tools
npm install -g ajv-cli
pip install jsonschema requests

# Validate all schemas
./scripts/validate_all_schemas.sh
```

### 2. Choose Your Integration
Select the integration you're implementing:

| Integration | Producer | Consumer | Schema Files |
|-------------|----------|----------|--------------|
| **Repository → Query** | Repository Analysis API | Query Intelligence | `ast-output-v1.json` → `query-context-v1.json` |
| **Repository → Pattern** | Repository Analysis API | Pattern Detection | `ast-output-v1.json` → `pattern-input-v1.json` |
| **Pattern → Query** | Pattern Detection | Query Intelligence | `pattern-output-v1.json` → `query-context-v1.json` |
| **Pattern → Marketplace** | Pattern Detection | Marketplace API | `pattern-output-v1.json` → `marketplace-pattern-v1.json` |

### 3. Implement Schema Validation
Add schema validation to your service:

#### Python Implementation
```python
import jsonschema
import json
from typing import Dict, Any, Tuple

class ContractValidator:
    def __init__(self, schema_path: str):
        with open(schema_path, 'r') as f:
            self.schema = json.load(f)
        self.validator = jsonschema.Draft7Validator(self.schema)
    
    def validate(self, data: Dict[Any, Any]) -> Tuple[bool, str]:
        try:
            self.validator.validate(data)
            return True, "Valid"
        except jsonschema.ValidationError as e:
            return False, f"Validation error: {e.message}"

# Usage example
ast_validator = ContractValidator("contracts/schemas/ast-output-v1.json")
is_valid, message = ast_validator.validate(ast_output_data)
if not is_valid:
    raise ValueError(f"Invalid AST output: {message}")
```

#### Rust Implementation
```rust
use serde_json::Value;
use jsonschema::{JSONSchema, ValidationError};
use std::fs;

pub struct ContractValidator {
    schema: JSONSchema,
}

impl ContractValidator {
    pub fn new(schema_path: &str) -> Result<Self, Box<dyn std::error::Error>> {
        let schema_content = fs::read_to_string(schema_path)?;
        let schema: Value = serde_json::from_str(&schema_content)?;
        let compiled = JSONSchema::compile(&schema)?;
        
        Ok(ContractValidator { schema: compiled })
    }
    
    pub fn validate(&self, data: &Value) -> Result<(), Vec<ValidationError>> {
        match self.schema.validate(data) {
            Ok(_) => Ok(()),
            Err(errors) => Err(errors.collect()),
        }
    }
}

// Usage example
let validator = ContractValidator::new("contracts/schemas/ast-output-v1.json")?;
match validator.validate(&ast_output_json) {
    Ok(_) => println!("Valid AST output"),
    Err(errors) => {
        for error in errors {
            eprintln!("Validation error: {}", error);
        }
    }
}
```

#### Go Implementation
```go
package contracts

import (
    "encoding/json"
    "fmt"
    "io/ioutil"
    "github.com/xeipuuv/gojsonschema"
)

type ContractValidator struct {
    schema *gojsonschema.Schema
}

func NewContractValidator(schemaPath string) (*ContractValidator, error) {
    schemaBytes, err := ioutil.ReadFile(schemaPath)
    if err != nil {
        return nil, err
    }
    
    schemaLoader := gojsonschema.NewBytesLoader(schemaBytes)
    schema, err := gojsonschema.NewSchema(schemaLoader)
    if err != nil {
        return nil, err
    }
    
    return &ContractValidator{schema: schema}, nil
}

func (cv *ContractValidator) Validate(data interface{}) error {
    dataLoader := gojsonschema.NewGoLoader(data)
    result, err := cv.schema.Validate(dataLoader)
    if err != nil {
        return err
    }
    
    if !result.Valid() {
        var errors []string
        for _, desc := range result.Errors() {
            errors = append(errors, desc.String())
        }
        return fmt.Errorf("validation errors: %v", errors)
    }
    
    return nil
}

// Usage example
validator, err := NewContractValidator("contracts/schemas/marketplace-pattern-v1.json")
if err != nil {
    return err
}

if err := validator.Validate(marketplacePattern); err != nil {
    return fmt.Errorf("invalid marketplace pattern: %w", err)
}
```

## Implementation by Service

### Repository Analysis API (Producer)

#### 1. AST Output Generation
```rust
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};

#[derive(Serialize, Deserialize)]
pub struct RepositoryAnalysisOutput {
    pub repository: RepositoryInfo,
    pub analysis: AnalysisResults,
    pub metadata: AnalysisMetadata,
}

impl RepositoryAnalysisOutput {
    pub fn new(repo_url: String, commit: String, branch: String) -> Self {
        Self {
            repository: RepositoryInfo {
                id: generate_repo_id(),
                url: repo_url,
                commit,
                branch,
                size_bytes: 0,
                clone_time_ms: 0,
            },
            analysis: AnalysisResults::default(),
            metadata: AnalysisMetadata {
                analysis_id: generate_analysis_id(),
                version: env!("CARGO_PKG_VERSION").to_string(),
                timestamp: Utc::now(),
                duration_ms: 0,
                performance: PerformanceMetrics::default(),
                warnings: Vec::new(),
            },
        }
    }
    
    pub fn validate_contract(&self) -> Result<(), String> {
        let validator = ContractValidator::new("contracts/schemas/ast-output-v1.json")
            .map_err(|e| format!("Failed to load schema: {}", e))?;
        
        let json_value = serde_json::to_value(self)
            .map_err(|e| format!("Failed to serialize: {}", e))?;
        
        validator.validate(&json_value)
            .map_err(|errors| format!("Contract validation failed: {:?}", errors))
    }
}
```

#### 2. Performance Monitoring
```rust
use std::time::Instant;
use prometheus::{Counter, Histogram, register_counter, register_histogram};

lazy_static! {
    static ref CONTRACT_VALIDATION_DURATION: Histogram = register_histogram!(
        "contract_validation_duration_seconds",
        "Time spent validating contracts"
    ).unwrap();
    
    static ref CONTRACT_VIOLATIONS: Counter = register_counter!(
        "contract_violations_total",
        "Total number of contract violations"
    ).unwrap();
}

impl RepositoryAnalysisOutput {
    pub fn validate_and_emit(&self) -> Result<(), String> {
        let start = Instant::now();
        
        match self.validate_contract() {
            Ok(_) => {
                CONTRACT_VALIDATION_DURATION.observe(start.elapsed().as_secs_f64());
                Ok(())
            },
            Err(e) => {
                CONTRACT_VIOLATIONS.inc();
                CONTRACT_VALIDATION_DURATION.observe(start.elapsed().as_secs_f64());
                Err(e)
            }
        }
    }
}
```

### Query Intelligence (Consumer)

#### 1. Input Transformation
```python
from typing import Dict, Any, List
import logging

class ASTToQueryContextTransformer:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def transform(self, ast_output: Dict[str, Any]) -> Dict[str, Any]:
        """Transform AST output to Query Intelligence context format."""
        
        # Validate input contract
        ast_validator = ContractValidator("contracts/schemas/ast-output-v1.json")
        is_valid, message = ast_validator.validate(ast_output)
        if not is_valid:
            raise ValueError(f"Invalid AST input: {message}")
        
        # Transform data
        query_context = {
            "repository_id": ast_output["repository"]["id"],
            "analysis_id": ast_output["metadata"]["analysis_id"],
            "ast_summary": self._build_ast_summary(ast_output["analysis"]),
            "embeddings": self._transform_embeddings(ast_output["analysis"]["embeddings"]),
            "patterns": self._extract_patterns(ast_output["analysis"].get("patterns", [])),
            "metadata": self._build_metadata(ast_output)
        }
        
        # Validate output contract
        context_validator = ContractValidator("contracts/schemas/query-context-v1.json")
        is_valid, message = context_validator.validate(query_context)
        if not is_valid:
            raise ValueError(f"Invalid query context output: {message}")
        
        return query_context
    
    def _build_ast_summary(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Build searchable AST summary from analysis data."""
        symbols = []
        
        for file_analysis in analysis["files"]:
            for symbol in file_analysis.get("symbols", []):
                symbols.append({
                    "name": symbol["name"],
                    "type": symbol["type"],
                    "file_path": file_analysis["path"],
                    "signature": symbol["signature"],
                    "visibility": symbol.get("visibility", "public"),
                    "documentation": symbol.get("documentation"),
                    "usage_count": 0,  # To be computed
                    "complexity": 1,   # Default complexity
                    "tags": self._generate_symbol_tags(symbol),
                    "related_symbols": []  # To be computed
                })
        
        return {
            "symbols": symbols,
            "structure": self._build_structure_summary(analysis),
            "metrics": self._build_metrics_summary(analysis)
        }
```

#### 2. Error Handling
```python
from dataclasses import dataclass
from datetime import datetime
from typing import Optional

@dataclass
class CCLError:
    error_id: str
    service: str
    error_type: str
    message: str
    retryable: bool
    user_message: str
    correlation_id: str
    timestamp: datetime
    context: Dict[str, Any]

class QueryIntelligenceService:
    def process_query(self, query_request: Dict[str, Any]) -> Dict[str, Any]:
        try:
            # Validate input
            validator = ContractValidator("contracts/schemas/query-request-v1.json")
            is_valid, message = validator.validate(query_request)
            if not is_valid:
                raise ValidationError(f"Invalid query request: {message}")
            
            # Process query
            result = self._process_query_internal(query_request)
            
            # Validate output
            result_validator = ContractValidator("contracts/schemas/query-result-v1.json")
            is_valid, message = result_validator.validate(result)
            if not is_valid:
                self.logger.error(f"Invalid query result: {message}")
                # Don't fail the request, but log the violation
            
            return result
            
        except ValidationError as e:
            return self._create_error_response("validation", str(e), retryable=False)
        except TimeoutError as e:
            return self._create_error_response("timeout", str(e), retryable=True)
        except Exception as e:
            return self._create_error_response("internal", str(e), retryable=False)
    
    def _create_error_response(self, error_type: str, message: str, retryable: bool) -> Dict[str, Any]:
        error = CCLError(
            error_id=f"error_{generate_id()}",
            service="query-intelligence",
            error_type=error_type,
            message=message,
            retryable=retryable,
            user_message=self._get_user_friendly_message(error_type),
            correlation_id=self.current_correlation_id,
            timestamp=datetime.utcnow(),
            context={"operation": "process_query"}
        )
        
        return asdict(error)
```

### Pattern Detection (Producer/Consumer)

#### 1. Input Processing
```python
class PatternDetectionService:
    def __init__(self):
        self.input_validator = ContractValidator("contracts/schemas/pattern-input-v1.json")
        self.output_validator = ContractValidator("contracts/schemas/pattern-output-v1.json")
    
    def detect_patterns(self, pattern_input: Dict[str, Any]) -> Dict[str, Any]:
        # Validate input contract
        is_valid, message = self.input_validator.validate(pattern_input)
        if not is_valid:
            raise ValueError(f"Invalid pattern input: {message}")
        
        # Extract configuration
        config = pattern_input["detection_config"]
        enabled_detectors = config["enabled_detectors"]
        confidence_threshold = config["confidence_threshold"]
        
        # Process each file
        detected_patterns = []
        for file_data in pattern_input["ast_data"]["files"]:
            file_patterns = self._detect_patterns_in_file(
                file_data, 
                enabled_detectors, 
                confidence_threshold
            )
            detected_patterns.extend(file_patterns)
        
        # Build output
        pattern_output = {
            "request_id": pattern_input["request_id"],
            "repository_id": pattern_input["repository_id"],
            "analysis_id": pattern_input["analysis_id"],
            "patterns": detected_patterns,
            "summary": self._build_summary(detected_patterns),
            "metadata": self._build_output_metadata(pattern_input)
        }
        
        # Validate output contract
        is_valid, message = self.output_validator.validate(pattern_output)
        if not is_valid:
            self.logger.error(f"Invalid pattern output: {message}")
            # Continue but log the violation
        
        return pattern_output
```

### Marketplace API (Consumer)

#### 1. Pattern Publishing
```go
package marketplace

import (
    "encoding/json"
    "fmt"
    "time"
)

type MarketplaceService struct {
    validator *ContractValidator
    db        *Database
}

func NewMarketplaceService(db *Database) (*MarketplaceService, error) {
    validator, err := NewContractValidator("contracts/schemas/marketplace-pattern-v1.json")
    if err != nil {
        return nil, fmt.Errorf("failed to load schema: %w", err)
    }
    
    return &MarketplaceService{
        validator: validator,
        db:        db,
    }, nil
}

func (ms *MarketplaceService) PublishPattern(patternOutput map[string]interface{}) error {
    // Transform pattern detection output to marketplace format
    marketplacePattern, err := ms.transformToMarketplacePattern(patternOutput)
    if err != nil {
        return fmt.Errorf("transformation failed: %w", err)
    }
    
    // Validate contract
    if err := ms.validator.Validate(marketplacePattern); err != nil {
        return fmt.Errorf("contract validation failed: %w", err)
    }
    
    // Store in database
    if err := ms.db.StorePattern(marketplacePattern); err != nil {
        return fmt.Errorf("storage failed: %w", err)
    }
    
    // Emit event
    ms.emitPatternPublishedEvent(marketplacePattern)
    
    return nil
}

func (ms *MarketplaceService) transformToMarketplacePattern(input map[string]interface{}) (map[string]interface{}, error) {
    // Extract pattern data from detection output
    patterns, ok := input["patterns"].([]interface{})
    if !ok || len(patterns) == 0 {
        return nil, fmt.Errorf("no patterns found in input")
    }
    
    // Take the first high-quality pattern
    pattern := patterns[0].(map[string]interface{})
    
    // Build marketplace pattern
    marketplacePattern := map[string]interface{}{
        "id":          fmt.Sprintf("mp_pattern_%s", generateID()),
        "name":        pattern["pattern_name"],
        "description": pattern["description"],
        "category":    ms.mapPatternTypeToCategory(pattern["pattern_type"].(string)),
        "languages":   []string{"Python"}, // Extract from input
        "tags":        pattern["tags"],
        "price": map[string]interface{}{
            "amount":       29.99,
            "currency":     "USD",
            "license_type": "team",
        },
        "author": map[string]interface{}{
            "id":               "ccl_ai_001",
            "name":             "CCL Pattern AI",
            "reputation_score": 95.0,
            "verified":         true,
        },
        "quality_metrics": map[string]interface{}{
            "overall_score":       pattern["confidence"].(float64) * 100,
            "detection_accuracy":  pattern["confidence"],
            "false_positive_rate": 0.05,
        },
        "marketplace_metadata": map[string]interface{}{
            "status":       "published",
            "created_at":   time.Now().Format(time.RFC3339),
            "updated_at":   time.Now().Format(time.RFC3339),
            "version":      "1.0.0",
        },
    }
    
    return marketplacePattern, nil
}
```

## Testing Your Implementation

### 1. Unit Tests
```python
import unittest
from contracts.validation import ContractValidator

class TestContractCompliance(unittest.TestCase):
    def setUp(self):
        self.ast_validator = ContractValidator("contracts/schemas/ast-output-v1.json")
        self.query_validator = ContractValidator("contracts/schemas/query-context-v1.json")
    
    def test_ast_output_compliance(self):
        # Load test data
        with open("contracts/examples/ast-output-example.json") as f:
            test_data = json.load(f)
        
        # Validate
        is_valid, message = self.ast_validator.validate(test_data)
        self.assertTrue(is_valid, f"AST output validation failed: {message}")
    
    def test_transformation_preserves_data(self):
        # Test that transformation doesn't lose critical data
        ast_output = self.load_test_ast_output()
        query_context = transform_ast_to_query_context(ast_output)
        
        # Verify key data is preserved
        self.assertEqual(ast_output["repository"]["id"], query_context["repository_id"])
        self.assertEqual(len(ast_output["analysis"]["files"]), 
                        len([s for s in query_context["ast_summary"]["symbols"]]))
```

### 2. Integration Tests
```bash
#!/bin/bash
# Integration test script

echo "Starting integration tests..."

# Start test services
docker-compose -f docker-compose.test.yml up -d

# Wait for services to be ready
sleep 30

# Run contract tests
python scripts/test_repository_to_query_integration.py
python scripts/test_pattern_to_marketplace_integration.py

# Check performance
python scripts/test_integration_performance.py

# Cleanup
docker-compose -f docker-compose.test.yml down

echo "Integration tests completed"
```

## Monitoring and Observability

### 1. Contract Compliance Metrics
```python
from prometheus_client import Counter, Histogram, Gauge

# Metrics
contract_validations = Counter('contract_validations_total', 
                              'Total contract validations', 
                              ['service', 'schema', 'status'])

contract_validation_duration = Histogram('contract_validation_duration_seconds',
                                       'Time spent validating contracts',
                                       ['service', 'schema'])

contract_violations = Counter('contract_violations_total',
                            'Total contract violations',
                            ['service', 'schema', 'error_type'])

# Usage
def validate_with_metrics(data, schema_name, service_name):
    with contract_validation_duration.labels(service=service_name, schema=schema_name).time():
        try:
            validator.validate(data)
            contract_validations.labels(service=service_name, schema=schema_name, status='success').inc()
            return True
        except ValidationError as e:
            contract_validations.labels(service=service_name, schema=schema_name, status='failure').inc()
            contract_violations.labels(service=service_name, schema=schema_name, error_type=e.error_type).inc()
            return False
```

### 2. Performance Monitoring
```yaml
# Grafana dashboard configuration
dashboard:
  title: "CCL Contract Performance"
  panels:
    - title: "Contract Validation Success Rate"
      type: "stat"
      targets:
        - expr: "rate(contract_validations_total{status='success'}[5m]) / rate(contract_validations_total[5m]) * 100"
    
    - title: "Integration Latency"
      type: "graph"
      targets:
        - expr: "histogram_quantile(0.95, rate(integration_duration_seconds_bucket[5m]))"
        - expr: "histogram_quantile(0.99, rate(integration_duration_seconds_bucket[5m]))"
    
    - title: "Contract Violations by Service"
      type: "table"
      targets:
        - expr: "sum by (service, schema) (rate(contract_violations_total[1h]))"
```

## Troubleshooting

### Common Issues

1. **Schema Validation Failures**
   - Check field types and required fields
   - Verify enum values are correct
   - Ensure array size limits are respected

2. **Performance Issues**
   - Monitor payload sizes
   - Check for unnecessary data serialization
   - Verify caching is working correctly

3. **Version Compatibility**
   - Use compatibility matrix
   - Test with multiple schema versions
   - Implement graceful degradation

### Support Resources
- **Documentation**: https://docs.ccl.dev/contracts
- **Community Forum**: https://community.ccl.dev
- **Issue Tracker**: https://github.com/ccl-platform/contracts/issues
- **Support Email**: <EMAIL>

---

**Last Updated**: 2025-01-15  
**Version**: 1.0.0  
**Maintainer**: CCL Platform Team
