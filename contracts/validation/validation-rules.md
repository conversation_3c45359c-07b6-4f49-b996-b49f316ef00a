# CCL Contract Validation Rules

## Overview
This document defines validation rules, version management policies, and data constraints for all CCL service integration contracts.

## Version Management

### Semantic Versioning
All schemas follow semantic versioning (MAJOR.MINOR.PATCH):
- **MAJOR**: Breaking changes that require consumer updates
- **MINOR**: Backward-compatible additions (new optional fields)
- **PATCH**: Bug fixes and clarifications

### Version Compatibility Matrix
| Schema | Current | Supported Versions | Deprecation Date |
|--------|---------|-------------------|------------------|
| AST Output | v1.0.0 | v1.x.x | N/A |
| Query Context | v1.0.0 | v1.x.x | N/A |
| Pattern Input | v1.0.0 | v1.x.x | N/A |
| Pattern Output | v1.0.0 | v1.x.x | N/A |
| Marketplace Pattern | v1.0.0 | v1.x.x | N/A |
| Service Events | v1.0.0 | v1.x.x | N/A |
| Error Response | v1.0.0 | v1.x.x | N/A |

### Breaking Change Policy

#### Before Making Breaking Changes
1. **RFC Process**: Create Request for Comments with impact analysis
2. **Team Approval**: Get sign-off from all affected service teams
3. **Migration Plan**: Define step-by-step migration process
4. **Deprecation Notice**: Provide 6-month advance notice
5. **Backward Compatibility**: Maintain support for 2 major versions

#### Change Implementation Process
1. **New Version**: Create new schema version (e.g., v2.0.0)
2. **Dual Support**: Support both old and new versions simultaneously
3. **Migration Tools**: Provide automated migration utilities
4. **Adoption Tracking**: Monitor usage of new version
5. **Deprecation**: Remove old version after compatibility period

## Required Field Rules

### Never Remove Required Fields
- Required fields MUST NOT be removed in minor or patch versions
- Removing required fields requires a major version bump
- Provide migration path for removed fields

### Adding Required Fields
- New required fields require major version bump
- Exception: Fields with sensible defaults can be added in minor versions
- Must provide backward compatibility mechanism

### Optional to Required Promotion
- Can only happen in major version updates
- Must provide 6-month deprecation notice
- Include migration guide and tooling

## Data Size Limits

### Repository Analysis Output
| Component | Maximum Size | Streaming Threshold | Notes |
|-----------|-------------|-------------------|-------|
| Total AST Output | 100MB | 10MB | Use streaming for large repos |
| Individual File AST | 10MB | 1MB | Split large files into chunks |
| AST Node Depth | 50 levels | N/A | Prevent stack overflow |
| File Count | 10,000 files | 1,000 files | Batch processing for large repos |
| Embedding Vector | 1536 dimensions | N/A | OpenAI standard |
| Code Chunk | 8KB | N/A | Optimal for embedding |

### Query Context
| Component | Maximum Size | Notes |
|-----------|-------------|-------|
| Total Context | 5MB | Optimized for query performance |
| Symbol Index | 50,000 symbols | Searchable symbol limit |
| Embeddings Array | 10,000 embeddings | Memory constraint |
| Pattern Array | 1,000 patterns | Query relevance limit |

### Pattern Detection
| Component | Maximum Size | Notes |
|-----------|-------------|-------|
| Input AST Data | 50MB | Processing memory limit |
| Pattern Results | 10MB | Response size limit |
| Patterns per File | 100 patterns | Prevent noise |
| Code Examples | 5KB each | Readability limit |

### Marketplace Patterns
| Component | Maximum Size | Notes |
|-----------|-------------|-------|
| Pattern Description | 1,000 chars | Search optimization |
| Code Examples | 5KB each | Display performance |
| Documentation | 50KB | Comprehensive docs |
| Total Pattern Data | 1MB | Storage efficiency |

## Performance Contracts

### Latency Requirements
| Integration | P50 Target | P95 Target | P99 Target | Timeout |
|-------------|-----------|-----------|-----------|---------|
| Repository → Query | 25ms | 50ms | 100ms | 200ms |
| Repository → Pattern | 50ms | 100ms | 200ms | 500ms |
| Pattern → Query | 10ms | 20ms | 40ms | 100ms |
| Pattern → Marketplace | 15ms | 30ms | 60ms | 150ms |

### Throughput Requirements
| Service | Requests/Second | Concurrent Requests | Notes |
|---------|----------------|-------------------|-------|
| Repository Analysis | 10 RPS | 50 concurrent | Large payload processing |
| Query Intelligence | 100 RPS | 500 concurrent | Fast response required |
| Pattern Detection | 20 RPS | 100 concurrent | ML processing overhead |
| Marketplace | 1000 RPS | 2000 concurrent | High read volume |

### Memory Constraints
| Service | Max Memory per Request | Notes |
|---------|----------------------|-------|
| Repository Analysis | 4GB | Large AST processing |
| Query Intelligence | 1GB | Embedding operations |
| Pattern Detection | 2GB | ML model inference |
| Marketplace | 256MB | Simple CRUD operations |

## Validation Rules by Schema

### AST Output Validation
```yaml
required_fields:
  - repository.id
  - repository.url
  - repository.commit
  - analysis.files
  - metadata.analysis_id
  - metadata.timestamp

field_constraints:
  repository.commit:
    pattern: "^[a-f0-9]{40}$"
    description: "Full 40-character SHA-1 hash"
  
  analysis.files:
    max_items: 10000
    description: "Maximum files per analysis"
  
  metadata.duration_ms:
    minimum: 0
    maximum: 1800000  # 30 minutes
    description: "Analysis duration limit"

custom_validations:
  - name: "file_size_consistency"
    rule: "sum(files[].size_bytes) <= repository.size_bytes"
  - name: "embedding_vector_consistency"
    rule: "all embeddings have same vector dimension"
```

### Query Context Validation
```yaml
required_fields:
  - repository_id
  - analysis_id
  - ast_summary.symbols
  - embeddings
  - metadata.created_at

field_constraints:
  embeddings:
    max_items: 10000
    description: "Memory and performance limit"
  
  ast_summary.symbols:
    max_items: 50000
    description: "Search index size limit"

custom_validations:
  - name: "embedding_chunk_consistency"
    rule: "all embedding.chunk_id references exist in chunks"
  - name: "symbol_file_consistency"
    rule: "all symbol.file_path references exist in structure"
```

### Pattern Detection Validation
```yaml
input_validation:
  ast_data.files:
    max_items: 10000
    max_size_mb: 50
  
  detection_config.confidence_threshold:
    minimum: 0.0
    maximum: 1.0

output_validation:
  patterns:
    max_items: 1000
    description: "Prevent result explosion"
  
  patterns[].confidence:
    minimum: 0.0
    maximum: 1.0
  
  patterns[].examples:
    max_items: 10
    max_size_kb: 5

custom_validations:
  - name: "confidence_threshold_consistency"
    rule: "all patterns.confidence >= detection_config.confidence_threshold"
  - name: "location_file_consistency"
    rule: "all pattern.locations[].file_path exist in input files"
```

### Error Response Validation
```yaml
required_fields:
  - error_id
  - service
  - error_type
  - message
  - retryable
  - timestamp

field_constraints:
  message:
    max_length: 500
    description: "Keep error messages concise"
  
  user_message:
    max_length: 200
    description: "User-friendly message limit"
  
  retry_after_seconds:
    minimum: 0
    maximum: 3600
    description: "Reasonable retry delay"

enum_validations:
  service:
    allowed: ["repository-analysis", "query-intelligence", "pattern-detection", "marketplace"]
  
  error_type:
    allowed: ["validation", "authentication", "authorization", "not_found", "conflict", "rate_limit", "timeout", "internal", "external_dependency", "quota_exceeded", "maintenance"]
```

## Validation Implementation

### Schema Validation Tools
```bash
# JSON Schema validation
ajv validate -s contracts/schemas/ast-output-v1.json -d contracts/examples/ast-output-example.json

# Custom validation rules
python scripts/validate_contracts.py --schema ast-output-v1 --data examples/

# Performance validation
python scripts/validate_performance.py --contract repository-to-query --threshold 50ms
```

### Continuous Validation
```yaml
# GitHub Actions workflow
name: Contract Validation
on: [push, pull_request]

jobs:
  validate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Validate Schemas
        run: |
          npm install -g ajv-cli
          for schema in contracts/schemas/*.json; do
            echo "Validating $schema"
            ajv compile -s "$schema"
          done
      
      - name: Validate Examples
        run: |
          python scripts/validate_examples.py
      
      - name: Check Breaking Changes
        run: |
          python scripts/check_breaking_changes.py --base main --head HEAD
```

### Runtime Validation
```python
# Service-side validation
from jsonschema import validate, ValidationError

def validate_ast_output(data):
    try:
        validate(data, ast_output_schema)
        return True, None
    except ValidationError as e:
        return False, f"Validation error: {e.message}"

# Performance monitoring
def monitor_contract_performance(service_pair, latency_ms):
    threshold = PERFORMANCE_CONTRACTS[service_pair]['p95_target']
    if latency_ms > threshold:
        alert_contract_violation(service_pair, latency_ms, threshold)
```

## Compliance Monitoring

### Metrics to Track
- Schema validation success rate (target: 100%)
- Contract performance adherence (target: >95% within SLA)
- Version adoption rate (target: >90% on current version within 6 months)
- Breaking change lead time (target: >6 months notice)

### Alerting Rules
- **Critical**: Schema validation failures
- **Warning**: Performance SLA violations (>5% of requests)
- **Info**: Deprecated version usage (>10% after 3 months)

### Compliance Reports
- Weekly: Contract health dashboard
- Monthly: Version adoption report
- Quarterly: Breaking change impact assessment

---

**Last Updated**: 2025-01-15
**Next Review**: 2025-04-15
**Owner**: Platform Team
