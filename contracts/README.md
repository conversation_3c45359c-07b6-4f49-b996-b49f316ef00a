# CCL Service Integration Contracts

## Overview
This directory contains the formal contracts between CCL services, defining how the four core services communicate and exchange data. These contracts are **critical** for ensuring seamless integration and preventing service compatibility issues.

## Service Integration Map

```mermaid
graph TB
    subgraph "Core Services"
        RA[Repository Analysis API<br/>Rust]
        QI[Query Intelligence<br/>Python]
        PD[Pattern Detection<br/>Python]
        MP[Marketplace API<br/>Go]
    end
    
    RA -->|AST Output v1| QI
    RA -->|AST Output v1| PD
    PD -->|Pattern Output v1| QI
    PD -->|Pattern Output v1| MP
    QI -->|Query Insights v1| MP
    
    subgraph "Data Stores"
        SP[Spanner]
        BQ[BigQuery]
        R[Redis]
    end
    
    RA --> SP
    QI --> SP
    QI --> R
    PD --> BQ
    MP --> SP
```

## Contract Files

### Core Schemas (`schemas/`)
- **`ast-output-v1.json`** - Repository Analysis API output format
- **`query-context-v1.json`** - Query Intelligence input format  
- **`pattern-input-v1.json`** - Pattern Detection input format
- **`pattern-output-v1.json`** - Pattern Detection output format
- **`marketplace-pattern-v1.json`** - Marketplace pattern format
- **`service-events-v1.json`** - Event bus message format
- **`error-response-v1.json`** - Unified error response format

### Example Payloads (`examples/`)
- **`ast-output-example.json`** - Complete Repository Analysis output
- **`query-context-example.json`** - Query Intelligence context
- **`pattern-detection-flow.json`** - End-to-end pattern detection
- **`marketplace-integration-example.json`** - Pattern to marketplace flow
- **`error-scenarios.json`** - Error handling examples

### Validation Rules (`validation/`)
- **`validation-rules.md`** - Version management and data limits
- **`performance-contracts.md`** - SLA requirements and budgets
- **`compatibility-matrix.md`** - Version compatibility matrix

### Integration Tests (`tests/`)
- **`contract-tests.md`** - Test specifications
- **`schema-validation-tests.json`** - Schema validation test cases
- **`integration-flow-tests.md`** - End-to-end flow tests

## Data Flow Overview

### 1. Repository Analysis → Query Intelligence
```
Repository Analysis API outputs:
├── AST data (parsed syntax trees)
├── Code metrics (complexity, LOC, etc.)
├── Language breakdown
└── File-level analysis

Query Intelligence consumes:
├── Processed code chunks (for embeddings)
├── AST summary (for context)
├── Repository metadata
└── Semantic embeddings
```

### 2. Repository Analysis → Pattern Detection
```
Repository Analysis API outputs:
├── Raw AST nodes
├── Code features
├── File structure
└── Language-specific data

Pattern Detection consumes:
├── AST data (for ML feature extraction)
├── Code content (for pattern matching)
├── File metadata
└── Language context
```

### 3. Pattern Detection → Marketplace
```
Pattern Detection outputs:
├── Detected patterns
├── Confidence scores
├── Pattern locations
└── Quality metrics

Marketplace consumes:
├── Validated patterns
├── Pattern metadata
├── Quality scores
└── Commercial viability data
```

## Version Management

### Current Versions
- **AST Output**: v1.0.0
- **Query Context**: v1.0.0  
- **Pattern Input/Output**: v1.0.0
- **Marketplace Pattern**: v1.0.0
- **Service Events**: v1.0.0
- **Error Response**: v1.0.0

### Versioning Strategy
- **Semantic Versioning**: MAJOR.MINOR.PATCH
- **Breaking Changes**: Require major version bump
- **Backward Compatibility**: Maintained for 2 major versions
- **Deprecation Policy**: 6-month notice for breaking changes

## Performance Contracts

### Latency Budgets
| Service Integration | Budget | P95 Target | P99 Target |
|-------------------|--------|------------|------------|
| Repository → Query | 50ms | 40ms | 80ms |
| Repository → Pattern | 100ms | 80ms | 150ms |
| Pattern → Query | 20ms | 15ms | 30ms |
| Pattern → Marketplace | 30ms | 25ms | 50ms |

### Data Size Limits
| Data Type | Maximum Size | Streaming Threshold |
|-----------|-------------|-------------------|
| AST Output | 100MB | 10MB |
| Pattern Results | 10MB | 1MB |
| Query Context | 5MB | N/A |
| Error Responses | 1KB | N/A |

## Error Handling

### Unified Error Schema
All services use the standardized error format defined in `schemas/error-response-v1.json`:

```json
{
  "error_id": "uuid",
  "service": "service-name",
  "error_type": "validation|timeout|internal|external",
  "message": "Human-readable message",
  "retryable": true|false,
  "user_message": "User-friendly message",
  "correlation_id": "request-trace-id",
  "timestamp": "ISO-8601",
  "context": {}
}
```

### Retry Strategies
- **Retryable Errors**: Exponential backoff with jitter
- **Circuit Breaker**: 5 failures in 60 seconds triggers open state
- **Timeout Handling**: Service-specific timeout values
- **Graceful Degradation**: Fallback mechanisms defined per integration

## Breaking Change Policy

### Before Making Breaking Changes
1. **Propose Change**: Create RFC with impact analysis
2. **Team Review**: Get approval from all affected service teams
3. **Migration Plan**: Define step-by-step migration process
4. **Deprecation Notice**: 6-month advance notice
5. **Backward Compatibility**: Maintain for 2 major versions

### Change Process
1. **New Version**: Create new schema version
2. **Dual Support**: Support both old and new versions
3. **Migration Tools**: Provide automated migration utilities
4. **Monitoring**: Track adoption of new version
5. **Deprecation**: Remove old version after compatibility period

## Getting Started

### For Service Developers
1. **Read Contracts**: Review schemas for your service integrations
2. **Validate Examples**: Test with provided example payloads
3. **Implement Validation**: Add schema validation to your service
4. **Add Tests**: Create integration tests using contract test specs
5. **Monitor Compliance**: Set up alerts for contract violations

### For Integration Testing
1. **Schema Validation**: Validate all payloads against schemas
2. **Contract Tests**: Run end-to-end integration tests
3. **Performance Tests**: Verify latency and throughput requirements
4. **Error Scenarios**: Test error handling and retry logic
5. **Version Compatibility**: Test version upgrade/downgrade scenarios

## Support and Maintenance

### Contract Ownership
- **Platform Team**: Overall contract governance and tooling
- **Service Teams**: Individual service contract compliance
- **DevOps Team**: Integration testing and monitoring
- **Architecture Team**: Breaking change approval and migration planning

### Monitoring and Alerts
- **Schema Violations**: Alert on invalid payloads
- **Performance Degradation**: Alert on SLA violations  
- **Version Drift**: Alert on deprecated version usage
- **Integration Failures**: Alert on service communication failures

## Resources

### Tools
- **Schema Validation**: JSON Schema validators for each language
- **Contract Testing**: Automated contract test runners
- **Migration Tools**: Schema migration and compatibility checkers
- **Monitoring**: Integration health dashboards

### Documentation
- **API Documentation**: OpenAPI specs with contract references
- **Integration Guides**: Step-by-step integration instructions
- **Best Practices**: Patterns for reliable service integration
- **Troubleshooting**: Common issues and solutions

---

**Last Updated**: 2025-01-15  
**Next Review**: 2025-04-15  
**Contact**: <EMAIL>