# CCL Integration Contract Tests

## Overview
This document defines comprehensive test specifications for validating CCL service integration contracts, ensuring compatibility, performance, and reliability across all service boundaries.

## Test Categories

### 1. Schema Validation Tests

#### JSON Schema Compliance
```yaml
test_suite: "schema_validation"
description: "Validate all payloads against their respective JSON schemas"

tests:
  - name: "ast_output_schema_validation"
    schema: "contracts/schemas/ast-output-v1.json"
    test_data:
      - "contracts/examples/ast-output-example.json"
      - "test-data/ast-output-minimal.json"
      - "test-data/ast-output-maximal.json"
    
  - name: "query_context_schema_validation"
    schema: "contracts/schemas/query-context-v1.json"
    test_data:
      - "contracts/examples/query-context-example.json"
      - "test-data/query-context-empty-patterns.json"
      - "test-data/query-context-large-embeddings.json"
    
  - name: "pattern_input_schema_validation"
    schema: "contracts/schemas/pattern-input-v1.json"
    test_data:
      - "test-data/pattern-input-python.json"
      - "test-data/pattern-input-javascript.json"
      - "test-data/pattern-input-multi-language.json"
    
  - name: "pattern_output_schema_validation"
    schema: "contracts/schemas/pattern-output-v1.json"
    test_data:
      - "contracts/examples/pattern-detection-flow.json"
      - "test-data/pattern-output-no-patterns.json"
      - "test-data/pattern-output-many-patterns.json"
    
  - name: "marketplace_pattern_schema_validation"
    schema: "contracts/schemas/marketplace-pattern-v1.json"
    test_data:
      - "test-data/marketplace-pattern-free.json"
      - "test-data/marketplace-pattern-premium.json"
      - "test-data/marketplace-pattern-enterprise.json"
    
  - name: "error_response_schema_validation"
    schema: "contracts/schemas/error-response-v1.json"
    test_data:
      - "test-data/error-validation.json"
      - "test-data/error-timeout.json"
      - "test-data/error-internal.json"
```

#### Edge Case Testing
```yaml
edge_case_tests:
  - name: "empty_repository"
    description: "Repository with no analyzable files"
    expected_behavior: "Valid AST output with empty files array"
    
  - name: "maximum_file_count"
    description: "Repository with 10,000 files (limit)"
    expected_behavior: "Successful processing within time limits"
    
  - name: "deeply_nested_ast"
    description: "AST with 50 levels of nesting (limit)"
    expected_behavior: "Valid AST structure without stack overflow"
    
  - name: "unicode_content"
    description: "Files with Unicode characters and emojis"
    expected_behavior: "Proper encoding and processing"
    
  - name: "binary_files"
    description: "Repository containing binary files"
    expected_behavior: "Binary files skipped, no processing errors"
```

### 2. Cross-Service Compatibility Tests

#### Repository Analysis → Query Intelligence
```yaml
test_suite: "repository_to_query_compatibility"
description: "Validate AST output can be transformed to Query context"

tests:
  - name: "ast_to_query_context_transformation"
    steps:
      1. "Generate AST output from Repository Analysis"
      2. "Transform to Query Intelligence input format"
      3. "Validate transformed data against query-context schema"
      4. "Verify all required fields are populated"
      5. "Check embedding vector dimensions match"
    
    validation_rules:
      - "All AST symbols appear in query context symbols"
      - "Embedding chunk_ids reference valid code chunks"
      - "Repository metadata is preserved"
      - "File structure is accurately represented"
    
  - name: "large_repository_compatibility"
    description: "Test with enterprise-scale repository"
    repository_size: "100MB, 5000 files"
    expected_outcomes:
      - "AST output under 100MB limit"
      - "Query context under 5MB limit"
      - "Processing time under 5 minutes"
      - "Memory usage under 4GB"
```

#### Repository Analysis → Pattern Detection
```yaml
test_suite: "repository_to_pattern_compatibility"
description: "Validate AST output can be processed for pattern detection"

tests:
  - name: "ast_to_pattern_input_transformation"
    steps:
      1. "Generate AST output from Repository Analysis"
      2. "Transform to Pattern Detection input format"
      3. "Validate transformed data against pattern-input schema"
      4. "Verify AST nodes are properly flattened"
      5. "Check code features are extracted"
    
    validation_rules:
      - "AST node hierarchy is preserved in flattened format"
      - "Symbol references are maintained"
      - "Code metrics are consistent"
      - "Language-specific features are extracted"
    
  - name: "multi_language_compatibility"
    description: "Test with polyglot repository"
    languages: ["Python", "JavaScript", "TypeScript", "Go", "Rust"]
    expected_outcomes:
      - "All languages properly detected"
      - "Language-specific AST features preserved"
      - "Cross-language dependencies tracked"
```

#### Pattern Detection → Marketplace
```yaml
test_suite: "pattern_to_marketplace_compatibility"
description: "Validate detected patterns can be published to marketplace"

tests:
  - name: "pattern_to_marketplace_transformation"
    steps:
      1. "Generate pattern detection output"
      2. "Transform to Marketplace pattern format"
      3. "Validate against marketplace-pattern schema"
      4. "Verify quality metrics are computed"
      5. "Check pricing and licensing fields"
    
    validation_rules:
      - "Pattern confidence scores are preserved"
      - "Code examples are properly formatted"
      - "Quality metrics are within valid ranges"
      - "Licensing information is complete"
```

### 3. Performance Tests

#### Latency Testing
```yaml
test_suite: "integration_latency"
description: "Validate service-to-service communication meets SLA requirements"

tests:
  - name: "repository_to_query_latency"
    target_latency:
      p50: 25ms
      p95: 50ms
      p99: 100ms
    test_scenarios:
      - payload_size: "1KB"
        expected_p95: "20ms"
      - payload_size: "100KB"
        expected_p95: "35ms"
      - payload_size: "1MB"
        expected_p95: "50ms"
    
  - name: "pattern_to_marketplace_latency"
    target_latency:
      p50: 15ms
      p95: 30ms
      p99: 60ms
    test_scenarios:
      - pattern_count: 1
        expected_p95: "20ms"
      - pattern_count: 50
        expected_p95: "30ms"
      - pattern_count: 100
        expected_p95: "45ms"
```

#### Throughput Testing
```yaml
test_suite: "integration_throughput"
description: "Validate service integrations can handle expected load"

tests:
  - name: "concurrent_request_handling"
    scenarios:
      - service_pair: "repository-to-query"
        concurrent_requests: 200
        duration: "5 minutes"
        success_criteria: "error_rate < 0.1%"
      
      - service_pair: "pattern-to-marketplace"
        concurrent_requests: 100
        duration: "10 minutes"
        success_criteria: "p95_latency < 30ms"
    
  - name: "burst_load_handling"
    scenarios:
      - service_pair: "repository-to-pattern"
        normal_rps: 20
        burst_rps: 100
        burst_duration: "30 seconds"
        success_criteria: "no service degradation"
```

### 4. Error Handling Tests

#### Error Propagation
```yaml
test_suite: "error_propagation"
description: "Validate error handling across service boundaries"

tests:
  - name: "upstream_service_failure"
    scenarios:
      - upstream: "Repository Analysis"
        downstream: "Query Intelligence"
        failure_type: "timeout"
        expected_behavior: "graceful degradation with cached data"
      
      - upstream: "Pattern Detection"
        downstream: "Marketplace"
        failure_type: "invalid_data"
        expected_behavior: "validation error with retry suggestion"
    
  - name: "error_response_format"
    validation_rules:
      - "All errors follow unified error schema"
      - "Error correlation IDs are propagated"
      - "User-friendly messages are provided"
      - "Retry guidance is included when applicable"
```

#### Circuit Breaker Testing
```yaml
test_suite: "circuit_breaker"
description: "Validate circuit breaker patterns prevent cascade failures"

tests:
  - name: "circuit_breaker_activation"
    steps:
      1. "Generate 5 consecutive failures"
      2. "Verify circuit breaker opens"
      3. "Confirm requests are rejected immediately"
      4. "Wait for half-open state"
      5. "Test recovery with successful request"
    
  - name: "fallback_behavior"
    scenarios:
      - service: "Query Intelligence"
        fallback: "cached_responses"
        degradation: "reduced_accuracy"
      
      - service: "Pattern Detection"
        fallback: "rule_based_detection"
        degradation: "lower_confidence_scores"
```

### 5. Version Compatibility Tests

#### Backward Compatibility
```yaml
test_suite: "version_compatibility"
description: "Validate backward compatibility across schema versions"

tests:
  - name: "v1_producer_v1_consumer"
    description: "Current version compatibility"
    producer_version: "v1.0.0"
    consumer_version: "v1.0.0"
    expected_result: "full_compatibility"
    
  - name: "v1_producer_v2_consumer"
    description: "Forward compatibility test"
    producer_version: "v1.0.0"
    consumer_version: "v2.0.0"
    expected_result: "graceful_degradation"
    
  - name: "v2_producer_v1_consumer"
    description: "Backward compatibility test"
    producer_version: "v2.0.0"
    consumer_version: "v1.0.0"
    expected_result: "compatibility_with_warnings"
```

#### Migration Testing
```yaml
test_suite: "schema_migration"
description: "Validate schema migration processes"

tests:
  - name: "automated_migration"
    steps:
      1. "Generate v1 data"
      2. "Run migration script to v2"
      3. "Validate migrated data against v2 schema"
      4. "Verify no data loss"
      5. "Check performance impact"
    
  - name: "rollback_capability"
    steps:
      1. "Migrate data from v1 to v2"
      2. "Perform rollback to v1"
      3. "Validate data integrity"
      4. "Verify service functionality"
```

## Test Implementation

### Automated Test Execution
```bash
#!/bin/bash
# Contract test runner

# Schema validation
echo "Running schema validation tests..."
python scripts/test_schema_validation.py

# Compatibility tests
echo "Running compatibility tests..."
python scripts/test_service_compatibility.py

# Performance tests
echo "Running performance tests..."
python scripts/test_integration_performance.py

# Error handling tests
echo "Running error handling tests..."
python scripts/test_error_scenarios.py

# Generate test report
python scripts/generate_test_report.py
```

### Continuous Integration
```yaml
# .github/workflows/contract-tests.yml
name: Contract Tests
on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  contract-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Test Environment
        run: |
          docker-compose -f docker-compose.test.yml up -d
          sleep 30  # Wait for services to start
      
      - name: Run Contract Tests
        run: |
          ./scripts/run_contract_tests.sh
      
      - name: Publish Test Results
        uses: dorny/test-reporter@v1
        with:
          name: Contract Test Results
          path: test-results/*.xml
          reporter: java-junit
```

### Test Data Management
```yaml
test_data_strategy:
  synthetic_data:
    - "Generated test cases covering edge cases"
    - "Parameterized tests for different scales"
    - "Randomized data for fuzz testing"
  
  real_data_samples:
    - "Anonymized production data samples"
    - "Open source repository samples"
    - "Curated test repositories"
  
  data_refresh:
    frequency: "weekly"
    automation: "GitHub Actions"
    validation: "schema compliance check"
```

---

**Last Updated**: 2025-01-15  
**Next Review**: 2025-04-15  
**Owner**: Platform Team
