# CCL Automated Secret Rotation Cron Jobs
# Add these to your cron configuration for automated secret rotation

# Weekly secret rotation check (Sundays at 2 AM)
# Checks all secrets and rotates those that need rotation
0 2 * * 0 /path/to/ccl/scripts/security/rotate-secrets.sh --environment production all >> /var/log/ccl-secret-rotation.log 2>&1

# Monthly forced rotation for critical secrets (First Sunday of month at 3 AM)
# Forces rotation of database passwords and service account keys
0 3 1-7 * 0 /path/to/ccl/scripts/security/rotate-secrets.sh --environment production --force database >> /var/log/ccl-secret-rotation.log 2>&1

# Daily secret verification (Every day at 6 AM)
# Verifies that all secrets are working correctly
0 6 * * * /path/to/ccl/scripts/security/rotate-secrets.sh --environment production --verify-only >> /var/log/ccl-secret-verification.log 2>&1

# Weekly encryption key rotation (Wednesdays at 1 AM)
# Rotates encryption keys more frequently for security
0 1 * * 3 /path/to/ccl/scripts/security/rotate-secrets.sh --environment production encryption-keys >> /var/log/ccl-secret-rotation.log 2>&1

# Staging environment - weekly rotation (Saturdays at 1 AM)
# Rotates staging secrets weekly for testing
0 1 * * 6 /path/to/ccl/scripts/security/rotate-secrets.sh --environment staging all >> /var/log/ccl-secret-rotation-staging.log 2>&1

# Development environment - daily rotation (Every day at 11 PM)
# Rotates development secrets daily for testing
0 23 * * * /path/to/ccl/scripts/security/rotate-secrets.sh --environment development all >> /var/log/ccl-secret-rotation-dev.log 2>&1

# Certificate check (Daily at 7 AM)
# Checks certificate expiry daily
0 7 * * * /path/to/ccl/scripts/security/rotate-secrets.sh --environment production certificates >> /var/log/ccl-cert-check.log 2>&1

# Emergency rotation capability - commented out by default
# Uncomment and modify as needed for emergency rotations
# 0 * * * * /path/to/ccl/scripts/security/rotate-secrets.sh --environment production --force all >> /var/log/ccl-emergency-rotation.log 2>&1

# Rotation schedule summary:
# - Daily: Secret verification, certificate checks
# - Weekly: General secret rotation, encryption keys, staging rotation  
# - Monthly: Forced rotation of critical secrets
# - As needed: Emergency rotations

# Log rotation for secret rotation logs
# Add to /etc/logrotate.d/ccl-secrets:
#
# /var/log/ccl-secret-*.log {
#     daily
#     missingok
#     rotate 30
#     compress
#     notifempty
#     create 644 root root
# }