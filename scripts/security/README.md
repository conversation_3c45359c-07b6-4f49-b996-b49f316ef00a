# CCL Automated Secret Rotation System

A comprehensive secret rotation system for the CCL platform that automatically rotates passwords, API keys, service account keys, encryption keys, and certificates with zero downtime.

## Overview

This system provides automated, secure rotation of all secrets used across the CCL platform, including:

- **Database passwords** (PostgreSQL, Redis, Spanner)
- **API keys** (Gemini, OpenAI, GitHub, Slack)
- **Service account keys** (GCP service accounts)
- **Encryption keys** (JWT, session, webhook secrets)
- **TLS certificates** (with expiry monitoring)

## Features

### Automated Rotation
- **Scheduled rotation**: Cron-based automatic rotation
- **Age-based rotation**: Rotate secrets based on age thresholds
- **Force rotation**: Manual force rotation when needed
- **Environment-specific**: Separate rotation for dev/staging/production

### Zero-Downtime Deployment
- **Rolling updates**: Services restart gradually during rotation
- **Health verification**: Verify services after rotation
- **Rollback capability**: Automatic rollback on failure
- **Backup creation**: Keep previous secret versions

### Security Features
- **Secure generation**: Cryptographically secure secret generation
- **Version tracking**: Track all secret versions with labels
- **Access logging**: Comprehensive audit trail
- **Verification**: Post-rotation health checks

### Monitoring & Alerting
- **Success/failure notifications**: Slack/email alerts
- **Rotation logs**: Detailed logging of all operations
- **Certificate monitoring**: Alert on upcoming certificate expiry
- **Failed verification alerts**: Immediate notification of issues

## Quick Start

### Prerequisites

1. **Required Tools**:
   ```bash
   # Install required tools
   gcloud components install kubectl
   pip install google-cloud-secret-manager
   ```

2. **Authentication**:
   ```bash
   # Authenticate with GCP
   gcloud auth login
   gcloud config set project ccl-platform
   ```

3. **Permissions**:
   - `secretmanager.admin` role on Secret Manager
   - `container.admin` role on GKE cluster
   - `iam.serviceAccountKeyAdmin` role for SA rotation

### Basic Usage

```bash
# Preview what would be rotated (dry run)
./rotate-secrets.sh --dry-run

# Rotate all secrets in production
./rotate-secrets.sh --environment production all

# Rotate only database passwords
./rotate-secrets.sh database

# Force rotate encryption keys
./rotate-secrets.sh --force encryption-keys

# Verify current secrets without rotating
./rotate-secrets.sh --verify-only
```

### Automated Scheduling

1. **Install cron jobs**:
   ```bash
   # Copy cron configuration
   sudo cp cron-secret-rotation.txt /etc/cron.d/ccl-secret-rotation
   
   # Verify cron jobs
   sudo crontab -l
   ```

2. **Setup log rotation**:
   ```bash
   # Add log rotation configuration
   sudo tee /etc/logrotate.d/ccl-secrets << EOF
   /var/log/ccl-secret-*.log {
       daily
       missingok
       rotate 30
       compress
       notifempty
       create 644 root root
   }
   EOF
   ```

## Command Reference

### Options

| Option | Description |
|--------|-------------|
| `-h, --help` | Show help message |
| `-e, --environment ENV` | Target environment (development\|staging\|production) |
| `-d, --dry-run` | Preview changes without executing |
| `-f, --force` | Force rotation regardless of age |
| `--verify-only` | Only verify secrets, don't rotate |
| `--rollback VERSION` | Rollback to specific version |

### Secret Types

| Type | Description | Default Rotation Age |
|------|-------------|---------------------|
| `database` | Database passwords | 60 days |
| `api-keys` | External API keys | 30 days |
| `service-accounts` | GCP service account keys | 90 days |
| `encryption-keys` | JWT, session, webhook keys | 30 days |
| `certificates` | TLS certificates | 30 days before expiry |
| `all` | All secret types | Varies by type |

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Cron Jobs     │───▶│ Rotation Script  │───▶│ Secret Manager  │
│                 │    │                  │    │                 │
│ • Weekly All    │    │ • Generate New   │    │ • Store Secrets │
│ • Daily Verify  │    │ • Backup Old     │    │ • Version Track │
│ • Monthly Force │    │ • Update K8s     │    │ • Access Control│
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   Kubernetes     │    │   Monitoring    │
                       │                  │    │                 │
                       │ • Rolling Update │    │ • Success/Fail  │
                       │ • Health Check   │    │ • Notifications │
                       │ • Auto Rollback  │    │ • Audit Logs    │
                       └──────────────────┘    └─────────────────┘
```

## Secret Types Detail

### Database Passwords

**Secrets managed**:
- `ccl-postgres-password`: PostgreSQL database password
- `ccl-redis-password`: Redis cache password
- `ccl-spanner-password`: Google Spanner password

**Rotation process**:
1. Generate new secure password (32 characters)
2. Update Secret Manager with new password
3. Update Kubernetes secrets
4. Rolling restart of all services using the database
5. Verify database connectivity

### API Keys

**Secrets managed**:
- `gemini-api-key`: Google Gemini API key
- `openai-api-key`: OpenAI API key
- `github-api-token`: GitHub API token
- `slack-webhook-url`: Slack webhook URL

**Rotation process**:
1. Manual rotation required for most API keys
2. Script provides instructions for manual steps
3. Updates Secret Manager after manual rotation
4. Updates Kubernetes secrets
5. Verifies API connectivity

### Service Account Keys

**Secrets managed**:
- `ccl-analysis-engine-key`: Analysis engine service account
- `ccl-query-intelligence-key`: Query intelligence service account
- `ccl-pattern-mining-key`: Pattern mining service account
- `ccl-marketplace-key`: Marketplace service account
- `ccl-ml-operations-key`: ML operations service account

**Rotation process**:
1. Create new service account key
2. Store new key in Secret Manager
3. Update Kubernetes secret with new key
4. Delete old service account keys (keep last 2)
5. Verify service account authentication

### Encryption Keys

**Secrets managed**:
- `ccl-jwt-secret`: JWT signing secret (64 bytes hex)
- `ccl-session-secret`: Session encryption key (32 bytes hex)
- `ccl-webhook-secret`: Webhook signing secret (32 bytes hex)

**Rotation process**:
1. Generate new cryptographically secure key
2. Update Secret Manager with new key
3. Update Kubernetes secrets
4. Services pick up new keys automatically
5. Old tokens/sessions expire naturally

### TLS Certificates

**Secrets managed**:
- `ccl-tls-cert`: Main TLS certificate
- `ccl-internal-ca`: Internal Certificate Authority

**Rotation process**:
1. Check certificate expiry dates
2. Alert if expiring within 30 days
3. Coordinate with cert-manager for automatic renewal
4. Manual intervention required for CA certificates

## Configuration

### Environment Variables

```bash
# GCP Configuration
export GCP_PROJECT_ID="ccl-platform"
export CLUSTER_NAME="ccl-cluster"
export CLUSTER_LOCATION="us-central1"

# Default environment
export ENVIRONMENT="production"

# Notification settings
export SLACK_WEBHOOK_URL="https://hooks.slack.com/..."
export NOTIFICATION_EMAIL="<EMAIL>"
```

### Secret Age Thresholds

Modify rotation thresholds by editing the script:

```bash
# In rotate-secrets.sh
rotate_database_secrets() {
    if needs_rotation "$secret_name" 60; then  # 60 days
        # Rotation logic
    fi
}
```

### Notification Settings

Configure Slack notifications:

```bash
# Add to script
send_slack_notification() {
    local message="$1"
    local status="$2"
    
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"🔐 Secret Rotation: $message\", \"channel\":\"#security\"}" \
        "$SLACK_WEBHOOK_URL"
}
```

## Monitoring & Alerting

### Log Locations

```bash
# Primary rotation log
tail -f /var/log/ccl-secret-rotation.log

# Verification log
tail -f /var/log/ccl-secret-verification.log

# Staging environment log
tail -f /var/log/ccl-secret-rotation-staging.log
```

### Success Indicators

✅ **Successful rotation**:
- New secret version created in Secret Manager
- Kubernetes secrets updated
- Services restarted successfully
- Health checks pass
- No failed authentication attempts

### Failure Indicators

❌ **Failed rotation**:
- Secret generation fails
- GCP API errors
- Kubernetes update failures
- Service restart failures
- Health check failures

### Alert Integration

**Slack Integration**:
```bash
# Add to cron job for notifications
./rotate-secrets.sh all && \
  curl -X POST -H 'Content-type: application/json' \
    --data '{"text":"✅ Secret rotation completed successfully"}' \
    "$SLACK_WEBHOOK_URL" || \
  curl -X POST -H 'Content-type: application/json' \
    --data '{"text":"❌ Secret rotation failed - check logs"}' \
    "$SLACK_WEBHOOK_URL"
```

**Email Alerts**:
```bash
# Add to cron job
./rotate-secrets.sh all || \
  echo "Secret rotation failed at $(date)" | \
  mail -s "CCL Secret Rotation Failure" <EMAIL>
```

## Emergency Procedures

### Emergency Rotation

If secrets are compromised:

```bash
# Immediately rotate all secrets
./rotate-secrets.sh --force all

# Rotate specific secret type
./rotate-secrets.sh --force database

# Verify all services after emergency rotation
./rotate-secrets.sh --verify-only
```

### Rollback Procedure

If rotation causes issues:

```bash
# Rollback to previous version
./rotate-secrets.sh --rollback 2 database

# Verify services after rollback
./rotate-secrets.sh --verify-only

# Check specific service health
kubectl get pods -n production
kubectl logs -f deployment/analysis-engine -n production
```

### Manual Secret Updates

For API keys requiring manual rotation:

```bash
# Update Gemini API key manually
gcloud secrets versions add gemini-api-key --data-file=new-key.txt

# Trigger Kubernetes secret update
kubectl create secret generic gemini-api-key-k8s \
  --from-literal=api-key="$(gcloud secrets versions access latest --secret=gemini-api-key)" \
  --namespace=production \
  --dry-run=client -o yaml | kubectl apply -f -

# Restart services using the key
kubectl rollout restart deployment/query-intelligence -n production
```

## Troubleshooting

### Common Issues

#### Permission Denied
```bash
# Check GCP authentication
gcloud auth list

# Verify project access
gcloud projects get-iam-policy ccl-platform

# Check Secret Manager permissions
gcloud secrets list
```

#### Secret Not Found
```bash
# List all secrets
gcloud secrets list --filter="name:ccl-"

# Create missing secret
gcloud secrets create ccl-new-secret --data-file=initial-value.txt
```

#### Kubernetes Update Failed
```bash
# Check cluster connectivity
kubectl cluster-info

# Verify namespace exists
kubectl get namespaces

# Check existing secrets
kubectl get secrets -n production
```

#### Service Health Check Failed
```bash
# Check pod status
kubectl get pods -n production

# View pod logs
kubectl logs -f deployment/analysis-engine -n production

# Check service endpoints
kubectl get endpoints -n production
```

### Debug Mode

Enable debug logging:

```bash
# Add to script beginning
set -x  # Enable bash debug mode

# Or set environment variable
export DEBUG=true
./rotate-secrets.sh --dry-run
```

## Security Considerations

### Access Control
- Script should run with minimal required permissions
- Use service accounts with specific roles
- Restrict access to rotation logs
- Monitor secret access patterns

### Audit Trail
- All operations are logged
- Secret Manager maintains version history
- Kubernetes events track updates
- Failed attempts are recorded

### Network Security
- Run from secure, monitored environment
- Use private networks when possible
- Monitor network traffic during rotation
- Validate all TLS connections

### Backup Strategy
- Previous secret versions are retained
- Backup labels enable easy identification
- Multiple environment isolation
- Regular backup verification

## Development

### Testing

```bash
# Test in development environment
./rotate-secrets.sh --environment development --dry-run

# Test specific secret types
./rotate-secrets.sh --environment development database

# Verify test results
./rotate-secrets.sh --environment development --verify-only
```

### Adding New Secret Types

1. **Define secret list**:
   ```bash
   rotate_new_secret_type() {
       local secrets=(
           "new-secret-1"
           "new-secret-2"
       )
       # Rotation logic
   }
   ```

2. **Add to main function**:
   ```bash
   case $SECRET_TYPE in
       "new-type")
           rotate_new_secret_type
           ;;
   esac
   ```

3. **Update help text and documentation**

### Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Update documentation
5. Submit a pull request

## License

This secret rotation system is part of the CCL platform and follows the same licensing terms.