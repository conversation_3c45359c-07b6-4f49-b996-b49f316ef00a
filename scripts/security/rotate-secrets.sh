#!/bin/bash

# CCL Automated Secret Rotation Script
# Rotates secrets across all CCL services with zero-downtime deployment

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &> /dev/null && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
GCP_PROJECT_ID="${GCP_PROJECT_ID:-ccl-platform}"
CLUSTER_NAME="${CLUSTER_NAME:-ccl-cluster}"
CLUSTER_LOCATION="${CLUSTER_LOCATION:-us-central1}"
ENVIRONMENT="${ENVIRONMENT:-production}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Help function
show_help() {
    cat << EOF
CCL Automated Secret Rotation Script

USAGE:
    $0 [OPTIONS] [SECRET_TYPE]

OPTIONS:
    -h, --help              Show this help message
    -e, --environment ENV   Target environment (development|staging|production)
    -d, --dry-run          Show what would be rotated without making changes
    -f, --force            Force rotation even if secrets are recently rotated
    --verify-only          Only verify current secrets without rotating
    --rollback VERSION     Rollback to a specific secret version

SECRET_TYPES:
    database               Rotate database passwords
    api-keys              Rotate external API keys
    service-accounts      Rotate service account keys
    encryption-keys       Rotate encryption keys
    certificates          Rotate TLS certificates
    all                   Rotate all secret types (default)

EXAMPLES:
    $0 --dry-run                           # Preview all rotations
    $0 database                            # Rotate only database secrets
    $0 --environment staging api-keys      # Rotate API keys in staging
    $0 --force encryption-keys             # Force rotate encryption keys
    $0 --rollback 2 database              # Rollback database secrets to version 2

EOF
}

# Parse command line arguments
ENVIRONMENT="production"
DRY_RUN=false
FORCE=false
VERIFY_ONLY=false
ROLLBACK_VERSION=""
SECRET_TYPE="all"

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        --verify-only)
            VERIFY_ONLY=true
            shift
            ;;
        --rollback)
            ROLLBACK_VERSION="$2"
            shift 2
            ;;
        database|api-keys|service-accounts|encryption-keys|certificates|all)
            SECRET_TYPE="$1"
            shift
            ;;
        *)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate environment
case $ENVIRONMENT in
    development|staging|production) ;;
    *)
        error "Invalid environment: $ENVIRONMENT"
        exit 1
        ;;
esac

log "Starting secret rotation for environment: $ENVIRONMENT"
log "Secret type: $SECRET_TYPE"
log "Dry run: $DRY_RUN"

# Initialize GCP authentication
init_gcp() {
    log "Initializing GCP authentication..."
    
    # Check if gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        error "gcloud CLI is not installed"
        exit 1
    fi
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed"
        exit 1
    fi
    
    # Set project
    gcloud config set project "$GCP_PROJECT_ID"
    
    # Get cluster credentials
    gcloud container clusters get-credentials "$CLUSTER_NAME" \
        --location "$CLUSTER_LOCATION" \
        --project "$GCP_PROJECT_ID"
    
    log "GCP authentication initialized"
}

# Generate secure random password
generate_password() {
    local length=${1:-32}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# Generate secure API key
generate_api_key() {
    local prefix=${1:-"ccl"}
    echo "${prefix}_$(date +%Y%m%d)_$(openssl rand -hex 16)"
}

# Backup current secret version
backup_secret() {
    local secret_name=$1
    local backup_label="backup-$(date +%Y%m%d-%H%M%S)"
    
    info "Backing up secret: $secret_name"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        info "[DRY RUN] Would backup secret $secret_name with label $backup_label"
        return 0
    fi
    
    # Add backup label to current version
    local current_version=$(gcloud secrets versions list "$secret_name" \
        --limit=1 --format="value(name)" 2>/dev/null || echo "")
    
    if [[ -n "$current_version" ]]; then
        gcloud secrets versions update "$current_version" \
            --secret="$secret_name" \
            --update-labels="backup=$backup_label" \
            --quiet
        
        log "Backed up secret $secret_name version $current_version"
    fi
}

# Check if secret needs rotation
needs_rotation() {
    local secret_name=$1
    local max_age_days=${2:-90}  # Default 90 days
    
    # Get creation time of latest version
    local creation_time=$(gcloud secrets versions list "$secret_name" \
        --limit=1 --format="value(createTime)" 2>/dev/null || echo "")
    
    if [[ -z "$creation_time" ]]; then
        return 0  # Secret doesn't exist, needs creation
    fi
    
    # Calculate age in days
    local creation_timestamp=$(date -d "$creation_time" +%s)
    local current_timestamp=$(date +%s)
    local age_days=$(( (current_timestamp - creation_timestamp) / 86400 ))
    
    if [[ "$FORCE" == "true" ]] || [[ $age_days -gt $max_age_days ]]; then
        return 0  # Needs rotation
    else
        info "Secret $secret_name is only $age_days days old (max: $max_age_days), skipping"
        return 1  # Does not need rotation
    fi
}

# Rotate database passwords
rotate_database_secrets() {
    log "Rotating database passwords..."
    
    local secrets=(
        "ccl-postgres-password"
        "ccl-redis-password"
        "ccl-spanner-password"
    )
    
    for secret_name in "${secrets[@]}"; do
        if needs_rotation "$secret_name" 60; then  # 60 days for DB passwords
            info "Rotating database secret: $secret_name"
            
            backup_secret "$secret_name"
            
            local new_password=$(generate_password 32)
            
            if [[ "$DRY_RUN" == "true" ]]; then
                info "[DRY RUN] Would create new version of $secret_name"
            else
                # Create new secret version
                echo -n "$new_password" | gcloud secrets versions add "$secret_name" \
                    --data-file=-
                
                # Update Kubernetes secrets
                kubectl create secret generic "${secret_name}-k8s" \
                    --from-literal=password="$new_password" \
                    --namespace="$ENVIRONMENT" \
                    --dry-run=client -o yaml | kubectl apply -f -
                
                # Trigger rolling restart of affected deployments
                local deployments=(
                    "analysis-engine"
                    "query-intelligence"
                    "pattern-mining"
                    "marketplace"
                )
                
                for deployment in "${deployments[@]}"; do
                    kubectl rollout restart deployment/"$deployment" \
                        --namespace="$ENVIRONMENT"
                done
                
                log "Successfully rotated $secret_name"
            fi
        fi
    done
}

# Rotate API keys
rotate_api_keys() {
    log "Rotating API keys..."
    
    local api_keys=(
        "gemini-api-key"
        "openai-api-key"
        "github-api-token"
        "slack-webhook-url"
    )
    
    for secret_name in "${api_keys[@]}"; do
        if needs_rotation "$secret_name" 30; then  # 30 days for API keys
            info "Rotating API key: $secret_name"
            
            backup_secret "$secret_name"
            
            if [[ "$DRY_RUN" == "true" ]]; then
                info "[DRY RUN] Would rotate API key $secret_name"
                continue
            fi
            
            case $secret_name in
                "gemini-api-key")
                    warn "Gemini API key rotation requires manual intervention in Google AI Studio"
                    info "Please rotate manually and update the secret"
                    ;;
                "openai-api-key")
                    warn "OpenAI API key rotation requires manual intervention in OpenAI dashboard"
                    info "Please rotate manually and update the secret"
                    ;;
                "github-api-token")
                    # Generate new GitHub token (requires manual setup)
                    warn "GitHub API token rotation requires manual intervention"
                    info "1. Go to GitHub Settings > Developer settings > Personal access tokens"
                    info "2. Generate new token with same permissions"
                    info "3. Update secret: gcloud secrets versions add $secret_name --data-file=-"
                    ;;
                "slack-webhook-url")
                    warn "Slack webhook URL rotation requires manual intervention in Slack app settings"
                    ;;
            esac
        fi
    done
}

# Rotate service account keys
rotate_service_account_keys() {
    log "Rotating service account keys..."
    
    local service_accounts=(
        "ccl-analysis-engine"
        "ccl-query-intelligence"
        "ccl-pattern-mining"
        "ccl-marketplace"
        "ccl-ml-operations"
    )
    
    for sa_name in "${service_accounts[@]}"; do
        local secret_name="${sa_name}-key"
        
        if needs_rotation "$secret_name" 90; then  # 90 days for SA keys
            info "Rotating service account key: $sa_name"
            
            backup_secret "$secret_name"
            
            if [[ "$DRY_RUN" == "true" ]]; then
                info "[DRY RUN] Would rotate service account key for $sa_name"
                continue
            fi
            
            # Create new service account key
            local temp_key_file=$(mktemp)
            gcloud iam service-accounts keys create "$temp_key_file" \
                --iam-account="${sa_name}@${GCP_PROJECT_ID}.iam.gserviceaccount.com"
            
            # Store new key in Secret Manager
            gcloud secrets versions add "$secret_name" \
                --data-file="$temp_key_file"
            
            # Update Kubernetes secret
            kubectl create secret generic "${secret_name}-k8s" \
                --from-file=key.json="$temp_key_file" \
                --namespace="$ENVIRONMENT" \
                --dry-run=client -o yaml | kubectl apply -f -
            
            # Clean up temp file
            rm -f "$temp_key_file"
            
            # Get old key IDs and delete them (keep last 2 versions)
            local old_keys=$(gcloud iam service-accounts keys list \
                --iam-account="${sa_name}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" \
                --format="value(name)" | head -n -2)
            
            for key_id in $old_keys; do
                if [[ "$key_id" != *"@"* ]]; then  # Skip the default key
                    gcloud iam service-accounts keys delete "$key_id" \
                        --iam-account="${sa_name}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" \
                        --quiet
                fi
            done
            
            log "Successfully rotated service account key for $sa_name"
        fi
    done
}

# Rotate encryption keys
rotate_encryption_keys() {
    log "Rotating encryption keys..."
    
    local encryption_keys=(
        "ccl-jwt-secret"
        "ccl-session-secret"
        "ccl-webhook-secret"
    )
    
    for secret_name in "${encryption_keys[@]}"; do
        if needs_rotation "$secret_name" 30; then  # 30 days for encryption keys
            info "Rotating encryption key: $secret_name"
            
            backup_secret "$secret_name"
            
            if [[ "$DRY_RUN" == "true" ]]; then
                info "[DRY RUN] Would rotate encryption key $secret_name"
                continue
            fi
            
            local new_key
            case $secret_name in
                "ccl-jwt-secret")
                    new_key=$(openssl rand -hex 64)
                    ;;
                "ccl-session-secret")
                    new_key=$(openssl rand -hex 32)
                    ;;
                "ccl-webhook-secret")
                    new_key=$(openssl rand -hex 32)
                    ;;
            esac
            
            # Store new key
            echo -n "$new_key" | gcloud secrets versions add "$secret_name" \
                --data-file=-
            
            # Update Kubernetes secrets
            kubectl create secret generic "${secret_name}-k8s" \
                --from-literal=key="$new_key" \
                --namespace="$ENVIRONMENT" \
                --dry-run=client -o yaml | kubectl apply -f -
            
            log "Successfully rotated encryption key $secret_name"
        fi
    done
}

# Rotate TLS certificates
rotate_certificates() {
    log "Rotating TLS certificates..."
    
    local certificates=(
        "ccl-tls-cert"
        "ccl-internal-ca"
    )
    
    for cert_name in "${certificates[@]}"; do
        if needs_rotation "$cert_name" 30; then  # 30 days before cert expiry
            info "Checking certificate: $cert_name"
            
            # Check certificate expiry
            local cert_data=$(gcloud secrets versions access latest \
                --secret="$cert_name" 2>/dev/null || echo "")
            
            if [[ -n "$cert_data" ]]; then
                local temp_cert=$(mktemp)
                echo "$cert_data" > "$temp_cert"
                
                local expiry_date=$(openssl x509 -in "$temp_cert" -noout -enddate 2>/dev/null | cut -d= -f2)
                local expiry_timestamp=$(date -d "$expiry_date" +%s 2>/dev/null || echo "0")
                local current_timestamp=$(date +%s)
                local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
                
                rm -f "$temp_cert"
                
                if [[ $days_until_expiry -lt 30 ]] || [[ "$FORCE" == "true" ]]; then
                    warn "Certificate $cert_name expires in $days_until_expiry days, needs rotation"
                    
                    if [[ "$DRY_RUN" == "true" ]]; then
                        info "[DRY RUN] Would rotate certificate $cert_name"
                    else
                        warn "Certificate rotation requires manual intervention or cert-manager"
                        info "Please check cert-manager or manually renew certificate $cert_name"
                    fi
                else
                    info "Certificate $cert_name is valid for $days_until_expiry more days"
                fi
            else
                warn "Could not retrieve certificate $cert_name"
            fi
        fi
    done
}

# Verify secret health after rotation
verify_secrets() {
    log "Verifying secret health..."
    
    local failed_verifications=0
    
    # Test database connections
    info "Testing database connections..."
    local db_pods=$(kubectl get pods -n "$ENVIRONMENT" -l app=analysis-engine --no-headers -o name | head -1)
    if [[ -n "$db_pods" ]]; then
        if kubectl exec -n "$ENVIRONMENT" "$db_pods" -- nc -z postgres 5432 &>/dev/null; then
            log "✅ Database connection test passed"
        else
            error "❌ Database connection test failed"
            ((failed_verifications++))
        fi
    fi
    
    # Test API key validity (basic check)
    info "Testing API key validity..."
    # This would require service-specific health checks
    
    # Test service account keys
    info "Testing service account keys..."
    local sa_pods=$(kubectl get pods -n "$ENVIRONMENT" -l app=marketplace --no-headers -o name | head -1)
    if [[ -n "$sa_pods" ]]; then
        if kubectl exec -n "$ENVIRONMENT" "$sa_pods" -- gcloud auth list --filter="status:ACTIVE" --format="value(account)" &>/dev/null; then
            log "✅ Service account authentication test passed"
        else
            error "❌ Service account authentication test failed"
            ((failed_verifications++))
        fi
    fi
    
    if [[ $failed_verifications -eq 0 ]]; then
        log "✅ All secret verifications passed"
        return 0
    else
        error "❌ $failed_verifications secret verifications failed"
        return 1
    fi
}

# Rollback secrets to previous version
rollback_secrets() {
    local version=$1
    local secret_type=$2
    
    log "Rolling back $secret_type secrets to version $version..."
    
    case $secret_type in
        "database")
            local secrets=("ccl-postgres-password" "ccl-redis-password" "ccl-spanner-password")
            ;;
        "api-keys")
            local secrets=("gemini-api-key" "openai-api-key" "github-api-token")
            ;;
        "service-accounts")
            local secrets=("ccl-analysis-engine-key" "ccl-query-intelligence-key")
            ;;
        *)
            error "Unknown secret type for rollback: $secret_type"
            exit 1
            ;;
    esac
    
    for secret_name in "${secrets[@]}"; do
        info "Rolling back $secret_name to version $version"
        
        if [[ "$DRY_RUN" == "true" ]]; then
            info "[DRY RUN] Would rollback $secret_name to version $version"
            continue
        fi
        
        # Get the secret value from the specified version
        local secret_value=$(gcloud secrets versions access "$version" \
            --secret="$secret_name" 2>/dev/null || echo "")
        
        if [[ -n "$secret_value" ]]; then
            # Create new version with old value
            echo -n "$secret_value" | gcloud secrets versions add "$secret_name" \
                --data-file=-
            
            log "Successfully rolled back $secret_name to version $version"
        else
            error "Could not retrieve version $version of secret $secret_name"
        fi
    done
}

# Main execution
main() {
    log "CCL Secret Rotation Script Started"
    log "Environment: $ENVIRONMENT"
    log "Secret Type: $SECRET_TYPE"
    
    # Initialize GCP
    init_gcp
    
    # Handle rollback if requested
    if [[ -n "$ROLLBACK_VERSION" ]]; then
        rollback_secrets "$ROLLBACK_VERSION" "$SECRET_TYPE"
        exit $?
    fi
    
    # Handle verify-only mode
    if [[ "$VERIFY_ONLY" == "true" ]]; then
        verify_secrets
        exit $?
    fi
    
    # Perform secret rotation based on type
    case $SECRET_TYPE in
        "database")
            rotate_database_secrets
            ;;
        "api-keys")
            rotate_api_keys
            ;;
        "service-accounts")
            rotate_service_account_keys
            ;;
        "encryption-keys")
            rotate_encryption_keys
            ;;
        "certificates")
            rotate_certificates
            ;;
        "all")
            rotate_database_secrets
            rotate_api_keys
            rotate_service_account_keys
            rotate_encryption_keys
            rotate_certificates
            ;;
        *)
            error "Unknown secret type: $SECRET_TYPE"
            exit 1
            ;;
    esac
    
    # Verify secrets after rotation (unless dry run)
    if [[ "$DRY_RUN" == "false" ]]; then
        log "Waiting 30 seconds for services to stabilize..."
        sleep 30
        
        if verify_secrets; then
            log "✅ Secret rotation completed successfully"
        else
            error "❌ Secret rotation completed but verification failed"
            exit 1
        fi
    else
        log "✅ Dry run completed successfully"
    fi
    
    log "Secret rotation script finished"
}

# Trap for cleanup on exit
cleanup() {
    local exit_code=$?
    if [[ $exit_code -ne 0 ]]; then
        error "Script failed with exit code $exit_code"
        info "Check logs above for error details"
        info "Consider running with --verify-only to check current state"
    fi
}

trap cleanup EXIT

# Run main function
main "$@"