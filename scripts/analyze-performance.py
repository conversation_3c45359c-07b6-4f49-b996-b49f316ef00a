#!/usr/bin/env python3
"""
Performance Test Results Analyzer
Analyzes k6 performance test results and determines if they meet baseline requirements
"""

import json
import sys
import argparse
from pathlib import Path
from typing import Dict, Any, Optional

# Performance baselines by service
SERVICE_BASELINES = {
    'analysis-engine': {
        'max_avg_response_time': 1000,  # ms
        'max_p95_response_time': 2000,  # ms
        'max_error_rate': 0.05,         # 5%
        'min_throughput': 10,           # requests/second
    },
    'query-intelligence': {
        'max_avg_response_time': 500,   # ms
        'max_p95_response_time': 1000,  # ms
        'max_error_rate': 0.02,         # 2%
        'min_throughput': 20,           # requests/second
    },
    'pattern-mining': {
        'max_avg_response_time': 2000,  # ms
        'max_p95_response_time': 5000,  # ms
        'max_error_rate': 0.03,         # 3%
        'min_throughput': 5,            # requests/second
    },
    'marketplace': {
        'max_avg_response_time': 300,   # ms
        'max_p95_response_time': 500,   # ms
        'max_error_rate': 0.01,         # 1%
        'min_throughput': 50,           # requests/second
    },
    'collaboration': {
        'max_avg_response_time': 200,   # ms
        'max_p95_response_time': 400,   # ms
        'max_error_rate': 0.02,         # 2%
        'min_throughput': 30,           # requests/second
    },
    'web': {
        'max_avg_response_time': 100,   # ms
        'max_p95_response_time': 200,   # ms
        'max_error_rate': 0.01,         # 1%
        'min_throughput': 100,          # requests/second
    },
    'default': {
        'max_avg_response_time': 500,   # ms
        'max_p95_response_time': 1000,  # ms
        'max_error_rate': 0.05,         # 5%
        'min_throughput': 10,           # requests/second
    }
}

class PerformanceAnalyzer:
    """Analyzes k6 performance test results"""
    
    def __init__(self, results_file: Path, service_name: str):
        self.results_file = results_file
        self.service_name = service_name
        self.baselines = SERVICE_BASELINES.get(service_name, SERVICE_BASELINES['default'])
        self.results = None
        self.metrics = None
        
    def load_results(self) -> bool:
        """Load k6 JSON results file"""
        try:
            with open(self.results_file, 'r') as f:
                # k6 outputs NDJSON, so we need to parse each line
                lines = f.readlines()
                
            # Parse the final summary metrics
            for line in reversed(lines):
                try:
                    data = json.loads(line.strip())
                    if data.get('type') == 'Point' and 'metric' in data:
                        # This is a metric point, skip for now
                        continue
                    elif 'metrics' in data:
                        # This is the final summary
                        self.results = data
                        self.metrics = data['metrics']
                        return True
                except json.JSONDecodeError:
                    continue
                    
            print("Error: Could not find summary metrics in results file")
            return False
            
        except FileNotFoundError:
            print(f"Error: Results file not found: {self.results_file}")
            return False
        except Exception as e:
            print(f"Error loading results: {e}")
            return False
    
    def extract_key_metrics(self) -> Dict[str, float]:
        """Extract key performance metrics"""
        if not self.metrics:
            return {}
            
        key_metrics = {}
        
        # HTTP request duration metrics
        if 'http_req_duration' in self.metrics:
            duration_stats = self.metrics['http_req_duration']
            key_metrics['avg_response_time'] = duration_stats.get('avg', 0)
            key_metrics['p95_response_time'] = duration_stats.get('p(95)', 0)
            key_metrics['p99_response_time'] = duration_stats.get('p(99)', 0)
            key_metrics['median_response_time'] = duration_stats.get('med', 0)
        
        # Request rate
        if 'http_reqs' in self.metrics:
            reqs_stats = self.metrics['http_reqs']
            key_metrics['throughput'] = reqs_stats.get('rate', 0)
            key_metrics['total_requests'] = reqs_stats.get('count', 0)
        
        # Error rate
        if 'http_req_failed' in self.metrics:
            failed_stats = self.metrics['http_req_failed']
            key_metrics['error_rate'] = failed_stats.get('rate', 0)
            key_metrics['failed_requests'] = failed_stats.get('count', 0)
        
        # Virtual users
        if 'vus' in self.metrics:
            vus_stats = self.metrics['vus']
            key_metrics['max_vus'] = vus_stats.get('max', 0)
            key_metrics['avg_vus'] = vus_stats.get('value', 0)
        
        # Custom metrics (if available)
        for metric_name, metric_data in self.metrics.items():
            if metric_name.startswith(self.service_name):
                key_metrics[metric_name] = metric_data.get('avg', metric_data.get('value', 0))
        
        return key_metrics
    
    def check_baselines(self, metrics: Dict[str, float]) -> Dict[str, bool]:
        """Check if metrics meet baseline requirements"""
        results = {}
        
        # Check average response time
        avg_response = metrics.get('avg_response_time', float('inf'))
        results['avg_response_time'] = avg_response <= self.baselines['max_avg_response_time']
        
        # Check 95th percentile response time
        p95_response = metrics.get('p95_response_time', float('inf'))
        results['p95_response_time'] = p95_response <= self.baselines['max_p95_response_time']
        
        # Check error rate
        error_rate = metrics.get('error_rate', 1.0)
        results['error_rate'] = error_rate <= self.baselines['max_error_rate']
        
        # Check throughput
        throughput = metrics.get('throughput', 0)
        results['throughput'] = throughput >= self.baselines['min_throughput']
        
        return results
    
    def generate_report(self, metrics: Dict[str, float], baseline_results: Dict[str, bool]) -> str:
        """Generate a detailed performance report"""
        report = f"\\n{'='*60}\\n"
        report += f"PERFORMANCE TEST REPORT - {self.service_name.upper()}\\n"
        report += f"{'='*60}\\n\\n"
        
        # Test Summary
        report += "TEST SUMMARY:\\n"
        report += f"  Service: {self.service_name}\\n"
        report += f"  Total Requests: {metrics.get('total_requests', 'N/A')}\\n"
        report += f"  Failed Requests: {metrics.get('failed_requests', 'N/A')}\\n"
        report += f"  Max Virtual Users: {metrics.get('max_vus', 'N/A')}\\n\\n"
        
        # Performance Metrics
        report += "PERFORMANCE METRICS:\\n"
        report += f"  Avg Response Time: {metrics.get('avg_response_time', 0):.2f}ms\\n"
        report += f"  95th Percentile: {metrics.get('p95_response_time', 0):.2f}ms\\n"
        report += f"  99th Percentile: {metrics.get('p99_response_time', 0):.2f}ms\\n"
        report += f"  Median Response Time: {metrics.get('median_response_time', 0):.2f}ms\\n"
        report += f"  Throughput: {metrics.get('throughput', 0):.2f} req/s\\n"
        report += f"  Error Rate: {metrics.get('error_rate', 0)*100:.2f}%\\n\\n"
        
        # Baseline Comparison
        report += "BASELINE COMPARISON:\\n"
        all_passed = True
        
        for check, passed in baseline_results.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            if not passed:
                all_passed = False
            
            if check == 'avg_response_time':
                actual = metrics.get('avg_response_time', 0)
                baseline = self.baselines['max_avg_response_time']
                report += f"  Avg Response Time: {status} ({actual:.2f}ms <= {baseline}ms)\\n"
            elif check == 'p95_response_time':
                actual = metrics.get('p95_response_time', 0)
                baseline = self.baselines['max_p95_response_time']
                report += f"  95th Percentile: {status} ({actual:.2f}ms <= {baseline}ms)\\n"
            elif check == 'error_rate':
                actual = metrics.get('error_rate', 0) * 100
                baseline = self.baselines['max_error_rate'] * 100
                report += f"  Error Rate: {status} ({actual:.2f}% <= {baseline:.2f}%)\\n"
            elif check == 'throughput':
                actual = metrics.get('throughput', 0)
                baseline = self.baselines['min_throughput']
                report += f"  Throughput: {status} ({actual:.2f} >= {baseline} req/s)\\n"
        
        report += f"\\nOVERALL RESULT: {'✅ ALL BASELINES PASSED' if all_passed else '❌ SOME BASELINES FAILED'}\\n"
        
        if not all_passed:
            report += "\\n⚠️  RECOMMENDATIONS:\\n"
            if not baseline_results.get('avg_response_time', True):
                report += "  - Optimize application response time\\n"
            if not baseline_results.get('p95_response_time', True):
                report += "  - Investigate tail latency issues\\n"
            if not baseline_results.get('error_rate', True):
                report += "  - Fix errors causing failures\\n"
            if not baseline_results.get('throughput', True):
                report += "  - Improve application throughput capacity\\n"
        
        report += f"\\n{'='*60}\\n"
        
        return report
    
    def analyze(self) -> int:
        """Main analysis function. Returns 0 if all baselines pass, 1 otherwise"""
        if not self.load_results():
            return 1
            
        metrics = self.extract_key_metrics()
        if not metrics:
            print("Error: No metrics found in results file")
            return 1
            
        baseline_results = self.check_baselines(metrics)
        report = self.generate_report(metrics, baseline_results)
        
        print(report)
        
        # Return 0 if all baselines pass, 1 if any fail
        return 0 if all(baseline_results.values()) else 1


def main():
    parser = argparse.ArgumentParser(description='Analyze k6 performance test results')
    parser.add_argument('results_file', help='Path to k6 JSON results file')
    parser.add_argument('service_name', help='Name of the service being tested')
    parser.add_argument('--baseline-config', help='Custom baseline configuration file (JSON)')
    parser.add_argument('--output-format', choices=['text', 'json'], default='text',
                       help='Output format')
    
    args = parser.parse_args()
    
    results_file = Path(args.results_file)
    if not results_file.exists():
        print(f"Error: Results file does not exist: {results_file}")
        return 1
    
    # Load custom baselines if provided
    if args.baseline_config:
        try:
            with open(args.baseline_config, 'r') as f:
                custom_baselines = json.load(f)
                SERVICE_BASELINES.update(custom_baselines)
        except Exception as e:
            print(f"Warning: Could not load custom baseline config: {e}")
    
    analyzer = PerformanceAnalyzer(results_file, args.service_name)
    return analyzer.analyze()


if __name__ == '__main__':
    sys.exit(main())