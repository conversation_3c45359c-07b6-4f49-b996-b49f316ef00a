#!/usr/bin/env python3
"""
Deployment Monitoring Script for CCL Platform
This script monitors deployment metrics and provides automated analysis
"""

import argparse
import json
import sys
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

import requests
from google.cloud import monitoring_v3
from google.cloud import logging as cloud_logging


class DeploymentMonitor:
    """Monitor deployment metrics and health"""
    
    def __init__(self, project_id: str, service: str, version: str, environment: str):
        self.project_id = project_id
        self.service = service
        self.version = version
        self.environment = environment
        
        # Initialize clients
        self.monitoring_client = monitoring_v3.MetricServiceClient()
        self.logging_client = cloud_logging.Client(project=project_id)
        
        # Thresholds
        self.error_rate_threshold = 0.05  # 5%
        self.latency_threshold_ms = 1000   # 1 second
        self.availability_threshold = 0.999  # 99.9%
        
    def monitor_deployment(self, duration_minutes: int = 5) -> bool:
        """
        Monitor deployment for specified duration
        Returns True if deployment is healthy, False otherwise
        """
        print(f"Monitoring {self.service}:{self.version} for {duration_minutes} minutes...")
        
        start_time = datetime.utcnow()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        # Monitoring intervals
        check_interval = 30  # seconds
        
        while datetime.utcnow() < end_time:
            try:
                # Check all metrics
                metrics = self._collect_metrics()
                health_status = self._analyze_health(metrics)
                
                # Log current status
                self._log_status(metrics, health_status)
                
                # Check if deployment is unhealthy
                if not health_status['healthy']:
                    print(f"❌ Deployment unhealthy: {health_status['reason']}")
                    return False
                
                # Wait before next check
                time.sleep(check_interval)
                
            except Exception as e:
                print(f"⚠️  Error during monitoring: {e}")
                time.sleep(check_interval)
        
        print("✅ Deployment monitoring completed successfully")
        return True
    
    def _collect_metrics(self) -> Dict:
        """Collect various deployment metrics"""
        now = datetime.utcnow()
        start_time = now - timedelta(minutes=5)
        
        metrics = {
            'timestamp': now.isoformat(),
            'error_rate': self._get_error_rate(start_time, now),
            'latency_p95': self._get_latency_p95(start_time, now),
            'request_count': self._get_request_count(start_time, now),
            'availability': self._get_availability(start_time, now),
            'cpu_utilization': self._get_cpu_utilization(start_time, now),
            'memory_utilization': self._get_memory_utilization(start_time, now),
            'instance_count': self._get_instance_count(),
        }
        
        return metrics
    
    def _get_error_rate(self, start_time: datetime, end_time: datetime) -> float:
        """Get error rate from Cloud Monitoring"""
        try:
            # Query for error count
            error_filter = (
                f'resource.type="cloud_run_revision" AND '
                f'resource.labels.service_name="{self.service}" AND '
                f'metric.type="run.googleapis.com/request_count" AND '
                f'metric.labels.response_code_class!="2xx"'
            )
            
            # Query for total count
            total_filter = (
                f'resource.type="cloud_run_revision" AND '
                f'resource.labels.service_name="{self.service}" AND '
                f'metric.type="run.googleapis.com/request_count"'
            )
            
            error_count = self._query_metric(error_filter, start_time, end_time)
            total_count = self._query_metric(total_filter, start_time, end_time)
            
            if total_count > 0:
                return error_count / total_count
            return 0.0
            
        except Exception as e:
            print(f"Error getting error rate: {e}")
            return 0.0
    
    def _get_latency_p95(self, start_time: datetime, end_time: datetime) -> float:
        """Get 95th percentile latency"""
        try:
            latency_filter = (
                f'resource.type="cloud_run_revision" AND '
                f'resource.labels.service_name="{self.service}" AND '
                f'metric.type="run.googleapis.com/request_latencies"'
            )
            
            # This is simplified - actual implementation would use percentile aggregation
            return self._query_metric(latency_filter, start_time, end_time, aggregation='ALIGN_PERCENTILE_95')
            
        except Exception as e:
            print(f"Error getting latency: {e}")
            return 0.0
    
    def _get_request_count(self, start_time: datetime, end_time: datetime) -> int:
        """Get total request count"""
        try:
            request_filter = (
                f'resource.type="cloud_run_revision" AND '
                f'resource.labels.service_name="{self.service}" AND '
                f'metric.type="run.googleapis.com/request_count"'
            )
            
            return int(self._query_metric(request_filter, start_time, end_time))
            
        except Exception as e:
            print(f"Error getting request count: {e}")
            return 0
    
    def _get_availability(self, start_time: datetime, end_time: datetime) -> float:
        """Calculate availability based on successful requests"""
        try:
            success_filter = (
                f'resource.type="cloud_run_revision" AND '
                f'resource.labels.service_name="{self.service}" AND '
                f'metric.type="run.googleapis.com/request_count" AND '
                f'metric.labels.response_code_class="2xx"'
            )
            
            total_filter = (
                f'resource.type="cloud_run_revision" AND '
                f'resource.labels.service_name="{self.service}" AND '
                f'metric.type="run.googleapis.com/request_count"'
            )
            
            success_count = self._query_metric(success_filter, start_time, end_time)
            total_count = self._query_metric(total_filter, start_time, end_time)
            
            if total_count > 0:
                return success_count / total_count
            return 1.0  # No requests = 100% availability
            
        except Exception as e:
            print(f"Error getting availability: {e}")
            return 1.0
    
    def _get_cpu_utilization(self, start_time: datetime, end_time: datetime) -> float:
        """Get CPU utilization"""
        try:
            cpu_filter = (
                f'resource.type="cloud_run_revision" AND '
                f'resource.labels.service_name="{self.service}" AND '
                f'metric.type="run.googleapis.com/container/cpu/utilizations"'
            )
            
            return self._query_metric(cpu_filter, start_time, end_time, aggregation='ALIGN_MEAN')
            
        except Exception as e:
            print(f"Error getting CPU utilization: {e}")
            return 0.0
    
    def _get_memory_utilization(self, start_time: datetime, end_time: datetime) -> float:
        """Get memory utilization"""
        try:
            memory_filter = (
                f'resource.type="cloud_run_revision" AND '
                f'resource.labels.service_name="{self.service}" AND '
                f'metric.type="run.googleapis.com/container/memory/utilizations"'
            )
            
            return self._query_metric(memory_filter, start_time, end_time, aggregation='ALIGN_MEAN')
            
        except Exception as e:
            print(f"Error getting memory utilization: {e}")
            return 0.0
    
    def _get_instance_count(self) -> int:
        """Get current instance count"""
        try:
            # This would typically use Cloud Run API to get current instance count
            # Simplified implementation
            return 1
            
        except Exception as e:
            print(f"Error getting instance count: {e}")
            return 0
    
    def _query_metric(self, filter_str: str, start_time: datetime, end_time: datetime, 
                     aggregation: str = 'ALIGN_SUM') -> float:
        """Query a metric from Cloud Monitoring"""
        try:
            project_name = f"projects/{self.project_id}"
            
            # Convert to timestamps
            interval = monitoring_v3.TimeInterval({
                "end_time": {"seconds": int(end_time.timestamp())},
                "start_time": {"seconds": int(start_time.timestamp())},
            })
            
            # Create aggregation
            aggregation_obj = monitoring_v3.Aggregation({
                "alignment_period": {"seconds": 300},  # 5 minutes
                "per_series_aligner": getattr(monitoring_v3.Aggregation.Aligner, aggregation),
            })
            
            # Query the metric
            results = self.monitoring_client.list_time_series(
                request={
                    "name": project_name,
                    "filter": filter_str,
                    "interval": interval,
                    "view": monitoring_v3.ListTimeSeriesRequest.TimeSeriesView.FULL,
                    "aggregation": aggregation_obj,
                }
            )
            
            # Extract value from results
            total_value = 0.0
            count = 0
            
            for result in results:
                for point in result.points:
                    if hasattr(point.value, 'double_value'):
                        total_value += point.value.double_value
                    elif hasattr(point.value, 'int64_value'):
                        total_value += point.value.int64_value
                    count += 1
            
            return total_value / count if count > 0 else 0.0
            
        except Exception as e:
            print(f"Error querying metric: {e}")
            return 0.0
    
    def _analyze_health(self, metrics: Dict) -> Dict:
        """Analyze metrics to determine deployment health"""
        issues = []
        
        # Check error rate
        if metrics['error_rate'] > self.error_rate_threshold:
            issues.append(f"High error rate: {metrics['error_rate']:.2%} > {self.error_rate_threshold:.2%}")
        
        # Check latency
        if metrics['latency_p95'] > self.latency_threshold_ms:
            issues.append(f"High latency: {metrics['latency_p95']:.0f}ms > {self.latency_threshold_ms}ms")
        
        # Check availability
        if metrics['availability'] < self.availability_threshold:
            issues.append(f"Low availability: {metrics['availability']:.3%} < {self.availability_threshold:.3%}")
        
        # Check resource utilization
        if metrics['cpu_utilization'] > 0.9:
            issues.append(f"High CPU utilization: {metrics['cpu_utilization']:.1%}")
        
        if metrics['memory_utilization'] > 0.9:
            issues.append(f"High memory utilization: {metrics['memory_utilization']:.1%}")
        
        return {
            'healthy': len(issues) == 0,
            'issues': issues,
            'reason': '; '.join(issues) if issues else 'All metrics within thresholds'
        }
    
    def _log_status(self, metrics: Dict, health_status: Dict):
        """Log current monitoring status"""
        status_icon = "✅" if health_status['healthy'] else "❌"
        
        print(f"{status_icon} {metrics['timestamp']}")
        print(f"   Error Rate: {metrics['error_rate']:.2%}")
        print(f"   Latency P95: {metrics['latency_p95']:.0f}ms")
        print(f"   Availability: {metrics['availability']:.3%}")
        print(f"   Requests: {metrics['request_count']}")
        print(f"   CPU: {metrics['cpu_utilization']:.1%}")
        print(f"   Memory: {metrics['memory_utilization']:.1%}")
        print(f"   Instances: {metrics['instance_count']}")
        
        if not health_status['healthy']:
            print(f"   Issues: {health_status['reason']}")
        
        print()


def main():
    parser = argparse.ArgumentParser(description='Monitor CCL deployment metrics')
    parser.add_argument('--service', required=True, help='Service name')
    parser.add_argument('--version', required=True, help='Version being deployed')
    parser.add_argument('--environment', required=True, help='Environment (dev/staging/prod)')
    parser.add_argument('--duration', type=int, default=5, help='Monitoring duration in minutes')
    parser.add_argument('--project-id', help='GCP Project ID (auto-detected if not provided)')
    
    args = parser.parse_args()
    
    # Auto-detect project ID based on environment
    if not args.project_id:
        project_mapping = {
            'development': 'ccl-platform-dev',
            'staging': 'ccl-platform-staging',
            'production': 'ccl-platform-prod'
        }
        args.project_id = project_mapping.get(args.environment, 'ccl-platform')
    
    # Create monitor and run
    monitor = DeploymentMonitor(
        project_id=args.project_id,
        service=args.service,
        version=args.version,
        environment=args.environment
    )
    
    success = monitor.monitor_deployment(args.duration)
    
    if not success:
        print("❌ Deployment monitoring failed")
        sys.exit(1)
    
    print("✅ Deployment monitoring completed successfully")


if __name__ == '__main__':
    main()
