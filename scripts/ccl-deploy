#!/usr/bin/env python3
"""
CCL Deployment CLI Tool

Consolidates deployment scripts into a single interface.
Replaces: deploy/rollout.sh, deploy/rollback.sh
"""

import argparse
import subprocess
import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime

# Script configuration
SCRIPT_DIR = Path(__file__).parent.resolve()
PROJECT_ROOT = SCRIPT_DIR.parent
SERVICES = ["analysis-engine", "query-intelligence", "pattern-mining", "marketplace", "collaboration-hub", "web"]


def run_command(cmd, check=True, capture_output=False):
    """Execute a shell command."""
    print(f"Running: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
    result = subprocess.run(
        cmd,
        shell=isinstance(cmd, str),
        check=check,
        capture_output=capture_output,
        text=True
    )
    return result


def get_current_version(service, env):
    """Get current deployed version of a service."""
    try:
        cmd = f"gcloud run services describe {service} --region=us-central1 --project=ccl-{env} --format='value(metadata.annotations.version)'"
        result = run_command(cmd, capture_output=True)
        return result.stdout.strip() or "unknown"
    except:
        return "unknown"


def rollout(args):
    """Deploy services to specified environment."""
    print(f"🚀 Rolling out to {args.env} environment...")
    
    # Validate environment
    if args.env not in ["development", "staging", "production"]:
        print(f"❌ Invalid environment: {args.env}")
        return 1
    
    # Production safeguards
    if args.env == "production":
        if not args.force:
            response = input("⚠️  Deploying to PRODUCTION. Are you sure? (yes/no): ")
            if response.lower() != "yes":
                print("Deployment cancelled.")
                return 0
        
        if not args.skip_tests:
            print("Running pre-deployment tests...")
            if run_command(["make", "test"], check=False).returncode != 0:
                print("❌ Tests failed. Aborting deployment.")
                return 1
    
    # Services to deploy
    services_to_deploy = args.service or SERVICES
    
    # Record deployment
    deployment_id = f"deploy-{int(time.time())}"
    deployment_record = {
        "id": deployment_id,
        "env": args.env,
        "services": services_to_deploy,
        "timestamp": datetime.now().isoformat(),
        "versions": {}
    }
    
    success_count = 0
    failed_services = []
    
    for service in services_to_deploy:
        print(f"\n📦 Deploying {service}...")
        
        # Get current version for rollback
        current_version = get_current_version(service, args.env)
        deployment_record["versions"][service] = {
            "previous": current_version,
            "new": args.tag or "latest"
        }
        
        # Build deployment command
        deploy_cmd = [
            "gcloud", "run", "deploy", service,
            f"--image=gcr.io/ccl-{args.env}/{service}:{args.tag or 'latest'}",
            "--region=us-central1",
            f"--project=ccl-{args.env}",
            "--platform=managed",
            f"--set-env-vars=ENVIRONMENT={args.env}"
        ]
        
        if args.env == "production":
            deploy_cmd.extend(["--min-instances=2", "--max-instances=100"])
        else:
            deploy_cmd.extend(["--min-instances=1", "--max-instances=10"])
        
        try:
            run_command(deploy_cmd)
            print(f"✅ {service} deployed successfully")
            success_count += 1
            
            # Run health check
            if not args.skip_health:
                print(f"🏥 Health check for {service}...")
                health_url = f"https://{service}-ccl-{args.env}.a.run.app/health"
                time.sleep(5)  # Give service time to start
                
                health_result = run_command(
                    ["curl", "-s", "-o", "/dev/null", "-w", "%{http_code}", health_url],
                    capture_output=True
                )
                
                if health_result.stdout.strip() != "200":
                    print(f"⚠️  Health check failed for {service}")
                    failed_services.append(service)
                else:
                    print(f"✅ {service} is healthy")
                    
        except subprocess.CalledProcessError:
            print(f"❌ Failed to deploy {service}")
            failed_services.append(service)
    
    # Save deployment record
    deployment_file = PROJECT_ROOT / f".deployments/{deployment_id}.json"
    deployment_file.parent.mkdir(exist_ok=True)
    with open(deployment_file, 'w') as f:
        json.dump(deployment_record, f, indent=2)
    
    # Summary
    print(f"\n📊 Deployment Summary:")
    print(f"  - Environment: {args.env}")
    print(f"  - Successful: {success_count}/{len(services_to_deploy)}")
    print(f"  - Deployment ID: {deployment_id}")
    
    if failed_services:
        print(f"  - Failed services: {', '.join(failed_services)}")
        return 1
    
    print("\n✅ Deployment completed successfully!")
    return 0


def rollback(args):
    """Rollback services to previous version."""
    print(f"🔄 Rolling back in {args.env} environment...")
    
    # Find deployment record
    if args.deployment_id:
        deployment_file = PROJECT_ROOT / f".deployments/{args.deployment_id}.json"
    else:
        # Find latest deployment
        deployment_dir = PROJECT_ROOT / ".deployments"
        if not deployment_dir.exists():
            print("❌ No deployment records found")
            return 1
        
        deployments = sorted(deployment_dir.glob("deploy-*.json"), reverse=True)
        if not deployments:
            print("❌ No deployment records found")
            return 1
        
        deployment_file = deployments[0]
        print(f"Using latest deployment: {deployment_file.stem}")
    
    if not deployment_file.exists():
        print(f"❌ Deployment record not found: {deployment_file}")
        return 1
    
    # Load deployment record
    with open(deployment_file) as f:
        deployment = json.load(f)
    
    if deployment["env"] != args.env:
        print(f"❌ Deployment was for {deployment['env']}, not {args.env}")
        return 1
    
    # Confirm rollback
    if not args.force:
        print(f"\nRolling back deployment {deployment['id']}:")
        print(f"  - Environment: {deployment['env']}")
        print(f"  - Services: {', '.join(deployment['services'])}")
        print(f"  - Timestamp: {deployment['timestamp']}")
        
        response = input("\nContinue with rollback? (y/N): ")
        if response.lower() != 'y':
            print("Rollback cancelled.")
            return 0
    
    # Rollback services
    services_to_rollback = args.service or deployment["services"]
    success_count = 0
    
    for service in services_to_rollback:
        if service not in deployment["versions"]:
            print(f"⚠️  No version info for {service}, skipping")
            continue
        
        previous_version = deployment["versions"][service]["previous"]
        if previous_version == "unknown":
            print(f"⚠️  Unknown previous version for {service}, skipping")
            continue
        
        print(f"\n🔄 Rolling back {service} to {previous_version}...")
        
        rollback_cmd = [
            "gcloud", "run", "deploy", service,
            f"--image=gcr.io/ccl-{args.env}/{service}:{previous_version}",
            "--region=us-central1",
            f"--project=ccl-{args.env}",
            "--platform=managed"
        ]
        
        try:
            run_command(rollback_cmd)
            print(f"✅ {service} rolled back successfully")
            success_count += 1
        except subprocess.CalledProcessError:
            print(f"❌ Failed to rollback {service}")
    
    print(f"\n📊 Rollback Summary:")
    print(f"  - Successful: {success_count}/{len(services_to_rollback)}")
    
    return 0 if success_count == len(services_to_rollback) else 1


def status(args):
    """Show deployment status for an environment."""
    print(f"📊 Deployment status for {args.env}...")
    
    for service in SERVICES:
        try:
            cmd = f"gcloud run services describe {service} --region=us-central1 --project=ccl-{args.env} --format=json"
            result = run_command(cmd, capture_output=True)
            service_info = json.loads(result.stdout)
            
            status = service_info.get("status", {}).get("conditions", [{}])[0].get("status", "Unknown")
            url = service_info.get("status", {}).get("url", "N/A")
            traffic = service_info.get("spec", {}).get("traffic", [{}])[0]
            revision = traffic.get("revisionName", "N/A")
            
            print(f"\n{service}:")
            print(f"  Status: {'✅' if status == 'True' else '❌'} {status}")
            print(f"  URL: {url}")
            print(f"  Revision: {revision}")
            
        except Exception as e:
            print(f"\n{service}: ❌ Not found or error occurred")
    
    return 0


def main():
    parser = argparse.ArgumentParser(
        description="CCL Deployment CLI - Deploy and manage services",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  ccl-deploy rollout staging                     # Deploy all services to staging
  ccl-deploy rollout production marketplace      # Deploy marketplace to production
  ccl-deploy rollback staging                    # Rollback latest deployment
  ccl-deploy rollback production -d deploy-123   # Rollback specific deployment
  ccl-deploy status production                   # Show production status
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Commands')
    
    # Rollout command
    rollout_parser = subparsers.add_parser('rollout', help='Deploy services')
    rollout_parser.add_argument('env', choices=['development', 'staging', 'production'], help='Target environment')
    rollout_parser.add_argument('service', nargs='*', help='Specific services to deploy')
    rollout_parser.add_argument('-t', '--tag', help='Docker image tag (default: latest)')
    rollout_parser.add_argument('-f', '--force', action='store_true', help='Skip confirmations')
    rollout_parser.add_argument('--skip-tests', action='store_true', help='Skip pre-deployment tests')
    rollout_parser.add_argument('--skip-health', action='store_true', help='Skip health checks')
    
    # Rollback command
    rollback_parser = subparsers.add_parser('rollback', help='Rollback deployment')
    rollback_parser.add_argument('env', choices=['development', 'staging', 'production'], help='Target environment')
    rollback_parser.add_argument('service', nargs='*', help='Specific services to rollback')
    rollback_parser.add_argument('-d', '--deployment-id', help='Specific deployment to rollback')
    rollback_parser.add_argument('-f', '--force', action='store_true', help='Skip confirmations')
    
    # Status command
    status_parser = subparsers.add_parser('status', help='Show deployment status')
    status_parser.add_argument('env', choices=['development', 'staging', 'production'], help='Environment to check')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 0
    
    # Map commands to functions
    commands = {
        'rollout': rollout,
        'rollback': rollback,
        'status': status
    }
    
    return commands[args.command](args)


if __name__ == '__main__':
    sys.exit(main())