#!/bin/bash

# CCL API Documentation Generator
# Generates OpenAPI documentation for all CCL services

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &> /dev/null && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
DOCS_OUTPUT_DIR="$PROJECT_ROOT/docs/api"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Help function
show_help() {
    cat << EOF
CCL API Documentation Generator

USAGE:
    $0 [OPTIONS] [SERVICE]

OPTIONS:
    -h, --help              Show this help message
    -o, --output DIR        Output directory for documentation (default: docs/api)
    -f, --format FORMAT     Output format (openapi|postman|markdown) (default: openapi)
    --serve                 Start local documentation server after generation
    --validate              Validate generated OpenAPI specs

SERVICES:
    analysis-engine         Generate docs for Analysis Engine (Rust)
    query-intelligence      Generate docs for Query Intelligence (Python)
    pattern-mining          Generate docs for Pattern Mining (Python)
    marketplace             Generate docs for Marketplace (Go)
    collaboration           Generate docs for Collaboration (TypeScript)
    web                     Generate docs for Web Frontend (TypeScript)
    all                     Generate docs for all services (default)

EXAMPLES:
    $0                                      # Generate all API docs
    $0 analysis-engine                      # Generate docs for Analysis Engine only
    $0 --format markdown --serve            # Generate markdown docs and serve locally
    $0 --validate all                       # Generate and validate all docs

EOF
}

# Parse command line arguments
OUTPUT_DIR="$DOCS_OUTPUT_DIR"
FORMAT="openapi"
SERVE=false
VALIDATE=false
SERVICE="all"

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -f|--format)
            FORMAT="$2"
            shift 2
            ;;
        --serve)
            SERVE=true
            shift
            ;;
        --validate)
            VALIDATE=true
            shift
            ;;
        analysis-engine|query-intelligence|pattern-mining|marketplace|collaboration|web|all)
            SERVICE="$1"
            shift
            ;;
        *)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

log "Starting API documentation generation"
log "Service: $SERVICE"
log "Format: $FORMAT"
log "Output: $OUTPUT_DIR"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Check required tools
check_requirements() {
    log "Checking requirements..."
    
    local missing_tools=()
    
    # Check for OpenAPI tools
    if [[ "$FORMAT" == "openapi" ]] || [[ "$VALIDATE" == "true" ]]; then
        if ! command -v swagger-codegen &> /dev/null && ! command -v openapi-generator &> /dev/null; then
            missing_tools+=("swagger-codegen or openapi-generator")
        fi
    fi
    
    # Check for language-specific tools
    if [[ "$SERVICE" == "analysis-engine" ]] || [[ "$SERVICE" == "all" ]]; then
        if ! command -v cargo &> /dev/null; then
            missing_tools+=("cargo (Rust)")
        fi
    fi
    
    if [[ "$SERVICE" == "query-intelligence" ]] || [[ "$SERVICE" == "pattern-mining" ]] || [[ "$SERVICE" == "all" ]]; then
        if ! command -v python3 &> /dev/null; then
            missing_tools+=("python3")
        fi
    fi
    
    if [[ "$SERVICE" == "marketplace" ]] || [[ "$SERVICE" == "all" ]]; then
        if ! command -v go &> /dev/null; then
            missing_tools+=("go")
        fi
    fi
    
    if [[ "$SERVICE" == "collaboration" ]] || [[ "$SERVICE" == "web" ]] || [[ "$SERVICE" == "all" ]]; then
        if ! command -v npm &> /dev/null; then
            missing_tools+=("npm")
        fi
    fi
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        error "Missing required tools: ${missing_tools[*]}"
        exit 1
    fi
    
    log "All requirements satisfied"
}

# Generate documentation for Rust services (Analysis Engine)
generate_rust_docs() {
    local service_name="$1"
    local service_dir="$PROJECT_ROOT/services/$service_name"
    
    if [[ ! -d "$service_dir" ]]; then
        warn "Service directory not found: $service_dir"
        return 1
    fi
    
    log "Generating documentation for $service_name (Rust)"
    
    cd "$service_dir"
    
    # Generate Rust documentation
    cargo doc --no-deps --document-private-items
    
    # Check if utoipa (OpenAPI for Rust) is available
    if grep -q "utoipa" Cargo.toml; then
        info "Found utoipa dependency, generating OpenAPI spec"
        
        # Create OpenAPI generation script
        cat > src/openapi_gen.rs << 'EOF'
use utoipa::OpenApi;
use std::fs;

#[derive(OpenApi)]
#[openapi(
    paths(
        // Add your API paths here
    ),
    components(
        schemas(
            // Add your schemas here
        )
    ),
    tags(
        (name = "analysis", description = "Code analysis endpoints"),
        (name = "repository", description = "Repository management endpoints"),
        (name = "health", description = "Health check endpoints")
    ),
    info(
        title = "CCL Analysis Engine API",
        version = "1.0.0",
        description = "CCL Analysis Engine REST API for code analysis and pattern detection",
        contact(
            name = "CCL Platform Team",
            email = "<EMAIL>"
        )
    ),
    servers(
        (url = "https://api.ccl.dev/v1/analysis", description = "Production server"),
        (url = "https://staging-api.ccl.dev/v1/analysis", description = "Staging server"),
        (url = "http://localhost:8001", description = "Local development server")
    )
)]
struct ApiDoc;

fn main() {
    let openapi = ApiDoc::openapi();
    let json = serde_json::to_string_pretty(&openapi).unwrap();
    fs::write("../../../docs/api/analysis-engine-openapi.json", json).unwrap();
    println!("OpenAPI spec generated successfully");
}
EOF
        
        # Compile and run OpenAPI generator
        rustc --edition 2021 src/openapi_gen.rs -o openapi_gen
        ./openapi_gen || warn "Failed to generate OpenAPI spec for $service_name"
        rm -f openapi_gen src/openapi_gen.rs
        
    else
        # Generate basic OpenAPI spec from code analysis
        info "Generating basic OpenAPI spec from code analysis"
        
        cat > "$OUTPUT_DIR/${service_name}-openapi.json" << EOF
{
  "openapi": "3.0.3",
  "info": {
    "title": "CCL ${service_name^} API",
    "version": "1.0.0",
    "description": "CCL ${service_name^} REST API",
    "contact": {
      "name": "CCL Platform Team",
      "email": "<EMAIL>"
    }
  },
  "servers": [
    {
      "url": "https://api.ccl.dev/v1/analysis",
      "description": "Production server"
    },
    {
      "url": "http://localhost:8001",
      "description": "Local development server"
    }
  ],
  "paths": {
    "/health": {
      "get": {
        "tags": ["health"],
        "summary": "Health check endpoint",
        "responses": {
          "200": {
            "description": "Service is healthy"
          }
        }
      }
    },
    "/analyze": {
      "post": {
        "tags": ["analysis"],
        "summary": "Analyze code repository",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "repository_url": {
                    "type": "string",
                    "format": "uri"
                  },
                  "analysis_type": {
                    "type": "string",
                    "enum": ["quick", "full"]
                  }
                }
              }
            }
          }
        },
        "responses": {
          "202": {
            "description": "Analysis started",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "job_id": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
EOF
    fi
    
    log "Documentation generated for $service_name"
}

# Generate documentation for Python services
generate_python_docs() {
    local service_name="$1"
    local service_dir="$PROJECT_ROOT/services/$service_name"
    
    if [[ ! -d "$service_dir" ]]; then
        warn "Service directory not found: $service_dir"
        return 1
    fi
    
    log "Generating documentation for $service_name (Python)"
    
    cd "$service_dir"
    
    # Check if FastAPI is used
    if grep -q "fastapi" requirements.txt 2>/dev/null || find . -name "*.py" -exec grep -l "from fastapi" {} \; | head -1 > /dev/null; then
        info "FastAPI detected, generating OpenAPI spec"
        
        # Create OpenAPI extraction script
        cat > extract_openapi.py << 'EOF'
import sys
import json
import importlib.util
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path.cwd()))

try:
    # Try to import the main FastAPI app
    spec = importlib.util.spec_from_file_location("main", "main.py")
    main_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(main_module)
    
    if hasattr(main_module, 'app'):
        app = main_module.app
        openapi_schema = app.openapi()
        
        with open('../../docs/api/SERVICE_NAME-openapi.json', 'w') as f:
            json.dump(openapi_schema, f, indent=2)
        
        print("OpenAPI spec extracted successfully")
    else:
        print("No FastAPI app found in main.py")
        
except Exception as e:
    print(f"Error extracting OpenAPI spec: {e}")
    # Generate basic spec as fallback
    basic_spec = {
        "openapi": "3.0.3",
        "info": {
            "title": f"CCL SERVICE_NAME API",
            "version": "1.0.0",
            "description": "CCL SERVICE_NAME REST API"
        },
        "paths": {
            "/health": {
                "get": {
                    "summary": "Health check",
                    "responses": {"200": {"description": "OK"}}
                }
            }
        }
    }
    
    with open('../../docs/api/SERVICE_NAME-openapi.json', 'w') as f:
        json.dump(basic_spec, f, indent=2)
EOF
        
        # Replace SERVICE_NAME placeholder
        sed -i "s/SERVICE_NAME/$service_name/g" extract_openapi.py
        
        # Run extraction
        python3 extract_openapi.py || warn "Failed to extract OpenAPI spec for $service_name"
        rm -f extract_openapi.py
        
    else
        # Generate basic spec for non-FastAPI services
        info "Generating basic OpenAPI spec"
        
        cat > "$OUTPUT_DIR/${service_name}-openapi.json" << EOF
{
  "openapi": "3.0.3",
  "info": {
    "title": "CCL ${service_name^} API",
    "version": "1.0.0",
    "description": "CCL ${service_name^} REST API",
    "contact": {
      "name": "CCL Platform Team",
      "email": "<EMAIL>"
    }
  },
  "servers": [
    {
      "url": "https://api.ccl.dev/v1/${service_name}",
      "description": "Production server"
    }
  ],
  "paths": {
    "/health": {
      "get": {
        "tags": ["health"],
        "summary": "Health check endpoint",
        "responses": {
          "200": {
            "description": "Service is healthy"
          }
        }
      }
    }
  }
}
EOF
    fi
    
    log "Documentation generated for $service_name"
}

# Generate documentation for Go services
generate_go_docs() {
    local service_name="$1"
    local service_dir="$PROJECT_ROOT/services/$service_name"
    
    if [[ ! -d "$service_dir" ]]; then
        warn "Service directory not found: $service_dir"
        return 1
    fi
    
    log "Generating documentation for $service_name (Go)"
    
    cd "$service_dir"
    
    # Check if swag (Swagger for Go) is available
    if command -v swag &> /dev/null; then
        info "Found swag, generating OpenAPI spec"
        
        # Generate swagger docs
        swag init -g main.go -o docs/ || warn "Failed to generate swagger docs"
        
        # Copy generated spec to output directory
        if [[ -f docs/swagger.json ]]; then
            cp docs/swagger.json "$OUTPUT_DIR/${service_name}-openapi.json"
        fi
        
    else
        # Generate basic spec
        info "swag not found, generating basic OpenAPI spec"
        
        cat > "$OUTPUT_DIR/${service_name}-openapi.json" << EOF
{
  "openapi": "3.0.3",
  "info": {
    "title": "CCL ${service_name^} API",
    "version": "1.0.0",
    "description": "CCL ${service_name^} REST API",
    "contact": {
      "name": "CCL Platform Team",
      "email": "<EMAIL>"
    }
  },
  "servers": [
    {
      "url": "https://api.ccl.dev/v1/${service_name}",
      "description": "Production server"
    }
  ],
  "paths": {
    "/health": {
      "get": {
        "tags": ["health"],
        "summary": "Health check endpoint",
        "responses": {
          "200": {
            "description": "Service is healthy"
          }
        }
      }
    }
  }
}
EOF
    fi
    
    log "Documentation generated for $service_name"
}

# Generate documentation for TypeScript services
generate_typescript_docs() {
    local service_name="$1"
    local service_dir="$PROJECT_ROOT/services/$service_name"
    
    if [[ ! -d "$service_dir" ]]; then
        warn "Service directory not found: $service_dir"
        return 1
    fi
    
    log "Generating documentation for $service_name (TypeScript)"
    
    cd "$service_dir"
    
    # Check for OpenAPI/Swagger packages
    if grep -q "@nestjs/swagger" package.json 2>/dev/null; then
        info "NestJS Swagger detected"
        
        # Generate OpenAPI spec using NestJS CLI
        npm run build && npm run generate:openapi || warn "Failed to generate OpenAPI spec"
        
    elif grep -q "swagger-jsdoc" package.json 2>/dev/null; then
        info "swagger-jsdoc detected"
        
        # Create extraction script
        cat > extract-openapi.js << 'EOF'
const swaggerJSDoc = require('swagger-jsdoc');
const fs = require('fs');

const options = {
  definition: {
    openapi: '3.0.3',
    info: {
      title: 'CCL SERVICE_NAME API',
      version: '1.0.0',
      description: 'CCL SERVICE_NAME REST API'
    },
    servers: [
      {
        url: 'https://api.ccl.dev/v1/SERVICE_NAME',
        description: 'Production server'
      }
    ]
  },
  apis: ['./src/**/*.ts', './src/**/*.js']
};

const specs = swaggerJSDoc(options);
fs.writeFileSync('../../docs/api/SERVICE_NAME-openapi.json', JSON.stringify(specs, null, 2));
console.log('OpenAPI spec generated successfully');
EOF
        
        # Replace SERVICE_NAME placeholder
        sed -i "s/SERVICE_NAME/$service_name/g" extract-openapi.js
        
        # Run extraction
        node extract-openapi.js || warn "Failed to extract OpenAPI spec"
        rm -f extract-openapi.js
        
    else
        # Generate basic spec
        info "Generating basic OpenAPI spec"
        
        cat > "$OUTPUT_DIR/${service_name}-openapi.json" << EOF
{
  "openapi": "3.0.3",
  "info": {
    "title": "CCL ${service_name^} API",
    "version": "1.0.0",
    "description": "CCL ${service_name^} REST API",
    "contact": {
      "name": "CCL Platform Team",
      "email": "<EMAIL>"
    }
  },
  "servers": [
    {
      "url": "https://api.ccl.dev/v1/${service_name}",
      "description": "Production server"
    }
  ],
  "paths": {
    "/health": {
      "get": {
        "tags": ["health"],
        "summary": "Health check endpoint",
        "responses": {
          "200": {
            "description": "Service is healthy"
          }
        }
      }
    }
  }
}
EOF
    fi
    
    log "Documentation generated for $service_name"
}

# Generate aggregated API documentation
generate_aggregated_docs() {
    log "Generating aggregated API documentation"
    
    # Create index page
    cat > "$OUTPUT_DIR/index.html" << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CCL API Documentation</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .service { border: 1px solid #ddd; border-radius: 8px; margin: 20px 0; padding: 20px; }
        .service h2 { color: #2563eb; }
        .links { display: flex; gap: 10px; margin-top: 10px; }
        .link { padding: 8px 16px; background: #3b82f6; color: white; text-decoration: none; border-radius: 4px; }
        .link:hover { background: #2563eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 CCL API Documentation</h1>
        <p>Comprehensive API documentation for all CCL platform services.</p>
        
        <div class="service">
            <h2>Analysis Engine</h2>
            <p>Code analysis and AST parsing service built with Rust.</p>
            <div class="links">
                <a href="analysis-engine-openapi.json" class="link">OpenAPI Spec</a>
                <a href="swagger-ui.html?url=analysis-engine-openapi.json" class="link">Swagger UI</a>
            </div>
        </div>
        
        <div class="service">
            <h2>Query Intelligence</h2>
            <p>Natural language query processing service built with Python.</p>
            <div class="links">
                <a href="query-intelligence-openapi.json" class="link">OpenAPI Spec</a>
                <a href="swagger-ui.html?url=query-intelligence-openapi.json" class="link">Swagger UI</a>
            </div>
        </div>
        
        <div class="service">
            <h2>Pattern Mining</h2>
            <p>ML-powered pattern detection service built with Python.</p>
            <div class="links">
                <a href="pattern-mining-openapi.json" class="link">OpenAPI Spec</a>
                <a href="swagger-ui.html?url=pattern-mining-openapi.json" class="link">Swagger UI</a>
            </div>
        </div>
        
        <div class="service">
            <h2>Marketplace</h2>
            <p>Pattern marketplace and commerce service built with Go.</p>
            <div class="links">
                <a href="marketplace-openapi.json" class="link">OpenAPI Spec</a>
                <a href="swagger-ui.html?url=marketplace-openapi.json" class="link">Swagger UI</a>
            </div>
        </div>
        
        <div class="service">
            <h2>Collaboration</h2>
            <p>Real-time collaboration service built with TypeScript.</p>
            <div class="links">
                <a href="collaboration-openapi.json" class="link">OpenAPI Spec</a>
                <a href="swagger-ui.html?url=collaboration-openapi.json" class="link">Swagger UI</a>
            </div>
        </div>
        
        <div class="service">
            <h2>Web Frontend</h2>
            <p>Main web application built with TypeScript/React.</p>
            <div class="links">
                <a href="web-openapi.json" class="link">OpenAPI Spec</a>
                <a href="swagger-ui.html?url=web-openapi.json" class="link">Swagger UI</a>
            </div>
        </div>
    </div>
</body>
</html>
EOF

    # Create Swagger UI page
    cat > "$OUTPUT_DIR/swagger-ui.html" << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CCL API Documentation - Swagger UI</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script>
        const urlParams = new URLSearchParams(window.location.search);
        const specUrl = urlParams.get('url') || 'analysis-engine-openapi.json';
        
        SwaggerUIBundle({
            url: specUrl,
            dom_id: '#swagger-ui',
            presets: [
                SwaggerUIBundle.presets.apis,
                SwaggerUIBundle.presets.standalone
            ]
        });
    </script>
</body>
</html>
EOF

    log "Aggregated documentation generated"
}

# Validate OpenAPI specifications
validate_openapi_specs() {
    log "Validating OpenAPI specifications"
    
    local validation_errors=0
    
    for spec_file in "$OUTPUT_DIR"/*-openapi.json; do
        if [[ -f "$spec_file" ]]; then
            local service_name=$(basename "$spec_file" -openapi.json)
            info "Validating $service_name OpenAPI spec"
            
            # Basic JSON validation
            if ! python3 -m json.tool "$spec_file" > /dev/null 2>&1; then
                error "Invalid JSON in $spec_file"
                ((validation_errors++))
                continue
            fi
            
            # OpenAPI schema validation (if swagger-codegen is available)
            if command -v swagger-codegen &> /dev/null; then
                if ! swagger-codegen validate -i "$spec_file" > /dev/null 2>&1; then
                    warn "OpenAPI validation warnings for $spec_file"
                fi
            fi
            
            log "✅ $service_name OpenAPI spec is valid"
        fi
    done
    
    if [[ $validation_errors -eq 0 ]]; then
        log "All OpenAPI specifications are valid"
        return 0
    else
        error "$validation_errors OpenAPI specifications have validation errors"
        return 1
    fi
}

# Start documentation server
serve_docs() {
    log "Starting documentation server"
    
    cd "$OUTPUT_DIR"
    
    # Try Python 3 first, then Python 2
    if command -v python3 &> /dev/null; then
        log "Documentation server running at http://localhost:8080"
        log "Press Ctrl+C to stop"
        python3 -m http.server 8080
    elif command -v python &> /dev/null; then
        log "Documentation server running at http://localhost:8080"
        log "Press Ctrl+C to stop"
        python -m SimpleHTTPServer 8080
    else
        error "Python not found, cannot start documentation server"
        return 1
    fi
}

# Main execution
main() {
    log "CCL API Documentation Generator Started"
    
    # Check requirements
    check_requirements
    
    # Generate documentation based on service selection
    case $SERVICE in
        "analysis-engine")
            generate_rust_docs "analysis-engine"
            ;;
        "query-intelligence")
            generate_python_docs "query-intelligence"
            ;;
        "pattern-mining")
            generate_python_docs "pattern-mining"
            ;;
        "marketplace")
            generate_go_docs "marketplace"
            ;;
        "collaboration")
            generate_typescript_docs "collaboration"
            ;;
        "web")
            generate_typescript_docs "web"
            ;;
        "all")
            generate_rust_docs "analysis-engine"
            generate_python_docs "query-intelligence"
            generate_python_docs "pattern-mining"
            generate_go_docs "marketplace"
            generate_typescript_docs "collaboration"
            generate_typescript_docs "web"
            ;;
        *)
            error "Unknown service: $SERVICE"
            exit 1
            ;;
    esac
    
    # Generate aggregated documentation
    generate_aggregated_docs
    
    # Validate specifications if requested
    if [[ "$VALIDATE" == "true" ]]; then
        validate_openapi_specs
    fi
    
    # Serve documentation if requested
    if [[ "$SERVE" == "true" ]]; then
        serve_docs
    fi
    
    log "✅ API documentation generation completed"
    log "Documentation available at: $OUTPUT_DIR"
}

# Run main function
main "$@"