# CCL Scripts Directory

This directory contains utility scripts for managing the CCL (Codebase Context Layer) platform.

## 🚀 Quick Start

All major operations have been consolidated into three main CLI tools:

```bash
# Development environment management
./ccl-dev setup          # Initial setup
./ccl-dev start          # Start services
./ccl-dev stop           # Stop services

# Deployment operations  
./ccl-deploy rollout staging       # Deploy to staging
./ccl-deploy rollback production   # Rollback production

# Validation checks
./ccl-validate all       # Run all validations
./ccl-validate security  # Security checks only
```

## 📁 Directory Structure

```
scripts/
├── ccl-dev              # Development environment CLI
├── ccl-deploy          # Deployment operations CLI
├── ccl-validate        # Validation and checks CLI
├── analyze-performance.py
├── monitor-deployment.py
├── documentation/
│   └── generate-api-docs.sh
├── security/
│   ├── README.md
│   ├── rotate-secrets.sh
│   └── cron-secret-rotation.txt
└── archived/           # Legacy scripts (reference only)
```

## 🛠️ CLI Tools

### ccl-dev - Development Environment Management

Manages the local development environment using Docker Compose.

**Commands:**
- `setup` - Set up the development environment
- `start [services...]` - Start all or specific services
- `stop [--volumes]` - Stop services (optionally remove volumes)
- `reset [--force]` - Reset environment to clean state
- `seed [services...]` - Seed databases with sample data
- `health [services...]` - Check service health status
- `logs [-f] [-t N] [services...]` - View service logs

**Examples:**
```bash
./ccl-dev setup                    # Initial setup
./ccl-dev start                    # Start all services
./ccl-dev start analysis-engine    # Start specific service
./ccl-dev logs -f marketplace      # Follow marketplace logs
./ccl-dev health                   # Check all services
./ccl-dev reset --force           # Reset without confirmation
```

### ccl-deploy - Deployment Operations

Handles deployments to different environments (development, staging, production).

**Commands:**
- `rollout <env> [services...]` - Deploy services to environment
- `rollback <env> [services...]` - Rollback to previous version
- `status <env>` - Show deployment status

**Options:**
- `-t, --tag` - Docker image tag to deploy
- `-f, --force` - Skip confirmation prompts
- `--skip-tests` - Skip pre-deployment tests
- `--skip-health` - Skip post-deployment health checks
- `-d, --deployment-id` - Specific deployment to rollback

**Examples:**
```bash
./ccl-deploy rollout staging                     # Deploy all to staging
./ccl-deploy rollout production marketplace      # Deploy one service
./ccl-deploy rollback staging                    # Rollback latest deployment
./ccl-deploy status production                   # Check production status
```

### ccl-validate - Validation Suite

Runs various validation checks on the codebase.

**Commands:**
- `all` - Run all validation checks
- `architecture` - Validate system architecture
- `contracts` - Validate service contracts
- `security` - Check security configurations

**Options:**
- `--strict` - Treat warnings as errors

**Examples:**
```bash
./ccl-validate all                 # Run all checks
./ccl-validate security --strict   # Strict security check
./ccl-validate contracts          # Check service contracts
```

## 📊 Other Scripts

### analyze-performance.py
Analyzes performance metrics from service logs and generates reports.

```bash
python analyze-performance.py --service analysis-engine --days 7
```

### monitor-deployment.py
Monitors active deployments and sends alerts for issues.

```bash
python monitor-deployment.py --env production --slack-webhook $WEBHOOK_URL
```

### documentation/generate-api-docs.sh
Generates API documentation from OpenAPI specs.

```bash
./documentation/generate-api-docs.sh
```

### security/rotate-secrets.sh
Rotates secrets in GCP Secret Manager. See `security/README.md` for details.

```bash
./security/rotate-secrets.sh --env production --service marketplace
```

## 🔒 Security Scripts

See `security/README.md` for detailed documentation on:
- Secret rotation procedures
- Automated rotation via cron
- Security best practices

## 🏗️ Archived Scripts

The `archived/` directory contains legacy scripts that have been replaced by the consolidated CLI tools. These are kept for reference but should not be used for new development.

## 🤝 Contributing

When adding new scripts:

1. **Consider consolidation** - Can this be added to an existing CLI tool?
2. **Use Python** for complex logic (better error handling)
3. **Add help text** - All scripts must support `--help`
4. **Follow naming** - Use kebab-case for script names
5. **Document usage** - Update this README with examples
6. **Add validation** - Check prerequisites before running
7. **Handle errors** - Fail gracefully with clear messages

## 🚨 Important Notes

- All scripts assume they're run from the project root or scripts directory
- Production deployments require explicit confirmation
- Validation scripts can be run in CI/CD pipelines
- Security scripts require appropriate GCP permissions

## 📋 Maintenance

Regular maintenance tasks:

1. **Weekly**: Run `ccl-validate all` to ensure consistency
2. **Before deploy**: Run `ccl-validate security --strict`
3. **Monthly**: Review and archive unused scripts
4. **Quarterly**: Update dependencies in script requirements

---

For issues or questions, consult the main project documentation or contact the CCL team.