#!/bin/bash
# Deploy CI/CD Monitoring Integration
# This script deploys all components of the CI/CD monitoring integration

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE=${NAMESPACE:-monitoring}
ENVIRONMENT=${ENVIRONMENT:-staging}
DRY_RUN=${DRY_RUN:-false}

# Logging functions
log() {
    echo -e "${BLUE}[DEPLOY]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check required tools
    local required_tools=("kubectl" "curl" "jq")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            error "Required tool not found: $tool"
            exit 1
        fi
    done
    
    # Check kubectl connectivity
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check namespace exists
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        warning "Namespace $NAMESPACE does not exist, creating..."
        kubectl create namespace "$NAMESPACE"
    fi
    
    success "Prerequisites check passed"
}

# Validate secrets
validate_secrets() {
    log "Validating required secrets..."
    
    local required_secrets=("github-credentials" "webhook-credentials")
    local missing_secrets=()
    
    for secret in "${required_secrets[@]}"; do
        if ! kubectl get secret "$secret" -n "$NAMESPACE" &> /dev/null; then
            missing_secrets+=("$secret")
        fi
    done
    
    if [[ ${#missing_secrets[@]} -gt 0 ]]; then
        error "Missing required secrets: ${missing_secrets[*]}"
        echo ""
        echo "Create missing secrets:"
        for secret in "${missing_secrets[@]}"; do
            case "$secret" in
                "github-credentials")
                    echo "kubectl create secret generic github-credentials --from-literal=token=YOUR_GITHUB_TOKEN -n $NAMESPACE"
                    ;;
                "webhook-credentials")
                    echo "kubectl create secret generic webhook-credentials --from-literal=secret=YOUR_WEBHOOK_SECRET -n $NAMESPACE"
                    ;;
            esac
        done
        exit 1
    fi
    
    success "All required secrets are present"
}

# Deploy metric exporters
deploy_exporters() {
    log "Deploying metric exporters..."
    
    local exporters=(
        "infrastructure/monitoring/exporters/github-actions-exporter.yml"
        "infrastructure/monitoring/exporters/deployment-webhook.yml"
        "infrastructure/monitoring/exporters/lead-time-calculator.yml"
    )
    
    for exporter in "${exporters[@]}"; do
        if [[ -f "$exporter" ]]; then
            log "Deploying $(basename "$exporter" .yml)..."
            if [[ "$DRY_RUN" == "true" ]]; then
                kubectl apply -f "$exporter" --dry-run=client
            else
                kubectl apply -f "$exporter"
            fi
        else
            warning "Exporter file not found: $exporter"
        fi
    done
    
    if [[ "$DRY_RUN" != "true" ]]; then
        # Wait for deployments to be ready
        log "Waiting for exporters to be ready..."
        kubectl wait --for=condition=available --timeout=300s deployment -l component=cicd-monitoring -n "$NAMESPACE"
    fi
    
    success "Metric exporters deployed"
}

# Update Prometheus configuration
update_prometheus() {
    log "Updating Prometheus configuration..."
    
    if [[ -f "infrastructure/monitoring/opentelemetry/collector-config.yaml" ]]; then
        if [[ "$DRY_RUN" == "true" ]]; then
            kubectl apply -f infrastructure/monitoring/opentelemetry/collector-config.yaml --dry-run=client
        else
            kubectl apply -f infrastructure/monitoring/opentelemetry/collector-config.yaml
            
            # Restart collector to pick up new configuration
            log "Restarting OpenTelemetry collector..."
            kubectl rollout restart deployment/opentelemetry-collector -n "$NAMESPACE"
            kubectl rollout status deployment/opentelemetry-collector -n "$NAMESPACE" --timeout=300s
        fi
        success "Prometheus configuration updated"
    else
        warning "Prometheus configuration file not found"
    fi
}

# Import Grafana dashboards
import_dashboards() {
    log "Importing Grafana dashboards..."
    
    # Get Grafana credentials
    local grafana_url="http://grafana.ccl.io"
    local grafana_user="admin"
    local grafana_pass="admin"  # Should be retrieved from secret in production
    
    local dashboards=(
        "infrastructure/monitoring/dashboards/cicd-pipeline-dashboard.json"
        "infrastructure/monitoring/dashboards/team-views/development-team-dashboard.json"
        "infrastructure/monitoring/dashboards/team-views/platform-team-dashboard.json"
    )
    
    for dashboard in "${dashboards[@]}"; do
        if [[ -f "$dashboard" ]]; then
            local dashboard_name=$(basename "$dashboard" .json)
            log "Importing $dashboard_name..."
            
            if [[ "$DRY_RUN" != "true" ]]; then
                # Import dashboard
                local response=$(curl -s -X POST \
                    -H "Content-Type: application/json" \
                    -u "$grafana_user:$grafana_pass" \
                    -d @"$dashboard" \
                    "$grafana_url/api/dashboards/db")
                
                if echo "$response" | jq -e '.status == "success"' > /dev/null; then
                    success "Dashboard imported: $dashboard_name"
                else
                    warning "Failed to import dashboard: $dashboard_name"
                    echo "Response: $response"
                fi
            else
                log "DRY RUN: Would import $dashboard_name"
            fi
        else
            warning "Dashboard file not found: $dashboard"
        fi
    done
}

# Deploy alerts
deploy_alerts() {
    log "Deploying enhanced alerts..."
    
    local alert_files=(
        "infrastructure/monitoring/alerts/service-alerts.yaml"
        "infrastructure/monitoring/alerts/cicd-alerts.yaml"
        "infrastructure/monitoring/alerts/notification-routing.yaml"
    )
    
    for alert_file in "${alert_files[@]}"; do
        if [[ -f "$alert_file" ]]; then
            log "Deploying $(basename "$alert_file")..."
            if [[ "$DRY_RUN" == "true" ]]; then
                kubectl apply -f "$alert_file" --dry-run=client
            else
                kubectl apply -f "$alert_file"
            fi
        else
            warning "Alert file not found: $alert_file"
        fi
    done
    
    if [[ "$DRY_RUN" != "true" ]]; then
        # Restart AlertManager to pick up new configuration
        log "Restarting AlertManager..."
        kubectl rollout restart deployment/alertmanager -n "$NAMESPACE"
        kubectl rollout status deployment/alertmanager -n "$NAMESPACE" --timeout=300s
    fi
    
    success "Alerts deployed"
}

# Configure SLOs
configure_slos() {
    log "Configuring SLOs..."
    
    if [[ -f "infrastructure/monitoring/slos/slo-definitions.yaml" ]]; then
        if [[ "$DRY_RUN" == "true" ]]; then
            kubectl apply -f infrastructure/monitoring/slos/slo-definitions.yaml --dry-run=client
        else
            kubectl apply -f infrastructure/monitoring/slos/slo-definitions.yaml
        fi
        success "SLOs configured"
    else
        warning "SLO configuration file not found"
    fi
}

# Apply performance optimizations
apply_optimizations() {
    log "Applying performance optimizations..."
    
    if [[ -f "infrastructure/monitoring/optimization/performance-tuning.yaml" ]]; then
        if [[ "$DRY_RUN" == "true" ]]; then
            kubectl apply -f infrastructure/monitoring/optimization/performance-tuning.yaml --dry-run=client
        else
            kubectl apply -f infrastructure/monitoring/optimization/performance-tuning.yaml
        fi
        success "Performance optimizations applied"
    else
        warning "Performance optimization file not found"
    fi
}

# Validate deployment
validate_deployment() {
    log "Validating deployment..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        success "DRY RUN: Validation skipped"
        return
    fi
    
    # Check exporter pods
    local exporter_pods=$(kubectl get pods -n "$NAMESPACE" -l component=cicd-monitoring --no-headers | wc -l)
    if [[ $exporter_pods -gt 0 ]]; then
        success "Exporter pods are running: $exporter_pods"
    else
        error "No exporter pods found"
    fi
    
    # Check metrics availability (requires port-forward or ingress)
    log "Checking metric availability..."
    local prometheus_url="http://prometheus.ccl.io"
    
    # Test if we can reach Prometheus
    if curl -s "$prometheus_url/api/v1/label/__name__/values" > /dev/null; then
        # Check for CI/CD metrics
        local cicd_metrics=$(curl -s "$prometheus_url/api/v1/label/__name__/values" | jq -r '.data[]' | grep -c "^ccl_" || true)
        if [[ $cicd_metrics -gt 0 ]]; then
            success "CI/CD metrics available: $cicd_metrics metrics"
        else
            warning "No CI/CD metrics found yet (may take a few minutes)"
        fi
    else
        warning "Cannot reach Prometheus API for validation"
    fi
    
    success "Deployment validation completed"
}

# Show next steps
show_next_steps() {
    echo ""
    log "Deployment completed successfully!"
    echo ""
    echo "Next Steps:"
    echo ""
    echo "1. Verify dashboards are accessible:"
    echo "   - CI/CD Pipeline: https://grafana.ccl.io/d/ccl-cicd-pipeline"
    echo "   - Development Team: https://grafana.ccl.io/d/ccl-dev-team"
    echo "   - Platform Team: https://grafana.ccl.io/d/ccl-platform-team"
    echo ""
    echo "2. Test alert routing:"
    echo "   - Trigger a test deployment failure"
    echo "   - Verify alerts are routed correctly"
    echo ""
    echo "3. Monitor metric collection:"
    echo "   - Check Prometheus targets: http://prometheus.ccl.io/targets"
    echo "   - Verify CI/CD metrics are being scraped"
    echo ""
    echo "4. Team training:"
    echo "   - Schedule dashboard training sessions"
    echo "   - Update runbooks with new monitoring capabilities"
    echo ""
    echo "Documentation:"
    echo "   - Implementation Guide: docs/cicd/monitoring-integration-implementation.md"
    echo "   - Troubleshooting: docs/cicd/troubleshooting.md"
    echo ""
    success "Happy monitoring! 📊"
}

# Main deployment function
main() {
    echo "🚀 CCL CI/CD Monitoring Integration Deployment"
    echo "=============================================="
    echo ""
    echo "Environment: $ENVIRONMENT"
    echo "Namespace: $NAMESPACE"
    echo "Dry Run: $DRY_RUN"
    echo ""
    
    check_prerequisites
    validate_secrets
    deploy_exporters
    update_prometheus
    import_dashboards
    deploy_alerts
    configure_slos
    apply_optimizations
    validate_deployment
    show_next_steps
}

# Handle script interruption
trap 'error "Deployment interrupted"; exit 1' INT TERM

# Show help if requested
if [[ "${1:-}" == "--help" || "${1:-}" == "-h" ]]; then
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --help, -h          Show this help message"
    echo "  --dry-run           Perform a dry run without making changes"
    echo ""
    echo "Environment Variables:"
    echo "  NAMESPACE           Kubernetes namespace (default: monitoring)"
    echo "  ENVIRONMENT         Deployment environment (default: staging)"
    echo "  DRY_RUN            Perform dry run (default: false)"
    echo ""
    echo "Examples:"
    echo "  $0                  # Deploy to staging"
    echo "  DRY_RUN=true $0     # Dry run"
    echo "  ENVIRONMENT=production $0  # Deploy to production"
    exit 0
fi

# Handle dry run flag
if [[ "${1:-}" == "--dry-run" ]]; then
    DRY_RUN=true
fi

# Run main function
main "$@"
