#!/usr/bin/env python3
"""
CCL Development CLI Tool

Consolidates all development scripts into a single interface.
Replaces: setup.sh, start.sh, stop.sh, reset.sh, seed-data.sh, health-check.sh
"""

import argparse
import subprocess
import sys
import os
from pathlib import Path

# Script root directory
SCRIPT_DIR = Path(__file__).parent.resolve()
PROJECT_ROOT = SCRIPT_DIR.parent
DOCKER_COMPOSE_FILE = PROJECT_ROOT / "docker" / "docker-compose.yml"


def run_command(cmd, check=True, capture_output=False):
    """Execute a shell command."""
    print(f"Running: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
    result = subprocess.run(
        cmd,
        shell=isinstance(cmd, str),
        check=check,
        capture_output=capture_output,
        text=True
    )
    return result


def setup(args):
    """Set up the development environment."""
    print("🔧 Setting up development environment...")
    
    # Check Docker
    try:
        run_command(["docker", "--version"], capture_output=True)
        print("✅ Docker is installed")
    except:
        print("❌ Docker is not installed. Please install Docker first.")
        return 1
    
    # Check docker-compose file
    if not DOCKER_COMPOSE_FILE.exists():
        print(f"❌ Docker compose file not found at {DOCKER_COMPOSE_FILE}")
        return 1
    
    # Build containers
    print("Building Docker containers...")
    run_command(["docker-compose", "-f", str(DOCKER_COMPOSE_FILE), "build"])
    
    # Create necessary directories
    dirs_to_create = [
        PROJECT_ROOT / "data",
        PROJECT_ROOT / "logs",
        PROJECT_ROOT / "tmp"
    ]
    for dir_path in dirs_to_create:
        dir_path.mkdir(exist_ok=True)
        print(f"✅ Created directory: {dir_path}")
    
    print("✅ Development environment setup complete!")
    return 0


def start(args):
    """Start the development environment."""
    print("🚀 Starting CCL development environment...")
    
    cmd = ["docker-compose", "-f", str(DOCKER_COMPOSE_FILE), "up", "-d"]
    if args.service:
        cmd.extend(args.service)
    
    run_command(cmd)
    
    if not args.no_logs:
        print("\n📋 Showing logs (Ctrl+C to exit)...")
        logs_cmd = ["docker-compose", "-f", str(DOCKER_COMPOSE_FILE), "logs", "-f"]
        if args.service:
            logs_cmd.extend(args.service)
        run_command(logs_cmd, check=False)
    
    return 0


def stop(args):
    """Stop the development environment."""
    print("🛑 Stopping CCL development environment...")
    
    cmd = ["docker-compose", "-f", str(DOCKER_COMPOSE_FILE), "down"]
    if args.volumes:
        cmd.append("-v")
        print("⚠️  Removing volumes...")
    
    run_command(cmd)
    print("✅ Development environment stopped!")
    return 0


def reset(args):
    """Reset the development environment."""
    print("🔄 Resetting CCL development environment...")
    
    if not args.force:
        response = input("⚠️  This will remove all data. Continue? (y/N): ")
        if response.lower() != 'y':
            print("Reset cancelled.")
            return 0
    
    # Stop and remove everything
    run_command(["docker-compose", "-f", str(DOCKER_COMPOSE_FILE), "down", "-v", "--remove-orphans"])
    
    # Clean up data directories
    data_dirs = [PROJECT_ROOT / "data", PROJECT_ROOT / "logs", PROJECT_ROOT / "tmp"]
    for dir_path in data_dirs:
        if dir_path.exists():
            import shutil
            shutil.rmtree(dir_path)
            print(f"✅ Removed {dir_path}")
    
    # Recreate directories
    for dir_path in data_dirs:
        dir_path.mkdir(exist_ok=True)
    
    print("✅ Development environment reset complete!")
    return 0


def seed(args):
    """Seed the database with sample data."""
    print("🌱 Seeding database with sample data...")
    
    # Check if services are running
    result = run_command(
        ["docker-compose", "-f", str(DOCKER_COMPOSE_FILE), "ps", "-q"],
        capture_output=True
    )
    
    if not result.stdout.strip():
        print("❌ Services not running. Please run 'ccl-dev start' first.")
        return 1
    
    # Run seed scripts for each service
    services_to_seed = args.service or ["analysis-engine", "query-intelligence", "marketplace"]
    
    for service in services_to_seed:
        print(f"\nSeeding {service}...")
        seed_cmd = [
            "docker-compose", "-f", str(DOCKER_COMPOSE_FILE),
            "exec", service, "python", "scripts/seed_data.py"
        ]
        try:
            run_command(seed_cmd)
            print(f"✅ {service} seeded successfully")
        except subprocess.CalledProcessError:
            print(f"⚠️  Failed to seed {service}")
    
    return 0


def health(args):
    """Check health of all services."""
    print("🏥 Checking service health...")
    
    services = [
        ("analysis-engine", "http://localhost:8001/health"),
        ("query-intelligence", "http://localhost:8002/health"),
        ("pattern-mining", "http://localhost:8003/health"),
        ("marketplace", "http://localhost:8004/health"),
        ("collaboration-hub", "http://localhost:8005/health"),
        ("web", "http://localhost:3000/api/health")
    ]
    
    healthy = 0
    unhealthy = 0
    
    for service, url in services:
        if args.service and service not in args.service:
            continue
            
        try:
            result = run_command(
                ["curl", "-s", "-o", "/dev/null", "-w", "%{http_code}", url],
                capture_output=True
            )
            status_code = result.stdout.strip()
            
            if status_code == "200":
                print(f"✅ {service}: Healthy")
                healthy += 1
            else:
                print(f"❌ {service}: Unhealthy (HTTP {status_code})")
                unhealthy += 1
        except:
            print(f"❌ {service}: Not responding")
            unhealthy += 1
    
    print(f"\n📊 Summary: {healthy} healthy, {unhealthy} unhealthy")
    return 1 if unhealthy > 0 else 0


def logs(args):
    """Show logs for services."""
    print("📋 Showing logs...")
    
    cmd = ["docker-compose", "-f", str(DOCKER_COMPOSE_FILE), "logs"]
    
    if args.follow:
        cmd.append("-f")
    
    if args.tail:
        cmd.extend(["--tail", str(args.tail)])
    
    if args.service:
        cmd.extend(args.service)
    
    run_command(cmd, check=False)
    return 0


def main():
    parser = argparse.ArgumentParser(
        description="CCL Development CLI - Manage your development environment",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  ccl-dev setup                    # Set up development environment
  ccl-dev start                    # Start all services
  ccl-dev start analysis-engine    # Start specific service
  ccl-dev stop                     # Stop all services
  ccl-dev reset --force           # Reset environment without confirmation
  ccl-dev seed                    # Seed all databases
  ccl-dev health                  # Check all services
  ccl-dev logs -f marketplace     # Follow logs for marketplace
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Commands')
    
    # Setup command
    setup_parser = subparsers.add_parser('setup', help='Set up development environment')
    
    # Start command
    start_parser = subparsers.add_parser('start', help='Start development environment')
    start_parser.add_argument('service', nargs='*', help='Specific services to start')
    start_parser.add_argument('--no-logs', action='store_true', help='Don\'t show logs after starting')
    
    # Stop command
    stop_parser = subparsers.add_parser('stop', help='Stop development environment')
    stop_parser.add_argument('-v', '--volumes', action='store_true', help='Remove volumes')
    
    # Reset command
    reset_parser = subparsers.add_parser('reset', help='Reset development environment')
    reset_parser.add_argument('-f', '--force', action='store_true', help='Skip confirmation')
    
    # Seed command
    seed_parser = subparsers.add_parser('seed', help='Seed databases with sample data')
    seed_parser.add_argument('service', nargs='*', help='Specific services to seed')
    
    # Health command
    health_parser = subparsers.add_parser('health', help='Check service health')
    health_parser.add_argument('service', nargs='*', help='Specific services to check')
    
    # Logs command
    logs_parser = subparsers.add_parser('logs', help='Show service logs')
    logs_parser.add_argument('service', nargs='*', help='Specific services')
    logs_parser.add_argument('-f', '--follow', action='store_true', help='Follow log output')
    logs_parser.add_argument('-t', '--tail', type=int, help='Number of lines to show')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 0
    
    # Map commands to functions
    commands = {
        'setup': setup,
        'start': start,
        'stop': stop,
        'reset': reset,
        'seed': seed,
        'health': health,
        'logs': logs
    }
    
    return commands[args.command](args)


if __name__ == '__main__':
    sys.exit(main())