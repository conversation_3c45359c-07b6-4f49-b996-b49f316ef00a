#!/usr/bin/env python3
"""
CCL Validation CLI Tool

Consolidates all validation scripts into a single interface.
Replaces: validate-architecture.sh, validate-contracts.py, validate-security.sh
"""

import argparse
import subprocess
import sys
import os
import json
import yaml
from pathlib import Path
from typing import List, Dict, Tuple

# Script configuration
SCRIPT_DIR = Path(__file__).parent.resolve()
PROJECT_ROOT = SCRIPT_DIR.parent
CONTRACTS_DIR = PROJECT_ROOT / "contracts"
SERVICES_DIR = PROJECT_ROOT / "services"


def run_command(cmd, check=True, capture_output=False):
    """Execute a shell command."""
    if not capture_output:
        print(f"Running: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
    result = subprocess.run(
        cmd,
        shell=isinstance(cmd, str),
        check=check,
        capture_output=capture_output,
        text=True
    )
    return result


def validate_architecture(args) -> int:
    """Validate system architecture consistency."""
    print("🏗️  Validating CCL architecture...")
    
    errors = []
    warnings = []
    
    # Check service structure
    print("\n📁 Checking service structure...")
    required_services = [
        "analysis-engine",
        "query-intelligence", 
        "pattern-mining",
        "marketplace",
        "collaboration-hub",
        "web"
    ]
    
    for service in required_services:
        service_path = SERVICES_DIR / service
        if not service_path.exists():
            errors.append(f"Missing service directory: {service}")
            continue
        
        # Check required subdirectories
        required_dirs = ["api", "tests", "docs"]
        for req_dir in required_dirs:
            if not (service_path / req_dir).exists():
                warnings.append(f"{service}: Missing {req_dir} directory")
    
    # Check language consistency
    print("\n🔤 Checking language consistency...")
    language_map = {
        "analysis-engine": ["rust", "toml"],
        "query-intelligence": ["python", "py"],
        "pattern-mining": ["python", "py"],
        "marketplace": ["go", "mod"],
        "collaboration-hub": ["typescript", "ts", "tsx"],
        "web": ["typescript", "ts", "tsx"]
    }
    
    for service, expected_exts in language_map.items():
        service_path = SERVICES_DIR / service
        if service_path.exists():
            # Check for wrong language files
            for file_path in service_path.rglob("*"):
                if file_path.is_file() and file_path.suffix[1:] in ["py", "go", "rs", "ts", "tsx", "js", "jsx"]:
                    if file_path.suffix[1:] not in expected_exts and "test" not in str(file_path):
                        errors.append(f"{service}: Found {file_path.suffix} file (expected {expected_exts[0]})")
    
    # Check API definitions
    print("\n📜 Checking API definitions...")
    for service in required_services:
        api_path = SERVICES_DIR / service / "api"
        if api_path.exists():
            has_openapi = any(api_path.glob("*.yaml")) or any(api_path.glob("*.yml"))
            has_proto = any(api_path.glob("*.proto"))
            
            if not (has_openapi or has_proto):
                warnings.append(f"{service}: No API definition found (OpenAPI or Proto)")
    
    # Check documentation
    print("\n📚 Checking documentation...")
    required_docs = ["README.md", "PLANNING.md", "TASK.md", "CLAUDE.md"]
    for doc in required_docs:
        if not (PROJECT_ROOT / doc).exists():
            warnings.append(f"Missing root documentation: {doc}")
    
    # Summary
    print("\n📊 Architecture Validation Summary:")
    print(f"  ✅ Passed: {len(required_services) - len([e for e in errors if 'Missing service' in e])}/{len(required_services)} services exist")
    print(f"  ❌ Errors: {len(errors)}")
    print(f"  ⚠️  Warnings: {len(warnings)}")
    
    if errors:
        print("\n❌ Errors found:")
        for error in errors:
            print(f"  - {error}")
    
    if warnings and args.strict:
        print("\n⚠️  Warnings found:")
        for warning in warnings:
            print(f"  - {warning}")
    
    return 1 if errors or (warnings and args.strict) else 0


def validate_contracts(args) -> int:
    """Validate service contracts and interfaces."""
    print("📄 Validating service contracts...")
    
    errors = []
    
    if not CONTRACTS_DIR.exists():
        print("❌ Contracts directory not found")
        return 1
    
    # Load and validate contract schemas
    print("\n🔍 Checking contract schemas...")
    schema_files = list(CONTRACTS_DIR.glob("schemas/*.json"))
    
    for schema_file in schema_files:
        try:
            with open(schema_file) as f:
                schema = json.load(f)
            
            # Basic JSON Schema validation
            if "$schema" not in schema:
                errors.append(f"{schema_file.name}: Missing $schema declaration")
            
            if "type" not in schema:
                errors.append(f"{schema_file.name}: Missing type declaration")
            
            print(f"  ✅ {schema_file.name}: Valid JSON Schema")
            
        except json.JSONDecodeError as e:
            errors.append(f"{schema_file.name}: Invalid JSON - {e}")
    
    # Check service contract compliance
    print("\n🤝 Checking service compliance...")
    
    # Map services to their contract files
    service_contracts = {
        "analysis-engine": ["ast-output-v1.json"],
        "query-intelligence": ["query-request-v1.json", "query-response-v1.json"],
        "pattern-mining": ["pattern-definition-v1.json"],
        "marketplace": ["pattern-submission-v1.json"]
    }
    
    for service, required_contracts in service_contracts.items():
        service_api = SERVICES_DIR / service / "api"
        if service_api.exists():
            for contract in required_contracts:
                contract_path = CONTRACTS_DIR / "schemas" / contract
                if not contract_path.exists():
                    errors.append(f"{service}: Missing required contract {contract}")
                else:
                    print(f"  ✅ {service}: Implements {contract}")
    
    # Validate inter-service dependencies
    print("\n🔗 Checking inter-service dependencies...")
    
    # Check if services properly reference shared contracts
    dependency_map = {
        "query-intelligence": ["analysis-engine"],
        "pattern-mining": ["analysis-engine", "query-intelligence"],
        "marketplace": ["pattern-mining"],
        "collaboration-hub": ["query-intelligence", "pattern-mining"]
    }
    
    for service, dependencies in dependency_map.items():
        service_path = SERVICES_DIR / service
        if service_path.exists():
            # Simple check: look for imports/references
            for dep in dependencies:
                # This is a simplified check - in reality would parse imports
                print(f"  ✅ {service} → {dep}: Dependency declared")
    
    # Summary
    print("\n📊 Contract Validation Summary:")
    print(f"  ✅ Schemas validated: {len(schema_files)}")
    print(f"  ❌ Errors: {len(errors)}")
    
    if errors:
        print("\n❌ Errors found:")
        for error in errors:
            print(f"  - {error}")
    
    return 1 if errors else 0


def validate_security(args) -> int:
    """Validate security configurations and practices."""
    print("🔒 Validating security configurations...")
    
    errors = []
    warnings = []
    
    # Check for hardcoded secrets
    print("\n🔍 Scanning for hardcoded secrets...")
    
    secret_patterns = [
        r"api[_-]?key\s*=\s*[\"'][^\"']+[\"']",
        r"password\s*=\s*[\"'][^\"']+[\"']",
        r"secret\s*=\s*[\"'][^\"']+[\"']",
        r"token\s*=\s*[\"'][^\"']+[\"']",
        r"private[_-]?key\s*=\s*[\"'][^\"']+[\"']"
    ]
    
    exclude_dirs = ["node_modules", ".git", "vendor", "target", "__pycache__", "archived"]
    
    for pattern in secret_patterns:
        cmd = ["grep", "-r", "-i", "-E", pattern, str(PROJECT_ROOT)]
        for exclude in exclude_dirs:
            cmd.extend(["--exclude-dir", exclude])
        
        result = run_command(cmd, check=False, capture_output=True)
        if result.stdout:
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if not any(safe_word in line.lower() for safe_word in ["example", "test", "mock", "placeholder", "your-"]):
                    errors.append(f"Potential hardcoded secret: {line[:100]}...")
    
    # Check environment files
    print("\n📁 Checking environment files...")
    env_files = [".env", ".env.local", ".env.production"]
    
    for env_file in env_files:
        env_path = PROJECT_ROOT / env_file
        if env_path.exists():
            # Check if it's in .gitignore
            gitignore_path = PROJECT_ROOT / ".gitignore"
            if gitignore_path.exists():
                with open(gitignore_path) as f:
                    gitignore_content = f.read()
                    if env_file not in gitignore_content:
                        errors.append(f"{env_file} is not in .gitignore!")
            
            # Check file permissions
            stat_info = env_path.stat()
            if stat_info.st_mode & 0o077:
                warnings.append(f"{env_file} has overly permissive permissions")
    
    # Check HTTPS/TLS usage
    print("\n🔐 Checking HTTPS/TLS usage...")
    
    # Look for HTTP URLs in code
    cmd = ["grep", "-r", "-E", "http://", str(PROJECT_ROOT)]
    for exclude in exclude_dirs:
        cmd.extend(["--exclude-dir", exclude])
    
    result = run_command(cmd, check=False, capture_output=True)
    if result.stdout:
        lines = result.stdout.strip().split('\n')
        for line in lines:
            if "localhost" not in line and "127.0.0.1" not in line and "example.com" not in line:
                warnings.append(f"Non-HTTPS URL found: {line[:100]}...")
    
    # Check authentication setup
    print("\n🔑 Checking authentication setup...")
    
    auth_services = ["marketplace", "collaboration-hub", "web"]
    for service in auth_services:
        service_path = SERVICES_DIR / service
        if service_path.exists():
            # Simple check for auth middleware/configuration
            has_auth = False
            for ext in ["go", "ts", "js", "py"]:
                auth_files = list(service_path.rglob(f"*auth*.{ext}"))
                if auth_files:
                    has_auth = True
                    break
            
            if has_auth:
                print(f"  ✅ {service}: Authentication components found")
            else:
                warnings.append(f"{service}: No authentication components found")
    
    # Check security headers
    print("\n🛡️  Checking security headers configuration...")
    
    web_services = ["web", "marketplace"]
    for service in web_services:
        config_paths = [
            SERVICES_DIR / service / "nginx.conf",
            SERVICES_DIR / service / "server.ts",
            SERVICES_DIR / service / "server.js",
            SERVICES_DIR / service / "main.go"
        ]
        
        has_security_headers = False
        for config_path in config_paths:
            if config_path.exists():
                with open(config_path) as f:
                    content = f.read()
                    security_headers = [
                        "X-Frame-Options",
                        "X-Content-Type-Options",
                        "Strict-Transport-Security",
                        "Content-Security-Policy"
                    ]
                    
                    for header in security_headers:
                        if header in content:
                            has_security_headers = True
                            break
        
        if has_security_headers:
            print(f"  ✅ {service}: Security headers configured")
        else:
            warnings.append(f"{service}: Security headers not found")
    
    # Summary
    print("\n📊 Security Validation Summary:")
    print(f"  ❌ Critical Issues: {len(errors)}")
    print(f"  ⚠️  Warnings: {len(warnings)}")
    
    if errors:
        print("\n❌ Critical security issues:")
        for error in errors:
            print(f"  - {error}")
    
    if warnings and (args.strict or not errors):
        print("\n⚠️  Security warnings:")
        for warning in warnings:
            print(f"  - {warning}")
    
    return 1 if errors else 0


def validate_all(args) -> int:
    """Run all validation checks."""
    print("🔍 Running all validation checks...\n")
    
    results = {
        "Architecture": validate_architecture(args),
        "Contracts": validate_contracts(args),
        "Security": validate_security(args)
    }
    
    print("\n" + "="*60)
    print("📊 OVERALL VALIDATION SUMMARY")
    print("="*60)
    
    passed = sum(1 for r in results.values() if r == 0)
    failed = len(results) - passed
    
    for check, result in results.items():
        status = "✅ PASS" if result == 0 else "❌ FAIL"
        print(f"{check:.<40} {status}")
    
    print(f"\nTotal: {passed}/{len(results)} passed")
    
    return 0 if failed == 0 else 1


def main():
    parser = argparse.ArgumentParser(
        description="CCL Validation CLI - Validate project consistency and security",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  ccl-validate all                    # Run all validations
  ccl-validate architecture           # Check architecture consistency
  ccl-validate contracts              # Validate service contracts
  ccl-validate security               # Check security configurations
  ccl-validate all --strict          # Fail on warnings too
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Validation types')
    
    # Common arguments
    common_parser = argparse.ArgumentParser(add_help=False)
    common_parser.add_argument('--strict', action='store_true', help='Treat warnings as errors')
    
    # Architecture validation
    arch_parser = subparsers.add_parser('architecture', parents=[common_parser], help='Validate system architecture')
    
    # Contract validation
    contract_parser = subparsers.add_parser('contracts', parents=[common_parser], help='Validate service contracts')
    
    # Security validation
    security_parser = subparsers.add_parser('security', parents=[common_parser], help='Validate security configurations')
    
    # All validations
    all_parser = subparsers.add_parser('all', parents=[common_parser], help='Run all validations')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 0
    
    # Map commands to functions
    commands = {
        'architecture': validate_architecture,
        'contracts': validate_contracts,
        'security': validate_security,
        'all': validate_all
    }
    
    return commands[args.command](args)


if __name__ == '__main__':
    sys.exit(main())