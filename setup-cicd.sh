#!/bin/bash
# CCL CI/CD Pipeline Setup Script
# This script sets up the complete CI/CD pipeline for the CCL platform

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Make scripts executable
make_scripts_executable() {
    log "Making scripts executable..."
    
    find scripts/ -name "*.sh" -type f -exec chmod +x {} \;
    find scripts/ -name "*.py" -type f -exec chmod +x {} \;
    
    success "Scripts are now executable"
}

# Validate prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    local missing_tools=()
    
    # Check required tools
    local required_tools=(
        "git"
        "docker"
        "gcloud"
        "make"
    )
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        error "Missing required tools: ${missing_tools[*]}"
        echo "Please install the missing tools and run this script again."
        exit 1
    fi
    
    success "All required tools are installed"
}

# Setup GitHub repository
setup_github_repository() {
    log "Setting up GitHub repository..."
    
    # Check if we're in a git repository
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        error "Not in a git repository. Please run this script from the repository root."
        exit 1
    fi
    
    # Check if GitHub CLI is available
    if command -v gh &> /dev/null; then
        log "GitHub CLI detected, setting up repository secrets..."
        
        # Note: These would need to be set manually or through GitHub UI
        warning "Please set the following GitHub repository secrets:"
        echo "  - GCP_SA_KEY: Google Cloud Service Account key"
        echo "  - SONAR_TOKEN: SonarQube authentication token"
        echo "  - SLACK_WEBHOOK_URL: Slack webhook for notifications"
        echo "  - PAGERDUTY_SERVICE_KEY: PagerDuty integration key"
    else
        warning "GitHub CLI not installed. Please set repository secrets manually."
    fi
    
    success "GitHub repository setup instructions provided"
}

# Setup Google Cloud Platform
setup_gcp() {
    log "Setting up Google Cloud Platform..."
    
    # Check if gcloud is authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        warning "Not authenticated with gcloud. Please run 'gcloud auth login'"
        return
    fi
    
    # Get current project
    local current_project=$(gcloud config get-value project 2>/dev/null || echo "")
    
    if [[ -z "$current_project" ]]; then
        warning "No GCP project set. Please run 'gcloud config set project PROJECT_ID'"
        return
    fi
    
    log "Current GCP project: $current_project"
    
    # Enable required APIs
    local required_apis=(
        "run.googleapis.com"
        "cloudbuild.googleapis.com"
        "containerregistry.googleapis.com"
        "artifactregistry.googleapis.com"
        "monitoring.googleapis.com"
        "logging.googleapis.com"
        "secretmanager.googleapis.com"
    )
    
    for api in "${required_apis[@]}"; do
        log "Enabling API: $api"
        gcloud services enable "$api" --quiet || warning "Failed to enable $api"
    done
    
    success "GCP setup completed"
}

# Create sample service
create_sample_service() {
    log "Creating sample service structure..."
    
    local service_name="sample-service"
    
    if [[ -d "$service_name" ]]; then
        warning "Sample service already exists, skipping creation"
        return
    fi
    
    mkdir -p "$service_name"
    cd "$service_name"
    
    # Create Makefile
    cat > Makefile << 'EOF'
# Sample Service Makefile
include ../build/makefiles/common.mk

# Set language (change as needed)
LANGUAGE := python

# Service-specific targets can be added here
EOF
    
    # Create Dockerfile
    cat > Dockerfile << 'EOF'
FROM python:3.11-slim

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Change ownership to non-root user
RUN chown -R appuser:appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

# Expose port
EXPOSE 8080

# Run application
CMD ["python", "app.py"]
EOF
    
    # Create requirements.txt
    cat > requirements.txt << 'EOF'
flask==2.3.3
gunicorn==21.2.0
prometheus-client==0.17.1
EOF
    
    # Create simple Python app
    cat > app.py << 'EOF'
from flask import Flask, jsonify
import os

app = Flask(__name__)

@app.route('/health')
def health():
    return jsonify({"status": "healthy", "service": "sample-service"})

@app.route('/')
def hello():
    return jsonify({"message": "Hello from CCL Sample Service!"})

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port)
EOF
    
    # Create README
    cat > README.md << 'EOF'
# Sample Service

This is a sample service demonstrating the CCL CI/CD pipeline structure.

## Development

```bash
# Install dependencies
make deps

# Run tests
make test

# Build service
make build

# Run locally
make dev-run
```

## Deployment

The service is automatically deployed through the CI/CD pipeline when changes are merged.
EOF
    
    cd ..
    
    success "Sample service created in $service_name/"
}

# Validate setup
validate_setup() {
    log "Validating CI/CD setup..."
    
    # Check if all required directories exist
    local required_dirs=(
        ".github/workflows"
        ".github/actions"
        "build/makefiles"
        "build/quality-gates"
        "build/monitoring"
        "environments"
        "scripts/deploy"
        "docs/cicd"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            success "Directory exists: $dir"
        else
            error "Missing directory: $dir"
        fi
    done
    
    # Check if key files exist
    local required_files=(
        ".github/workflows/ci-common.yml"
        ".github/workflows/cd-deploy.yml"
        "build/makefiles/common.mk"
        "scripts/deploy/rollout.sh"
        "scripts/deploy/rollback.sh"
        "docs/cicd/README.md"
    )
    
    for file in "${required_files[@]}"; do
        if [[ -f "$file" ]]; then
            success "File exists: $file"
        else
            error "Missing file: $file"
        fi
    done
}

# Display next steps
show_next_steps() {
    echo ""
    log "CI/CD Pipeline Setup Complete!"
    echo ""
    echo "Next Steps:"
    echo ""
    echo "1. Configure GitHub Repository Secrets:"
    echo "   - GCP_SA_KEY: Service account key for deployments"
    echo "   - SONAR_TOKEN: SonarQube authentication token"
    echo "   - SLACK_WEBHOOK_URL: Slack notifications webhook"
    echo ""
    echo "2. Set up your first service:"
    echo "   - Copy the sample-service/ directory"
    echo "   - Modify for your specific language and requirements"
    echo "   - Create a GitHub workflow in .github/workflows/"
    echo ""
    echo "3. Configure environments:"
    echo "   - Review environments/*.yml files"
    echo "   - Update project IDs and resource configurations"
    echo "   - Set up GCP projects for dev/staging/production"
    echo ""
    echo "4. Test the pipeline:"
    echo "   - Push changes to a feature branch"
    echo "   - Create a pull request"
    echo "   - Verify CI pipeline runs successfully"
    echo ""
    echo "5. Set up monitoring:"
    echo "   - Import build/monitoring/deployment-dashboard.json to Cloud Monitoring"
    echo "   - Configure alerting policies from build/monitoring/alerting-policies.yml"
    echo "   - Set up notification channels"
    echo ""
    echo "Documentation:"
    echo "   - Main guide: docs/cicd/README.md"
    echo "   - Deployment strategies: docs/cicd/deployment-strategies.md"
    echo "   - Security guide: docs/cicd/security.md"
    echo "   - Troubleshooting: docs/cicd/troubleshooting.md"
    echo ""
    success "Happy deploying! 🚀"
}

# Main setup function
main() {
    echo "🚀 CCL CI/CD Pipeline Setup"
    echo "=========================="
    echo ""
    
    check_prerequisites
    make_scripts_executable
    setup_github_repository
    setup_gcp
    create_sample_service
    validate_setup
    show_next_steps
}

# Run main function
main "$@"
