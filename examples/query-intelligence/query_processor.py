"""
Example: Query Processing Pipeline for CCL Query Intelligence
Demonstrates natural language query processing with Vertex AI integration
"""

from typing import List, Dict, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime
import asyncio
import structlog
from google.cloud import aiplatform
from google.cloud.aiplatform import initializer
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

# Initialize structured logging
logger = structlog.get_logger()

@dataclass
class QueryContext:
    """Context for query processing"""
    repository_id: str
    user_id: str
    session_id: Optional[str] = None
    history: List[Dict[str, Any]] = field(default_factory=list)
    filters: Dict[str, Any] = field(default_factory=dict)

@dataclass
class CodeReference:
    """Reference to code location"""
    file_path: str
    start_line: int
    end_line: int
    snippet: str
    relevance_score: float

@dataclass
class QueryResult:
    """Result of query processing"""
    answer: str
    confidence: float
    references: List[CodeReference]
    execution_time_ms: float
    follow_up_questions: List[str] = field(default_factory=list)
    
class QueryProcessor:
    """Main query processing engine"""
    
    def __init__(
        self,
        project_id: str,
        location: str = "us-central1",
        model_name: str = "gemini-2.5-flash"
    ):
        self.project_id = project_id
        self.location = location
        self.model_name = model_name
        
        # Initialize Vertex AI
        aiplatform.init(project=project_id, location=location)
        
        # Initialize components
        self.embedder = CodeEmbedder()
        self.retriever = SemanticRetriever()
        self.generator = ResponseGenerator(model_name)
        
    async def process_query(
        self,
        query: str,
        context: QueryContext
    ) -> QueryResult:
        """Process a natural language query about code"""
        start_time = datetime.now()
        
        # Log query processing start
        logger.info(
            "processing_query",
            query=query,
            repository_id=context.repository_id,
            user_id=context.user_id
        )
        
        try:
            # Step 1: Understand query intent
            intent = await self._analyze_intent(query, context)
            
            # Step 2: Generate query embedding
            query_embedding = await self.embedder.embed_text(query)
            
            # Step 3: Retrieve relevant code chunks
            code_chunks = await self.retriever.search(
                embedding=query_embedding,
                repository_id=context.repository_id,
                filters=self._build_filters(intent, context),
                limit=20
            )
            
            # Step 4: Rerank results based on intent
            ranked_chunks = await self._rerank_chunks(
                query, intent, code_chunks
            )
            
            # Step 5: Generate response
            response = await self.generator.generate(
                query=query,
                intent=intent,
                code_chunks=ranked_chunks[:10],
                context=context
            )
            
            # Step 6: Extract code references
            references = self._extract_references(ranked_chunks[:5])
            
            # Step 7: Generate follow-up questions
            follow_ups = await self._generate_follow_ups(
                query, response, context
            )
            
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # Log successful processing
            logger.info(
                "query_processed",
                query_id=context.session_id,
                execution_time_ms=execution_time,
                confidence=response.confidence,
                reference_count=len(references)
            )
            
            return QueryResult(
                answer=response.text,
                confidence=response.confidence,
                references=references,
                execution_time_ms=execution_time,
                follow_up_questions=follow_ups
            )
            
        except Exception as e:
            logger.error(
                "query_processing_failed",
                query=query,
                error=str(e),
                exc_info=True
            )
            raise QueryProcessingError(f"Failed to process query: {str(e)}")
    
    async def _analyze_intent(
        self,
        query: str,
        context: QueryContext
    ) -> 'QueryIntent':
        """Analyze the intent of the query"""
        prompt = f"""
        Analyze the following code-related query and identify:
        1. Primary intent (explain, find, debug, refactor, etc.)
        2. Code elements mentioned (functions, classes, patterns)
        3. Scope (specific file, entire codebase, module)
        4. Required context depth
        
        Query: {query}
        Repository context: {context.repository_id}
        
        Return a structured analysis.
        """
        
        response = await self.generator.model.predict(prompt)
        return QueryIntent.from_response(response)
    
    async def _rerank_chunks(
        self,
        query: str,
        intent: 'QueryIntent',
        chunks: List['CodeChunk']
    ) -> List['CodeChunk']:
        """Rerank code chunks based on intent and relevance"""
        # Create feature vectors for each chunk
        features = []
        for chunk in chunks:
            feature_vector = self._extract_chunk_features(chunk, intent)
            features.append(feature_vector)
        
        # Calculate relevance scores
        query_features = self._extract_query_features(query, intent)
        scores = cosine_similarity([query_features], features)[0]
        
        # Combine with original scores
        for i, chunk in enumerate(chunks):
            chunk.combined_score = (
                0.6 * chunk.similarity_score +  # Semantic similarity
                0.3 * scores[i] +               # Intent-based score
                0.1 * chunk.recency_score       # Recency boost
            )
        
        # Sort by combined score
        return sorted(chunks, key=lambda x: x.combined_score, reverse=True)
    
    def _extract_references(
        self,
        chunks: List['CodeChunk']
    ) -> List[CodeReference]:
        """Extract code references from chunks"""
        references = []
        
        for chunk in chunks:
            ref = CodeReference(
                file_path=chunk.file_path,
                start_line=chunk.start_line,
                end_line=chunk.end_line,
                snippet=self._format_snippet(chunk.content),
                relevance_score=chunk.combined_score
            )
            references.append(ref)
        
        return references
    
    async def _generate_follow_ups(
        self,
        query: str,
        response: 'GeneratedResponse',
        context: QueryContext
    ) -> List[str]:
        """Generate relevant follow-up questions"""
        prompt = f"""
        Based on this Q&A about code:
        Question: {query}
        Answer: {response.text}
        
        Generate 3 relevant follow-up questions that would help the user
        dive deeper into the topic or explore related areas.
        """
        
        follow_ups_response = await self.generator.model.predict(prompt)
        return self._parse_follow_ups(follow_ups_response)
    
    def _format_snippet(self, content: str, max_lines: int = 10) -> str:
        """Format code snippet for display"""
        lines = content.split('\n')
        if len(lines) > max_lines:
            # Take first and last parts
            head = lines[:max_lines//2]
            tail = lines[-(max_lines//2):]
            return '\n'.join(head) + '\n...\n' + '\n'.join(tail)
        return content

class CodeEmbedder:
    """Handles text and code embedding"""
    
    def __init__(self, model_name: str = "textembedding-gecko"):
        self.model = aiplatform.TextEmbeddingModel.from_pretrained(model_name)
    
    async def embed_text(self, text: str) -> np.ndarray:
        """Generate embedding for text"""
        embeddings = await asyncio.to_thread(
            self.model.get_embeddings,
            [text]
        )
        return np.array(embeddings[0].values)
    
    async def embed_code(self, code: str, language: str) -> np.ndarray:
        """Generate embedding for code with language context"""
        # Prepend language context for better embeddings
        contextualized = f"[{language}]\n{code}"
        return await self.embed_text(contextualized)

class ResponseGenerator:
    """Generates natural language responses"""
    
    def __init__(self, model_name: str):
        self.model = aiplatform.GenerativeModel(model_name)
    
    async def generate(
        self,
        query: str,
        intent: 'QueryIntent',
        code_chunks: List['CodeChunk'],
        context: QueryContext
    ) -> 'GeneratedResponse':
        """Generate response based on query and code context"""
        
        # Build context for generation
        code_context = self._build_code_context(code_chunks)
        
        prompt = f"""
        You are an AI assistant helping developers understand their codebase.
        
        User Query: {query}
        Intent: {intent.primary_intent}
        
        Relevant Code Context:
        {code_context}
        
        Previous Context: {self._format_history(context.history[-3:])}
        
        Provide a clear, helpful response that:
        1. Directly answers the question
        2. References specific code when relevant
        3. Explains technical concepts clearly
        4. Suggests next steps if applicable
        
        Keep the response concise but comprehensive.
        """
        
        response = await asyncio.to_thread(
            self.model.generate_content,
            prompt,
            generation_config={
                "temperature": 0.3,
                "max_output_tokens": 2048,
            }
        )
        
        return GeneratedResponse(
            text=response.text,
            confidence=self._calculate_confidence(response, code_chunks)
        )
    
    def _build_code_context(self, chunks: List['CodeChunk']) -> str:
        """Build formatted code context from chunks"""
        context_parts = []
        
        for i, chunk in enumerate(chunks[:5]):  # Top 5 chunks
            context_parts.append(f"""
File: {chunk.file_path} (lines {chunk.start_line}-{chunk.end_line})
```{chunk.language}
{chunk.content}
```
""")
        
        return "\n---\n".join(context_parts)
    
    def _calculate_confidence(
        self,
        response: Any,
        chunks: List['CodeChunk']
    ) -> float:
        """Calculate confidence score for the response"""
        # Base confidence from model
        base_confidence = 0.7
        
        # Boost based on code chunk relevance
        if chunks:
            avg_relevance = sum(c.combined_score for c in chunks[:3]) / 3
            base_confidence += 0.2 * avg_relevance
        
        # Cap at 0.95
        return min(base_confidence, 0.95)

# Supporting classes
@dataclass
class QueryIntent:
    primary_intent: str
    code_elements: List[str]
    scope: str
    context_depth: str
    
    @classmethod
    def from_response(cls, response: str) -> 'QueryIntent':
        # Parse LLM response into structured intent
        # Implementation depends on response format
        pass

@dataclass
class CodeChunk:
    file_path: str
    start_line: int
    end_line: int
    content: str
    language: str
    similarity_score: float
    recency_score: float
    combined_score: float = 0.0

@dataclass
class GeneratedResponse:
    text: str
    confidence: float

class QueryProcessingError(Exception):
    """Custom exception for query processing errors"""
    pass

# Example usage
async def main():
    # Initialize processor
    processor = QueryProcessor(
        project_id="ccl-production",
        location="us-central1"
    )
    
    # Create context
    context = QueryContext(
        repository_id="repo-123",
        user_id="user-456",
        session_id="session-789"
    )
    
    # Process query
    result = await processor.process_query(
        "How does the authentication middleware work?",
        context
    )
    
    print(f"Answer: {result.answer}")
    print(f"Confidence: {result.confidence}")
    print(f"References: {len(result.references)}")
    print(f"Execution time: {result.execution_time_ms}ms")

if __name__ == "__main__":
    asyncio.run(main())